/*    */ package WEB-INF.classes.com.lenovo.iqs.sap;
/*    */ 
/*    */ import com.lenovo.iqs.sap.SAPConfig;
/*    */ import com.sap.conn.jco.JCoDestination;
/*    */ import com.sap.conn.jco.JCoDestinationManager;
/*    */ import com.sap.conn.jco.JCoException;
/*    */ import java.io.File;
/*    */ import java.io.FileOutputStream;
/*    */ import java.io.IOException;
/*    */ import java.util.Properties;
/*    */ import org.apache.logging.log4j.LogManager;
/*    */ import org.apache.logging.log4j.Logger;
/*    */ import org.springframework.beans.factory.annotation.Autowired;
/*    */ import org.springframework.stereotype.Component;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Component
/*    */ public class SAPConnection
/*    */ {
/* 24 */   private static final Logger log = LogManager.getLogger(com.lenovo.iqs.sap.SAPConnection.class);
/*    */ 
/*    */ 
/*    */   
/* 28 */   private String ABAP_AS_POOLED = "ABAP_AS_WITH_POOL";
/*    */   
/*    */   @Autowired
/*    */   private SAPConnection(SAPConfig config) {
/* 32 */     Properties connectProperties = new Properties();
/* 33 */     connectProperties.setProperty("jco.client.ashost", config.appServerHost);
/* 34 */     connectProperties.setProperty("jco.client.sysnr", config.systemNumber);
/* 35 */     connectProperties.setProperty("jco.client.client", config.client);
/* 36 */     connectProperties.setProperty("jco.client.user", config.user);
/* 37 */     connectProperties.setProperty("jco.client.passwd", config.password);
/* 38 */     connectProperties.setProperty("jco.client.lang", config.language);
/* 39 */     connectProperties.setProperty("jco.destination.pool_capacity", config.poolCapacity);
/* 40 */     connectProperties.setProperty("jco.destination.peak_limit", config.peakLimit);
/* 41 */     createDataFile(this.ABAP_AS_POOLED, "jcoDestination", connectProperties);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private void createDataFile(String name, String suffix, Properties properties) {
/* 55 */     File cfg = new File(name + "." + suffix);
/* 56 */     FileOutputStream fos = null;
/*    */     try {
/* 58 */       fos = new FileOutputStream(cfg, false);
/* 59 */       properties.store(fos, "RFC connction infos");
/* 60 */       fos.close();
/* 61 */     } catch (Exception e) {
/* 62 */       log.error("Create Data file fault, error msg: " + e.toString(), e);
/*    */     } finally {
/* 64 */       if (fos != null) {
/*    */         try {
/* 66 */           fos.close();
/* 67 */         } catch (IOException e) {
/* 68 */           log.error("Close fox fault, error msg: " + e.toString(), e);
/*    */         } 
/*    */       }
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public JCoDestination connect() {
/* 80 */     JCoDestination destination = null;
/*    */     try {
/* 82 */       destination = JCoDestinationManager.getDestination(this.ABAP_AS_POOLED);
/* 83 */     } catch (JCoException e) {
/* 84 */       log.error("Connect SAP fault, error msg: " + e.toString(), (Throwable)e);
/*    */     } 
/* 86 */     return destination;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\sap\SAPConnection.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */