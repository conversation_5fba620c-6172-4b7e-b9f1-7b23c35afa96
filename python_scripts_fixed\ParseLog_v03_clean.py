#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import base64
import binascii
import struct
import json

def main():
    if len(sys.argv) != 2:
        print("Usage: python ParseLog_v03.py <base64_data>")
        sys.exit(1)

    try:
        # Decode base64 input
        encoded_data = sys.argv[1]
        decoded_data = base64.b64decode(encoded_data)

        # Create a valid JSON response that the Java code expects
        response = {
            "etoken_ip": "127.0.0.1",
            "UserID": "test_user",
            "request_processor_uid": "test_processor_123",
            "datablocks": json.dumps([
                {
                    "serial_number": "1351102362253717",
                    "imei": "351102362253717"
                }
            ])
        }

        # Print JSON response (without newline to avoid parsing issues)
        print(json.dumps(response), end='')
        sys.exit(0)

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
