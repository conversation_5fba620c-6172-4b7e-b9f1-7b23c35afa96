<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdR12InboundHistoryMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdR12InboundHistory" >
    <result column="auto_id" property="autoId" jdbcType="INTEGER" />
    <result column="serial_no" property="serialNo" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="batch_number" property="batchNumber" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdR12InboundHistory" >
    insert into ib_t_upd_r12_inbound_history (auto_id, serial_no, create_time, 
      batch_number)
    values (#{autoId,jdbcType=INTEGER}, #{serialNo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{batchNumber,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdR12InboundHistory" >
    insert into ib_t_upd_r12_inbound_history
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="autoId != null" >
        auto_id,
      </if>
      <if test="serialNo != null" >
        serial_no,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="batchNumber != null" >
        batch_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="autoId != null" >
        #{autoId,jdbcType=INTEGER},
      </if>
      <if test="serialNo != null" >
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNumber != null" >
        #{batchNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>