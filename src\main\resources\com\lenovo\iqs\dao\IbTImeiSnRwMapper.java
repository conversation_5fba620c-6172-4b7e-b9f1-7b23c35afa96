package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTImeiSnRw;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTImeiSnRwMapper {
  int deleteByPrimaryKey(Integer paramInteger);
  
  int insert(IbTImeiSnRw paramIbTImeiSnRw);
  
  IbTImeiSnRw selectByPrimaryKey(Integer paramInteger);
  
  int updateByPrimaryKey(IbTImeiSnRw paramIbTImeiSnRw);
  
  List<IbTImeiSnRw> selectByImei(@Param("imeiCode") String paramString);
  
  List<IbTImeiSnRw> selectBySn(@Param("serialNumber") String paramString);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTImeiSnRwMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */