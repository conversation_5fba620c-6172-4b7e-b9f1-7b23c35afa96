<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdR12InboundMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdR12Inbound" >
    <id column="auto_id" property="autoId" jdbcType="INTEGER" />
    <result column="serial_no" property="serialNo" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="batch_number" property="batchNumber" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    auto_id, serial_no, create_time, batch_number
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_upd_r12_inbound
    where auto_id = #{autoId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from ib_t_upd_r12_inbound
    where auto_id = #{autoId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdR12Inbound" >
    <selectKey resultType="java.lang.Integer" keyProperty="autoId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_upd_r12_inbound (serial_no, create_time, batch_number
      )
    values (#{serialNo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{batchNumber,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdR12Inbound" >
    <selectKey resultType="java.lang.Integer" keyProperty="autoId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_upd_r12_inbound
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="serialNo != null" >
        serial_no,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="batchNumber != null" >
        batch_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="serialNo != null" >
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNumber != null" >
        #{batchNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTUpdR12Inbound" >
    update ib_t_upd_r12_inbound
    <set >
      <if test="serialNo != null" >
        serial_no = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNumber != null" >
        batch_number = #{batchNumber,jdbcType=VARCHAR},
      </if>
    </set>
    where auto_id = #{autoId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdR12Inbound" >
    update ib_t_upd_r12_inbound
    set serial_no = #{serialNo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      batch_number = #{batchNumber,jdbcType=VARCHAR}
    where auto_id = #{autoId,jdbcType=INTEGER}
  </update>
</mapper>