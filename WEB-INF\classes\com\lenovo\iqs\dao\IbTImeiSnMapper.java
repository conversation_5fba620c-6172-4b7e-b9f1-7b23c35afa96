package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTImeiSn;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTImeiSnMapper {
  int deleteByPrimaryKey(Integer paramInteger);
  
  int insert(IbTImeiSn paramIbTImeiSn);
  
  IbTImeiSn selectByPrimaryKey(Integer paramInteger);
  
  int updateByPrimaryKey(IbTImeiSn paramIbTImeiSn);
  
  List<IbTImeiSn> selectByImei(@Param("imeiCode") String paramString);
  
  List<IbTImeiSn> selectBySn(@Param("serialNumber") String paramString);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTImeiSnMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */