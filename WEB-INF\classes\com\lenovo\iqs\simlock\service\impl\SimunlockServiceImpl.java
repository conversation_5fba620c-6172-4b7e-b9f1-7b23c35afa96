/*     */ package WEB-INF.classes.com.lenovo.iqs.simlock.service.impl;
/*     */ import com.alibaba.druid.util.StringUtils;
/*     */ import com.lenovo.iqs.datablocksign.bean.Config;
/*     */ import com.lenovo.iqs.datablocksign.bean.RSDRules;
/*     */ import com.lenovo.iqs.datablocksign.bean.RSDValidationRequest;
/*     */ import com.lenovo.iqs.datablocksign.bean.RSDValidationResponse;
/*     */ import com.lenovo.iqs.rsu.dao.RsuWebserviceMapper;
/*     */ import com.lenovo.iqs.simlock.bean.KSUnlockRequest;
/*     */ import com.lenovo.iqs.simlock.bean.KSUnlockResponse;
/*     */ import com.lenovo.iqs.simlock.bean.KeysRequest;
/*     */ import com.lenovo.iqs.simlock.bean.KeysResponse;
/*     */ import com.lenovo.iqs.simlock.bean.SimunlockRequest;
/*     */ import com.lenovo.iqs.simlock.bean.SimunlockResponse;
/*     */ import com.lenovo.iqs.utils.HttpClientUtils;
/*     */ import java.util.List;
/*     */ import org.apache.logging.log4j.LogManager;
/*     */ import org.apache.logging.log4j.Logger;
/*     */ import org.springframework.beans.factory.annotation.Autowired;
/*     */ import org.springframework.beans.factory.annotation.Value;
/*     */ 
/*     */ @Service
/*     */ public class SimunlockServiceImpl implements SimunlockService {
/*  23 */   private static final Logger log = LogManager.getLogger(com.lenovo.iqs.simlock.service.impl.SimunlockServiceImpl.class);
/*     */   
/*     */   @Value("${GPS_URL}")
/*     */   private String gpsUrl;
/*     */   
/*     */   @Value("${GPS_USER}")
/*     */   private String authUser;
/*     */   
/*     */   @Value("${GPS_PWD}")
/*     */   private String authPwd;
/*     */   
/*     */   @Autowired
/*     */   private RsuWebserviceMapper mapper;
/*     */   @Autowired
/*     */   private RsdValidationService rsdValidator;
/*     */   @Autowired
/*     */   private IbTUpdConfigMapper ibTUpdConfigMapper;
/*  40 */   private String rsuSoapXml = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:rsu=\"http://rsu.programmingservice.cfc.nextest.globaltest.motorolamobility.com/\">   <soapenv:Header/>   <soapenv:Body>      <rsu:RequestDataSign>         <!--Optional:-->         <proid>{PRODID}</proid>         <!--Optional:-->         <type>{TYPE}</type>         <!--Optional:-->         <keyname>{keyname}</keyname>         <!--Optional:-->         <data>{data}</data>      </rsu:RequestDataSign>   </soapenv:Body></soapenv:Envelope>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  56 */   private String certsXml = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:rsu=\"http://rsu.programmingservice.cfc.nextest.globaltest.motorolamobility.com/\">   <soapenv:Header/>   <soapenv:Body>      <rsu:RetrieveCertByType>         <!--Optional:-->         <certModel>{MODEL}</certModel>         <!--Optional:-->         <certType>{TYPE}</certType>      </rsu:RetrieveCertByType>   </soapenv:Body></soapenv:Envelope>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  68 */   private String ksXml = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:rsu=\"http://rsu.programmingservice.cfc.nextest.globaltest.motorolamobility.com/\">   <soapenv:Header/>   <soapenv:Body>      <rsu:RequestUnlockPassword>         <!--Optional:-->         <product_name>{PNAME}</product_name>         <!--Optional:-->         <cpu_id>{CPU_ID}</cpu_id>         <!--Optional:-->         <build_type>{BUILD_TYPE}</build_type>      </rsu:RequestUnlockPassword>   </soapenv:Body></soapenv:Envelope>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String buildParams(SimunlockRequest req) {
/*  82 */     return this.rsuSoapXml.replace("{PRODID}", req.getProd_id())
/*  83 */       .replace("{TYPE}", req.getType())
/*  84 */       .replace("{keyname}", req.getKeyname())
/*  85 */       .replace("{data}", req.getData());
/*     */   }
/*     */   
/*     */   private String buildParams(KeysRequest req) {
/*  89 */     return this.certsXml.replace("{MODEL}", req.getCertModel())
/*  90 */       .replace("{TYPE}", req.getCertType());
/*     */   }
/*     */   
/*     */   private String buildParams(KSUnlockRequest req) {
/*  94 */     return this.ksXml.replace("{PNAME}", req.getPname())
/*  95 */       .replace("{BUILD_TYPE}", req.getBuildType())
/*  96 */       .replace("{CPU_ID}", req.getCpuid());
/*     */   }
/*     */ 
/*     */   
/*     */   public SimunlockResponse processRsuRequest(SimunlockRequest request, String userid, String publicip) throws Exception {
/* 101 */     SimunlockResponse rsuResponse = validateRequestParam(request);
/* 102 */     if (rsuResponse != null) {
/* 103 */       return rsuResponse;
/*     */     }
/* 105 */     String soapRsuParam = buildParams(request);
/* 106 */     String response = HttpClientUtils.postWebservice(this.gpsUrl, soapRsuParam, "", this.authUser, this.authPwd);
/* 107 */     log.info("after the gps call");
/* 108 */     log.info(response + " --> GPS response");
/* 109 */     rsuResponse = SimunlockResponse.build(response);
/* 110 */     return rsuResponse;
/*     */   }
/*     */ 
/*     */   
/*     */   public KeysResponse processRsuRequest(KeysRequest request, String userid, String publicip) throws Exception {
/* 115 */     KeysResponse rsuResponse = validateRequestParam(request);
/* 116 */     if (rsuResponse != null) {
/* 117 */       return rsuResponse;
/*     */     }
/* 119 */     String soapRsuParam = buildParams(request);
/* 120 */     String response = HttpClientUtils.postWebservice(this.gpsUrl, soapRsuParam, "", this.authUser, this.authPwd);
/* 121 */     log.info("after the gps call");
/* 122 */     log.info(response + " --> GPS response");
/* 123 */     rsuResponse = KeysResponse.build(response);
/* 124 */     return rsuResponse;
/*     */   }
/*     */   
/*     */   private SimunlockResponse validateRequestParam(SimunlockRequest requestBean) {
/*     */     boolean isPrepaidAllowed;
/* 129 */     SimunlockResponse response = null;
/*     */ 
/*     */     
/* 132 */     String imei = requestBean.getNewIMEI();
/* 133 */     if (StringUtils.isEmpty(imei)) {
/* 134 */       response = new SimunlockResponse();
/* 135 */       response.setResponseCode("5070");
/* 136 */       response.setResponseMsg("Invalid serialNumber");
/* 137 */       return response;
/*     */     } 
/*     */     
/* 140 */     if (StringUtils.isEmpty(requestBean.getUserId())) {
/* 141 */       response = new SimunlockResponse();
/* 142 */       response.setResponseCode("5079");
/* 143 */       response.setResponseMsg("RSU Service Invalid RSD User");
/* 144 */       return response;
/*     */     } 
/*     */     
/* 147 */     String mascId = requestBean.getMASCID();
/* 148 */     if (StringUtils.isEmpty(mascId)) {
/* 149 */       response = new SimunlockResponse();
/* 150 */       response.setResponseCode("5080");
/* 151 */       response.setResponseMsg("RSU Service Invalid Masc ID");
/* 152 */       return response;
/*     */     } 
/*     */     
/* 155 */     String suid = requestBean.getProd_id();
/* 156 */     if (StringUtils.isEmpty(suid)) {
/* 157 */       response = new SimunlockResponse();
/* 158 */       response.setResponseCode("5082");
/* 159 */       response.setResponseMsg("GPS Subsidy fail - Prodid null");
/* 160 */       return response;
/*     */     } 
/*     */ 
/*     */     
/* 164 */     String keyname = requestBean.getKeyname();
/* 165 */     if (StringUtils.isEmpty(keyname)) {
/* 166 */       response = new SimunlockResponse();
/* 167 */       response.setResponseCode("5083");
/* 168 */       response.setResponseMsg("GPS Subsidy fail - keyname null");
/* 169 */       return response;
/*     */     } 
/*     */     
/* 172 */     String data = requestBean.getData();
/* 173 */     if (StringUtils.isEmpty(data)) {
/* 174 */       response = new SimunlockResponse();
/* 175 */       response.setResponseCode("5086");
/* 176 */       response.setResponseMsg("GPS Subsidy fail - Data null");
/* 177 */       return response;
/*     */     } 
/*     */     
/* 180 */     String type = requestBean.getType();
/* 181 */     if (StringUtils.isEmpty(type)) {
/* 182 */       response = new SimunlockResponse();
/*     */       
/* 184 */       response.setResponseCode("5085");
/* 185 */       response.setResponseMsg("GPS Subsidy fail - type null");
/* 186 */       return response;
/*     */     } 
/*     */     
/* 189 */     String cip = requestBean.getClientIP();
/* 190 */     if (StringUtils.isEmpty(cip)) {
/* 191 */       response = new SimunlockResponse();
/*     */       
/* 193 */       response.setResponseCode("5088");
/* 194 */       response.setResponseMsg("GPS Subsidy fail - Client is null or not valid");
/* 195 */       return response;
/*     */     } 
/*     */ 
/*     */     
/* 199 */     String clrqt = requestBean.getClientReqType();
/* 200 */     if (StringUtils.isEmpty(clrqt)) {
/* 201 */       response = new SimunlockResponse();
/*     */       
/* 203 */       response.setResponseCode("5089");
/* 204 */       response.setResponseMsg("GPS Subsidy fail - ClientReqType is null or not valid");
/* 205 */       return response;
/*     */     } 
/*     */     
/* 208 */     String rlid = requestBean.getRsd_log_id();
/* 209 */     if (StringUtils.isEmpty(rlid)) {
/* 210 */       response = new SimunlockResponse();
/*     */       
/* 212 */       response.setResponseCode("5087");
/* 213 */       response.setResponseMsg("GPS Subsidy fail - RSD LOGID cannot be null");
/* 214 */       return response;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 221 */     if (this.ibTUpdConfigMapper.countBySectionAndKey("DATABLOCK_SIGN", "SKIP_RSD_VALIDATION") == 0) {
/* 222 */       log.info("Switch not found. Using RSD Validation");
/*     */ 
/*     */ 
/*     */       
/* 226 */       RSDValidationRequest vrequest = buildValidationRequest(requestBean.getClientIP(), requestBean.getPublicIP(), requestBean.getClientReqType(), requestBean.getUserId());
/* 227 */       log.info(vrequest);
/*     */       
/*     */       try {
/* 230 */         RSDValidationResponse rsdresponse = this.rsdValidator.validateRequest(vrequest);
/* 231 */         log.info("validation step completed");
/* 232 */         requestBean.setRsdResponse(rsdresponse.toString());
/* 233 */         List<Config> configs = this.ibTUpdConfigMapper.getConfigRules();
/* 234 */         RSDRules RSDValidation = new RSDRules(configs, rsdresponse);
/* 235 */         if (!RSDValidation.isEtokenAllowed()) {
/* 236 */           log.info("Error while validating request by RSD Etoken not allowed" + imei);
/* 237 */           response = new SimunlockResponse();
/* 238 */           String[] error = "7058, Etoken Not allowed as per RSD validation".split(",");
/* 239 */           response.setResponseCode(error[0]);
/*     */           
/* 241 */           response.setResponseMsg(error[1]);
/* 242 */           return response;
/*     */         } 
/* 244 */         if (!RSDValidation.isPublicIPAllowed()) {
/* 245 */           log.info("Error while validating request by RSD publicip not allowed" + imei);
/* 246 */           response = new SimunlockResponse();
/* 247 */           String[] error = "7059, publicip Not allowed as per RSD validation".split(",");
/* 248 */           response.setResponseCode(error[0]);
/* 249 */           response.setResponseMsg(error[1]);
/* 250 */           return response;
/*     */         } 
/* 252 */         if (!RSDValidation.isUserIdAllowed()) {
/* 253 */           log.info("Error while validating request by RSD userid not allowed" + imei);
/* 254 */           response = new SimunlockResponse();
/* 255 */           String[] error = "7060, userid Not allowed as per RSD validation".split(",");
/* 256 */           response.setResponseCode(error[0]);
/* 257 */           response.setResponseMsg(error[1]);
/* 258 */           return response;
/*     */         } 
/* 260 */         if (!RSDValidation.isReqTypeAllowed()) {
/* 261 */           log.info("Error while validating request by RSD reType not allowed for user/etoken" + imei);
/* 262 */           response = new SimunlockResponse();
/* 263 */           String[] error = "7061, reqType Not allowed as per RSD validation".split(",");
/* 264 */           response.setResponseCode(error[0]);
/* 265 */           response.setResponseMsg(error[1]);
/* 266 */           return response;
/*     */         } 
/* 268 */         isPrepaidAllowed = RSDValidation.isPrepaidAllowed();
/* 269 */       } catch (Exception e) {
/* 270 */         log.error(e.getMessage());
/* 271 */         log.info("Error while validating request by RSD" + imei);
/* 272 */         requestBean.setRsdResponse(e.getMessage());
/* 273 */         response = new SimunlockResponse();
/* 274 */         String[] error = "7057, RSD Validation service failed with exception".split(",");
/* 275 */         response.setResponseCode(error[0]);
/* 276 */         response.setResponseMsg(error[1]);
/* 277 */         return response;
/*     */       } 
/*     */     } else {
/* 280 */       log.info("Switch found. Using RSD Validation");
/* 281 */       isPrepaidAllowed = (this.ibTUpdConfigMapper.countBySectionAndKey("EtokenIP", requestBean.getClientIP()) != 0);
/* 282 */       log.info("isPrepaidallowed--> " + isPrepaidAllowed);
/*     */     } 
/*     */ 
/*     */     
/* 286 */     if (!isPrepaidAllowed) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 300 */       if (this.ibTUpdConfigMapper.countForbiddenIMEI(imei) > 0 && this.ibTUpdConfigMapper.countExceptionIMEI(imei) == 0) {
/* 301 */         log.info("DatablockSign validate error: request serial is " + requestBean.getNewIMEI() + "is forbidden!");
/* 302 */         requestBean.setError("IMEI: " + imei + " in prepay model");
/* 303 */         response = new SimunlockResponse();
/* 304 */         String[] error = "7051,Serial is Forbidden".split(",");
/* 305 */         response.setResponseCode(error[0]);
/* 306 */         response.setResponseMsg(error[1]);
/* 307 */         return response;
/*     */       } 
/*     */     } else {
/* 310 */       requestBean.setEtokenInException(true);
/*     */     } 
/* 312 */     return response;
/*     */   }
/*     */ 
/*     */   
/*     */   private KeysResponse validateRequestParam(KeysRequest requestBean) {
/*     */     boolean isPrepaidAllowed;
/* 318 */     KeysResponse response = null;
/*     */ 
/*     */     
/* 321 */     String imei = requestBean.getNewIMEI();
/* 322 */     if (StringUtils.isEmpty(imei)) {
/* 323 */       response = new KeysResponse();
/* 324 */       response.setResponseCode("5070");
/* 325 */       response.setResponseMsg("Invalid serialNumber");
/* 326 */       return response;
/*     */     } 
/*     */     
/* 329 */     if (StringUtils.isEmpty(requestBean.getUserId())) {
/* 330 */       response = new KeysResponse();
/* 331 */       response.setResponseCode("5079");
/* 332 */       response.setResponseMsg("RSU Service Invalid RSD User");
/* 333 */       return response;
/*     */     } 
/*     */     
/* 336 */     String mascId = requestBean.getMASCID();
/* 337 */     if (StringUtils.isEmpty(mascId)) {
/* 338 */       response = new KeysResponse();
/* 339 */       response.setResponseCode("5080");
/* 340 */       response.setResponseMsg("RSU Service Invalid Masc ID");
/* 341 */       return response;
/*     */     } 
/*     */     
/* 344 */     String suid = requestBean.getCertModel();
/*     */ 
/*     */     
/* 347 */     if (StringUtils.isEmpty(suid)) {
/* 348 */       response = new KeysResponse();
/* 349 */       response.setResponseCode("5082");
/* 350 */       response.setResponseMsg("GPS dispatch keys fail - certmodel null");
/* 351 */       return response;
/*     */     } 
/* 353 */     String correctModel = this.mapper.getCorrectModel(suid);
/* 354 */     suid = StringUtils.isEmpty(correctModel) ? suid : correctModel;
/* 355 */     requestBean.setCertModel(suid);
/*     */ 
/*     */ 
/*     */     
/* 359 */     String keyname = requestBean.getCertType();
/* 360 */     if (StringUtils.isEmpty(keyname)) {
/* 361 */       response = new KeysResponse();
/* 362 */       response.setResponseCode("5082");
/* 363 */       response.setResponseMsg("GPS dispatch keys fail - certtype null");
/* 364 */       return response;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 384 */     String cip = requestBean.getClientIP();
/* 385 */     if (StringUtils.isEmpty(cip)) {
/* 386 */       response = new KeysResponse();
/*     */       
/* 388 */       response.setResponseCode("5088");
/* 389 */       response.setResponseMsg("GPS Subsidy fail - Client is null or not valid");
/* 390 */       return response;
/*     */     } 
/*     */ 
/*     */     
/* 394 */     String clrqt = requestBean.getClientReqType();
/* 395 */     if (StringUtils.isEmpty(clrqt)) {
/* 396 */       response = new KeysResponse();
/*     */       
/* 398 */       response.setResponseCode("5089");
/* 399 */       response.setResponseMsg("GPS Subsidy fail - ClientReqType is null or not valid");
/* 400 */       return response;
/*     */     } 
/*     */     
/* 403 */     String rlid = requestBean.getRsd_log_id();
/* 404 */     if (StringUtils.isEmpty(rlid)) {
/* 405 */       response = new KeysResponse();
/*     */       
/* 407 */       response.setResponseCode("5087");
/* 408 */       response.setResponseMsg("GPS Subsidy fail - RSD LOGID cannot be null");
/* 409 */       return response;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 416 */     if (this.ibTUpdConfigMapper.countBySectionAndKey("DATABLOCK_SIGN", "SKIP_RSD_VALIDATION") == 0) {
/* 417 */       log.info("Switch not found. Using RSD Validation");
/*     */ 
/*     */ 
/*     */       
/* 421 */       RSDValidationRequest vrequest = buildValidationRequest(requestBean.getClientIP(), requestBean.getPublicIP(), requestBean.getClientReqType(), requestBean.getUserId());
/* 422 */       log.info(vrequest);
/*     */       
/*     */       try {
/* 425 */         RSDValidationResponse rsdresponse = this.rsdValidator.validateRequest(vrequest);
/* 426 */         log.info("validation step completed");
/* 427 */         requestBean.setRsdResponse(rsdresponse.toString());
/* 428 */         List<Config> configs = this.ibTUpdConfigMapper.getConfigRules();
/* 429 */         RSDRules RSDValidation = new RSDRules(configs, rsdresponse);
/* 430 */         if (!RSDValidation.isEtokenAllowed()) {
/* 431 */           log.info("Error while validating request by RSD Etoken not allowed" + imei);
/* 432 */           response = new KeysResponse();
/* 433 */           String[] error = "7058, Etoken Not allowed as per RSD validation".split(",");
/* 434 */           response.setResponseCode(error[0]);
/*     */           
/* 436 */           response.setResponseMsg(error[1]);
/* 437 */           return response;
/*     */         } 
/* 439 */         if (!RSDValidation.isPublicIPAllowed()) {
/* 440 */           log.info("Error while validating request by RSD publicip not allowed" + imei);
/* 441 */           response = new KeysResponse();
/* 442 */           String[] error = "7059, publicip Not allowed as per RSD validation".split(",");
/* 443 */           response.setResponseCode(error[0]);
/* 444 */           response.setResponseMsg(error[1]);
/* 445 */           return response;
/*     */         } 
/* 447 */         if (!RSDValidation.isUserIdAllowed()) {
/* 448 */           log.info("Error while validating request by RSD userid not allowed" + imei);
/* 449 */           response = new KeysResponse();
/* 450 */           String[] error = "7060, userid Not allowed as per RSD validation".split(",");
/* 451 */           response.setResponseCode(error[0]);
/* 452 */           response.setResponseMsg(error[1]);
/* 453 */           return response;
/*     */         } 
/* 455 */         if (!RSDValidation.isReqTypeAllowed()) {
/* 456 */           log.info("Error while validating request by RSD reType not allowed for user/etoken" + imei);
/* 457 */           response = new KeysResponse();
/* 458 */           String[] error = "7061, reqType Not allowed as per RSD validation".split(",");
/* 459 */           response.setResponseCode(error[0]);
/* 460 */           response.setResponseMsg(error[1]);
/* 461 */           return response;
/*     */         } 
/* 463 */         isPrepaidAllowed = RSDValidation.isPrepaidAllowed();
/* 464 */       } catch (Exception e) {
/* 465 */         log.error(e.getMessage());
/* 466 */         log.info("Error while validating request by RSD" + imei);
/* 467 */         requestBean.setRsdResponse(e.getMessage());
/* 468 */         response = new KeysResponse();
/* 469 */         String[] error = "7057, RSD Validation service failed with exception".split(",");
/* 470 */         response.setResponseCode(error[0]);
/* 471 */         response.setResponseMsg(error[1]);
/* 472 */         return response;
/*     */       } 
/*     */     } else {
/* 475 */       log.info("Switch found. Using RSD Validation");
/* 476 */       isPrepaidAllowed = (this.ibTUpdConfigMapper.countBySectionAndKey("EtokenIP", requestBean.getClientIP()) != 0);
/* 477 */       log.info("isPrepaidallowed--> " + isPrepaidAllowed);
/*     */     } 
/*     */ 
/*     */     
/* 481 */     if (!isPrepaidAllowed) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 495 */       if (this.ibTUpdConfigMapper.countForbiddenIMEI(imei) > 0 && this.ibTUpdConfigMapper.countExceptionIMEI(imei) == 0) {
/* 496 */         log.info("DatablockSign validate error: request serial is " + requestBean.getNewIMEI() + "is forbidden!");
/* 497 */         requestBean.setError("IMEI: " + imei + " in prepay model");
/* 498 */         response = new KeysResponse();
/* 499 */         String[] error = "7051,Serial is Forbidden".split(",");
/* 500 */         response.setResponseCode(error[0]);
/* 501 */         response.setResponseMsg(error[1]);
/* 502 */         return response;
/*     */       } 
/*     */     } else {
/* 505 */       requestBean.setEtokenInException(true);
/*     */     } 
/* 507 */     return response;
/*     */   }
/*     */   
/*     */   private boolean isValidMNO(String mno) {
/* 511 */     if (!StringUtils.isEmpty(mno)) {
/* 512 */       List<String> mnos = this.mapper.getAllowedTMOValues();
/* 513 */       for (String mob_no : mnos) {
/* 514 */         if (mno.equalsIgnoreCase(mob_no)) {
/* 515 */           return false;
/*     */         }
/*     */       } 
/*     */     } 
/* 519 */     return true;
/*     */   }
/*     */   
/*     */   private KSUnlockResponse validateRequestParam(KSUnlockRequest requestBean) {
/*     */     boolean isPrepaidAllowed;
/* 524 */     KSUnlockResponse response = null;
/*     */ 
/*     */     
/* 527 */     String imei = requestBean.getNewIMEI();
/* 528 */     if (StringUtils.isEmpty(imei)) {
/* 529 */       response = new KSUnlockResponse();
/* 530 */       response.setResponseCode("5070");
/* 531 */       response.setResponseMsg("Invalid serialNumber");
/* 532 */       return response;
/*     */     } 
/*     */     
/* 535 */     if (StringUtils.isEmpty(requestBean.getUserId())) {
/* 536 */       response = new KSUnlockResponse();
/* 537 */       response.setResponseCode("5079");
/* 538 */       response.setResponseMsg("RSU Service Invalid RSD User");
/* 539 */       return response;
/*     */     } 
/*     */     
/* 542 */     String mascId = requestBean.getMASCID();
/* 543 */     if (StringUtils.isEmpty(mascId)) {
/* 544 */       response = new KSUnlockResponse();
/* 545 */       response.setResponseCode("5080");
/* 546 */       response.setResponseMsg("RSU Service Invalid Masc ID");
/* 547 */       return response;
/*     */     } 
/*     */     
/* 550 */     String suid = requestBean.getCpuid();
/*     */ 
/*     */     
/* 553 */     if (StringUtils.isEmpty(suid)) {
/* 554 */       response = new KSUnlockResponse();
/* 555 */       response.setResponseCode("5082");
/* 556 */       response.setResponseMsg("GPS dispatch keys fail - CPUID null");
/* 557 */       return response;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 566 */     String keyname = requestBean.getBuildType();
/* 567 */     if (StringUtils.isEmpty(keyname)) {
/* 568 */       response = new KSUnlockResponse();
/* 569 */       response.setResponseCode("5083");
/* 570 */       response.setResponseMsg("GPS Subsidy fail - BUILDTYPE null");
/* 571 */       return response;
/*     */     } 
/*     */     
/* 574 */     String data = requestBean.getPname();
/* 575 */     if (StringUtils.isEmpty(data)) {
/* 576 */       response = new KSUnlockResponse();
/* 577 */       response.setResponseCode("5085");
/* 578 */       response.setResponseMsg("GPS Subsidy fail - PNAME null");
/* 579 */       return response;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 591 */     String cip = requestBean.getClientIP();
/* 592 */     if (StringUtils.isEmpty(cip)) {
/* 593 */       response = new KSUnlockResponse();
/*     */       
/* 595 */       response.setResponseCode("5088");
/* 596 */       response.setResponseMsg("GPS Subsidy fail - Client is null or not valid");
/* 597 */       return response;
/*     */     } 
/*     */ 
/*     */     
/* 601 */     String clrqt = requestBean.getClientReqType();
/* 602 */     if (StringUtils.isEmpty(clrqt)) {
/* 603 */       response = new KSUnlockResponse();
/*     */       
/* 605 */       response.setResponseCode("5089");
/* 606 */       response.setResponseMsg("GPS Subsidy fail - ClientReqType is null or not valid");
/* 607 */       return response;
/*     */     } 
/*     */     
/* 610 */     String rlid = requestBean.getRsd_log_id();
/* 611 */     if (StringUtils.isEmpty(rlid)) {
/* 612 */       response = new KSUnlockResponse();
/*     */       
/* 614 */       response.setResponseCode("5087");
/* 615 */       response.setResponseMsg("GPS Subsidy fail - RSD LOGID cannot be null");
/* 616 */       return response;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 623 */     if (this.ibTUpdConfigMapper.countBySectionAndKey("DATABLOCK_SIGN", "SKIP_RSD_VALIDATION") == 0) {
/* 624 */       log.info("Switch not found. Using RSD Validation");
/*     */ 
/*     */ 
/*     */       
/* 628 */       RSDValidationRequest vrequest = buildValidationRequest(requestBean.getClientIP(), requestBean.getPublicIP(), requestBean.getClientReqType(), requestBean.getUserId());
/* 629 */       log.info(vrequest);
/*     */       
/*     */       try {
/* 632 */         RSDValidationResponse rsdresponse = this.rsdValidator.validateRequest(vrequest);
/* 633 */         log.info("validation step completed");
/* 634 */         requestBean.setRsdResponse(rsdresponse.toString());
/* 635 */         List<Config> configs = this.ibTUpdConfigMapper.getConfigRules();
/* 636 */         RSDRules RSDValidation = new RSDRules(configs, rsdresponse);
/* 637 */         if (!RSDValidation.isEtokenAllowed()) {
/* 638 */           log.info("Error while validating request by RSD Etoken not allowed" + imei);
/* 639 */           response = new KSUnlockResponse();
/* 640 */           String[] error = "7058, Etoken Not allowed as per RSD validation".split(",");
/* 641 */           response.setResponseCode(error[0]);
/*     */           
/* 643 */           response.setResponseMsg(error[1]);
/* 644 */           return response;
/*     */         } 
/* 646 */         if (!RSDValidation.isPublicIPAllowed()) {
/* 647 */           log.info("Error while validating request by RSD publicip not allowed" + imei);
/* 648 */           response = new KSUnlockResponse();
/* 649 */           String[] error = "7059, publicip Not allowed as per RSD validation".split(",");
/* 650 */           response.setResponseCode(error[0]);
/* 651 */           response.setResponseMsg(error[1]);
/* 652 */           return response;
/*     */         } 
/* 654 */         if (!RSDValidation.isUserIdAllowed()) {
/* 655 */           log.info("Error while validating request by RSD userid not allowed" + imei);
/* 656 */           response = new KSUnlockResponse();
/* 657 */           String[] error = "7060, userid Not allowed as per RSD validation".split(",");
/* 658 */           response.setResponseCode(error[0]);
/* 659 */           response.setResponseMsg(error[1]);
/* 660 */           return response;
/*     */         } 
/* 662 */         if (!RSDValidation.isReqTypeAllowed()) {
/* 663 */           log.info("Error while validating request by RSD reType not allowed for user/etoken" + imei);
/* 664 */           response = new KSUnlockResponse();
/* 665 */           String[] error = "7061, reqType Not allowed as per RSD validation".split(",");
/* 666 */           response.setResponseCode(error[0]);
/* 667 */           response.setResponseMsg(error[1]);
/* 668 */           return response;
/*     */         } 
/* 670 */         isPrepaidAllowed = RSDValidation.isPrepaidAllowed();
/* 671 */       } catch (Exception e) {
/* 672 */         log.error(e.getMessage());
/* 673 */         log.info("Error while validating request by RSD" + imei);
/* 674 */         requestBean.setRsdResponse(e.getMessage());
/* 675 */         response = new KSUnlockResponse();
/* 676 */         String[] error = "7057, RSD Validation service failed with exception".split(",");
/* 677 */         response.setResponseCode(error[0]);
/* 678 */         response.setResponseMsg(error[1]);
/* 679 */         return response;
/*     */       } 
/*     */     } else {
/* 682 */       log.info("Switch found. Using RSD Validation");
/* 683 */       isPrepaidAllowed = (this.ibTUpdConfigMapper.countBySectionAndKey("EtokenIP", requestBean.getClientIP()) != 0);
/* 684 */       log.info("isPrepaidallowed--> " + isPrepaidAllowed);
/*     */     } 
/*     */ 
/*     */     
/* 688 */     if (!isPrepaidAllowed) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 702 */       if (this.ibTUpdConfigMapper.countForbiddenIMEI(imei) > 0 && this.ibTUpdConfigMapper.countExceptionIMEI(imei) == 0) {
/* 703 */         log.info("DatablockSign validate error: request serial is " + requestBean.getNewIMEI() + "is forbidden!");
/* 704 */         requestBean.setError("IMEI: " + imei + " in prepay model");
/* 705 */         response = new KSUnlockResponse();
/* 706 */         String[] error = "7051,Serial is Forbidden".split(",");
/* 707 */         response.setResponseCode(error[0]);
/* 708 */         response.setResponseMsg(error[1]);
/* 709 */         return response;
/*     */       } 
/*     */     } else {
/* 712 */       requestBean.setEtokenInException(true);
/*     */     } 
/* 714 */     return response;
/*     */   }
/*     */   public KSUnlockResponse processKsUnlock(KSUnlockRequest request) throws Exception {
/* 717 */     KSUnlockResponse rsuResponse = validateRequestParam(request);
/* 718 */     if (rsuResponse != null) {
/* 719 */       return rsuResponse;
/*     */     }
/* 721 */     String soapRsuParam = buildParams(request);
/* 722 */     String response = HttpClientUtils.postWebservice(this.gpsUrl, soapRsuParam, "", this.authUser, this.authPwd);
/* 723 */     log.info("after the gps call");
/* 724 */     log.info(response + " --> GPS response");
/* 725 */     rsuResponse = KSUnlockResponse.build(response);
/* 726 */     return rsuResponse;
/*     */   }
/*     */   
/*     */   public RSDValidationRequest buildValidationRequest(String eip, String publicip, String reqtype, String username) {
/* 730 */     return new RSDValidationRequest(eip, publicip, username, reqtype, "");
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\simlock\service\impl\SimunlockServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */