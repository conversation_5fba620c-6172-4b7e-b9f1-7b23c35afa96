import json
import argparse
from collections import defaultdict, namedtuple
import csv
from datetime import datetime
import os
import re
import sys
import json
import base64
import binascii
import struct
import cryptography
from cryptography import x509
from cryptography import utils
from cryptography.hazmat.backends import default_backend
import traceback
import warnings
from datetime import datetime
import ast

class Object:
    def __init__(self):
        self.error_code = -1
        self.error_message = "Parsing not complete yet"

    # def toJSON(self):
    #     return json.dumps(self, default=lambda o: o.__dict__, 
    #         sort_keys=True, indent=4)

    # def jsonDefault(self):
    #     return self.decode('utf-8')


SIMLOCK_DBTYPE = "SIMLOCK - 0x0033"
IMEI_DBTYPE = "IMEI - 0x000F"
LEGACY_SIMLOCK = "Legacy SIMLOCK - 0x0066"
CID_DATABLOCK = "CID - 0x00F0"


def getDetailsFromRequestParam(message, bean):
    regex = re.compile("=")
    pad = regex.match(message)
    if not pad:
        padneeded = ((4 - len(message) % 4) % 4)
        message += "A" * (padneeded - 1)
        message += "="

    #print(message)
    dbreq = base64.b64decode(message)
    version = -1
    request_length = struct.unpack(">H", dbreq[0:2])[0]
    input_byte_array = bytearray(dbreq)

    if (input_byte_array[2] == 0x01):
        bean["req_protocol_version"] = 1
        start = 128
        #print(1)
        version = 1
        parse_request_blob(dbreq, start, bean, version)
    elif (input_byte_array[2] == 0x02):
        bean["req_protocol_version"] = 2
        start = 160
        #print(2)
        version = 2
        parse_request_blob(dbreq, start, bean, version)
    elif (input_byte_array[2] == 0x03):
        bean["req_protocol_version"] = 3
        start = 160
        version = 3
        pkis_key = struct.unpack(">H", input_byte_array[126:128])[0]
        #print("pkis_key")
        #print(pkis_key)
        if (pkis_key != 1) :
            bean["error_code"].append("45")
            bean["error_message"].append("for protocol version 3, pkiskey has to be 1 but found" + str(pkis_key))
        #print(2)
        parse_request_blob(dbreq, start, bean, version)
    else:
        #print('unsupported')
        #print(input_byte_array[2])
        bean["error_code"] = -1
        bean["error_message"] = "Unsupported req_protocol_version"


# modified from Joel's script to support request compiles in version 1 and 2. This can also be overridden in Version 3.
def parse_request_blob(dbreq, start, bean, version):
    pdata = defaultdict(dict)
    ab = dbreq[0:2]
    dbtype = None
    request_length = struct.unpack(">H", dbreq[0:2])[0]
    if (request_length != len(dbreq)) :
        bean["error_code"].append("11")
        bean["error_message"].append('header length doesnt seem to be correct')
    #print(str(request_length))
    #print(len(dbreq))
    #num_datablocks = struct.unpack(">H", dbreq[126:128])[0]
    num_datablocks = struct.unpack(">H", dbreq[start-2:start])[0]
    ### print(str(num_datablocks))
    # db_start = 128
    db_start = start
    db_next = 0
    request_processor_uid = bytearray()
    err = None
    pdata["Type"] = 0
    # We shouldn't ever see more than 3 datablocks in a request
    bean["no_of_data_blocks_in_request"] = num_datablocks
    # bean["datablocks = []
    # if num_datablocks <= 3:
    bean["datablocks"] = []
    for i in range(num_datablocks):
        datablock_in_requestparam = {}
        db_next = db_start + struct.unpack(">I", dbreq[db_start + 4:db_start + 8])[0]
        # print("db_start" + str(db_start))
        # print("db_start" + str(db_next))
        # print("DB length: " + binascii.b2a_hex(dbreq[db_start+4:db_start+8]).upper())
        # datablock_in_requestparam["length"] = binascii.b2a_hex(dbreq[db_start+4:db_start+8]).upper()

        # changed from Joels code to decode utf-8
        # comment for error RGB764 - DEC
        # datablock_in_requestparam["length"] = binascii.hexlify(dbreq[db_start + 4:db_start + 8]).decode("utf-8")
        # comment for error RGB764 - DEC
        datablock_in_requestparam["length"] = binascii.b2a_hex(dbreq[db_start+4:db_start+8]).upper()
        if struct.unpack(">H", dbreq[db_start:db_start + 2])[0] == 0x000F:
            # print"DB type IMEI"
            if dbtype is None:
                dbtype = "IMEI - 0x000F"
                datablock_in_requestparam["dbtype"] = "IMEI - 0x000F"
            elif dbtype == "IMEI - 0x000F":
                datablock_in_requestparam["dbtype"] = "IMEI - 0x000F"
            else:
                bean["error_code"].append("6")
                bean["error_message"].append(
                    "DB request contains datablocks with different DBtypes. " + "it contains IMEI - 0x000F and " + dbtype)

            (db_pid, db_serial, db_cscid, db_version, status) = unpack_identity(dbreq[db_start:db_next])
            datablock_in_requestparam["db_version"] = db_version
            if not status : 
                bean["error_code"].append("10")
                bean["error_message"].append('unpack_identity failed. Possibly incorrect format version') 
            pdata["Type"] = 1
            #print(binascii.hexlify(db_cscid))
            #datablock_in_requestparam["csc_id"] = binascii.hexlify(db_cscid).decode("utf-8")
            datablock_in_requestparam["csc_id"] = binascii.hexlify(db_cscid)
            #### print"PID: " + binascii.hexlify(db_pid)
            #### print"Serial: " + binascii.hexlify(db_serial)
        elif struct.unpack(">H", dbreq[db_start:db_start + 2])[0] == 0x0033:
            if version == 3 : 
                 bean["error_code"].append("7")
                 bean["error_message"].append("Unsupported Simlock db 0x33 in version 3"); 
            if dbtype is None:
                dbtype = "SIMLOCK - 0x0033"
                datablock_in_requestparam["dbtype"] = dbtype
            elif dbtype == "SIMLOCK - 0x0033":
                datablock_in_requestparam["dbtype"] = "SIMLOCK - 0x0033"
            else:
                bean["error_code"].append("6")
                bean["error_message"].append(
                    " DB request contains datablocks with different DBtypes. " + "it contains SIMLOCK - 0x0033 and " + dbtype)
            # print"DB type SIMLOCK"
            datablock_in_requestparam["dbtype"] = "SIMLOCK - 0x0033"
            (db_pid, db_serial, db_version, status) = unpack_simlock(dbreq[db_start:db_next])
            datablock_in_requestparam["db_version"] = db_version
            if not status : 
                bean["error_code"].append("11")
                bean["error_message"].append('unpack_simlock failed. Possibly incorrect format version') 
            # print"PID: " + binascii.hexlify(db_pid)
            # print"Serial: " + binascii.hexlify(db_serial)
			
        elif struct.unpack(">H", dbreq[db_start:db_start + 2])[0] == 0x0C03:
            if version == 1 or version == 2 : 
                 bean["error_code"].append("7")
                 bean["error_message"].append("Unsupported Simlock db 0x0C03 in version " + version); 
            if dbtype is None:
                dbtype = "UniversalSIMLOCKv2 - 0x0C03"
                datablock_in_requestparam["dbtype"] = dbtype
            elif dbtype == "UniversalSIMLOCKv2 - 0x0C03":
                datablock_in_requestparam["dbtype"] = "UniversalSIMLOCKv2 - 0x0C03"
            else:
                bean["error_code"].append("6")
                bean["error_message"].append(
                    " DB request contains datablocks with different DBtypes. " + "it contains SIMLOCK - 0x0033 and " + dbtype)
            # print"DB type SIMLOCK"
            #datablock_in_requestparam["dbtype"] = "SIMLOCK - 0x0033"
            (db_pid, db_serial, db_version, status) = unpack_new_simlock(dbreq[db_start:db_next])
            datablock_in_requestparam["db_version"] = db_version
            if not status : 
                bean["error_code"].append("11")
                bean["error_message"].append('unpack_simlock failed. Possibly incorrect format version')
        elif struct.unpack(">H", dbreq[db_start:db_start + 2])[0] == 0x0066:
            if dbtype is None:
                dbtype = "Legacy SIMLOCK - 0x0066"
                datablock_in_requestparam["dbtype"] = dbtype
            elif dbtype == "Legacy SIMLOCK - 0x0066":
                datablock_in_requestparam["dbtype"] = "Legacy SIMLOCK - 0x0066"
            else:
                bean["error_code"].append("6")
                bean["error_message"].append(
                    " DB request contains datablocks with different DBtypes. " + "it contains Legacy SIMLOCK - 0x0066 and " + dbtype)
            ## print"DB type Legacy SIMLOCK"
            datablock_in_requestparam["dbtype"] = "Legacy SIMLOCK - 0x0066"
            (db_pid, db_serial, db_version, status) = unpack_simlock(dbreq[db_start:db_next])
            datablock_in_requestparam["db_version"] = db_version
            if not status : 
                bean["error_code"].append("11")
                bean["error_message"].append('unpack_simlock failed. Possibly incorrect format version')
        elif struct.unpack(">H", dbreq[db_start:db_start + 2])[0] == 0x00F0:
            if dbtype is None:
                dbtype = "CID - 0x00F0"
                datablock_in_requestparam["dbtype"] = dbtype
            elif dbtype == "CID - 0x00F0":
                datablock_in_requestparam["dbtype"] = "CID - 0x00F0"
            else:
                bean["error_code"].append("6")
                bean["error_message"].append(
                    " DB request contains datablocks with different DBtypes. " + "it contains Legacy SIMLOCK - 0x0066 and " + dbtype)
            #### print"DB type CID"
            datablock_in_requestparam["dbtype"] = "CID - 0x00F0"
            (db_pid, db_serial, db_cid, db_version, status) = unpack_cid(dbreq[db_start:db_next])
            datablock_in_requestparam["db_version"] = db_version
            if not status : 
                bean["error_code"].append("11")
                bean["error_message"].append('unpack_simlock failed. Possibly incorrect format version') 
            datablock_in_requestparam["customer_identifier"] = binascii.hexlify(db_cid).decode("utf-8")
        else:
            # This is generally happening when malformed requests are passed in, it was noted
            # that some requests were missing requried fields and later rejected by the PKI server
            (db_pid, db_serial) = (bytearray("notavalidpid", 'utf-8'), bytearray("notavalidserial", 'utf-8'))
            # bean["error_code"] = -3
            bean["error_code"].append("3")
            bean["error_message"].append("Unknown DB type: " + binascii.b2a_hex(dbreq[db_start:db_start + 2]).decode(
                "utf-8") + " DB Length: " + str(int(db_next - db_start)) + \
                                         " Header Request Length: " + str(int(request_length)) + \
                                         " Number DB: " + str(hex(num_datablocks)) + \
                                         " Current DB Offset: " + str(int(db_start)))
            # ### print"Unknown DB type: " + binascii.b2a_hex(dbreq[db_start:db_start+2]).upper() + \
            #         " DB Length: " + str(int(db_next - db_start)) + \
            #         " Header Request Length: " + str(int(request_length)) + \
            #         " Number DB: " + str(hex(num_datablocks)) + \
            #         " Current DB Offset: " + str(int(db_start))
            bean["error_message"].append("------ERROR LOGS------" + binhexdump(dbreq))
            return
        datablock_in_requestparam["processor_uid"] = binascii.hexlify(db_pid).decode("utf-8")
        datablock_in_requestparam["serial_number"] = binascii.hexlify(db_serial).decode("utf-8")
        if request_processor_uid:
            if request_processor_uid != db_pid:
                bean["error_code"].append("2")
                bean["error_message"].append(" DB request contains datablocks with different processor uids!")
        else:
            request_processor_uid = db_pid
            bean["request_processor_uid"] = binascii.hexlify(db_pid).decode("utf-8")
        bean["datablocks"].append(datablock_in_requestparam)
        db_start = db_next
        if not((int(db_version) < 2 and version == 1) or (int(db_version) >= 2 and version == 3)):
            bean["error_code"].append("88")
            bean["error_message"].append("db_version and req_protocol_version dont match. DB_VERSION = " + db_version + " PROTCOL_VERSION =" + str(version))

    # end of for i in range(num_datablocks) for loop
    # Validation # number of data blocks in each type validation.
    if dbtype == SIMLOCK_DBTYPE and num_datablocks > 3:
        bean["error_code"].append("7")
        bean["error_message"].append(" SIMLOCK_DBTYPE cannot contain more than 3 datablocks")
    if dbtype == IMEI_DBTYPE and num_datablocks > 2:
        bean["error_code"].append("8")
        bean["error_message"].append(" IMEI_DBTYPE cannot contain more than 2 datablocks")
    if dbtype == CID_DATABLOCK and num_datablocks > 1:
        bean["error_code"].append("8")
        bean["error_message"].append(" CID_DATABLOCK cannot contain more than 1 datablocks")
    extended_data_length = struct.unpack(">H", dbreq[db_start:db_start + 2])[0]
    db_start += 2
    # print(db_start)
    # print("extended_data_length")
    #print("extended_data_length --> "+ str(extended_data_length))

	# extended data validation
    #print "left -->" + str(db_start-2 + extended_data_length + 6)
    #print "right -->" + str (len(dbreq))
    val_len = db_start-2 + extended_data_length + 6
    if val_len != len(dbreq):
        bean["error_code"].append("81")
        bean["error_message"].append("extended_data_length is not valid")
    # extended data validation ends

	# Validation # Unpack certs to get etoken username etc. 
    unpack_extended_data(dbreq[db_start:db_start + extended_data_length], extended_data_length, bean)
    # else:
    #   ### print"Unsupported number of datablocks in request: " + str(int(num_datablocks))
    #   err = 1

    pdata["PID"] = request_processor_uid
    pdata["Err"] = err
    return pdata


def unpack_identity(db):
    # check format version
    try :
        ## print(struct.unpack(">H", db[2:4])[0] == 0x0001)
	db_version = binascii.hexlify(db[2:4])
        if struct.unpack(">H", db[2:4])[0] == 0x0000 or struct.unpack(">H", db[2:4])[0] == 0x0001:
            # print("id version 0 ad 1 ")
            # changes for version 3: k_dsv3 add support for 0x0002 
            # unpack IMEI w/nibble swapping
            serial = bytearray(db[40:48])
            for i in range(0, 8):
                serial[i] = ((serial[i] & 0xF0) >> 4) | ((serial[i] & 0x0F) << 4)
            # mask high nibble (legacy length)
            serial[0] = serial[0] & 0x0F
            # processor UID, serial
            return (bytearray(db[8:24]), serial, bytearray(db[64:66]), db_version, True)
        if struct.unpack(">H", db[2:4])[0] == 0x0002: 
            # print("id version 2")
            serial = bytearray(db[42:50])
            for i in range(0, 8):
                serial[i] = ((serial[i] & 0xF0) >> 4) | ((serial[i] & 0x0F) << 4)
            # mask high nibble (legacy length)
            serial[0] = serial[0] & 0x0F
            return (bytearray(db[8:24]), serial, bytearray(db[66:68]), db_version, True)
        else:
            # # print("Unsupported IMEI format version")
            return (bytearray(), bytearray(), bytearray(), -1, False)
    except Exception, e:
            return (bytearray(), bytearray(), bytearray(), -1, False)


def unpack_simlock(db):
    try :
        db_version = binascii.hexlify(db[2:4])
        if struct.unpack(">H", db[2:4])[0] == 0x0000:
            return (bytearray(db[8:24]), bytearray(0), db_version, True)
        else:
            ### print"Unsupported SIMLOCK DB version"
            return (bytearray(0), bytearray(0), -1, False)
    except Exception, e :
        return (bytearray(0), bytearray(0), -1, False)

def unpack_new_simlock(db):
    #print('here')
    try :
        db_version = binascii.hexlify(db[2:4])
        if struct.unpack(">H", db[2:4])[0] == 0x0002:
            return (bytearray(db[8:24]), bytearray(0), db_version, True)
        else:
            ### print"Unsupported SIMLOCK DB version"
            return (bytearray(0), bytearray(0), -1, False)
    except Exception, e :
        return (bytearray(0), bytearray(0), -1, False)


def unpack_cid(db):

    try :
        db_version = binascii.hexlify(db[2:4])
        if struct.unpack(">H", db[2:4])[0] == 0x0000:
            return (bytearray(db[8:24]), bytearray(0), bytearray(db[42:44]), db_version, True)
        if struct.unpack(">H", db[2:4])[0] == 0x0001:
            serial = bytearray(db[46:54])
            for i in range(0, 8):
                serial[i] = ((serial[i] & 0xF0) >> 4) | ((serial[i] & 0x0F) << 4)
            # mask high nibble (legacy length)
            serial[0] = serial[0] & 0x0F
            #return (bytearray(db[8:24]), bytearray(0), bytearray(db[42:44]), db_version, True)
            return (bytearray(db[8:24]), serial, bytearray(db[42:44]), db_version, True)
        if struct.unpack(">H", db[2:4])[0] == 0x0002:
            #print('two')
            serial = bytearray(db[48:56])
            for i in range(0, 8):
                serial[i] = ((serial[i] & 0xF0) >> 4) | ((serial[i] & 0x0F) << 4)
            # mask high nibble (legacy length)
            serial[0] = serial[0] & 0x0F
            #return (bytearray(db[8:24]), bytearray(0), bytearray(db[44:46]), db_version, True)
            return (bytearray(db[8:24]), serial, bytearray(db[42:44]), db_version, True)

        else:
            ### print"Unsupported CID DB version"
            return (bytearray(0), bytearray(0), bytearray(0), db_version, False)
    except Exception, e :
        return (bytearray(0), bytearray(0), bytearray(0), db_version, False)


def binhexdump(data):
    wblocks = len(data) // 32
    pblocks = len(data) % 32
    out = ""
    out += "len=" + str((len(data))) + " 32 byte blocks: " + str((wblocks)) + " partial blocks: " + str((pblocks))
    for i in range(0, wblocks):
        out += binascii.hexlify(data[i * 32:(i * 32) + 32]).decode("utf-8")
    if pblocks:
        out += binascii.hexlify(data[i:i + pblocks]).decode("utf-8")
    return out


def unpack_extended_data(edata, edata_len, bean):
    # check syntax byte
    # print(type(edata))
    # print("req_protocol_version --> " + str(bean['req_protocol_version']))
    #print(edata[0])
    # if struct.unpack("B", edata[0])[0] != 0x01:
    syntax = struct.unpack("B", edata[0])[0]
    #syntax = struct.unpack("B", edata[1])
    # # print"new one"
    # print(syntax)
    if syntax != 0x01:
    #if syntax != 0x00:
        ### print"Extended data syntax byte incorrect: " + binascii.hexlify(edata[0])
        # TODONE: implement error capture
        bean["error_code"].append("4")
        bean["error_message"].append(" Extended data syntax byte incorrect: " + binascii.hexlify(edata[0]))

    if struct.unpack("B", edata[1])[0] == 0x01:
        # authenticator type = 1 (supported types are 1 and 2)
        if (bean['req_protocol_version'] == 3) :
            bean["error_code"].append("41")
            bean["error_message"].append("req_protocol_version 3 message has authenticator type 1. Unsupported")
    # if edata[0] == 1:
        userid = edata[2:23]
        # # print(userid.decode("utf-8"))
        bean['UserID'] = userid.decode("utf-8").rstrip('\x00')
        # timestamp = struct.unpack(">I", edata[23:27])[0]
        # signature - 128 bytes
        cert_length = struct.unpack(">H", edata[155:157])[0]
        # if (cert_length > 1200) :
        #     bean["error_code"].append("42")
        #     bean["error_message"].append("authenticator type 1, cert length cannot be more than 1200. Unsupported")
        cert_start = 157
        if cert_length > 1200 or cert_start + 1200 > len(edata) or cert_length == 0:
            bean["error_code"].append("44")
            bean["error_message"].append("authenticator type 1, cert_length(found=" + str(cert_length) + ")cannot be "
                                                                                                         "more than "
                                                                                                         "1200 or equal"
                                                                                                         " to zero "
                                                                                                         "cert length "
                                                                                                         "is invalid. "
                                                                                                         "Unsupported")

        try:
            # well, 1200 is lazy as it might be cut off
            cert = x509.load_der_x509_certificate(edata[157:157 + edata_len], default_backend())
            # print(cert.__dict__)
            ip_name_attr = cert.subject.get_attributes_for_oid(x509.OID_COMMON_NAME)
            bean["etoken_ip"] = ip_name_attr[0].value.split(':')[0]
            ### printip_name_attr
            # dump data as temporary test to see attributes
            # for attribute in cert:
            #    print(attribute)
        # except :
        except Exception, e :
            traceback.print_exc()
            # It's happening because the request message log is cut off @4000 bytes, so in
            # requests with multiple datablocks, the cert data at the end gets chopped off
            # # print("Cert decode exception:")
            #### print"PKI cert length: " + str(int(cert_length))
            #### printbinascii.hexlify(edata[157:157+1200])
            # TODONE: implement error capture
            bean["error_code"].append("5")
            bean["error_message"].append("PKI cert length: " + str(int(cert_length)) + ", Exception is " + str(e))
            # pass
    if struct.unpack("B", edata[1])[0] == 0x02:
        # new pki authenticator type
        #print('ew pki authenticator type')

        if (bean['req_protocol_version'] != 3) :
            bean["error_code"].append("42")
            bean["error_message"].append("req_protocol_version 3 message has authenticator type 1. Unsupported")
        userid = edata[2:23]

        #sample
        userid = userid + bytearray([0] * 1)
        #smaple ends
        # # print(userid.decode("utf-8"))
        bean['UserID'] = userid.decode("utf-8").rstrip('\x00')
        # bean['UserID'] = userid.decode("ascii").rstrip('\x00')
        # timestamp = struct.unpack(">I", edata[23:27])[0]
        # print(struct.unpack("B", edata[27])[0])
        #if struct.unpack("B", edata[28])[0] == 0x02:
        if struct.unpack("B", edata[27])[0] == 2:
            # 26 + 256 + 1 (sig type)
            # print('ew pki authenticator type 2')
            cert_length = struct.unpack(">H", edata[284:286])[0]
            # print(44)
            # print(cert_length)
            #
            cert_start = 286
            # print(cert_length + cert_start + 4)
            # print(len(edata))
            if cert_start + cert_length > len(edata) or cert_length == 0:
                bean["error_code"].append("43")
                bean["error_message"].append("authenticator type 2 sig 2, cert length is invalid. Unsupported")
        else :
            # 26 + 128 + 1
            # signature - 128 bytes
            # print('ew pki authenticator typewwww')
            cert_length = struct.unpack(">H", edata[156:158])[0]
            cert_start = 158
            # print(cert_length)
            # print(cert_length + cert_start + 4)
            # print(len(edata))

            if cert_start + cert_length > len(edata) or cert_length == 0:
                bean["error_code"].append("43")
                bean["error_message"].append("authenticator type 2 sig 1, cert length is invalid. Unsupported")

        try:
            # print('cert length')
            # print(cert_length)
            # well, 1200 is lazy as it might be cut off
            # cert = x509.load_der_x509_certificate(edata[157:157 + edata_len], default_backend())
            cert = x509.load_der_x509_certificate(edata[cert_start:cert_start + cert_length], default_backend())
            # cert = x509.load_der_x509_certificate(edata[cert_start:], default_backend())
            # print(cert.__dict__)
            ip_name_attr = cert.subject.get_attributes_for_oid(x509.OID_COMMON_NAME)
            bean["etoken_ip"] = ip_name_attr[0].value.split(':')[0]
            ### printip_name_attr
            # dump data as temporary test to see attributes
            # for attribute in cert:
            #    print(attribute)
        # except :
        except Exception, e :
            traceback.print_exc()
            # It's happening because the request message log is cut off @4000 bytes, so in
            # requests with multiple datablocks, the cert data at the end gets chopped off
            # # print("Cert decode exception:")
            #### print"PKI cert length: " + str(int(cert_length))
            #### printbinascii.hexlify(edata[157:157+1200])
            # TODONE: implement error capture
            bean["error_code"].append("5")
            bean["error_message"].append("PKI cert length: " + str(int(cert_length)) + ", Exception is " + str(e))
        

def something(s):
    bean = defaultdict(list)
    #print('here')
    try:
        getDetailsFromRequestParam(s, bean)
    except Exception, e :
        bean["error_code"].append("99")
        bean["error_message"].append("uncaught exception occured" + ", Exception is " + str(e))
    sorted_bean = sorted(bean.items())
    json_data = json.dumps(bean, indent=2)
    #print(json.loads(json_data))
    json_data = ast.literal_eval(json.dumps(json_data))
    print(json_data)
    #print(type(json_data))
    return json_data


if __name__ == "__main__":
    # mode = sys.argv[1]
    # if mode == 'File':
    #     now = datetime.now()
    #     outname = "Mylogs" + now.strftime("%m_%d_%Y_%H_%M_%S") + ".txt"
    #     fname = sys.argv[2]
    #     logy = open(outname, "w")
    #     with open(fname) as csv_file:
    #         csv_reader = csv.reader(csv_file, delimiter=',')
    #         for row in csv_reader:
    #             yello = something(row[1])
    #             if 'error_code' in yello.keys():
    #                 print('error')
    #                 logy.write(row[0] + "||" + json.dumps(yello) + "\n")
    #             else:
    #                 print('neigh')
    #     logy.close()
    # else :
    something(sys.argv[1])
    # debugg for version 2
    #     #something('BXoDBQAPMzUxNjE1MTEwMDA1NDM2AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALqtwN6R0p6140OL4AVh0igBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAPAAIAAABGmSkJLwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACADoVFhUBAEVjAAAAAAAA/////////////4AAAAAADwACAAAARpkpCS8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgA6FRYVAQBFRAAAAAAAAP////////////+AAAEABEgBAnc2MDAxMwAAAAAAAAAAAAAAAAAAAF/XwOIBOC3XAcDSiZ+n2VwCQ+grfK8KSVIz9zzPRiiQaxjIzPDAlyVS99uPjM2GeBaxE4JLp7AviBjBAjdtmcefCAyEJe/To//dCLV2cYGbxL2fn2NeishA2nHzP29GkHcrcL0FKCLa032SDJVN9/QAIXcfsARUUiQJWU+Ents+XM7byB0DqjCCA6YwggKOoAMCAQICEFMVCNDnLcWc3tU7NUUZNlwwDQYJKoZIhvcNAQEFBQAwgYsxCzAJBgNVBAYTAlVTMRcwFQYDVQQKEw5Nb3Rvcm9sYSwgSW5jLjEbMBkGA1UECxMSUEtJIFN0YXRpb24gU3ViLUNBMREwDwYDVQQLEwhIYW5kc2V0czEWMBQGA1UECxMNTW90b3JvbGEtR05QTzEbMBkGA1UEAxMSTGliZXJ0eXZpbGxlLUlMLVVTMB4XDTE0MDMwMzIyNTcyMFoXDTI0MDMwMzIyNTcyMFowgc8xCzAJBgNVBAYTAlVTMRcwFQYDVQQKEw5Nb3Rvcm9sYSwgSW5jLjEdMBsGA1UECxMUSGFuZHNldHMgUEtJIFN0YXRpb24xIDAeBgNVBAsTF0dOUE86TGliZXJ0eXZpbGxlLUlMLVVTMRMwEQYDVQQLEwpTaXRlIElEOjEyMRgwFgYDVQQDEw8xMDAuNjQuMjQyLjE0OjAxNzA1BgNVBAUTLkFsYWRkaW4gS25vd2xlZGdlIFN5c3RlbXMgTHRkLi1lVG9rZW4tMDBiZmY4MDMwgZ8wDQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBALz0kWMe5kqMaVyi0nKe6c6Njf08beyioZxfJyXzAwHFOirQP70XeG1Vs8BnQ0QFDj04ebIwxosjJVyt72VkYpls1c0ZYA8KhI44RRfMHjBxxjtIETxOs6BnW4HDelMcisa+20Y4BFCB2zpEZLaoxkY1JEer6H2InRq8eJidbcztAgMBAAGjRDBCMA4GA1UdDwEB/wQEAwIFoDAfBgNVHSMEGDAWgBQP7qVkINw4MtP+ajEwl/jjoa+mAjAPBgNVHREECDAGhwRkQPIOMA0GCSqGSIb3DQEBBQUAA4IBAQB+FPdARBxOfKE1DWc0bUysHohFGq6ZbkrhyYkunSeGEjgFTYjrhwUJmBDsb3xWsK0+nK7WFVLQ0c3Tsy+kUEkOjb2g1secV7CbpEA82iNP8ihJqscSYYDdMiNsugmYBj64ctwZIIM+S7+nhb7MetPT4YLJqXkXB/vEiVPJDF1IjWL+6dYqIA742spIG1t8t1X7c9bRjH0UIJf0OfuiTDQc4BN/IHnvi12B1RB1jTR8WeSQ4ZaMe7aZs8rwk2PRrqBFdH7MA2gCfA6Mw8ylpe/Lr7q4qbM7p9fX3bRC9A/F8CPUaD5IhItniBUK+m5jM5YQkNDiJZxGvzIYyyPFQKIQ7W7E0g==')
    #     #something('BV4DBQAPMDA0NDAxMDIyNjE3NzEyAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALqtwN4n1zEOtblBcu4M/O0BAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQDwAAIAAABwJupk+wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACAAAAAP8AAApABAEiFnchAAZMWEFCMUswMDgzAFhUMTA1OAAAAAAB//////////////////////////////////////////8ESAECdzYwMDEzAAAAAAAAAAAAAAAAAAAAX9fANgE3apASuyNNHIdhrikBNGxWCjPa8WhhmZrYOEz+bXwOYkZaTyjrrjJvgq2MCeUoIhHe2oa7d7oavhlnChYnO3Q7L4CSh5HIfmB8hGsts74temVCC+brejRgs4hEh+3BsI/jbql9xz+7RBeuXldKHcBtkFk2GZ2R3bEne3TPzRhsjgOqMIIDpjCCAo6gAwIBAgIQUxUI0OctxZze1Ts1RRk2XDANBgkqhkiG9w0BAQUFADCBizELMAkGA1UEBhMCVVMxFzAVBgNVBAoTDk1vdG9yb2xhLCBJbmMuMRswGQYDVQQLExJQS0kgU3RhdGlvbiBTdWItQ0ExETAPBgNVBAsTCEhhbmRzZXRzMRYwFAYDVQQLEw1Nb3Rvcm9sYS1HTlBPMRswGQYDVQQDExJMaWJlcnR5dmlsbGUtSUwtVVMwHhcNMTQwMzAzMjI1NzIwWhcNMjQwMzAzMjI1NzIwWjCBzzELMAkGA1UEBhMCVVMxFzAVBgNVBAoTDk1vdG9yb2xhLCBJbmMuMR0wGwYDVQQLExRIYW5kc2V0cyBQS0kgU3RhdGlvbjEgMB4GA1UECxMXR05QTzpMaWJlcnR5dmlsbGUtSUwtVVMxEzARBgNVBAsTClNpdGUgSUQ6MTIxGDAWBgNVBAMTDzEwMC42NC4yNDIuMTQ6MDE3MDUGA1UEBRMuQWxhZGRpbiBLbm93bGVkZ2UgU3lzdGVtcyBMdGQuLWVUb2tlbi0wMGJmZjgwMzCBnzANBgkqhkiG9w0BAQEFAAOBjQAwgYkCgYEAvPSRYx7mSoxpXKLScp7pzo2N/Txt7KKhnF8nJfMDAcU6KtA/vRd4bVWzwGdDRAUOPTh5sjDGiyMlXK3vZWRimWzVzRlgDwqEjjhFF8weMHHGO0gRPE6zoGdbgcN6UxyKxr7bRjgEUIHbOkRktqjGRjUkR6vofYidGrx4mJ1tzO0CAwEAAaNEMEIwDgYDVR0PAQH/BAQDAgWgMB8GA1UdIwQYMBaAFA/upWQg3Dgy0/5qMTCX+OOhr6YCMA8GA1UdEQQIMAaHBGRA8g4wDQYJKoZIhvcNAQEFBQADggEBAH4U90BEHE58oTUNZzRtTKweiEUarpluSuHJiS6dJ4YSOAVNiOuHBQmYEOxvfFawrT6crtYVUtDRzdOzL6RQSQ6NvaDWx5xXsJukQDzaI0/yKEmqxxJhgN0yI2y6CZgGPrhy3Bkggz5Lv6eFvsx609PhgsmpeRcH+8SJU8kMXUiNYv7p1iogDvjaykgbW3y3Vftz1tGMfRQgl/Q5+6JMNBzgE38gee+LXYHVEHWNNHxZ5JDhlox7tpmzyvCTY9GuoEV0fswDaAJ8DozDzKWl78uvuripszun19fdtEL0D8XwI9RoPkiEi2eIFQr6bmMzlhCQ0OIlnEa/MhjLI8VAohA7SbSn')
    #     #something('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')
    # # something(
    # #     '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')
    #
    # # 6th line 3 sl
    # something('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')
    #
    # # 5rd line 2 sl
    # something('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')
    # #
    # # # 4nd line
    # something('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')
    # #
    # # # 3rd line
    # something('BXoDBQAPMzUxNjE1MTEwMDA1NDM2AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALqtwN6R0p6140OL4AVh0igBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAPAAIAAABGmSkJLwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACADoVFhUBAEVjAAAAAAAA/////////////4AAAAAADwACAAAARpkpCS8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgA6FRYVAQBFRAAAAAAAAP////////////+AAAEABEgBAnc2MDAxMwAAAAAAAAAAAAAAAAAAAF/XwOIBOC3XAcDSiZ+n2VwCQ+grfK8KSVIz9zzPRiiQaxjIzPDAlyVS99uPjM2GeBaxE4JLp7AviBjBAjdtmcefCAyEJe/To//dCLV2cYGbxL2fn2NeishA2nHzP29GkHcrcL0FKCLa032SDJVN9/QAIXcfsARUUiQJWU+Ents+XM7byB0DqjCCA6YwggKOoAMCAQICEFMVCNDnLcWc3tU7NUUZNlwwDQYJKoZIhvcNAQEFBQAwgYsxCzAJBgNVBAYTAlVTMRcwFQYDVQQKEw5Nb3Rvcm9sYSwgSW5jLjEbMBkGA1UECxMSUEtJIFN0YXRpb24gU3ViLUNBMREwDwYDVQQLEwhIYW5kc2V0czEWMBQGA1UECxMNTW90b3JvbGEtR05QTzEbMBkGA1UEAxMSTGliZXJ0eXZpbGxlLUlMLVVTMB4XDTE0MDMwMzIyNTcyMFoXDTI0MDMwMzIyNTcyMFowgc8xCzAJBgNVBAYTAlVTMRcwFQYDVQQKEw5Nb3Rvcm9sYSwgSW5jLjEdMBsGA1UECxMUSGFuZHNldHMgUEtJIFN0YXRpb24xIDAeBgNVBAsTF0dOUE86TGliZXJ0eXZpbGxlLUlMLVVTMRMwEQYDVQQLEwpTaXRlIElEOjEyMRgwFgYDVQQDEw8xMDAuNjQuMjQyLjE0OjAxNzA1BgNVBAUTLkFsYWRkaW4gS25vd2xlZGdlIFN5c3RlbXMgTHRkLi1lVG9rZW4tMDBiZmY4MDMwgZ8wDQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBALz0kWMe5kqMaVyi0nKe6c6Njf08beyioZxfJyXzAwHFOirQP70XeG1Vs8BnQ0QFDj04ebIwxosjJVyt72VkYpls1c0ZYA8KhI44RRfMHjBxxjtIETxOs6BnW4HDelMcisa+20Y4BFCB2zpEZLaoxkY1JEer6H2InRq8eJidbcztAgMBAAGjRDBCMA4GA1UdDwEB/wQEAwIFoDAfBgNVHSMEGDAWgBQP7qVkINw4MtP+ajEwl/jjoa+mAjAPBgNVHREECDAGhwRkQPIOMA0GCSqGSIb3DQEBBQUAA4IBAQB+FPdARBxOfKE1DWc0bUysHohFGq6ZbkrhyYkunSeGEjgFTYjrhwUJmBDsb3xWsK0+nK7WFVLQ0c3Tsy+kUEkOjb2g1secV7CbpEA82iNP8ihJqscSYYDdMiNsugmYBj64ctwZIIM+S7+nhb7MetPT4YLJqXkXB/vEiVPJDF1IjWL+6dYqIA742spIG1t8t1X7c9bRjH0UIJf0OfuiTDQc4BN/IHnvi12B1RB1jTR8WeSQ4ZaMe7aZs8rwk2PRrqBFdH7MA2gCfA6Mw8ylpe/Lr7q4qbM7p9fX3bRC9A/F8CPUaD5IhItniBUK+m5jM5YQkNDiJZxGvzIYyyPFQKIQ7W7E0g==')
    # #
    # # # 2nd line
    # something('BXoDBQAPMzUxNjE1MTEwMDA1NDM2AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALqtwN6R0p6140OL4AVh0igBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAPAAIAAABGmSkJLwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACADoVFhUBAEVjAAAAAAAA/////////////4AAAAAADwACAAAARpkpCS8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgA6FRYVAQBFRAAAAAAAAP////////////+AAAEABEgBAnc2MDAxMwAAAAAAAAAAAAAAAAAAAF/XwOIBOC3XAcDSiZ+n2VwCQ+grfK8KSVIz9zzPRiiQaxjIzPDAlyVS99uPjM2GeBaxE4JLp7AviBjBAjdtmcefCAyEJe/To//dCLV2cYGbxL2fn2NeishA2nHzP29GkHcrcL0FKCLa032SDJVN9/QAIXcfsARUUiQJWU+Ents+XM7byB0DqjCCA6YwggKOoAMCAQICEFMVCNDnLcWc3tU7NUUZNlwwDQYJKoZIhvcNAQEFBQAwgYsxCzAJBgNVBAYTAlVTMRcwFQYDVQQKEw5Nb3Rvcm9sYSwgSW5jLjEbMBkGA1UECxMSUEtJIFN0YXRpb24gU3ViLUNBMREwDwYDVQQLEwhIYW5kc2V0czEWMBQGA1UECxMNTW90b3JvbGEtR05QTzEbMBkGA1UEAxMSTGliZXJ0eXZpbGxlLUlMLVVTMB4XDTE0MDMwMzIyNTcyMFoXDTI0MDMwMzIyNTcyMFowgc8xCzAJBgNVBAYTAlVTMRcwFQYDVQQKEw5Nb3Rvcm9sYSwgSW5jLjEdMBsGA1UECxMUSGFuZHNldHMgUEtJIFN0YXRpb24xIDAeBgNVBAsTF0dOUE86TGliZXJ0eXZpbGxlLUlMLVVTMRMwEQYDVQQLEwpTaXRlIElEOjEyMRgwFgYDVQQDEw8xMDAuNjQuMjQyLjE0OjAxNzA1BgNVBAUTLkFsYWRkaW4gS25vd2xlZGdlIFN5c3RlbXMgTHRkLi1lVG9rZW4tMDBiZmY4MDMwgZ8wDQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBALz0kWMe5kqMaVyi0nKe6c6Njf08beyioZxfJyXzAwHFOirQP70XeG1Vs8BnQ0QFDj04ebIwxosjJVyt72VkYpls1c0ZYA8KhI44RRfMHjBxxjtIETxOs6BnW4HDelMcisa+20Y4BFCB2zpEZLaoxkY1JEer6H2InRq8eJidbcztAgMBAAGjRDBCMA4GA1UdDwEB/wQEAwIFoDAfBgNVHSMEGDAWgBQP7qVkINw4MtP+ajEwl/jjoa+mAjAPBgNVHREECDAGhwRkQPIOMA0GCSqGSIb3DQEBBQUAA4IBAQB+FPdARBxOfKE1DWc0bUysHohFGq6ZbkrhyYkunSeGEjgFTYjrhwUJmBDsb3xWsK0+nK7WFVLQ0c3Tsy+kUEkOjb2g1secV7CbpEA82iNP8ihJqscSYYDdMiNsugmYBj64ctwZIIM+S7+nhb7MetPT4YLJqXkXB/vEiVPJDF1IjWL+6dYqIA742spIG1t8t1X7c9bRjH0UIJf0OfuiTDQc4BN/IHnvi12B1RB1jTR8WeSQ4ZaMe7aZs8rwk2PRrqBFdH7MA2gCfA6Mw8ylpe/Lr7q4qbM7p9fX3bRC9A/F8CPUaD5IhItniBUK+m5jM5YQkNDiJZxGvzIYyyPFQKIQ7W7E0g==')
    # #
    # # # line 1
    # something('BV4DBQAPMDA0NDAxMDIyNjE3NzEyAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALqtwN4n1zEOtblBcu4M/O0BAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQDwAAIAAABwJupk+wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACAAAAAP8AAApABAEiFnchAAZMWEFCMUswMDgzAFhUMTA1OAAAAAAB//////////////////////////////////////////8ESAECdzYwMDEzAAAAAAAAAAAAAAAAAAAAX9fANgE3apASuyNNHIdhrikBNGxWCjPa8WhhmZrYOEz+bXwOYkZaTyjrrjJvgq2MCeUoIhHe2oa7d7oavhlnChYnO3Q7L4CSh5HIfmB8hGsts74temVCC+brejRgs4hEh+3BsI/jbql9xz+7RBeuXldKHcBtkFk2GZ2R3bEne3TPzRhsjgOqMIIDpjCCAo6gAwIBAgIQUxUI0OctxZze1Ts1RRk2XDANBgkqhkiG9w0BAQUFADCBizELMAkGA1UEBhMCVVMxFzAVBgNVBAoTDk1vdG9yb2xhLCBJbmMuMRswGQYDVQQLExJQS0kgU3RhdGlvbiBTdWItQ0ExETAPBgNVBAsTCEhhbmRzZXRzMRYwFAYDVQQLEw1Nb3Rvcm9sYS1HTlBPMRswGQYDVQQDExJMaWJlcnR5dmlsbGUtSUwtVVMwHhcNMTQwMzAzMjI1NzIwWhcNMjQwMzAzMjI1NzIwWjCBzzELMAkGA1UEBhMCVVMxFzAVBgNVBAoTDk1vdG9yb2xhLCBJbmMuMR0wGwYDVQQLExRIYW5kc2V0cyBQS0kgU3RhdGlvbjEgMB4GA1UECxMXR05QTzpMaWJlcnR5dmlsbGUtSUwtVVMxEzARBgNVBAsTClNpdGUgSUQ6MTIxGDAWBgNVBAMTDzEwMC42NC4yNDIuMTQ6MDE3MDUGA1UEBRMuQWxhZGRpbiBLbm93bGVkZ2UgU3lzdGVtcyBMdGQuLWVUb2tlbi0wMGJmZjgwMzCBnzANBgkqhkiG9w0BAQEFAAOBjQAwgYkCgYEAvPSRYx7mSoxpXKLScp7pzo2N/Txt7KKhnF8nJfMDAcU6KtA/vRd4bVWzwGdDRAUOPTh5sjDGiyMlXK3vZWRimWzVzRlgDwqEjjhFF8weMHHGO0gRPE6zoGdbgcN6UxyKxr7bRjgEUIHbOkRktqjGRjUkR6vofYidGrx4mJ1tzO0CAwEAAaNEMEIwDgYDVR0PAQH/BAQDAgWgMB8GA1UdIwQYMBaAFA/upWQg3Dgy0/5qMTCX+OOhr6YCMA8GA1UdEQQIMAaHBGRA8g4wDQYJKoZIhvcNAQEFBQADggEBAH4U90BEHE58oTUNZzRtTKweiEUarpluSuHJiS6dJ4YSOAVNiOuHBQmYEOxvfFawrT6crtYVUtDRzdOzL6RQSQ6NvaDWx5xXsJukQDzaI0/yKEmqxxJhgN0yI2y6CZgGPrhy3Bkggz5Lv6eFvsx609PhgsmpeRcH+8SJU8kMXUiNYv7p1iogDvjaykgbW3y3Vftz1tGMfRQgl/Q5+6JMNBzgE38gee+LXYHVEHWNNHxZ5JDhlox7tpmzyvCTY9GuoEV0fswDaAJ8DozDzKWl78uvuripszun19fdtEL0D8XwI9RoPkiEi2eIFQr6bmMzlhCQ0OIlnEa/MhjLI8VAohA7SbSn')
    # #
    # # print('reference for old db')
    #
    # something('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')

# change anything after this. But code above this is the prod version and it does not have any "mode" feature at all.
    # mode = sys.argv[1]
    # if mode == 'File':
    #     fname = sys.argv[2]
    #     with open(fname) as csv_file:
    #         csv_reader = csv.reader(csv_file, delimiter=',')
    #         line_count = 0
    #         results = []
    #         for row in csv_reader:
    #             try:
    #                 print(row)
    #                 print(len(row))
    #                 if len(row) == 1:
    #                     row = row[0].split(",")
    #                 if len(row) == 8:
    #                     row.insert(0, "userId:tracking-notimplemented")
    #                     row.insert(1, "PublicIP:tracking-notimplemented")
    #                 result = {}
    #                 data = row[9].split(':')[1].replace('\"', '')
    #                 first = row[0].replace('{', '')
    #                 first = first.split(':')[1]
    #                 result['AuthenticatedID'] = first
    #                 result['PublicIP'] = row[1].split(':')[1].replace('\"', '')
    #                 result['istrClientReqType'] = row[3].split(':')[1].replace('\"', '')
    #                 result['istrMASCID'] = row[5].split(':')[1].replace('\"', '')
    #                 result['istrNewIMEI'] = row[6].split(':')[1].replace('\"', '')
    #                 result['istrOldIMEI'] = row[7].split(':')[1].replace('\"', '')
    #                 result['istrOldIMEI'] = row[8].split(':')[1].replace('\"', '')
    #                 # myData = first + "," + row[1] + "," + row[2] + "," + row[3] + "," + row[4] + "," + row[5] + "," + row[6] + "," + row[7] + "," + row[8]
    #                 data = data.replace('}', '')
    #                 print(data)
    #                 result['reqParam'] = data
    #                 jdata = something(data)
    #                 result['parserlog'] = jdata
    #                 # result['request_processor_id'] = '\'' + jdata['request_processor_uid'] +'\''
    #                 # result['serial_number'] = jdata['datablocks'][0]['serial_number']
    #                 # result['dbtype_from_db'] = jdata['datablocks'][0]['dbtype']
    #                 # result['userid_from_db'] = jdata['UserID']
    #                 # result['etoken_ip_from_db'] = jdata['etoken_ip']
    #             except KeyError e :
    #                 print(str(e))
    #             results.append(result)
    #             # jData = json.loads(row[0])
    #             # with open('AllZero.txt', 'a') as outfile:
    #             #     outfile.write(mydata)
    #         keys = results[0].keys()
    #         outname = fname.split('.')[0] + '_results.csv'
    #         with open(outname, 'w') as output_file:
    #             dict_writer = csv.DictWriter(output_file, delimiter=',', lineterminator='\n', fieldnames=keys)
    #             dict_writer.writeheader()
    #             dict_writer.writerows(results)
    # elif mode == 'Batch':
    #     now = datetime.now()
    #     outname = "Mylogs" + now.strftime("%m_%d_%Y_%H_%M_%S") + ".txt"
    #     fname = sys.argv[2]
    #
    #     logy = open(outname, "w")
    #     with open(fname) as csv_file:
    #         csv_reader = csv.reader(csv_file, delimiter=',')
    #         for row in csv_reader:
    #             yello = something(row[1])
    #             if 'error_code' in yello.keys():
    #                 print('error')
    #                 logy.write(row[0] + "||" + json.dumps(yello) + "\n")
    #             else:
    #                 print('neigh')
    #     logy.close()
    # else:
    #     print(mode)
    #     message = sys.argv[2]
    #     print(something(message))
