/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTLclaimWarrantyQueryInboundHistory implements Serializable { private String machineType; private String serialNumber;
/*    */   private String imei1;
/*    */   private String imei2;
/*    */   private Date lastChangeTime;
/*    */   
/*  7 */   public void setMachineType(String machineType) { this.machineType = machineType; } private String batchNumber; private String lc; private Integer autoId; private int claimId; private String closeMonth; private static final long serialVersionUID = 1L; public void setSerialNumber(String serialNumber) { this.serialNumber = serialNumber; } public void setImei1(String imei1) { this.imei1 = imei1; } public void setImei2(String imei2) { this.imei2 = imei2; } public void setLastChangeTime(Date lastChangeTime) { this.lastChangeTime = lastChangeTime; } public void setBatchNumber(String batchNumber) { this.batchNumber = batchNumber; } public void setLc(String lc) { this.lc = lc; } public void setAutoId(Integer autoId) { this.autoId = autoId; } public void setClaimId(int claimId) { this.claimId = claimId; } public void setCloseMonth(String closeMonth) { this.closeMonth = closeMonth; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTLclaimWarrantyQueryInboundHistory)) return false;  com.lenovo.iqs.entity.IbTLclaimWarrantyQueryInboundHistory other = (com.lenovo.iqs.entity.IbTLclaimWarrantyQueryInboundHistory)o; if (!other.canEqual(this)) return false;  Object this$machineType = getMachineType(), other$machineType = other.getMachineType(); if ((this$machineType == null) ? (other$machineType != null) : !this$machineType.equals(other$machineType)) return false;  Object this$serialNumber = getSerialNumber(), other$serialNumber = other.getSerialNumber(); if ((this$serialNumber == null) ? (other$serialNumber != null) : !this$serialNumber.equals(other$serialNumber)) return false;  Object this$imei1 = getImei1(), other$imei1 = other.getImei1(); if ((this$imei1 == null) ? (other$imei1 != null) : !this$imei1.equals(other$imei1)) return false;  Object this$imei2 = getImei2(), other$imei2 = other.getImei2(); if ((this$imei2 == null) ? (other$imei2 != null) : !this$imei2.equals(other$imei2)) return false;  Object this$lastChangeTime = getLastChangeTime(), other$lastChangeTime = other.getLastChangeTime(); if ((this$lastChangeTime == null) ? (other$lastChangeTime != null) : !this$lastChangeTime.equals(other$lastChangeTime)) return false;  Object this$batchNumber = getBatchNumber(), other$batchNumber = other.getBatchNumber(); if ((this$batchNumber == null) ? (other$batchNumber != null) : !this$batchNumber.equals(other$batchNumber)) return false;  Object this$lc = getLc(), other$lc = other.getLc(); if ((this$lc == null) ? (other$lc != null) : !this$lc.equals(other$lc)) return false;  Object this$autoId = getAutoId(), other$autoId = other.getAutoId(); if ((this$autoId == null) ? (other$autoId != null) : !this$autoId.equals(other$autoId)) return false;  if (getClaimId() != other.getClaimId()) return false;  Object this$closeMonth = getCloseMonth(), other$closeMonth = other.getCloseMonth(); return !((this$closeMonth == null) ? (other$closeMonth != null) : !this$closeMonth.equals(other$closeMonth)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTLclaimWarrantyQueryInboundHistory; } public int hashCode() { int PRIME = 59; result = 1; Object $machineType = getMachineType(); result = result * 59 + (($machineType == null) ? 43 : $machineType.hashCode()); Object $serialNumber = getSerialNumber(); result = result * 59 + (($serialNumber == null) ? 43 : $serialNumber.hashCode()); Object $imei1 = getImei1(); result = result * 59 + (($imei1 == null) ? 43 : $imei1.hashCode()); Object $imei2 = getImei2(); result = result * 59 + (($imei2 == null) ? 43 : $imei2.hashCode()); Object $lastChangeTime = getLastChangeTime(); result = result * 59 + (($lastChangeTime == null) ? 43 : $lastChangeTime.hashCode()); Object $batchNumber = getBatchNumber(); result = result * 59 + (($batchNumber == null) ? 43 : $batchNumber.hashCode()); Object $lc = getLc(); result = result * 59 + (($lc == null) ? 43 : $lc.hashCode()); Object $autoId = getAutoId(); result = result * 59 + (($autoId == null) ? 43 : $autoId.hashCode()); result = result * 59 + getClaimId(); Object $closeMonth = getCloseMonth(); return result * 59 + (($closeMonth == null) ? 43 : $closeMonth.hashCode()); } public String toString() { return "IbTLclaimWarrantyQueryInboundHistory(machineType=" + getMachineType() + ", serialNumber=" + getSerialNumber() + ", imei1=" + getImei1() + ", imei2=" + getImei2() + ", lastChangeTime=" + getLastChangeTime() + ", batchNumber=" + getBatchNumber() + ", lc=" + getLc() + ", autoId=" + getAutoId() + ", claimId=" + getClaimId() + ", closeMonth=" + getCloseMonth() + ")"; }
/*    */   
/*    */   public String getMachineType() {
/* 10 */     return this.machineType;
/*    */   } public String getSerialNumber() {
/* 12 */     return this.serialNumber;
/*    */   } public String getImei1() {
/* 14 */     return this.imei1;
/*    */   } public String getImei2() {
/* 16 */     return this.imei2;
/*    */   } public Date getLastChangeTime() {
/* 18 */     return this.lastChangeTime;
/*    */   } public String getBatchNumber() {
/* 20 */     return this.batchNumber;
/*    */   } public String getLc() {
/* 22 */     return this.lc;
/*    */   } public Integer getAutoId() {
/* 24 */     return this.autoId;
/*    */   } public int getClaimId() {
/* 26 */     return this.claimId;
/*    */   } public String getCloseMonth() {
/* 28 */     return this.closeMonth;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTLclaimWarrantyQueryInboundHistory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */