/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTUpdServicelinkOutboundHistory implements Serializable { private String warrantyClaimNo; private String serialNo; private String sourceSerialNo; private String storedSerialNo; private String statusCode; private String factoryCode; private String apcCode; private String transceiverModelNo; private String customerModelNo; private Date shipDate; private String soldToCustId; private String soldToCustName; private String shipToCustId; private String shipToCustAddrId;
/*    */   private String shipToCustName;
/*    */   private String shipToCountryCode;
/*    */   private String originalWarrTypeCode;
/*    */   
/*  7 */   public void setWarrantyClaimNo(String warrantyClaimNo) { this.warrantyClaimNo = warrantyClaimNo; } private Date originalStdWarrExpDate; private Date originalExtWarrExpDate; private String renewalWarrTypeCode; private Date renewalStdWarrExpiryDate; private Date renewalExtWarrExpiryDate; private String warrCancelCode; private Date warrCanCodeEffectiveDate; private String currWarrTypeCode; private Date currStdWarrExpiryDate; private Date currExtendedWarrExpiryDate; private String warrFlag; private String dualSerialNo; private String dualSerialNoType; private String triSerialNo; private String triSerialNoType; private Date createTime; private String batchNumber; private static final long serialVersionUID = 1L; public void setSerialNo(String serialNo) { this.serialNo = serialNo; } public void setSourceSerialNo(String sourceSerialNo) { this.sourceSerialNo = sourceSerialNo; } public void setStoredSerialNo(String storedSerialNo) { this.storedSerialNo = storedSerialNo; } public void setStatusCode(String statusCode) { this.statusCode = statusCode; } public void setFactoryCode(String factoryCode) { this.factoryCode = factoryCode; } public void setApcCode(String apcCode) { this.apcCode = apcCode; } public void setTransceiverModelNo(String transceiverModelNo) { this.transceiverModelNo = transceiverModelNo; } public void setCustomerModelNo(String customerModelNo) { this.customerModelNo = customerModelNo; } public void setShipDate(Date shipDate) { this.shipDate = shipDate; } public void setSoldToCustId(String soldToCustId) { this.soldToCustId = soldToCustId; } public void setSoldToCustName(String soldToCustName) { this.soldToCustName = soldToCustName; } public void setShipToCustId(String shipToCustId) { this.shipToCustId = shipToCustId; } public void setShipToCustAddrId(String shipToCustAddrId) { this.shipToCustAddrId = shipToCustAddrId; } public void setShipToCustName(String shipToCustName) { this.shipToCustName = shipToCustName; } public void setShipToCountryCode(String shipToCountryCode) { this.shipToCountryCode = shipToCountryCode; } public void setOriginalWarrTypeCode(String originalWarrTypeCode) { this.originalWarrTypeCode = originalWarrTypeCode; } public void setOriginalStdWarrExpDate(Date originalStdWarrExpDate) { this.originalStdWarrExpDate = originalStdWarrExpDate; } public void setOriginalExtWarrExpDate(Date originalExtWarrExpDate) { this.originalExtWarrExpDate = originalExtWarrExpDate; } public void setRenewalWarrTypeCode(String renewalWarrTypeCode) { this.renewalWarrTypeCode = renewalWarrTypeCode; } public void setRenewalStdWarrExpiryDate(Date renewalStdWarrExpiryDate) { this.renewalStdWarrExpiryDate = renewalStdWarrExpiryDate; } public void setRenewalExtWarrExpiryDate(Date renewalExtWarrExpiryDate) { this.renewalExtWarrExpiryDate = renewalExtWarrExpiryDate; } public void setWarrCancelCode(String warrCancelCode) { this.warrCancelCode = warrCancelCode; } public void setWarrCanCodeEffectiveDate(Date warrCanCodeEffectiveDate) { this.warrCanCodeEffectiveDate = warrCanCodeEffectiveDate; } public void setCurrWarrTypeCode(String currWarrTypeCode) { this.currWarrTypeCode = currWarrTypeCode; } public void setCurrStdWarrExpiryDate(Date currStdWarrExpiryDate) { this.currStdWarrExpiryDate = currStdWarrExpiryDate; } public void setCurrExtendedWarrExpiryDate(Date currExtendedWarrExpiryDate) { this.currExtendedWarrExpiryDate = currExtendedWarrExpiryDate; } public void setWarrFlag(String warrFlag) { this.warrFlag = warrFlag; } public void setDualSerialNo(String dualSerialNo) { this.dualSerialNo = dualSerialNo; } public void setDualSerialNoType(String dualSerialNoType) { this.dualSerialNoType = dualSerialNoType; } public void setTriSerialNo(String triSerialNo) { this.triSerialNo = triSerialNo; } public void setTriSerialNoType(String triSerialNoType) { this.triSerialNoType = triSerialNoType; } public void setCreateTime(Date createTime) { this.createTime = createTime; } public void setBatchNumber(String batchNumber) { this.batchNumber = batchNumber; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdServicelinkOutboundHistory)) return false;  com.lenovo.iqs.entity.IbTUpdServicelinkOutboundHistory other = (com.lenovo.iqs.entity.IbTUpdServicelinkOutboundHistory)o; if (!other.canEqual(this)) return false;  Object this$warrantyClaimNo = getWarrantyClaimNo(), other$warrantyClaimNo = other.getWarrantyClaimNo(); if ((this$warrantyClaimNo == null) ? (other$warrantyClaimNo != null) : !this$warrantyClaimNo.equals(other$warrantyClaimNo)) return false;  Object this$serialNo = getSerialNo(), other$serialNo = other.getSerialNo(); if ((this$serialNo == null) ? (other$serialNo != null) : !this$serialNo.equals(other$serialNo)) return false;  Object this$sourceSerialNo = getSourceSerialNo(), other$sourceSerialNo = other.getSourceSerialNo(); if ((this$sourceSerialNo == null) ? (other$sourceSerialNo != null) : !this$sourceSerialNo.equals(other$sourceSerialNo)) return false;  Object this$storedSerialNo = getStoredSerialNo(), other$storedSerialNo = other.getStoredSerialNo(); if ((this$storedSerialNo == null) ? (other$storedSerialNo != null) : !this$storedSerialNo.equals(other$storedSerialNo)) return false;  Object this$statusCode = getStatusCode(), other$statusCode = other.getStatusCode(); if ((this$statusCode == null) ? (other$statusCode != null) : !this$statusCode.equals(other$statusCode)) return false;  Object this$factoryCode = getFactoryCode(), other$factoryCode = other.getFactoryCode(); if ((this$factoryCode == null) ? (other$factoryCode != null) : !this$factoryCode.equals(other$factoryCode)) return false;  Object this$apcCode = getApcCode(), other$apcCode = other.getApcCode(); if ((this$apcCode == null) ? (other$apcCode != null) : !this$apcCode.equals(other$apcCode)) return false;  Object this$transceiverModelNo = getTransceiverModelNo(), other$transceiverModelNo = other.getTransceiverModelNo(); if ((this$transceiverModelNo == null) ? (other$transceiverModelNo != null) : !this$transceiverModelNo.equals(other$transceiverModelNo)) return false;  Object this$customerModelNo = getCustomerModelNo(), other$customerModelNo = other.getCustomerModelNo(); if ((this$customerModelNo == null) ? (other$customerModelNo != null) : !this$customerModelNo.equals(other$customerModelNo)) return false;  Object this$shipDate = getShipDate(), other$shipDate = other.getShipDate(); if ((this$shipDate == null) ? (other$shipDate != null) : !this$shipDate.equals(other$shipDate)) return false;  Object this$soldToCustId = getSoldToCustId(), other$soldToCustId = other.getSoldToCustId(); if ((this$soldToCustId == null) ? (other$soldToCustId != null) : !this$soldToCustId.equals(other$soldToCustId)) return false;  Object this$soldToCustName = getSoldToCustName(), other$soldToCustName = other.getSoldToCustName(); if ((this$soldToCustName == null) ? (other$soldToCustName != null) : !this$soldToCustName.equals(other$soldToCustName)) return false;  Object this$shipToCustId = getShipToCustId(), other$shipToCustId = other.getShipToCustId(); if ((this$shipToCustId == null) ? (other$shipToCustId != null) : !this$shipToCustId.equals(other$shipToCustId)) return false;  Object this$shipToCustAddrId = getShipToCustAddrId(), other$shipToCustAddrId = other.getShipToCustAddrId(); if ((this$shipToCustAddrId == null) ? (other$shipToCustAddrId != null) : !this$shipToCustAddrId.equals(other$shipToCustAddrId)) return false;  Object this$shipToCustName = getShipToCustName(), other$shipToCustName = other.getShipToCustName(); if ((this$shipToCustName == null) ? (other$shipToCustName != null) : !this$shipToCustName.equals(other$shipToCustName)) return false;  Object this$shipToCountryCode = getShipToCountryCode(), other$shipToCountryCode = other.getShipToCountryCode(); if ((this$shipToCountryCode == null) ? (other$shipToCountryCode != null) : !this$shipToCountryCode.equals(other$shipToCountryCode)) return false;  Object this$originalWarrTypeCode = getOriginalWarrTypeCode(), other$originalWarrTypeCode = other.getOriginalWarrTypeCode(); if ((this$originalWarrTypeCode == null) ? (other$originalWarrTypeCode != null) : !this$originalWarrTypeCode.equals(other$originalWarrTypeCode)) return false;  Object this$originalStdWarrExpDate = getOriginalStdWarrExpDate(), other$originalStdWarrExpDate = other.getOriginalStdWarrExpDate(); if ((this$originalStdWarrExpDate == null) ? (other$originalStdWarrExpDate != null) : !this$originalStdWarrExpDate.equals(other$originalStdWarrExpDate)) return false;  Object this$originalExtWarrExpDate = getOriginalExtWarrExpDate(), other$originalExtWarrExpDate = other.getOriginalExtWarrExpDate(); if ((this$originalExtWarrExpDate == null) ? (other$originalExtWarrExpDate != null) : !this$originalExtWarrExpDate.equals(other$originalExtWarrExpDate)) return false;  Object this$renewalWarrTypeCode = getRenewalWarrTypeCode(), other$renewalWarrTypeCode = other.getRenewalWarrTypeCode(); if ((this$renewalWarrTypeCode == null) ? (other$renewalWarrTypeCode != null) : !this$renewalWarrTypeCode.equals(other$renewalWarrTypeCode)) return false;  Object this$renewalStdWarrExpiryDate = getRenewalStdWarrExpiryDate(), other$renewalStdWarrExpiryDate = other.getRenewalStdWarrExpiryDate(); if ((this$renewalStdWarrExpiryDate == null) ? (other$renewalStdWarrExpiryDate != null) : !this$renewalStdWarrExpiryDate.equals(other$renewalStdWarrExpiryDate)) return false;  Object this$renewalExtWarrExpiryDate = getRenewalExtWarrExpiryDate(), other$renewalExtWarrExpiryDate = other.getRenewalExtWarrExpiryDate(); if ((this$renewalExtWarrExpiryDate == null) ? (other$renewalExtWarrExpiryDate != null) : !this$renewalExtWarrExpiryDate.equals(other$renewalExtWarrExpiryDate)) return false;  Object this$warrCancelCode = getWarrCancelCode(), other$warrCancelCode = other.getWarrCancelCode(); if ((this$warrCancelCode == null) ? (other$warrCancelCode != null) : !this$warrCancelCode.equals(other$warrCancelCode)) return false;  Object this$warrCanCodeEffectiveDate = getWarrCanCodeEffectiveDate(), other$warrCanCodeEffectiveDate = other.getWarrCanCodeEffectiveDate(); if ((this$warrCanCodeEffectiveDate == null) ? (other$warrCanCodeEffectiveDate != null) : !this$warrCanCodeEffectiveDate.equals(other$warrCanCodeEffectiveDate)) return false;  Object this$currWarrTypeCode = getCurrWarrTypeCode(), other$currWarrTypeCode = other.getCurrWarrTypeCode(); if ((this$currWarrTypeCode == null) ? (other$currWarrTypeCode != null) : !this$currWarrTypeCode.equals(other$currWarrTypeCode)) return false;  Object this$currStdWarrExpiryDate = getCurrStdWarrExpiryDate(), other$currStdWarrExpiryDate = other.getCurrStdWarrExpiryDate(); if ((this$currStdWarrExpiryDate == null) ? (other$currStdWarrExpiryDate != null) : !this$currStdWarrExpiryDate.equals(other$currStdWarrExpiryDate)) return false;  Object this$currExtendedWarrExpiryDate = getCurrExtendedWarrExpiryDate(), other$currExtendedWarrExpiryDate = other.getCurrExtendedWarrExpiryDate(); if ((this$currExtendedWarrExpiryDate == null) ? (other$currExtendedWarrExpiryDate != null) : !this$currExtendedWarrExpiryDate.equals(other$currExtendedWarrExpiryDate)) return false;  Object this$warrFlag = getWarrFlag(), other$warrFlag = other.getWarrFlag(); if ((this$warrFlag == null) ? (other$warrFlag != null) : !this$warrFlag.equals(other$warrFlag)) return false;  Object this$dualSerialNo = getDualSerialNo(), other$dualSerialNo = other.getDualSerialNo(); if ((this$dualSerialNo == null) ? (other$dualSerialNo != null) : !this$dualSerialNo.equals(other$dualSerialNo)) return false;  Object this$dualSerialNoType = getDualSerialNoType(), other$dualSerialNoType = other.getDualSerialNoType(); if ((this$dualSerialNoType == null) ? (other$dualSerialNoType != null) : !this$dualSerialNoType.equals(other$dualSerialNoType)) return false;  Object this$triSerialNo = getTriSerialNo(), other$triSerialNo = other.getTriSerialNo(); if ((this$triSerialNo == null) ? (other$triSerialNo != null) : !this$triSerialNo.equals(other$triSerialNo)) return false;  Object this$triSerialNoType = getTriSerialNoType(), other$triSerialNoType = other.getTriSerialNoType(); if ((this$triSerialNoType == null) ? (other$triSerialNoType != null) : !this$triSerialNoType.equals(other$triSerialNoType)) return false;  Object this$createTime = getCreateTime(), other$createTime = other.getCreateTime(); if ((this$createTime == null) ? (other$createTime != null) : !this$createTime.equals(other$createTime)) return false;  Object this$batchNumber = getBatchNumber(), other$batchNumber = other.getBatchNumber(); return !((this$batchNumber == null) ? (other$batchNumber != null) : !this$batchNumber.equals(other$batchNumber)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdServicelinkOutboundHistory; } public int hashCode() { int PRIME = 59; result = 1; Object $warrantyClaimNo = getWarrantyClaimNo(); result = result * 59 + (($warrantyClaimNo == null) ? 43 : $warrantyClaimNo.hashCode()); Object $serialNo = getSerialNo(); result = result * 59 + (($serialNo == null) ? 43 : $serialNo.hashCode()); Object $sourceSerialNo = getSourceSerialNo(); result = result * 59 + (($sourceSerialNo == null) ? 43 : $sourceSerialNo.hashCode()); Object $storedSerialNo = getStoredSerialNo(); result = result * 59 + (($storedSerialNo == null) ? 43 : $storedSerialNo.hashCode()); Object $statusCode = getStatusCode(); result = result * 59 + (($statusCode == null) ? 43 : $statusCode.hashCode()); Object $factoryCode = getFactoryCode(); result = result * 59 + (($factoryCode == null) ? 43 : $factoryCode.hashCode()); Object $apcCode = getApcCode(); result = result * 59 + (($apcCode == null) ? 43 : $apcCode.hashCode()); Object $transceiverModelNo = getTransceiverModelNo(); result = result * 59 + (($transceiverModelNo == null) ? 43 : $transceiverModelNo.hashCode()); Object $customerModelNo = getCustomerModelNo(); result = result * 59 + (($customerModelNo == null) ? 43 : $customerModelNo.hashCode()); Object $shipDate = getShipDate(); result = result * 59 + (($shipDate == null) ? 43 : $shipDate.hashCode()); Object $soldToCustId = getSoldToCustId(); result = result * 59 + (($soldToCustId == null) ? 43 : $soldToCustId.hashCode()); Object $soldToCustName = getSoldToCustName(); result = result * 59 + (($soldToCustName == null) ? 43 : $soldToCustName.hashCode()); Object $shipToCustId = getShipToCustId(); result = result * 59 + (($shipToCustId == null) ? 43 : $shipToCustId.hashCode()); Object $shipToCustAddrId = getShipToCustAddrId(); result = result * 59 + (($shipToCustAddrId == null) ? 43 : $shipToCustAddrId.hashCode()); Object $shipToCustName = getShipToCustName(); result = result * 59 + (($shipToCustName == null) ? 43 : $shipToCustName.hashCode()); Object $shipToCountryCode = getShipToCountryCode(); result = result * 59 + (($shipToCountryCode == null) ? 43 : $shipToCountryCode.hashCode()); Object $originalWarrTypeCode = getOriginalWarrTypeCode(); result = result * 59 + (($originalWarrTypeCode == null) ? 43 : $originalWarrTypeCode.hashCode()); Object $originalStdWarrExpDate = getOriginalStdWarrExpDate(); result = result * 59 + (($originalStdWarrExpDate == null) ? 43 : $originalStdWarrExpDate.hashCode()); Object $originalExtWarrExpDate = getOriginalExtWarrExpDate(); result = result * 59 + (($originalExtWarrExpDate == null) ? 43 : $originalExtWarrExpDate.hashCode()); Object $renewalWarrTypeCode = getRenewalWarrTypeCode(); result = result * 59 + (($renewalWarrTypeCode == null) ? 43 : $renewalWarrTypeCode.hashCode()); Object $renewalStdWarrExpiryDate = getRenewalStdWarrExpiryDate(); result = result * 59 + (($renewalStdWarrExpiryDate == null) ? 43 : $renewalStdWarrExpiryDate.hashCode()); Object $renewalExtWarrExpiryDate = getRenewalExtWarrExpiryDate(); result = result * 59 + (($renewalExtWarrExpiryDate == null) ? 43 : $renewalExtWarrExpiryDate.hashCode()); Object $warrCancelCode = getWarrCancelCode(); result = result * 59 + (($warrCancelCode == null) ? 43 : $warrCancelCode.hashCode()); Object $warrCanCodeEffectiveDate = getWarrCanCodeEffectiveDate(); result = result * 59 + (($warrCanCodeEffectiveDate == null) ? 43 : $warrCanCodeEffectiveDate.hashCode()); Object $currWarrTypeCode = getCurrWarrTypeCode(); result = result * 59 + (($currWarrTypeCode == null) ? 43 : $currWarrTypeCode.hashCode()); Object $currStdWarrExpiryDate = getCurrStdWarrExpiryDate(); result = result * 59 + (($currStdWarrExpiryDate == null) ? 43 : $currStdWarrExpiryDate.hashCode()); Object $currExtendedWarrExpiryDate = getCurrExtendedWarrExpiryDate(); result = result * 59 + (($currExtendedWarrExpiryDate == null) ? 43 : $currExtendedWarrExpiryDate.hashCode()); Object $warrFlag = getWarrFlag(); result = result * 59 + (($warrFlag == null) ? 43 : $warrFlag.hashCode()); Object $dualSerialNo = getDualSerialNo(); result = result * 59 + (($dualSerialNo == null) ? 43 : $dualSerialNo.hashCode()); Object $dualSerialNoType = getDualSerialNoType(); result = result * 59 + (($dualSerialNoType == null) ? 43 : $dualSerialNoType.hashCode()); Object $triSerialNo = getTriSerialNo(); result = result * 59 + (($triSerialNo == null) ? 43 : $triSerialNo.hashCode()); Object $triSerialNoType = getTriSerialNoType(); result = result * 59 + (($triSerialNoType == null) ? 43 : $triSerialNoType.hashCode()); Object $createTime = getCreateTime(); result = result * 59 + (($createTime == null) ? 43 : $createTime.hashCode()); Object $batchNumber = getBatchNumber(); return result * 59 + (($batchNumber == null) ? 43 : $batchNumber.hashCode()); } public String toString() { return "IbTUpdServicelinkOutboundHistory(warrantyClaimNo=" + getWarrantyClaimNo() + ", serialNo=" + getSerialNo() + ", sourceSerialNo=" + getSourceSerialNo() + ", storedSerialNo=" + getStoredSerialNo() + ", statusCode=" + getStatusCode() + ", factoryCode=" + getFactoryCode() + ", apcCode=" + getApcCode() + ", transceiverModelNo=" + getTransceiverModelNo() + ", customerModelNo=" + getCustomerModelNo() + ", shipDate=" + getShipDate() + ", soldToCustId=" + getSoldToCustId() + ", soldToCustName=" + getSoldToCustName() + ", shipToCustId=" + getShipToCustId() + ", shipToCustAddrId=" + getShipToCustAddrId() + ", shipToCustName=" + getShipToCustName() + ", shipToCountryCode=" + getShipToCountryCode() + ", originalWarrTypeCode=" + getOriginalWarrTypeCode() + ", originalStdWarrExpDate=" + getOriginalStdWarrExpDate() + ", originalExtWarrExpDate=" + getOriginalExtWarrExpDate() + ", renewalWarrTypeCode=" + getRenewalWarrTypeCode() + ", renewalStdWarrExpiryDate=" + getRenewalStdWarrExpiryDate() + ", renewalExtWarrExpiryDate=" + getRenewalExtWarrExpiryDate() + ", warrCancelCode=" + getWarrCancelCode() + ", warrCanCodeEffectiveDate=" + getWarrCanCodeEffectiveDate() + ", currWarrTypeCode=" + getCurrWarrTypeCode() + ", currStdWarrExpiryDate=" + getCurrStdWarrExpiryDate() + ", currExtendedWarrExpiryDate=" + getCurrExtendedWarrExpiryDate() + ", warrFlag=" + getWarrFlag() + ", dualSerialNo=" + getDualSerialNo() + ", dualSerialNoType=" + getDualSerialNoType() + ", triSerialNo=" + getTriSerialNo() + ", triSerialNoType=" + getTriSerialNoType() + ", createTime=" + getCreateTime() + ", batchNumber=" + getBatchNumber() + ")"; }
/*    */    public String getWarrantyClaimNo() {
/*  9 */     return this.warrantyClaimNo;
/*    */   } public String getSerialNo() {
/* 11 */     return this.serialNo;
/*    */   } public String getSourceSerialNo() {
/* 13 */     return this.sourceSerialNo;
/*    */   } public String getStoredSerialNo() {
/* 15 */     return this.storedSerialNo;
/*    */   } public String getStatusCode() {
/* 17 */     return this.statusCode;
/*    */   } public String getFactoryCode() {
/* 19 */     return this.factoryCode;
/*    */   } public String getApcCode() {
/* 21 */     return this.apcCode;
/*    */   } public String getTransceiverModelNo() {
/* 23 */     return this.transceiverModelNo;
/*    */   } public String getCustomerModelNo() {
/* 25 */     return this.customerModelNo;
/*    */   } public Date getShipDate() {
/* 27 */     return this.shipDate;
/*    */   } public String getSoldToCustId() {
/* 29 */     return this.soldToCustId;
/*    */   } public String getSoldToCustName() {
/* 31 */     return this.soldToCustName;
/*    */   } public String getShipToCustId() {
/* 33 */     return this.shipToCustId;
/*    */   } public String getShipToCustAddrId() {
/* 35 */     return this.shipToCustAddrId;
/*    */   } public String getShipToCustName() {
/* 37 */     return this.shipToCustName;
/*    */   } public String getShipToCountryCode() {
/* 39 */     return this.shipToCountryCode;
/*    */   } public String getOriginalWarrTypeCode() {
/* 41 */     return this.originalWarrTypeCode;
/*    */   } public Date getOriginalStdWarrExpDate() {
/* 43 */     return this.originalStdWarrExpDate;
/*    */   } public Date getOriginalExtWarrExpDate() {
/* 45 */     return this.originalExtWarrExpDate;
/*    */   } public String getRenewalWarrTypeCode() {
/* 47 */     return this.renewalWarrTypeCode;
/*    */   } public Date getRenewalStdWarrExpiryDate() {
/* 49 */     return this.renewalStdWarrExpiryDate;
/*    */   } public Date getRenewalExtWarrExpiryDate() {
/* 51 */     return this.renewalExtWarrExpiryDate;
/*    */   } public String getWarrCancelCode() {
/* 53 */     return this.warrCancelCode;
/*    */   } public Date getWarrCanCodeEffectiveDate() {
/* 55 */     return this.warrCanCodeEffectiveDate;
/*    */   } public String getCurrWarrTypeCode() {
/* 57 */     return this.currWarrTypeCode;
/*    */   } public Date getCurrStdWarrExpiryDate() {
/* 59 */     return this.currStdWarrExpiryDate;
/*    */   } public Date getCurrExtendedWarrExpiryDate() {
/* 61 */     return this.currExtendedWarrExpiryDate;
/*    */   } public String getWarrFlag() {
/* 63 */     return this.warrFlag;
/*    */   } public String getDualSerialNo() {
/* 65 */     return this.dualSerialNo;
/*    */   } public String getDualSerialNoType() {
/* 67 */     return this.dualSerialNoType;
/*    */   } public String getTriSerialNo() {
/* 69 */     return this.triSerialNo;
/*    */   } public String getTriSerialNoType() {
/* 71 */     return this.triSerialNoType;
/*    */   } public Date getCreateTime() {
/* 73 */     return this.createTime;
/*    */   } public String getBatchNumber() {
/* 75 */     return this.batchNumber;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdServicelinkOutboundHistory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */