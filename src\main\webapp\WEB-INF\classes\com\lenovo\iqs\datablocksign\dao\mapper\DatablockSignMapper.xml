<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.datablocksign.dao.DatablockSignMapper">
<select id="queryWhiteMascs" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT ACTIVE_LOCK_CODE , NWSCP_LOCK_CODE  ,SSCP_LOCK_CODE FROM ib_t_white_mascs WHERE MASC_ID=#{mascId};
    </select>

    <select id="countWihiteMascs" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT count(1) from ib_t_white_mascs WHERE MASC_ID=#{mascId};
    </select>

    <select id="countWhiteImei" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(SERIAL_NO) FROM IB_T_WHITE_IMEI WHERE SERIAL_NO=#{serialNo};
    </select>
</mapper>