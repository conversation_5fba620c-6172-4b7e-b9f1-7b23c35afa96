/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.service.impl;
/*    */ 
/*    */ import com.fasterxml.jackson.databind.ObjectMapper;
/*    */ import com.lenovo.iqs.datablocksign.bean.Config;
/*    */ import com.lenovo.iqs.datablocksign.bean.RSDRules;
/*    */ import com.lenovo.iqs.datablocksign.bean.RSDValidationRequest;
/*    */ import com.lenovo.iqs.datablocksign.bean.RSDValidationResponse;
/*    */ import com.lenovo.iqs.datablocksign.service.RsdValidationService;
/*    */ import com.lenovo.iqs.utils.HttpClientUtils;
/*    */ import java.io.IOException;
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import org.apache.logging.log4j.LogManager;
/*    */ import org.apache.logging.log4j.Logger;
/*    */ import org.springframework.beans.factory.annotation.Value;
/*    */ import org.springframework.stereotype.Service;
/*    */ 
/*    */ 
/*    */ 
/*    */ @Service
/*    */ public class RSDValidationServiceImpl
/*    */   implements RsdValidationService
/*    */ {
/* 24 */   private static final Logger log = LogManager.getLogger(com.lenovo.iqs.datablocksign.service.impl.RSDValidationServiceImpl.class);
/*    */   
/*    */   @Value("${RSD_VALIDATE_USER}")
/*    */   private String user;
/*    */   
/*    */   @Value("${RSD_VALIDATE_PWD}")
/*    */   private String password;
/*    */   @Value("${RSD_VALIDATE_URL}")
/*    */   private String url;
/*    */   
/*    */   public RSDValidationServiceImpl() {}
/*    */   
/*    */   private RSDValidationServiceImpl(String user, String url, String pwd) {
/* 37 */     this.password = pwd;
/* 38 */     this.user = user;
/* 39 */     this.url = url;
/*    */   }
/*    */ 
/*    */   
/*    */   public RSDValidationResponse validateRequest(RSDValidationRequest request) throws Exception {
/* 44 */     log.info("URL -->" + this.url);
/* 45 */     log.info("user -->" + this.user);
/* 46 */     log.info("pwd -->" + this.password);
/* 47 */     log.info(request.toString());
/*    */     try {
/* 49 */       String res = HttpClientUtils.postJSONWebservice(this.url, request, this.user, this.password);
/* 50 */       log.debug(res);
/* 51 */       return parseJson(res);
/*    */     }
/* 53 */     catch (Exception ex) {
/* 54 */       log.error("RSD Validation failed");
/* 55 */       log.error(ex.getMessage());
/* 56 */       log.error(ex.getStackTrace());
/* 57 */       throw ex;
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   private RSDValidationResponse parseJson(String res) throws IOException {
/* 63 */     ObjectMapper mapper = new ObjectMapper();
/* 64 */     RSDValidationResponse response = null;
/*    */     try {
/* 66 */       response = (RSDValidationResponse)mapper.readValue(res, RSDValidationResponse.class);
/* 67 */     } catch (IOException e) {
/* 68 */       e.printStackTrace();
/* 69 */       log.error("Error While parsing json response from RSD");
/* 70 */       log.error(e.getMessage());
/* 71 */       throw e;
/*    */     } 
/* 73 */     return response;
/*    */   }
/*    */   
/*    */   public static void main(String[] args) throws Exception {
/* 77 */     com.lenovo.iqs.datablocksign.service.impl.RSDValidationServiceImpl service = new com.lenovo.iqs.datablocksign.service.impl.RSDValidationServiceImpl("rsdgfs", "https://ebiz-esb-test.cloud.motorola.net/SRPEtokenDBSValidationService", "Moto@123");
/* 78 */     List<Config> configs = new ArrayList<>(4);
/* 79 */     String[] a = { "etoken", "publicip", "reqtype", "prepaid", "username" };
/* 80 */     for (int i = 0; i < configs.size(); i++) {
/* 81 */       Config c = new Config();
/* 82 */       c.setKey(a[i]);
/* 83 */       c.setValue("accept");
/*    */     } 
/* 85 */     RSDValidationRequest req = new RSDValidationRequest();
/* 86 */     req.setEtokenip("**********");
/* 87 */     req.setPublicip("");
/* 88 */     req.setRequesttype("0x02");
/* 89 */     req.setUsername("rgb764");
/* 90 */     RSDValidationResponse resp = service.validateRequest(req);
/* 91 */     RSDRules validation = new RSDRules(configs, resp);
/* 92 */     System.out.println(validation.isReqTypeAllowed());
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\service\impl\RSDValidationServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */