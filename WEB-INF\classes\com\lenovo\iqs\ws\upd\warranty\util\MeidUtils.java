/*     */ package WEB-INF.classes.com.lenovo.iqs.ws.upd.warranty.util;
/*     */ 
/*     */ import com.lenovo.iqs.ws.upd.warranty.util.MEIDException;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MeidUtils
/*     */ {
/*     */   public static String getDecimalMEID(String meid) throws MEIDException {
/*  63 */     validateMEID(meid);
/*  64 */     return hex2dec(meid.substring(0, 8), 10) + hex2dec(meid.substring(8, 14), 8);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getHexMEID(String dmeid) throws MEIDException {
/*  77 */     if (dmeid.length() != 18)
/*     */     {
/*  79 */       throw new MEIDException("Invalid decimal MEID length: " + dmeid);
/*     */     }
/*     */     
/*  82 */     return validateMEID(dmeid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String convertDMEIDtoHex(String dmeid) throws MEIDException {
/*  93 */     String meidA = dec2hex(dmeid.substring(0, 10), 8);
/*  94 */     String meidB = dec2hex(dmeid.substring(10), 6);
/*  95 */     if (meidA.length() > 8 || meidB.length() > 6)
/*     */     {
/*  97 */       throw new MEIDException("Invalid decimal MEID: " + dmeid);
/*     */     }
/*  99 */     return meidA + meidB;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String validateMEID(String meidIn) throws MEIDException {
/* 119 */     if (meidIn == null) {
/* 120 */       throw new MEIDException("validateMEID: Input MEID is null.");
/*     */     }
/*     */     
/* 123 */     String meid = meidIn.toUpperCase();
/*     */     try {
/* 125 */       if (meid.length() == 18) {
/* 126 */         meid = convertDMEIDtoHex(meid);
/*     */       }
/*     */       
/* 129 */       if (meid.length() == 15 || meid.length() == 14) {
/* 130 */         if (!isDecMeid(meid)) {
/* 131 */           int check0 = Integer.parseInt(meid.substring(0, 1), 16);
/* 132 */           int check1 = Integer.parseInt(meid.substring(1, 2), 16);
/* 133 */           if (check0 < 10 && check1 < 10) {
/* 134 */             throw new MEIDException("MEID not within valid prefix range: " + meid);
/*     */           }
/*     */         } 
/* 137 */         String checksum = getChecksum(meid);
/* 138 */         if (meid.length() == 14) {
/* 139 */           return meid + checksum;
/*     */         }
/* 141 */         if (!checksum.equals(meid.substring(14))) {
/* 142 */           throw new MEIDException("Invalid checksum value for MEID: " + meid + " Correct checksum: " + checksum);
/*     */         }
/* 144 */         return meid;
/*     */       } 
/* 146 */       throw new MEIDException("Invalid MEID length for " + meid);
/*     */     }
/* 148 */     catch (NumberFormatException nfe) {
/* 149 */       throw new MEIDException("Invalid character found in MEID: " + meid);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String hex2dec(String hex, int len) {
/* 161 */     return zeroPad(Long.toString(Long.parseLong(hex, 16)), len);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String dec2hex(String dec, int len) {
/* 172 */     return zeroPad(Long.toHexString(Long.parseLong(dec)).toUpperCase(), len);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String zeroPad(String in, int len) {
/* 183 */     String prefix = "";
/* 184 */     for (int i = 0; i < len - in.length(); i++)
/*     */     {
/* 186 */       prefix = prefix + "0";
/*     */     }
/* 188 */     return prefix + in;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getChecksum(String meid) throws MEIDException {
/* 198 */     int total = 0;
/* 199 */     int valAtIdx = -1;
/* 200 */     int doubleVal = -1;
/* 201 */     int lenthSerialNo = -1;
/* 202 */     lenthSerialNo = meid.length();
/* 203 */     int baseLenChk = -1;
/* 204 */     int checkSumExpected = -1;
/* 205 */     if (lenthSerialNo == 14) {
/* 206 */       baseLenChk = 14;
/*     */     } else {
/*     */       
/* 209 */       baseLenChk = lenthSerialNo - 1;
/*     */     } 
/* 211 */     if (isDecMeid(meid) && baseLenChk == 14) {
/* 212 */       for (int i = 0; i < baseLenChk; i++) {
/* 213 */         valAtIdx = Integer.parseInt(meid.substring(i, i + 1), 10);
/* 214 */         if (i % 2 != 0) {
/* 215 */           doubleVal = valAtIdx * 2;
/* 216 */           total += doubleVal / 10;
/* 217 */           total += doubleVal % 10;
/*     */         } else {
/* 219 */           total += valAtIdx;
/*     */         } 
/*     */       } 
/* 222 */       checkSumExpected = 10 - total % 10;
/* 223 */       if (checkSumExpected == 10) {
/* 224 */         checkSumExpected = 0;
/*     */       }
/*     */     } else {
/* 227 */       for (int i = 0; i < baseLenChk; i++) {
/* 228 */         valAtIdx = Integer.parseInt(meid.substring(i, i + 1), 16);
/* 229 */         if (i % 2 != 0) {
/* 230 */           doubleVal = valAtIdx * 2;
/* 231 */           total += doubleVal / 16;
/* 232 */           total += doubleVal % 16;
/*     */         } else {
/* 234 */           total += valAtIdx;
/*     */         } 
/*     */       } 
/* 237 */       checkSumExpected = 16 - total % 16;
/* 238 */       if (checkSumExpected == 16)
/* 239 */         checkSumExpected = 0; 
/*     */     } 
/* 241 */     return Integer.toHexString(checkSumExpected).toUpperCase();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String getMeidChecksum(String meid) throws MEIDException {
/* 252 */     if (meid.length() == 15)
/*     */     {
/* 254 */       return meid;
/*     */     }
/* 256 */     return meid + getChecksum(meid);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isDecMeid(String meid) {
/* 267 */     boolean isDec = true;
/*     */     
/* 269 */     for (int i = 0; i < meid.length(); i++) {
/*     */       
/* 271 */       char valAtIndex = meid.charAt(i);
/* 272 */       if ((valAtIndex >= 'A' && valAtIndex <= 'F') || (valAtIndex >= 'a' && valAtIndex <= 'f')) {
/*     */ 
/*     */         
/* 275 */         isDec = false;
/*     */         break;
/*     */       } 
/*     */     } 
/* 279 */     return isDec;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] args) {
/*     */     try {
/* 287 */       System.out.println("********************************");
/* 288 */       System.out.println("\nInvalid MEID length test >>");
/*     */       
/*     */       try {
/* 291 */         validateMEID("12345");
/* 292 */         System.err.println("Should not get here!");
/*     */       }
/* 294 */       catch (MEIDException me) {
/*     */         
/* 296 */         System.out.println("<Expected error> Message: " + me.getMessage());
/*     */       } 
/*     */       
/* 299 */       System.out.println("\n********************************");
/* 300 */       System.out.println("\nInvalid MEID prefix test >>");
/*     */       
/*     */       try {
/* 303 */         validateMEID("12345678901234");
/* 304 */         System.err.println("Should not get here!");
/*     */       }
/* 306 */       catch (MEIDException me) {
/*     */         
/* 308 */         System.out.println("<Expected error> Message: " + me.getMessage());
/*     */       } 
/*     */       
/* 311 */       System.out.println("\n********************************");
/* 312 */       System.out.println("\nInvalid character test >>");
/*     */       
/*     */       try {
/* 315 */         validateMEID("A012345678901G");
/* 316 */         System.err.println("Should not get here!");
/*     */       }
/* 318 */       catch (MEIDException me) {
/*     */         
/* 320 */         System.out.println("<Expected error> Message: " + me.getMessage());
/*     */       } 
/*     */       
/* 323 */       System.out.println("\n********************************");
/* 324 */       System.out.println("\nInvalid checksum test >>");
/*     */       
/*     */       try {
/* 327 */         validateMEID("A01234567890120");
/* 328 */         System.err.println("Should not get here!");
/*     */       }
/* 330 */       catch (MEIDException me) {
/*     */         
/* 332 */         System.out.println("<Expected error> Message: " + me.getMessage());
/*     */       } 
/* 334 */       System.out.println("\n********************************");
/* 335 */       System.out.println("\nInvalid length of decimal MEID");
/*     */       
/*     */       try {
/* 338 */         validateMEID("2684354559999999991");
/* 339 */         System.err.println("Should not get here!");
/*     */       }
/* 341 */       catch (MEIDException me) {
/*     */         
/* 343 */         System.out.println("<Expected error> Message: " + me.getMessage());
/*     */       } 
/* 345 */       System.out.println("\n********************************");
/* 346 */       System.out.println("\nDecimal MEID value too low");
/*     */       
/*     */       try {
/* 349 */         validateMEID("268435455999999999");
/* 350 */         System.err.println("Should not get here!");
/*     */       }
/* 352 */       catch (MEIDException me) {
/*     */         
/* 354 */         System.out.println("<Expected error> Message: " + me.getMessage());
/*     */       } 
/* 356 */       System.out.println("\n********************************");
/* 357 */       System.out.println("\nDecimal MEID value too high");
/*     */       
/*     */       try {
/* 360 */         validateMEID("429496729516777216");
/* 361 */         System.err.println("Should not get here!");
/*     */       }
/* 363 */       catch (MEIDException me) {
/*     */         
/* 365 */         System.out.println("<Expected error> Message: " + me.getMessage());
/*     */       } 
/* 367 */       System.out.println("\n********************************");
/* 368 */       System.out.println("\nDecimal MEID value too high");
/*     */       
/*     */       try {
/* 371 */         validateMEID("429496729616777215");
/* 372 */         System.err.println("Should not get here!");
/*     */       }
/* 374 */       catch (MEIDException me) {
/*     */         
/* 376 */         System.out.println("<Expected error> Message: " + me.getMessage());
/*     */       } 
/* 378 */       System.out.println("\n********************************");
/* 379 */       System.out.println("\nDecimal MEID value too low");
/*     */       
/*     */       try {
/* 382 */         validateMEID("000000000016777215");
/* 383 */         System.err.println("Should not get here!");
/*     */       }
/* 385 */       catch (MEIDException me) {
/*     */         
/* 387 */         System.out.println("<Expected error> Message: " + me.getMessage());
/*     */       } 
/* 389 */       System.out.println("\n********************************");
/*     */       
/* 391 */       String[] meidList = { "A0123456123450", "A0123456123451", "A0123456123452", "A0123456123453", "A0123456123454", "A0123456123455", "A0123456123456", "A0123456123457", "A0123456123458", "A0123456123459", "A012345612345A", "A012345612345B", "A012345612345C", "A012345612345D", "A012345612345E", "A012345612345F" };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 400 */       System.out.println(""); int i;
/* 401 */       for (i = 0; i < meidList.length; i++) {
/*     */         
/* 403 */         System.out.println("********************************");
/* 404 */         System.out.println("MEID: " + meidList[i] + "(" + 
/* 405 */             validateMEID(meidList[i]) + ")");
/* 406 */         System.out.println("Checksum:    " + getChecksum(meidList[i]));
/* 407 */         System.out.println("Decimal MEID: " + getDecimalMEID(meidList[i]));
/* 408 */         System.out.println("Dec to Hex: " + 
/* 409 */             validateMEID(getDecimalMEID(meidList[i])));
/*     */       } 
/*     */       
/* 412 */       System.out.println("\n<MEID>,<Decimal MEID>");
/* 413 */       for (i = 0; i < meidList.length; i++)
/*     */       {
/* 415 */         System.out.println(meidList[i]
/* 416 */             .substring(0, 14)
/* 417 */             .toUpperCase() + getChecksum(meidList[i]) + "," + 
/* 418 */             getDecimalMEID(meidList[i]));
/*     */       }
/*     */     }
/* 421 */     catch (MEIDException me) {
/*     */       
/* 423 */       me.printStackTrace();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\w\\upd\warrant\\util\MeidUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */