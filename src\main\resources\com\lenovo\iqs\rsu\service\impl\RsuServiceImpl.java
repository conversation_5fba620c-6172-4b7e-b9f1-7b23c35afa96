/*     */ package WEB-INF.classes.com.lenovo.iqs.rsu.service.impl;
/*     */ 
/*     */ import com.alibaba.druid.util.StringUtils;
/*     */ import com.lenovo.iqs.rsu.bean.RsuRequest;
/*     */ import com.lenovo.iqs.rsu.bean.RsuResponse;
/*     */ import com.lenovo.iqs.rsu.dao.RsuWebserviceMapper;
/*     */ import com.lenovo.iqs.rsu.service.RsuService;
/*     */ import com.lenovo.iqs.utils.HttpClientUtils;
/*     */ import java.util.List;
/*     */ import org.springframework.beans.factory.annotation.Autowired;
/*     */ import org.springframework.beans.factory.annotation.Value;
/*     */ import org.springframework.stereotype.Service;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Service
/*     */ public class RsuServiceImpl
/*     */   implements RsuService
/*     */ {
/*     */   @Value("${GPS_URL}")
/*     */   private String gpsUrl;
/*     */   @Value("${GPS_USER}")
/*     */   private String authUser;
/*     */   @Value("${GPS_PWD}")
/*     */   private String authPwd;
/*     */   @Autowired
/*     */   private RsuWebserviceMapper mapper;
/*  31 */   private String rsuSoapXml = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:rsu=\"http://rsu.programmingservice.cfc.nextest.globaltest.motorolamobility.com/\">   <soapenv:Header/>   <soapenv:Body>      <rsu:SaveRsuDataForService>         <trackid>{trackId}</trackid>         <rsureceiptdata>            <deviceModel>{deviceModel}</deviceModel>            <IMEI>{imei}</IMEI>            <MNO>{mno}</MNO>            <receiptCounter></receiptCounter>            <receiptData>cid:************</receiptData>            <receiptDataString>{receiptDataString}</receiptDataString>            <SUID>{suid}</SUID>            <siP>{sip}</siP>            <signedReceipt>cid:************</signedReceipt>\n            <soCModel>{socModel}</soCModel>         </rsureceiptdata>      </rsu:SaveRsuDataForService>   </soapenv:Body></soapenv:Envelope>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String buildParams(RsuRequest rsuRequest) {
/*  54 */     return this.rsuSoapXml.replace("{trackId}", rsuRequest.getTrack_id())
/*  55 */       .replace("{deviceModel}", rsuRequest.getDeviceModel())
/*  56 */       .replace("{imei}", rsuRequest.getSerialNo())
/*  57 */       .replace("{mno}", rsuRequest.getMn_operator())
/*  58 */       .replace("{receiptDataString}", rsuRequest.getReceiptData())
/*  59 */       .replace("{suid}", rsuRequest.getSuid())
/*  60 */       .replace("{sip}", rsuRequest.getSip())
/*  61 */       .replace("{socModel}", rsuRequest.getSoCModel());
/*     */   }
/*     */ 
/*     */   
/*     */   public RsuResponse processRsuRequest(RsuRequest request) throws Exception {
/*  66 */     RsuResponse rsuResponse = validateRequestParam(request);
/*  67 */     if (rsuResponse != null) {
/*  68 */       return rsuResponse;
/*     */     }
/*  70 */     String soapRsuParam = buildParams(request);
/*  71 */     String response = HttpClientUtils.postWebservice(this.gpsUrl, soapRsuParam, "", this.authUser, this.authPwd);
/*     */     
/*  73 */     rsuResponse = RsuResponse.build(response);
/*  74 */     return rsuResponse;
/*     */   }
/*     */   
/*     */   private RsuResponse validateRequestParam(RsuRequest request) {
/*  78 */     RsuResponse response = null;
/*     */     
/*  80 */     String serialNumber = request.getSerialNo();
/*  81 */     if (StringUtils.isEmpty(serialNumber)) {
/*  82 */       response = new RsuResponse();
/*  83 */       response.setResponseCode("5070");
/*  84 */       response.setResponseMsg("Invalid serialNumber");
/*  85 */       return response;
/*     */     } 
/*     */     
/*  88 */     String rsdUser = request.getRsdUser();
/*  89 */     if (StringUtils.isEmpty(rsdUser)) {
/*  90 */       response = new RsuResponse();
/*  91 */       response.setResponseCode("5079");
/*  92 */       response.setResponseMsg("RSU Service Invalid RSD User");
/*  93 */       return response;
/*     */     } 
/*     */     
/*  96 */     String mascId = request.getMascId();
/*  97 */     if (StringUtils.isEmpty(mascId)) {
/*  98 */       response = new RsuResponse();
/*  99 */       response.setResponseCode("5080");
/* 100 */       response.setResponseMsg("RSU Service Invalid Masc ID");
/* 101 */       return response;
/*     */     } 
/*     */     
/* 104 */     String suid = request.getSuid();
/* 105 */     if (StringUtils.isEmpty(suid)) {
/* 106 */       response = new RsuResponse();
/* 107 */       response.setResponseCode("5082");
/* 108 */       response.setResponseMsg("RSU Service Invalid suid");
/* 109 */       return response;
/*     */     } 
/*     */ 
/*     */     
/* 113 */     String receiptData = request.getReceiptData();
/* 114 */     if (StringUtils.isEmpty(receiptData)) {
/* 115 */       response = new RsuResponse();
/* 116 */       response.setResponseCode("5083");
/* 117 */       response.setResponseMsg("RSU Service Invalid Reciept Data");
/* 118 */       return response;
/*     */     } 
/*     */     
/* 121 */     String mno = request.getMn_operator();
/* 122 */     if (isValidMNO(mno)) {
/* 123 */       response = new RsuResponse();
/* 124 */       response.setResponseCode("5085");
/* 125 */       response.setResponseMsg("RSU Service Invalid MNO");
/* 126 */       return response;
/*     */     } 
/*     */     
/* 129 */     String deviceModel = request.getDeviceModel();
/* 130 */     if (StringUtils.isEmpty(deviceModel)) {
/* 131 */       response = new RsuResponse();
/* 132 */       response.setResponseCode("5084");
/* 133 */       response.setResponseMsg("RSU Service Invalid Reciept Data");
/* 134 */       return response;
/*     */     } 
/* 136 */     String correctModel = this.mapper.getCorrectModel(deviceModel);
/* 137 */     if (StringUtils.isEmpty(correctModel)) {
/* 138 */       response = new RsuResponse();
/* 139 */       response.setResponseCode("5087");
/* 140 */       response.setResponseMsg("DeviceModel is not set up in IBASE_CONFIG setup. Please contact support team to device model mapping to SOC and SIP");
/* 141 */       return response;
/*     */     } 
/* 143 */     request.setDeviceModel(correctModel);
/*     */     
/* 145 */     String sip = request.getSip();
/* 146 */     if (StringUtils.isEmpty(sip)) {
/* 147 */       sip = this.mapper.getSipForModel(correctModel.trim());
/* 148 */       if (StringUtils.isEmpty(sip)) {
/* 149 */         response = new RsuResponse();
/* 150 */         response.setResponseCode("5087");
/* 151 */         response.setResponseMsg("DeviceModel is not set up in IBASE_CONFIG setup. Please contact support team to device model mapping to SOC and SIP");
/* 152 */         return response;
/*     */       } 
/* 154 */       request.setSip(sip);
/*     */     } 
/*     */     
/* 157 */     String socModel = request.getSoCModel();
/* 158 */     if (StringUtils.isEmpty(socModel)) {
/* 159 */       socModel = this.mapper.getSocForModel(correctModel.trim());
/* 160 */       if (StringUtils.isEmpty(socModel)) {
/* 161 */         response = new RsuResponse();
/* 162 */         response.setResponseCode("5087");
/* 163 */         response.setResponseMsg("DeviceModel is not set up in IBASE_CONFIG setup. Please contact support team to device model mapping to SOC and SIP");
/* 164 */         return response;
/*     */       } 
/* 166 */       request.setSoCModel(socModel);
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 173 */     String trackId = request.getTrack_id();
/* 174 */     if (StringUtils.isEmpty(trackId)) {
/* 175 */       response = new RsuResponse();
/* 176 */       response.setResponseCode("5086");
/* 177 */       response.setResponseMsg("RSU Service Invalid Track ID");
/* 178 */       return response;
/*     */     } 
/*     */     
/* 181 */     return response;
/*     */   }
/*     */   
/*     */   private boolean isValidMNO(String mno) {
/* 185 */     if (!StringUtils.isEmpty(mno)) {
/* 186 */       List<String> mnos = this.mapper.getAllowedTMOValues();
/* 187 */       for (String mob_no : mnos) {
/* 188 */         if (mno.equalsIgnoreCase(mob_no)) {
/* 189 */           return false;
/*     */         }
/*     */       } 
/*     */     } 
/* 193 */     return true;
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\rsu\service\impl\RsuServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */