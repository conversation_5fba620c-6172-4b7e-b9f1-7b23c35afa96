/*    */ package WEB-INF.classes.com.lenovo.iqs.utils;
/*    */ 
/*    */ import com.google.common.base.Strings;
/*    */ import com.lenovo.iqs.utils.BrandObject;
/*    */ import com.lenovo.iqs.utils.ProductTypeObject;
/*    */ import java.util.List;
/*    */ import java.util.Set;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ProdHieHelper
/*    */ {
/*    */   private static String GetPreffix(String operator) {
/*    */     try {
/* 21 */       return (null == operator) ? "" : operator.substring(0, 4);
/* 22 */     } catch (Exception e) {
/* 23 */       return "";
/*    */     } 
/*    */   }
/*    */   
/*    */   public static String GetBrand(String prodHie) {
/* 28 */     switch (GetPreffix(prodHie)) {
/*    */ 
/*    */ 
/*    */ 
/*    */       
/*    */       case "":
/*    */       case "PMIP":
/*    */       case "PWIP":
/*    */       case "PMOP":
/* 37 */         return BrandObject.MOTO.toString();
/*    */     } 
/* 39 */     return BrandObject.LENOVO.toString();
/*    */   }
/*    */   
/*    */   public static String GetProductType(String prodHie) {
/* 43 */     switch (GetPreffix(prodHie)) {
/*    */       
/*    */       case "PMIP":
/* 46 */         return ProductTypeObject.PHONE.toString();
/*    */       
/*    */       case "PWIP":
/* 49 */         return ProductTypeObject.WATCH.toString();
/*    */       
/*    */       case "":
/*    */       case "PMOP":
/* 53 */         return ProductTypeObject.ACCY.toString();
/*    */     } 
/* 55 */     return null;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/* 61 */   public static List<String> motoMbgHierarchySet = (List<String>)new Object();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/* 72 */   public static Set<String> lenovoMbgHierarchySet = (Set<String>)new Object();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/* 83 */   private static Set<String> mbgHierarchySet = (Set<String>)new Object();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static boolean isMBGHie(String hierarchy) {
/* 93 */     if (!Strings.isNullOrEmpty(hierarchy) && hierarchy.length() > 4) {
/* 94 */       return mbgHierarchySet.contains(hierarchy.substring(0, 4).toUpperCase());
/*    */     }
/* 96 */     return false;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iq\\utils\ProdHieHelper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */