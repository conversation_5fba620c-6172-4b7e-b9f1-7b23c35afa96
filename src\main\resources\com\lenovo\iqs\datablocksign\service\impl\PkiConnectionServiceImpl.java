/*     */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.service.impl;
/*     */ import com.google.gson.Gson;
/*     */ import com.lenovo.iqs.datablocksign.bean.PkiNLHTTPSMessage;
/*     */ import com.lenovo.iqs.datablocksign.bean.PkiNodeLockingResponse;
/*     */ import com.lenovo.iqs.datablocksign.bean.RequestBean;
/*     */ import com.lenovo.iqs.exceptions.PKIException;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.InputStream;
/*     */ import java.io.OutputStream;
/*     */ import java.security.KeyStore;
/*     */ import java.sql.Timestamp;
/*     */ import java.util.Arrays;
/*     */ import java.util.Base64;
/*     */ import javax.net.ssl.KeyManager;
/*     */ import javax.net.ssl.KeyManagerFactory;
/*     */ import javax.net.ssl.SSLContext;
/*     */ import javax.net.ssl.SSLSocket;
/*     */ import javax.net.ssl.SSLSocketFactory;
/*     */ import javax.net.ssl.TrustManager;
/*     */ import javax.net.ssl.TrustManagerFactory;
/*     */ import okhttp3.MediaType;
/*     */ import okhttp3.OkHttpClient;
/*     */ import okhttp3.Request;
/*     */ import okhttp3.RequestBody;
/*     */ import okhttp3.Response;
/*     */ import org.apache.log4j.Logger;
/*     */ import org.springframework.beans.factory.annotation.Value;
/*     */ import org.springframework.stereotype.Service;
/*     */ 
/*     */ @Service
/*     */ public class PkiConnectionServiceImpl implements PkiConnectionService {
/*  32 */   private static final Logger log = Logger.getLogger(com.lenovo.iqs.datablocksign.service.impl.PkiConnectionServiceImpl.class);
/*     */   
/*     */   public static int expSize;
/*     */   
/*     */   @Value("${pki_trust_cert}")
/*     */   private String pkiTrustCert;
/*     */   
/*     */   @Value("${pki_trust_pass}")
/*     */   private String pkiTrustPass;
/*     */   
/*     */   @Value("${pki_identity_cert}")
/*     */   private String pkiIdentityCert;
/*     */   @Value("${pki_identity_pass}")
/*     */   private String pkiIdentityPass;
/*     */   @Value("${nl_pki_trust_cert}")
/*     */   private String nlpkiTrustCert;
/*     */   @Value("${nl_pki_trust_pass}")
/*     */   private String nlpkiTrustPass;
/*     */   @Value("${nl_pki_identity_cert}")
/*     */   private String nlpkiIdentityCert;
/*     */   @Value("${nl_pki_identity_pass}")
/*     */   private String nlpkiIdentityPass;
/*     */   @Value("${nl_pki_ip}")
/*     */   private String nlpkiip;
/*     */   @Value("${nl_pki_url}")
/*     */   private String nlpkiurl;
/*  58 */   private final String DEFAULT_PKI_SERVER = "***********";
/*     */   
/*  60 */   public static String SIGNED_MESSAGE = "{\n  \"tnlDbsRequest\": {\n    \"tnlRequestMessage\": \"<NodeLockingReqMessage>\",\n    \"dbsRequestMessage\": \"<dbsRequestMessage>\"\n  }\n}";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private SSLSocket connect(int port, String pkiIp) throws Exception {
/*  86 */     SSLSocketFactory sslSocketFactory = (SSLSocketFactory)SSLSocketFactory.getDefault();
/*  87 */     SSLSocket sslSocket = (SSLSocket)sslSocketFactory.createSocket(pkiIp, port);
/*  88 */     sslSocket.setEnabledProtocols(new String[] { "TLSv1.2" });
/*     */     
/*  90 */     Timestamp ts = new Timestamp(System.currentTimeMillis());
/*  91 */     int preTime = ts.getNanos();
/*  92 */     if (StringUtils.isEmpty(pkiIp)) {
/*  93 */       log.info("PKI warning: pkiIp is empty");
/*  94 */       pkiIp = "***********";
/*     */     } 
/*     */ 
/*     */     
/*  98 */     Timestamp ts1 = new Timestamp(System.currentTimeMillis());
/*  99 */     int postTime = ts1.getNanos();
/* 100 */     int timediff = (postTime - preTime) / 1000000;
/* 101 */     log.info("PKI access success, and costTime:" + timediff);
/* 102 */     return sslSocket;
/*     */   }
/*     */ 
/*     */   
/*     */   public byte[] forwardToPKI(RequestBean requestBean, String pkiIp) throws Exception {
/* 107 */     int port = 0;
/*     */     
/* 109 */     if ("0x05".equals(requestBean.getIstrClientReqType())) {
/* 110 */       port = 2002;
/*     */     } else {
/* 112 */       port = 2003;
/*     */     } 
/* 114 */     return forwardToPKI(requestBean.getIstrReqParam(), port, pkiIp);
/*     */   }
/*     */   
/*     */   public PkiNLHTTPSMessage forwardToNLPKI(RequestBean requestBean, String pkiIp) throws Exception {
/* 118 */     PkiNLHTTPSMessage pkiresponse = new PkiNLHTTPSMessage();
/* 119 */     int port = 2029;
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 124 */     String tnlmessage = Base64.getEncoder().encodeToString(requestBean.getIstrNodeLockParam());
/* 125 */     String dbReq = Base64.getEncoder().encodeToString(requestBean.getIstrReqParam());
/*     */ 
/*     */     
/* 128 */     String sign_message = SIGNED_MESSAGE.replace("<NodeLockingReqMessage>", tnlmessage);
/* 129 */     sign_message = sign_message.replace("<dbsRequestMessage>", dbReq);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 165 */       OkHttpClient client = getSSLClientForNLPKI();
/* 166 */       MediaType mediaType = MediaType.parse("application/json");
/* 167 */       RequestBody body = RequestBody.create(mediaType, sign_message);
/* 168 */       log.info("nlpkiurl set here is " + this.nlpkiurl);
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 173 */       Request request = (new Request.Builder()).url(this.nlpkiurl).method("POST", body).addHeader("Content-Type", "application/json").build();
/* 174 */       Response response = client.newCall(request).execute();
/* 175 */       String result = "";
/* 176 */       result = response.body().string();
/* 177 */       if (result != null) {
/* 178 */         pkiresponse.setStatus_code(response.code());
/*     */       } else {
/* 180 */         pkiresponse.setStatus_code(222);
/* 181 */         result = "";
/*     */       } 
/*     */       
/* 184 */       log.info("after calling new PKI NL server");
/*     */       
/* 186 */       log.info(result);
/*     */       
/* 188 */       if (pkiresponse.isRequestSuccessful()) {
/* 189 */         log.info("after calling new PKI NL server SUCCESS");
/*     */ 
/*     */ 
/*     */         
/* 193 */         PkiNodeLockingResponse resp = parseJson(result);
/* 194 */         log.info(result);
/* 195 */         pkiresponse.setResponse(resp);
/*     */ 
/*     */       
/*     */       }
/*     */ 
/*     */     
/*     */     }
/* 202 */     catch (Exception ex) {
/* 203 */       log.error("new pki NL exception blok");
/* 204 */       log.error(ex);
/*     */     } 
/*     */     
/* 207 */     return pkiresponse;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private OkHttpClient getSSLClientForNLPKI() throws Exception {
/*     */     try {
/* 222 */       log.debug("Inside getSSLClientForNLPKI");
/*     */       
/* 224 */       KeyStore trustKeyStore = KeyStore.getInstance("JKS");
/* 225 */       trustKeyStore.load(new FileInputStream(this.pkiTrustCert), this.pkiTrustPass.toCharArray());
/* 226 */       TrustManagerFactory tmf = TrustManagerFactory.getInstance("SunX509", "SunJSSE");
/* 227 */       tmf.init(trustKeyStore);
/* 228 */       TrustManager[] tm = tmf.getTrustManagers();
/*     */       
/* 230 */       KeyStore keyStore = KeyStore.getInstance("PKCS12");
/* 231 */       keyStore.load(new FileInputStream(this.pkiIdentityCert), this.pkiIdentityPass.toCharArray());
/* 232 */       KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509", "SunJSSE");
/* 233 */       kmf.init(keyStore, this.pkiIdentityPass.toCharArray());
/* 234 */       KeyManager[] km = kmf.getKeyManagers();
/*     */       
/* 236 */       SSLContext sslContext = SSLContext.getInstance("TLSv1.2", "SunJSSE");
/* 237 */       sslContext.init(km, tm, null);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 251 */       OkHttpClient client = (new OkHttpClient.Builder()).hostnameVerifier((HostnameVerifier)new Object(this)).sslSocketFactory(sslContext.getSocketFactory()).build();
/* 252 */       return client;
/* 253 */     } catch (Exception ex) {
/* 254 */       log.error("Inside getSSLClientForNLPKI");
/* 255 */       log.error(ex);
/* 256 */       throw ex;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static PkiNodeLockingResponse parseJson(String json) {
/*     */     try {
/* 277 */       Gson gson = new Gson();
/* 278 */       PkiNodeLockingResponse resp = (PkiNodeLockingResponse)gson.fromJson(json, PkiNodeLockingResponse.class);
/* 279 */       System.out.println(resp.getTnlDbsResponse().getTnlResponseMessage());
/* 280 */       return resp;
/* 281 */     } catch (Exception ex) {
/* 282 */       log.error("Inside Parse JSON NLPKI");
/*     */       
/* 284 */       log.error(ex);
/* 285 */       throw ex;
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public byte[] forwardToPKI(byte[] signedMessage, int port, String pkiIp) throws Exception {
/* 291 */     byte[] actualPKIResponse = null;
/*     */     try {
/* 293 */       SSLSocket sslSocket = connect(port, pkiIp);
/* 294 */       log.info("Got the SSLSocket. TLS version = " + sslSocket.getSession().getProtocol());
/* 295 */       OutputStream outToServer = sslSocket.getOutputStream();
/* 296 */       outToServer.write(signedMessage);
/* 297 */       outToServer.flush();
/*     */       
/* 299 */       sslSocket.setReceiveBufferSize(65535);
/* 300 */       InputStream inStream = sslSocket.getInputStream();
/* 301 */       byte[] pkiResponse = null;
/* 302 */       byte[] buff1 = new byte[2];
/* 303 */       int numRead = 0;
/* 304 */       while (numRead < buff1.length && numRead != -1) {
/* 305 */         numRead += inStream.read(buff1, numRead, buff1.length - numRead);
/* 306 */         log.info("numRead is : " + numRead);
/*     */       } 
/* 308 */       log.info("PKI return data: " + Arrays.toString(buff1));
/* 309 */       expSize = computeExpSize(buff1);
/* 310 */       log.info("PKI expSize:" + expSize);
/* 311 */       pkiResponse = new byte[expSize];
/* 312 */       if (expSize != 0) {
/* 313 */         System.arraycopy(buff1, 0, pkiResponse, 0, 2);
/* 314 */         while (numRead != expSize) {
/* 315 */           numRead += inStream.read(pkiResponse, numRead, expSize - numRead);
/*     */         }
/*     */       } 
/* 318 */       log.info("PKI return data pkiResponse:" + Arrays.toString(pkiResponse));
/* 319 */       int totalBytes = pkiResponse.length;
/* 320 */       actualPKIResponse = new byte[totalBytes];
/* 321 */       for (int i = 0; i < totalBytes; i++) {
/* 322 */         actualPKIResponse[i] = pkiResponse[i];
/*     */       }
/*     */       
/* 325 */       outToServer.close();
/* 326 */       inStream.close();
/* 327 */       sslSocket.close();
/* 328 */     } catch (Exception exp) {
/* 329 */       PKIException p = new PKIException(exp);
/* 330 */       throw p;
/*     */     } 
/* 332 */     return actualPKIResponse;
/*     */   }
/*     */ 
/*     */   
/*     */   public byte[] forwardDeviceUnlockToPKI(byte[] signedMessage, int port, float version) throws Exception {
/* 337 */     SSLSocket clientSocket = connect(port, "10.181.214.143");
/* 338 */     OutputStream outToServer = clientSocket.getOutputStream();
/*     */     
/* 340 */     outToServer.write(signedMessage);
/* 341 */     outToServer.flush();
/*     */     
/* 343 */     InputStream inStream = clientSocket.getInputStream();
/* 344 */     byte[] outputArrayfromPKI = new byte[1795];
/* 345 */     inStream.read(outputArrayfromPKI);
/*     */     
/* 347 */     outToServer.close();
/* 348 */     inStream.close();
/* 349 */     clientSocket.close();
/* 350 */     return outputArrayfromPKI;
/*     */   }
/*     */ 
/*     */   
/*     */   private int computeExpSize(byte[] buff1) {
/* 355 */     String hexFirst = Integer.toHexString(buff1[0] & 0xFF);
/* 356 */     hexFirst = (hexFirst.length() == 1) ? ("0" + hexFirst) : hexFirst;
/* 357 */     String hexSecond = Integer.toHexString(buff1[1] & 0xFF);
/* 358 */     hexSecond = (hexSecond.length() == 1) ? ("0" + hexSecond) : hexSecond;
/* 359 */     return Integer.parseInt(hexFirst + hexSecond, 16);
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\service\impl\PkiConnectionServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */