/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTObjectWarranty implements Serializable { private Integer id; private String machineType;
/*    */   private String serialNumber;
/*    */   private String sequenceNumber;
/*    */   private String wtyId;
/*    */   
/*  7 */   public void setId(Integer id) { this.id = id; } private Date wtyStartDate; private Date wtyEndDate; private Date lastChangeTime; private String partitionChar; private static final long serialVersionUID = 1L; public void setMachineType(String machineType) { this.machineType = machineType; } public void setSerialNumber(String serialNumber) { this.serialNumber = serialNumber; } public void setSequenceNumber(String sequenceNumber) { this.sequenceNumber = sequenceNumber; } public void setWtyId(String wtyId) { this.wtyId = wtyId; } public void setWtyStartDate(Date wtyStartDate) { this.wtyStartDate = wtyStartDate; } public void setWtyEndDate(Date wtyEndDate) { this.wtyEndDate = wtyEndDate; } public void setLastChangeTime(Date lastChangeTime) { this.lastChangeTime = lastChangeTime; } public void setPartitionChar(String partitionChar) { this.partitionChar = partitionChar; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTObjectWarranty)) return false;  com.lenovo.iqs.entity.IbTObjectWarranty other = (com.lenovo.iqs.entity.IbTObjectWarranty)o; if (!other.canEqual(this)) return false;  Object this$id = getId(), other$id = other.getId(); if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id)) return false;  Object this$machineType = getMachineType(), other$machineType = other.getMachineType(); if ((this$machineType == null) ? (other$machineType != null) : !this$machineType.equals(other$machineType)) return false;  Object this$serialNumber = getSerialNumber(), other$serialNumber = other.getSerialNumber(); if ((this$serialNumber == null) ? (other$serialNumber != null) : !this$serialNumber.equals(other$serialNumber)) return false;  Object this$sequenceNumber = getSequenceNumber(), other$sequenceNumber = other.getSequenceNumber(); if ((this$sequenceNumber == null) ? (other$sequenceNumber != null) : !this$sequenceNumber.equals(other$sequenceNumber)) return false;  Object this$wtyId = getWtyId(), other$wtyId = other.getWtyId(); if ((this$wtyId == null) ? (other$wtyId != null) : !this$wtyId.equals(other$wtyId)) return false;  Object this$wtyStartDate = getWtyStartDate(), other$wtyStartDate = other.getWtyStartDate(); if ((this$wtyStartDate == null) ? (other$wtyStartDate != null) : !this$wtyStartDate.equals(other$wtyStartDate)) return false;  Object this$wtyEndDate = getWtyEndDate(), other$wtyEndDate = other.getWtyEndDate(); if ((this$wtyEndDate == null) ? (other$wtyEndDate != null) : !this$wtyEndDate.equals(other$wtyEndDate)) return false;  Object this$lastChangeTime = getLastChangeTime(), other$lastChangeTime = other.getLastChangeTime(); if ((this$lastChangeTime == null) ? (other$lastChangeTime != null) : !this$lastChangeTime.equals(other$lastChangeTime)) return false;  Object this$partitionChar = getPartitionChar(), other$partitionChar = other.getPartitionChar(); return !((this$partitionChar == null) ? (other$partitionChar != null) : !this$partitionChar.equals(other$partitionChar)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTObjectWarranty; } public int hashCode() { int PRIME = 59; result = 1; Object $id = getId(); result = result * 59 + (($id == null) ? 43 : $id.hashCode()); Object $machineType = getMachineType(); result = result * 59 + (($machineType == null) ? 43 : $machineType.hashCode()); Object $serialNumber = getSerialNumber(); result = result * 59 + (($serialNumber == null) ? 43 : $serialNumber.hashCode()); Object $sequenceNumber = getSequenceNumber(); result = result * 59 + (($sequenceNumber == null) ? 43 : $sequenceNumber.hashCode()); Object $wtyId = getWtyId(); result = result * 59 + (($wtyId == null) ? 43 : $wtyId.hashCode()); Object $wtyStartDate = getWtyStartDate(); result = result * 59 + (($wtyStartDate == null) ? 43 : $wtyStartDate.hashCode()); Object $wtyEndDate = getWtyEndDate(); result = result * 59 + (($wtyEndDate == null) ? 43 : $wtyEndDate.hashCode()); Object $lastChangeTime = getLastChangeTime(); result = result * 59 + (($lastChangeTime == null) ? 43 : $lastChangeTime.hashCode()); Object $partitionChar = getPartitionChar(); return result * 59 + (($partitionChar == null) ? 43 : $partitionChar.hashCode()); } public String toString() { return "IbTObjectWarranty(id=" + getId() + ", machineType=" + getMachineType() + ", serialNumber=" + getSerialNumber() + ", sequenceNumber=" + getSequenceNumber() + ", wtyId=" + getWtyId() + ", wtyStartDate=" + getWtyStartDate() + ", wtyEndDate=" + getWtyEndDate() + ", lastChangeTime=" + getLastChangeTime() + ", partitionChar=" + getPartitionChar() + ")"; }
/*    */    public Integer getId() {
/*  9 */     return this.id;
/*    */   } public String getMachineType() {
/* 11 */     return this.machineType;
/*    */   } public String getSerialNumber() {
/* 13 */     return this.serialNumber;
/*    */   } public String getSequenceNumber() {
/* 15 */     return this.sequenceNumber;
/*    */   } public String getWtyId() {
/* 17 */     return this.wtyId;
/*    */   } public Date getWtyStartDate() {
/* 19 */     return this.wtyStartDate;
/*    */   } public Date getWtyEndDate() {
/* 21 */     return this.wtyEndDate;
/*    */   } public Date getLastChangeTime() {
/* 23 */     return this.lastChangeTime;
/*    */   } public String getPartitionChar() {
/* 25 */     return this.partitionChar;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTObjectWarranty.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */