/*    */ package WEB-INF.classes.com.lenovo.iqs.googlerpk.bean;
/*    */ 
/*    */ import com.lenovo.iqs.utils.XmlUtils;
/*    */ import org.springframework.util.StringUtils;
/*    */ 
/*    */ public class GoogleRPKResponse {
/*    */   private String responseCode;
/*    */   private String responseMsg;
/*    */   
/* 10 */   public void setResponseCode(String responseCode) { this.responseCode = responseCode; } public void setResponseMsg(String responseMsg) { this.responseMsg = responseMsg; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.googlerpk.bean.GoogleRPKResponse)) return false;  com.lenovo.iqs.googlerpk.bean.GoogleRPKResponse other = (com.lenovo.iqs.googlerpk.bean.GoogleRPKResponse)o; if (!other.canEqual(this)) return false;  Object this$responseCode = getResponseCode(), other$responseCode = other.getResponseCode(); if ((this$responseCode == null) ? (other$responseCode != null) : !this$responseCode.equals(other$responseCode)) return false;  Object this$responseMsg = getResponseMsg(), other$responseMsg = other.getResponseMsg(); return !((this$responseMsg == null) ? (other$responseMsg != null) : !this$responseMsg.equals(other$responseMsg)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.googlerpk.bean.GoogleRPKResponse; } public int hashCode() { int PRIME = 59; result = 1; Object $responseCode = getResponseCode(); result = result * 59 + (($responseCode == null) ? 43 : $responseCode.hashCode()); Object $responseMsg = getResponseMsg(); return result * 59 + (($responseMsg == null) ? 43 : $responseMsg.hashCode()); } public String toString() { return "GoogleRPKResponse(responseCode=" + getResponseCode() + ", responseMsg=" + getResponseMsg() + ")"; }
/*    */   
/* 12 */   public String getResponseCode() { return this.responseCode; } public String getResponseMsg() {
/* 13 */     return this.responseMsg;
/*    */   }
/*    */   public static com.lenovo.iqs.googlerpk.bean.GoogleRPKResponse build(String data) throws Exception {
/* 16 */     com.lenovo.iqs.googlerpk.bean.GoogleRPKResponse goolgleRPKResponse = new com.lenovo.iqs.googlerpk.bean.GoogleRPKResponse();
/* 17 */     String errorCode = XmlUtils.getContentByTag(data, "errorCode");
/* 18 */     String errorMessage = XmlUtils.getContentByTag(data, "errorMessage");
/* 19 */     if ("0".equalsIgnoreCase(errorCode)) {
/* 20 */       errorMessage = "Success";
/*    */     }
/*    */     
/* 23 */     goolgleRPKResponse.setResponseCode(errorCode);
/* 24 */     goolgleRPKResponse.setResponseMsg(StringUtils.isEmpty(errorMessage) ? "" : errorMessage);
/*    */     
/* 26 */     return goolgleRPKResponse;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\googlerpk\bean\GoogleRPKResponse.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */