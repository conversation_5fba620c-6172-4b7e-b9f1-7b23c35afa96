/*    */ package WEB-INF.classes.com.lenovo.iqs.simlock.bean;
/*    */ 
/*    */ 
/*    */ public class KSUnlockResponse {
/*    */   private String responseCode;
/*    */   private String responseMsg;
/*    */   private String msg;
/*    */   private String password;
/*    */   
/* 10 */   public void setResponseCode(String responseCode) { this.responseCode = responseCode; } public void setResponseMsg(String responseMsg) { this.responseMsg = responseMsg; } public void setMsg(String msg) { this.msg = msg; } public void setPassword(String password) { this.password = password; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.simlock.bean.KSUnlockResponse)) return false;  com.lenovo.iqs.simlock.bean.KSUnlockResponse other = (com.lenovo.iqs.simlock.bean.KSUnlockResponse)o; if (!other.canEqual(this)) return false;  Object this$responseCode = getResponseCode(), other$responseCode = other.getResponseCode(); if ((this$responseCode == null) ? (other$responseCode != null) : !this$responseCode.equals(other$responseCode)) return false;  Object this$responseMsg = getResponseMsg(), other$responseMsg = other.getResponseMsg(); if ((this$responseMsg == null) ? (other$responseMsg != null) : !this$responseMsg.equals(other$responseMsg)) return false;  Object this$msg = getMsg(), other$msg = other.getMsg(); if ((this$msg == null) ? (other$msg != null) : !this$msg.equals(other$msg)) return false;  Object this$password = getPassword(), other$password = other.getPassword(); return !((this$password == null) ? (other$password != null) : !this$password.equals(other$password)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.simlock.bean.KSUnlockResponse; } public int hashCode() { int PRIME = 59; result = 1; Object $responseCode = getResponseCode(); result = result * 59 + (($responseCode == null) ? 43 : $responseCode.hashCode()); Object $responseMsg = getResponseMsg(); result = result * 59 + (($responseMsg == null) ? 43 : $responseMsg.hashCode()); Object $msg = getMsg(); result = result * 59 + (($msg == null) ? 43 : $msg.hashCode()); Object $password = getPassword(); return result * 59 + (($password == null) ? 43 : $password.hashCode()); } public String toString() { return "KSUnlockResponse(responseCode=" + getResponseCode() + ", responseMsg=" + getResponseMsg() + ", msg=" + getMsg() + ", password=" + getPassword() + ")"; }
/*    */   
/* 12 */   public String getResponseCode() { return this.responseCode; }
/* 13 */   public String getResponseMsg() { return this.responseMsg; }
/* 14 */   public String getMsg() { return this.msg; } public String getPassword() {
/* 15 */     return this.password;
/*    */   }
/*    */   public static com.lenovo.iqs.simlock.bean.KSUnlockResponse build(String data) throws Exception {
/* 18 */     com.lenovo.iqs.simlock.bean.KSUnlockResponse rsuResponse = new com.lenovo.iqs.simlock.bean.KSUnlockResponse();
/* 19 */     String errorCode = XmlUtils.getContentByTag(data, "errorCode");
/* 20 */     String errorMessage = XmlUtils.getContentByTag(data, "errorMessage");
/* 21 */     String certBlob = XmlUtils.getContentByTag(data, "msg");
/* 22 */     String pwd = XmlUtils.getContentByTag(data, "password");
/* 23 */     if ("0".equalsIgnoreCase(errorCode)) {
/* 24 */       errorMessage = "Success";
/*    */     }
/*    */     
/* 27 */     rsuResponse.setResponseCode(errorCode);
/* 28 */     rsuResponse.setResponseMsg(StringUtils.isEmpty(errorMessage) ? "" : errorMessage);
/* 29 */     rsuResponse.setMsg(StringUtils.isEmpty(certBlob) ? "" : certBlob);
/* 30 */     rsuResponse.setPassword(StringUtils.isEmpty(pwd) ? "" : pwd);
/* 31 */     return rsuResponse;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\simlock\bean\KSUnlockResponse.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */