<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdR12OutboundMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdR12Outbound" >
    <result column="SERIAL_NO" property="serialNo" jdbcType="VARCHAR" />
    <result column="SERIAL_NUMBER_TYPE" property="serialNumberType" jdbcType="VARCHAR" />
    <result column="FACTORY_CODE" property="factoryCode" jdbcType="VARCHAR" />
    <result column="GENERATION_DATE" property="generationDate" jdbcType="TIMESTAMP" />
    <result column="APC" property="apc" jdbcType="VARCHAR" />
    <result column="TRANSCEIVER_MODEL" property="transceiverModel" jdbcType="VARCHAR" />
    <result column="CUSTOMER_MODEL" property="customerModel" jdbcType="VARCHAR" />
    <result column="MARKET_NAME" property="marketName" jdbcType="VARCHAR" />
    <result column="ITEM_CODE" property="itemCode" jdbcType="VARCHAR" />
    <result column="WARRANTY_CODE" property="warrantyCode" jdbcType="VARCHAR" />
    <result column="SHIP_DATE" property="shipDate" jdbcType="TIMESTAMP" />
    <result column="SHIP_TO_CUSTOMER_NUMBER" property="shipToCustomerNumber" jdbcType="VARCHAR" />
    <result column="SHIP_TO_CUSTOMER_ADDRESS_ID" property="shipToCustomerAddressId" jdbcType="VARCHAR" />
    <result column="SHIP_TO_CUSTOMER_NAME" property="shipToCustomerName" jdbcType="VARCHAR" />
    <result column="SHIP_TO_CITY" property="shipToCity" jdbcType="VARCHAR" />
    <result column="SHIP_TO_COUNTRY" property="shipToCountry" jdbcType="VARCHAR" />
    <result column="SOLD_TO_CUSTOMER_NUMBER" property="soldToCustomerNumber" jdbcType="VARCHAR" />
    <result column="SOLD_TO_CUSTOMER_NAME" property="soldToCustomerName" jdbcType="VARCHAR" />
    <result column="SOLD_DATE" property="soldDate" jdbcType="TIMESTAMP" />
    <result column="TRACK_ID" property="trackId" jdbcType="VARCHAR" />
    <result column="TA_NUMBER" property="taNumber" jdbcType="VARCHAR" />
    <result column="CARTON_ID" property="cartonId" jdbcType="VARCHAR" />
    <result column="PO_NUMBER" property="poNumber" jdbcType="VARCHAR" />
    <result column="SO_NUMBER" property="soNumber" jdbcType="VARCHAR" />
    <result column="PALLET_ID" property="palletId" jdbcType="VARCHAR" />
    <result column="PRIMARY_UNLOCK_CODE" property="primaryUnlockCode" jdbcType="VARCHAR" />
    <result column="PRIMECO_UNLOCK_CODE" property="primecoUnlockCode" jdbcType="VARCHAR" />
    <result column="VERIZON_UNLOCK_CODE" property="verizonUnlockCode" jdbcType="VARCHAR" />
    <result column="SECONDARY_UNLOCK_CODE" property="secondaryUnlockCode" jdbcType="VARCHAR" />
    <result column="SERVICE_PASSCODE" property="servicePasscode" jdbcType="VARCHAR" />
    <result column="LOCK_4" property="lock4" jdbcType="VARCHAR" />
    <result column="LOCK_5" property="lock5" jdbcType="VARCHAR" />
    <result column="A_KEY_RANDOM" property="aKeyRandom" jdbcType="VARCHAR" />
    <result column="A_KEY_ZERO" property="aKeyZero" jdbcType="VARCHAR" />
    <result column="MSN" property="msn" jdbcType="VARCHAR" />
    <result column="BT" property="bt" jdbcType="VARCHAR" />
    <result column="WLAN" property="wlan" jdbcType="VARCHAR" />
    <result column="BASE_PROCESSOR_ID" property="baseProcessorId" jdbcType="VARCHAR" />
    <result column="FASTT_ID" property="fasttId" jdbcType="VARCHAR" />
    <result column="LOCATION_TYPE" property="locationType" jdbcType="VARCHAR" />
    <result column="PACKING_LIST" property="packingList" jdbcType="VARCHAR" />
    <result column="FAB_DATE" property="fabDate" jdbcType="VARCHAR" />
    <result column="SOFTWARE_VERSION" property="softwareVersion" jdbcType="VARCHAR" />
    <result column="ORGANIZATION_CODE" property="organizationCode" jdbcType="VARCHAR" />
    <result column="FLEX_OPTION" property="flexOption" jdbcType="VARCHAR" />
    <result column="ICC_ID" property="iccId" jdbcType="VARCHAR" />
    <result column="SO_LINE_NUMBER" property="soLineNumber" jdbcType="VARCHAR" />
    <result column="DIRECT_SHIP_REGION_CODE" property="directShipRegionCode" jdbcType="VARCHAR" />
    <result column="DIRECT_SHIP_SO_NUMBER" property="directShipSoNumber" jdbcType="VARCHAR" />
    <result column="DIRECT_SHIP_PO_NUMBER" property="directShipPoNumber" jdbcType="VARCHAR" />
    <result column="DIRECT_SHIP_CUSTOMER_NUMBER" property="directShipCustomerNumber" jdbcType="VARCHAR" />
    <result column="DIRECT_SHIP_SHIP_TO_ADDRESS_ID" property="directShipShipToAddressId" jdbcType="VARCHAR" />
    <result column="DIRECT_SHIP_CUSTOMER_COUNTRY" property="directShipCustomerCountry" jdbcType="VARCHAR" />
    <result column="DIRECT_SHIP_CUSTOMER_NAME" property="directShipCustomerName" jdbcType="VARCHAR" />
    <result column="DIRECT_SHIP_BILL_TO_ID" property="directShipBillToId" jdbcType="VARCHAR" />
    <result column="SHIPMENT_NUMBER" property="shipmentNumber" jdbcType="VARCHAR" />
    <result column="WIP_DJ" property="wipDj" jdbcType="VARCHAR" />
    <result column="DELETE_FLAG" property="deleteFlag" jdbcType="VARCHAR" />
    <result column="ULTIMATE_DESTINATION_COUNTRY" property="ultimateDestinationCountry" jdbcType="VARCHAR" />
    <result column="CSSN" property="cssn" jdbcType="VARCHAR" />
    <result column="MIN" property="min" jdbcType="VARCHAR" />
    <result column="BILL_TO_ID" property="billToId" jdbcType="VARCHAR" />
    <result column="CURR_FLEX_VER" property="currFlexVer" jdbcType="VARCHAR" />
    <result column="CURR_FLASH_NAME" property="currFlashName" jdbcType="VARCHAR" />
    <result column="CURR_PRI_VER" property="currPriVer" jdbcType="VARCHAR" />
    <result column="LANG_PKG_ID" property="langPkgId" jdbcType="VARCHAR" />
    <result column="KJAVA_VER" property="kjavaVer" jdbcType="VARCHAR" />
    <result column="BOOTLOADER_VER" property="bootloaderVer" jdbcType="VARCHAR" />
    <result column="HARDWARE_VER" property="hardwareVer" jdbcType="VARCHAR" />
    <result column="FOTA_ENABLED" property="fotaEnabled" jdbcType="VARCHAR" />
    <result column="DUAL_SERIAL_NO" property="dualSerialNo" jdbcType="VARCHAR" />
    <result column="DUAL_SN_TYPE" property="dualSnType" jdbcType="VARCHAR" />
    <result column="POP_IN_SYSDATE" property="popInSysdate" jdbcType="VARCHAR" />
    <result column="POP_DATE" property="popDate" jdbcType="VARCHAR" />
    <result column="POP_IDENTIFIER" property="popIdentifier" jdbcType="VARCHAR" />
    <result column="LAST_REPAIR_DATE" property="lastRepairDate" jdbcType="VARCHAR" />
    <result column="REPAIR_COUNT" property="repairCount" jdbcType="VARCHAR" />
    <result column="WIMAX_MAC_ADDR" property="wimaxMacAddr" jdbcType="VARCHAR" />
    <result column="HSN" property="hsn" jdbcType="VARCHAR" />
    <result column="DSNMUC1" property="dsnmuc1" jdbcType="VARCHAR" />
    <result column="DSNMUC2" property="dsnmuc2" jdbcType="VARCHAR" />
    <result column="DSNMUC3" property="dsnmuc3" jdbcType="VARCHAR" />
    <result column="DSNOTUC" property="dsnotuc" jdbcType="VARCHAR" />
    <result column="DSNSERVICEPASS" property="dsnservicepass" jdbcType="VARCHAR" />
    <result column="DSNLOCK4" property="dsnlock4" jdbcType="VARCHAR" />
    <result column="DSNLOCK5" property="dsnlock5" jdbcType="VARCHAR" />
    <result column="DSN_AKEY_RANDOM" property="dsnAkeyRandom" jdbcType="VARCHAR" />
    <result column="DSN_AKEY_ZERO" property="dsnAkeyZero" jdbcType="VARCHAR" />
    <result column="DSN_AKEY2_TYPE" property="dsnAkey2Type" jdbcType="VARCHAR" />
    <result column="DSN_AKEY2" property="dsnAkey2" jdbcType="VARCHAR" />
    <result column="WLAN2" property="wlan2" jdbcType="VARCHAR" />
    <result column="WLAN3" property="wlan3" jdbcType="VARCHAR" />
    <result column="WLAN4" property="wlan4" jdbcType="VARCHAR" />
    <result column="TRI_SERIAL_NO" property="triSerialNo" jdbcType="VARCHAR" />
    <result column="TRI_SERIAL_NO_TYPE" property="triSerialNoType" jdbcType="VARCHAR" />
    <result column="TSNMUC1" property="tsnmuc1" jdbcType="VARCHAR" />
    <result column="TSNMUC2" property="tsnmuc2" jdbcType="VARCHAR" />
    <result column="TSNMUC3" property="tsnmuc3" jdbcType="VARCHAR" />
    <result column="TSNOTUC" property="tsnotuc" jdbcType="VARCHAR" />
    <result column="TSNSERVICEPASS" property="tsnservicepass" jdbcType="VARCHAR" />
    <result column="TSNLOCK4" property="tsnlock4" jdbcType="VARCHAR" />
    <result column="TSNLOCK5" property="tsnlock5" jdbcType="VARCHAR" />
    <result column="TSN_AKEY_RANDOM" property="tsnAkeyRandom" jdbcType="VARCHAR" />
    <result column="TSN_AKEY_ZERO" property="tsnAkeyZero" jdbcType="VARCHAR" />
    <result column="TSN_AKEY2_TYPE" property="tsnAkey2Type" jdbcType="VARCHAR" />
    <result column="TSN_AKEY2" property="tsnAkey2" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="batch_number" property="batchNumber" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdR12Outbound" >
    insert into ib_t_upd_r12_outbound (SERIAL_NO, SERIAL_NUMBER_TYPE, FACTORY_CODE, 
      GENERATION_DATE, APC, TRANSCEIVER_MODEL, 
      CUSTOMER_MODEL, MARKET_NAME, ITEM_CODE, 
      WARRANTY_CODE, SHIP_DATE, SHIP_TO_CUSTOMER_NUMBER, 
      SHIP_TO_CUSTOMER_ADDRESS_ID, SHIP_TO_CUSTOMER_NAME, 
      SHIP_TO_CITY, SHIP_TO_COUNTRY, SOLD_TO_CUSTOMER_NUMBER, 
      SOLD_TO_CUSTOMER_NAME, SOLD_DATE, TRACK_ID, 
      TA_NUMBER, CARTON_ID, PO_NUMBER, 
      SO_NUMBER, PALLET_ID, PRIMARY_UNLOCK_CODE, 
      PRIMECO_UNLOCK_CODE, VERIZON_UNLOCK_CODE, SECONDARY_UNLOCK_CODE, 
      SERVICE_PASSCODE, LOCK_4, LOCK_5, 
      A_KEY_RANDOM, A_KEY_ZERO, MSN, 
      BT, WLAN, BASE_PROCESSOR_ID, 
      FASTT_ID, LOCATION_TYPE, PACKING_LIST, 
      FAB_DATE, SOFTWARE_VERSION, ORGANIZATION_CODE, 
      FLEX_OPTION, ICC_ID, SO_LINE_NUMBER, 
      DIRECT_SHIP_REGION_CODE, DIRECT_SHIP_SO_NUMBER, 
      DIRECT_SHIP_PO_NUMBER, DIRECT_SHIP_CUSTOMER_NUMBER, 
      DIRECT_SHIP_SHIP_TO_ADDRESS_ID, DIRECT_SHIP_CUSTOMER_COUNTRY, 
      DIRECT_SHIP_CUSTOMER_NAME, DIRECT_SHIP_BILL_TO_ID, 
      SHIPMENT_NUMBER, WIP_DJ, DELETE_FLAG, 
      ULTIMATE_DESTINATION_COUNTRY, CSSN, MIN, 
      BILL_TO_ID, CURR_FLEX_VER, CURR_FLASH_NAME, 
      CURR_PRI_VER, LANG_PKG_ID, KJAVA_VER, 
      BOOTLOADER_VER, HARDWARE_VER, FOTA_ENABLED, 
      DUAL_SERIAL_NO, DUAL_SN_TYPE, POP_IN_SYSDATE, 
      POP_DATE, POP_IDENTIFIER, LAST_REPAIR_DATE, 
      REPAIR_COUNT, WIMAX_MAC_ADDR, HSN, 
      DSNMUC1, DSNMUC2, DSNMUC3, 
      DSNOTUC, DSNSERVICEPASS, DSNLOCK4, 
      DSNLOCK5, DSN_AKEY_RANDOM, DSN_AKEY_ZERO, 
      DSN_AKEY2_TYPE, DSN_AKEY2, WLAN2, 
      WLAN3, WLAN4, TRI_SERIAL_NO, 
      TRI_SERIAL_NO_TYPE, TSNMUC1, TSNMUC2, 
      TSNMUC3, TSNOTUC, TSNSERVICEPASS, 
      TSNLOCK4, TSNLOCK5, TSN_AKEY_RANDOM, 
      TSN_AKEY_ZERO, TSN_AKEY2_TYPE, TSN_AKEY2, 
      create_time, batch_number)
    values (#{serialNo,jdbcType=VARCHAR}, #{serialNumberType,jdbcType=VARCHAR}, #{factoryCode,jdbcType=VARCHAR}, 
      #{generationDate,jdbcType=TIMESTAMP}, #{apc,jdbcType=VARCHAR}, #{transceiverModel,jdbcType=VARCHAR}, 
      #{customerModel,jdbcType=VARCHAR}, #{marketName,jdbcType=VARCHAR}, #{itemCode,jdbcType=VARCHAR}, 
      #{warrantyCode,jdbcType=VARCHAR}, #{shipDate,jdbcType=TIMESTAMP}, #{shipToCustomerNumber,jdbcType=VARCHAR}, 
      #{shipToCustomerAddressId,jdbcType=VARCHAR}, #{shipToCustomerName,jdbcType=VARCHAR}, 
      #{shipToCity,jdbcType=VARCHAR}, #{shipToCountry,jdbcType=VARCHAR}, #{soldToCustomerNumber,jdbcType=VARCHAR}, 
      #{soldToCustomerName,jdbcType=VARCHAR}, #{soldDate,jdbcType=TIMESTAMP}, #{trackId,jdbcType=VARCHAR}, 
      #{taNumber,jdbcType=VARCHAR}, #{cartonId,jdbcType=VARCHAR}, #{poNumber,jdbcType=VARCHAR}, 
      #{soNumber,jdbcType=VARCHAR}, #{palletId,jdbcType=VARCHAR}, #{primaryUnlockCode,jdbcType=VARCHAR}, 
      #{primecoUnlockCode,jdbcType=VARCHAR}, #{verizonUnlockCode,jdbcType=VARCHAR}, #{secondaryUnlockCode,jdbcType=VARCHAR}, 
      #{servicePasscode,jdbcType=VARCHAR}, #{lock4,jdbcType=VARCHAR}, #{lock5,jdbcType=VARCHAR}, 
      #{aKeyRandom,jdbcType=VARCHAR}, #{aKeyZero,jdbcType=VARCHAR}, #{msn,jdbcType=VARCHAR}, 
      #{bt,jdbcType=VARCHAR}, #{wlan,jdbcType=VARCHAR}, #{baseProcessorId,jdbcType=VARCHAR}, 
      #{fasttId,jdbcType=VARCHAR}, #{locationType,jdbcType=VARCHAR}, #{packingList,jdbcType=VARCHAR}, 
      #{fabDate,jdbcType=VARCHAR}, #{softwareVersion,jdbcType=VARCHAR}, #{organizationCode,jdbcType=VARCHAR}, 
      #{flexOption,jdbcType=VARCHAR}, #{iccId,jdbcType=VARCHAR}, #{soLineNumber,jdbcType=VARCHAR}, 
      #{directShipRegionCode,jdbcType=VARCHAR}, #{directShipSoNumber,jdbcType=VARCHAR}, 
      #{directShipPoNumber,jdbcType=VARCHAR}, #{directShipCustomerNumber,jdbcType=VARCHAR}, 
      #{directShipShipToAddressId,jdbcType=VARCHAR}, #{directShipCustomerCountry,jdbcType=VARCHAR}, 
      #{directShipCustomerName,jdbcType=VARCHAR}, #{directShipBillToId,jdbcType=VARCHAR}, 
      #{shipmentNumber,jdbcType=VARCHAR}, #{wipDj,jdbcType=VARCHAR}, #{deleteFlag,jdbcType=VARCHAR}, 
      #{ultimateDestinationCountry,jdbcType=VARCHAR}, #{cssn,jdbcType=VARCHAR}, #{min,jdbcType=VARCHAR}, 
      #{billToId,jdbcType=VARCHAR}, #{currFlexVer,jdbcType=VARCHAR}, #{currFlashName,jdbcType=VARCHAR}, 
      #{currPriVer,jdbcType=VARCHAR}, #{langPkgId,jdbcType=VARCHAR}, #{kjavaVer,jdbcType=VARCHAR}, 
      #{bootloaderVer,jdbcType=VARCHAR}, #{hardwareVer,jdbcType=VARCHAR}, #{fotaEnabled,jdbcType=VARCHAR}, 
      #{dualSerialNo,jdbcType=VARCHAR}, #{dualSnType,jdbcType=VARCHAR}, #{popInSysdate,jdbcType=VARCHAR}, 
      #{popDate,jdbcType=VARCHAR}, #{popIdentifier,jdbcType=VARCHAR}, #{lastRepairDate,jdbcType=VARCHAR}, 
      #{repairCount,jdbcType=VARCHAR}, #{wimaxMacAddr,jdbcType=VARCHAR}, #{hsn,jdbcType=VARCHAR}, 
      #{dsnmuc1,jdbcType=VARCHAR}, #{dsnmuc2,jdbcType=VARCHAR}, #{dsnmuc3,jdbcType=VARCHAR}, 
      #{dsnotuc,jdbcType=VARCHAR}, #{dsnservicepass,jdbcType=VARCHAR}, #{dsnlock4,jdbcType=VARCHAR}, 
      #{dsnlock5,jdbcType=VARCHAR}, #{dsnAkeyRandom,jdbcType=VARCHAR}, #{dsnAkeyZero,jdbcType=VARCHAR}, 
      #{dsnAkey2Type,jdbcType=VARCHAR}, #{dsnAkey2,jdbcType=VARCHAR}, #{wlan2,jdbcType=VARCHAR}, 
      #{wlan3,jdbcType=VARCHAR}, #{wlan4,jdbcType=VARCHAR}, #{triSerialNo,jdbcType=VARCHAR}, 
      #{triSerialNoType,jdbcType=VARCHAR}, #{tsnmuc1,jdbcType=VARCHAR}, #{tsnmuc2,jdbcType=VARCHAR}, 
      #{tsnmuc3,jdbcType=VARCHAR}, #{tsnotuc,jdbcType=VARCHAR}, #{tsnservicepass,jdbcType=VARCHAR}, 
      #{tsnlock4,jdbcType=VARCHAR}, #{tsnlock5,jdbcType=VARCHAR}, #{tsnAkeyRandom,jdbcType=VARCHAR}, 
      #{tsnAkeyZero,jdbcType=VARCHAR}, #{tsnAkey2Type,jdbcType=VARCHAR}, #{tsnAkey2,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{batchNumber,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdR12Outbound" >
    insert into ib_t_upd_r12_outbound
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="serialNo != null" >
        SERIAL_NO,
      </if>
      <if test="serialNumberType != null" >
        SERIAL_NUMBER_TYPE,
      </if>
      <if test="factoryCode != null" >
        FACTORY_CODE,
      </if>
      <if test="generationDate != null" >
        GENERATION_DATE,
      </if>
      <if test="apc != null" >
        APC,
      </if>
      <if test="transceiverModel != null" >
        TRANSCEIVER_MODEL,
      </if>
      <if test="customerModel != null" >
        CUSTOMER_MODEL,
      </if>
      <if test="marketName != null" >
        MARKET_NAME,
      </if>
      <if test="itemCode != null" >
        ITEM_CODE,
      </if>
      <if test="warrantyCode != null" >
        WARRANTY_CODE,
      </if>
      <if test="shipDate != null" >
        SHIP_DATE,
      </if>
      <if test="shipToCustomerNumber != null" >
        SHIP_TO_CUSTOMER_NUMBER,
      </if>
      <if test="shipToCustomerAddressId != null" >
        SHIP_TO_CUSTOMER_ADDRESS_ID,
      </if>
      <if test="shipToCustomerName != null" >
        SHIP_TO_CUSTOMER_NAME,
      </if>
      <if test="shipToCity != null" >
        SHIP_TO_CITY,
      </if>
      <if test="shipToCountry != null" >
        SHIP_TO_COUNTRY,
      </if>
      <if test="soldToCustomerNumber != null" >
        SOLD_TO_CUSTOMER_NUMBER,
      </if>
      <if test="soldToCustomerName != null" >
        SOLD_TO_CUSTOMER_NAME,
      </if>
      <if test="soldDate != null" >
        SOLD_DATE,
      </if>
      <if test="trackId != null" >
        TRACK_ID,
      </if>
      <if test="taNumber != null" >
        TA_NUMBER,
      </if>
      <if test="cartonId != null" >
        CARTON_ID,
      </if>
      <if test="poNumber != null" >
        PO_NUMBER,
      </if>
      <if test="soNumber != null" >
        SO_NUMBER,
      </if>
      <if test="palletId != null" >
        PALLET_ID,
      </if>
      <if test="primaryUnlockCode != null" >
        PRIMARY_UNLOCK_CODE,
      </if>
      <if test="primecoUnlockCode != null" >
        PRIMECO_UNLOCK_CODE,
      </if>
      <if test="verizonUnlockCode != null" >
        VERIZON_UNLOCK_CODE,
      </if>
      <if test="secondaryUnlockCode != null" >
        SECONDARY_UNLOCK_CODE,
      </if>
      <if test="servicePasscode != null" >
        SERVICE_PASSCODE,
      </if>
      <if test="lock4 != null" >
        LOCK_4,
      </if>
      <if test="lock5 != null" >
        LOCK_5,
      </if>
      <if test="aKeyRandom != null" >
        A_KEY_RANDOM,
      </if>
      <if test="aKeyZero != null" >
        A_KEY_ZERO,
      </if>
      <if test="msn != null" >
        MSN,
      </if>
      <if test="bt != null" >
        BT,
      </if>
      <if test="wlan != null" >
        WLAN,
      </if>
      <if test="baseProcessorId != null" >
        BASE_PROCESSOR_ID,
      </if>
      <if test="fasttId != null" >
        FASTT_ID,
      </if>
      <if test="locationType != null" >
        LOCATION_TYPE,
      </if>
      <if test="packingList != null" >
        PACKING_LIST,
      </if>
      <if test="fabDate != null" >
        FAB_DATE,
      </if>
      <if test="softwareVersion != null" >
        SOFTWARE_VERSION,
      </if>
      <if test="organizationCode != null" >
        ORGANIZATION_CODE,
      </if>
      <if test="flexOption != null" >
        FLEX_OPTION,
      </if>
      <if test="iccId != null" >
        ICC_ID,
      </if>
      <if test="soLineNumber != null" >
        SO_LINE_NUMBER,
      </if>
      <if test="directShipRegionCode != null" >
        DIRECT_SHIP_REGION_CODE,
      </if>
      <if test="directShipSoNumber != null" >
        DIRECT_SHIP_SO_NUMBER,
      </if>
      <if test="directShipPoNumber != null" >
        DIRECT_SHIP_PO_NUMBER,
      </if>
      <if test="directShipCustomerNumber != null" >
        DIRECT_SHIP_CUSTOMER_NUMBER,
      </if>
      <if test="directShipShipToAddressId != null" >
        DIRECT_SHIP_SHIP_TO_ADDRESS_ID,
      </if>
      <if test="directShipCustomerCountry != null" >
        DIRECT_SHIP_CUSTOMER_COUNTRY,
      </if>
      <if test="directShipCustomerName != null" >
        DIRECT_SHIP_CUSTOMER_NAME,
      </if>
      <if test="directShipBillToId != null" >
        DIRECT_SHIP_BILL_TO_ID,
      </if>
      <if test="shipmentNumber != null" >
        SHIPMENT_NUMBER,
      </if>
      <if test="wipDj != null" >
        WIP_DJ,
      </if>
      <if test="deleteFlag != null" >
        DELETE_FLAG,
      </if>
      <if test="ultimateDestinationCountry != null" >
        ULTIMATE_DESTINATION_COUNTRY,
      </if>
      <if test="cssn != null" >
        CSSN,
      </if>
      <if test="min != null" >
        MIN,
      </if>
      <if test="billToId != null" >
        BILL_TO_ID,
      </if>
      <if test="currFlexVer != null" >
        CURR_FLEX_VER,
      </if>
      <if test="currFlashName != null" >
        CURR_FLASH_NAME,
      </if>
      <if test="currPriVer != null" >
        CURR_PRI_VER,
      </if>
      <if test="langPkgId != null" >
        LANG_PKG_ID,
      </if>
      <if test="kjavaVer != null" >
        KJAVA_VER,
      </if>
      <if test="bootloaderVer != null" >
        BOOTLOADER_VER,
      </if>
      <if test="hardwareVer != null" >
        HARDWARE_VER,
      </if>
      <if test="fotaEnabled != null" >
        FOTA_ENABLED,
      </if>
      <if test="dualSerialNo != null" >
        DUAL_SERIAL_NO,
      </if>
      <if test="dualSnType != null" >
        DUAL_SN_TYPE,
      </if>
      <if test="popInSysdate != null" >
        POP_IN_SYSDATE,
      </if>
      <if test="popDate != null" >
        POP_DATE,
      </if>
      <if test="popIdentifier != null" >
        POP_IDENTIFIER,
      </if>
      <if test="lastRepairDate != null" >
        LAST_REPAIR_DATE,
      </if>
      <if test="repairCount != null" >
        REPAIR_COUNT,
      </if>
      <if test="wimaxMacAddr != null" >
        WIMAX_MAC_ADDR,
      </if>
      <if test="hsn != null" >
        HSN,
      </if>
      <if test="dsnmuc1 != null" >
        DSNMUC1,
      </if>
      <if test="dsnmuc2 != null" >
        DSNMUC2,
      </if>
      <if test="dsnmuc3 != null" >
        DSNMUC3,
      </if>
      <if test="dsnotuc != null" >
        DSNOTUC,
      </if>
      <if test="dsnservicepass != null" >
        DSNSERVICEPASS,
      </if>
      <if test="dsnlock4 != null" >
        DSNLOCK4,
      </if>
      <if test="dsnlock5 != null" >
        DSNLOCK5,
      </if>
      <if test="dsnAkeyRandom != null" >
        DSN_AKEY_RANDOM,
      </if>
      <if test="dsnAkeyZero != null" >
        DSN_AKEY_ZERO,
      </if>
      <if test="dsnAkey2Type != null" >
        DSN_AKEY2_TYPE,
      </if>
      <if test="dsnAkey2 != null" >
        DSN_AKEY2,
      </if>
      <if test="wlan2 != null" >
        WLAN2,
      </if>
      <if test="wlan3 != null" >
        WLAN3,
      </if>
      <if test="wlan4 != null" >
        WLAN4,
      </if>
      <if test="triSerialNo != null" >
        TRI_SERIAL_NO,
      </if>
      <if test="triSerialNoType != null" >
        TRI_SERIAL_NO_TYPE,
      </if>
      <if test="tsnmuc1 != null" >
        TSNMUC1,
      </if>
      <if test="tsnmuc2 != null" >
        TSNMUC2,
      </if>
      <if test="tsnmuc3 != null" >
        TSNMUC3,
      </if>
      <if test="tsnotuc != null" >
        TSNOTUC,
      </if>
      <if test="tsnservicepass != null" >
        TSNSERVICEPASS,
      </if>
      <if test="tsnlock4 != null" >
        TSNLOCK4,
      </if>
      <if test="tsnlock5 != null" >
        TSNLOCK5,
      </if>
      <if test="tsnAkeyRandom != null" >
        TSN_AKEY_RANDOM,
      </if>
      <if test="tsnAkeyZero != null" >
        TSN_AKEY_ZERO,
      </if>
      <if test="tsnAkey2Type != null" >
        TSN_AKEY2_TYPE,
      </if>
      <if test="tsnAkey2 != null" >
        TSN_AKEY2,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="batchNumber != null" >
        batch_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="serialNo != null" >
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNumberType != null" >
        #{serialNumberType,jdbcType=VARCHAR},
      </if>
      <if test="factoryCode != null" >
        #{factoryCode,jdbcType=VARCHAR},
      </if>
      <if test="generationDate != null" >
        #{generationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="apc != null" >
        #{apc,jdbcType=VARCHAR},
      </if>
      <if test="transceiverModel != null" >
        #{transceiverModel,jdbcType=VARCHAR},
      </if>
      <if test="customerModel != null" >
        #{customerModel,jdbcType=VARCHAR},
      </if>
      <if test="marketName != null" >
        #{marketName,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null" >
        #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="warrantyCode != null" >
        #{warrantyCode,jdbcType=VARCHAR},
      </if>
      <if test="shipDate != null" >
        #{shipDate,jdbcType=TIMESTAMP},
      </if>
      <if test="shipToCustomerNumber != null" >
        #{shipToCustomerNumber,jdbcType=VARCHAR},
      </if>
      <if test="shipToCustomerAddressId != null" >
        #{shipToCustomerAddressId,jdbcType=VARCHAR},
      </if>
      <if test="shipToCustomerName != null" >
        #{shipToCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="shipToCity != null" >
        #{shipToCity,jdbcType=VARCHAR},
      </if>
      <if test="shipToCountry != null" >
        #{shipToCountry,jdbcType=VARCHAR},
      </if>
      <if test="soldToCustomerNumber != null" >
        #{soldToCustomerNumber,jdbcType=VARCHAR},
      </if>
      <if test="soldToCustomerName != null" >
        #{soldToCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="soldDate != null" >
        #{soldDate,jdbcType=TIMESTAMP},
      </if>
      <if test="trackId != null" >
        #{trackId,jdbcType=VARCHAR},
      </if>
      <if test="taNumber != null" >
        #{taNumber,jdbcType=VARCHAR},
      </if>
      <if test="cartonId != null" >
        #{cartonId,jdbcType=VARCHAR},
      </if>
      <if test="poNumber != null" >
        #{poNumber,jdbcType=VARCHAR},
      </if>
      <if test="soNumber != null" >
        #{soNumber,jdbcType=VARCHAR},
      </if>
      <if test="palletId != null" >
        #{palletId,jdbcType=VARCHAR},
      </if>
      <if test="primaryUnlockCode != null" >
        #{primaryUnlockCode,jdbcType=VARCHAR},
      </if>
      <if test="primecoUnlockCode != null" >
        #{primecoUnlockCode,jdbcType=VARCHAR},
      </if>
      <if test="verizonUnlockCode != null" >
        #{verizonUnlockCode,jdbcType=VARCHAR},
      </if>
      <if test="secondaryUnlockCode != null" >
        #{secondaryUnlockCode,jdbcType=VARCHAR},
      </if>
      <if test="servicePasscode != null" >
        #{servicePasscode,jdbcType=VARCHAR},
      </if>
      <if test="lock4 != null" >
        #{lock4,jdbcType=VARCHAR},
      </if>
      <if test="lock5 != null" >
        #{lock5,jdbcType=VARCHAR},
      </if>
      <if test="aKeyRandom != null" >
        #{aKeyRandom,jdbcType=VARCHAR},
      </if>
      <if test="aKeyZero != null" >
        #{aKeyZero,jdbcType=VARCHAR},
      </if>
      <if test="msn != null" >
        #{msn,jdbcType=VARCHAR},
      </if>
      <if test="bt != null" >
        #{bt,jdbcType=VARCHAR},
      </if>
      <if test="wlan != null" >
        #{wlan,jdbcType=VARCHAR},
      </if>
      <if test="baseProcessorId != null" >
        #{baseProcessorId,jdbcType=VARCHAR},
      </if>
      <if test="fasttId != null" >
        #{fasttId,jdbcType=VARCHAR},
      </if>
      <if test="locationType != null" >
        #{locationType,jdbcType=VARCHAR},
      </if>
      <if test="packingList != null" >
        #{packingList,jdbcType=VARCHAR},
      </if>
      <if test="fabDate != null" >
        #{fabDate,jdbcType=VARCHAR},
      </if>
      <if test="softwareVersion != null" >
        #{softwareVersion,jdbcType=VARCHAR},
      </if>
      <if test="organizationCode != null" >
        #{organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="flexOption != null" >
        #{flexOption,jdbcType=VARCHAR},
      </if>
      <if test="iccId != null" >
        #{iccId,jdbcType=VARCHAR},
      </if>
      <if test="soLineNumber != null" >
        #{soLineNumber,jdbcType=VARCHAR},
      </if>
      <if test="directShipRegionCode != null" >
        #{directShipRegionCode,jdbcType=VARCHAR},
      </if>
      <if test="directShipSoNumber != null" >
        #{directShipSoNumber,jdbcType=VARCHAR},
      </if>
      <if test="directShipPoNumber != null" >
        #{directShipPoNumber,jdbcType=VARCHAR},
      </if>
      <if test="directShipCustomerNumber != null" >
        #{directShipCustomerNumber,jdbcType=VARCHAR},
      </if>
      <if test="directShipShipToAddressId != null" >
        #{directShipShipToAddressId,jdbcType=VARCHAR},
      </if>
      <if test="directShipCustomerCountry != null" >
        #{directShipCustomerCountry,jdbcType=VARCHAR},
      </if>
      <if test="directShipCustomerName != null" >
        #{directShipCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="directShipBillToId != null" >
        #{directShipBillToId,jdbcType=VARCHAR},
      </if>
      <if test="shipmentNumber != null" >
        #{shipmentNumber,jdbcType=VARCHAR},
      </if>
      <if test="wipDj != null" >
        #{wipDj,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null" >
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="ultimateDestinationCountry != null" >
        #{ultimateDestinationCountry,jdbcType=VARCHAR},
      </if>
      <if test="cssn != null" >
        #{cssn,jdbcType=VARCHAR},
      </if>
      <if test="min != null" >
        #{min,jdbcType=VARCHAR},
      </if>
      <if test="billToId != null" >
        #{billToId,jdbcType=VARCHAR},
      </if>
      <if test="currFlexVer != null" >
        #{currFlexVer,jdbcType=VARCHAR},
      </if>
      <if test="currFlashName != null" >
        #{currFlashName,jdbcType=VARCHAR},
      </if>
      <if test="currPriVer != null" >
        #{currPriVer,jdbcType=VARCHAR},
      </if>
      <if test="langPkgId != null" >
        #{langPkgId,jdbcType=VARCHAR},
      </if>
      <if test="kjavaVer != null" >
        #{kjavaVer,jdbcType=VARCHAR},
      </if>
      <if test="bootloaderVer != null" >
        #{bootloaderVer,jdbcType=VARCHAR},
      </if>
      <if test="hardwareVer != null" >
        #{hardwareVer,jdbcType=VARCHAR},
      </if>
      <if test="fotaEnabled != null" >
        #{fotaEnabled,jdbcType=VARCHAR},
      </if>
      <if test="dualSerialNo != null" >
        #{dualSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="dualSnType != null" >
        #{dualSnType,jdbcType=VARCHAR},
      </if>
      <if test="popInSysdate != null" >
        #{popInSysdate,jdbcType=VARCHAR},
      </if>
      <if test="popDate != null" >
        #{popDate,jdbcType=VARCHAR},
      </if>
      <if test="popIdentifier != null" >
        #{popIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="lastRepairDate != null" >
        #{lastRepairDate,jdbcType=VARCHAR},
      </if>
      <if test="repairCount != null" >
        #{repairCount,jdbcType=VARCHAR},
      </if>
      <if test="wimaxMacAddr != null" >
        #{wimaxMacAddr,jdbcType=VARCHAR},
      </if>
      <if test="hsn != null" >
        #{hsn,jdbcType=VARCHAR},
      </if>
      <if test="dsnmuc1 != null" >
        #{dsnmuc1,jdbcType=VARCHAR},
      </if>
      <if test="dsnmuc2 != null" >
        #{dsnmuc2,jdbcType=VARCHAR},
      </if>
      <if test="dsnmuc3 != null" >
        #{dsnmuc3,jdbcType=VARCHAR},
      </if>
      <if test="dsnotuc != null" >
        #{dsnotuc,jdbcType=VARCHAR},
      </if>
      <if test="dsnservicepass != null" >
        #{dsnservicepass,jdbcType=VARCHAR},
      </if>
      <if test="dsnlock4 != null" >
        #{dsnlock4,jdbcType=VARCHAR},
      </if>
      <if test="dsnlock5 != null" >
        #{dsnlock5,jdbcType=VARCHAR},
      </if>
      <if test="dsnAkeyRandom != null" >
        #{dsnAkeyRandom,jdbcType=VARCHAR},
      </if>
      <if test="dsnAkeyZero != null" >
        #{dsnAkeyZero,jdbcType=VARCHAR},
      </if>
      <if test="dsnAkey2Type != null" >
        #{dsnAkey2Type,jdbcType=VARCHAR},
      </if>
      <if test="dsnAkey2 != null" >
        #{dsnAkey2,jdbcType=VARCHAR},
      </if>
      <if test="wlan2 != null" >
        #{wlan2,jdbcType=VARCHAR},
      </if>
      <if test="wlan3 != null" >
        #{wlan3,jdbcType=VARCHAR},
      </if>
      <if test="wlan4 != null" >
        #{wlan4,jdbcType=VARCHAR},
      </if>
      <if test="triSerialNo != null" >
        #{triSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="triSerialNoType != null" >
        #{triSerialNoType,jdbcType=VARCHAR},
      </if>
      <if test="tsnmuc1 != null" >
        #{tsnmuc1,jdbcType=VARCHAR},
      </if>
      <if test="tsnmuc2 != null" >
        #{tsnmuc2,jdbcType=VARCHAR},
      </if>
      <if test="tsnmuc3 != null" >
        #{tsnmuc3,jdbcType=VARCHAR},
      </if>
      <if test="tsnotuc != null" >
        #{tsnotuc,jdbcType=VARCHAR},
      </if>
      <if test="tsnservicepass != null" >
        #{tsnservicepass,jdbcType=VARCHAR},
      </if>
      <if test="tsnlock4 != null" >
        #{tsnlock4,jdbcType=VARCHAR},
      </if>
      <if test="tsnlock5 != null" >
        #{tsnlock5,jdbcType=VARCHAR},
      </if>
      <if test="tsnAkeyRandom != null" >
        #{tsnAkeyRandom,jdbcType=VARCHAR},
      </if>
      <if test="tsnAkeyZero != null" >
        #{tsnAkeyZero,jdbcType=VARCHAR},
      </if>
      <if test="tsnAkey2Type != null" >
        #{tsnAkey2Type,jdbcType=VARCHAR},
      </if>
      <if test="tsnAkey2 != null" >
        #{tsnAkey2,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNumber != null" >
        #{batchNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>