/*     */ package WEB-INF.classes.com.lenovo.iqs.googlerpk.service.impl;
/*     */ 
/*     */ import com.alibaba.druid.util.StringUtils;
/*     */ import com.lenovo.iqs.googlerpk.bean.GoogleRPKRequest;
/*     */ import com.lenovo.iqs.googlerpk.bean.GoogleRPKResponse;
/*     */ import com.lenovo.iqs.googlerpk.service.GoogleRPKService;
/*     */ import com.lenovo.iqs.utils.HttpClientUtils;
/*     */ import com.lenovo.iqs.ws.upd.warranty.util.SerialValidateTAC;
/*     */ import org.apache.logging.log4j.LogManager;
/*     */ import org.apache.logging.log4j.Logger;
/*     */ import org.springframework.beans.factory.annotation.Value;
/*     */ import org.springframework.stereotype.Service;
/*     */ 
/*     */ 
/*     */ 
/*     */ @Service
/*     */ public class GoogleRPKServiceImpl
/*     */   implements GoogleRPKService
/*     */ {
/*  20 */   private static final Logger log = LogManager.getLogger(com.lenovo.iqs.googlerpk.service.impl.GoogleRPKServiceImpl.class);
/*     */   
/*     */   @Value("${GPS_URL}")
/*     */   private String gpsUrl;
/*     */   
/*     */   @Value("${GPS_USER}")
/*     */   private String authUser;
/*     */   
/*     */   @Value("${GPS_PWD}")
/*     */   private String authPwd;
/*  30 */   private String rsuSoapXml = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:rsu=\"http://rsu.programmingservice.cfc.nextest.globaltest.motorolamobility.com/\">   <soapenv:Header/>   <soapenv:Body>      <rsu:SaveRpkDataForService>         <trackid>{trackId}</trackid>\t\t  <googlecsr>{googlecsr}</googlecsr>      </rsu:SaveRpkDataForService>   </soapenv:Body></soapenv:Envelope>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String buildParams(GoogleRPKRequest googleRPKRequest) {
/*  42 */     return this.rsuSoapXml.replace("{trackId}", googleRPKRequest.getTrack_id())
/*  43 */       .replace("{googlecsr}", googleRPKRequest.getGoogleCsr());
/*     */   }
/*     */ 
/*     */   
/*     */   public GoogleRPKResponse processGoogleRPKRequest(GoogleRPKRequest request) throws Exception {
/*  48 */     GoogleRPKResponse googleRPKResponse = validateRequestParam(request);
/*  49 */     if (googleRPKResponse != null) {
/*  50 */       return googleRPKResponse;
/*     */     }
/*  52 */     String soapGoogleRPKParam = buildParams(request);
/*  53 */     log.debug("Request to send GPS : " + soapGoogleRPKParam);
/*  54 */     String response = HttpClientUtils.postWebservice(this.gpsUrl, soapGoogleRPKParam, "", this.authUser, this.authPwd);
/*  55 */     log.debug("Response from GPS : " + response);
/*  56 */     googleRPKResponse = GoogleRPKResponse.build(response);
/*  57 */     log.debug("Response sent to user : " + googleRPKResponse.toString());
/*  58 */     return googleRPKResponse;
/*     */   }
/*     */   
/*     */   private GoogleRPKResponse validateRequestParam(GoogleRPKRequest request) {
/*  62 */     GoogleRPKResponse response = null;
/*     */     
/*  64 */     String serialNumber = request.getSerialNo();
/*  65 */     if (StringUtils.isEmpty(serialNumber)) {
/*  66 */       response = new GoogleRPKResponse();
/*  67 */       response.setResponseCode("5070");
/*  68 */       response.setResponseMsg("Invalid serialNumber");
/*  69 */       return response;
/*  70 */     }  if (StringUtils.isEmpty(SerialValidateTAC.getSerialNoType(serialNumber))) {
/*  71 */       response = new GoogleRPKResponse();
/*  72 */       response.setResponseCode("5070");
/*  73 */       response.setResponseMsg("Invalid serialNumber");
/*     */     } 
/*     */     
/*  76 */     String rsdUser = request.getRsdUser();
/*  77 */     if (StringUtils.isEmpty(rsdUser)) {
/*  78 */       response = new GoogleRPKResponse();
/*  79 */       response.setResponseCode("5101");
/*  80 */       response.setResponseMsg("RSD User ID in the request is either empty or invalid.");
/*  81 */       return response;
/*     */     } 
/*     */     
/*  84 */     String mascId = request.getMascId();
/*  85 */     if (StringUtils.isEmpty(mascId)) {
/*  86 */       response = new GoogleRPKResponse();
/*  87 */       response.setResponseCode("5102");
/*  88 */       response.setResponseMsg("MASC ID in the request is either empty or invalid.");
/*  89 */       return response;
/*     */     } 
/*     */     
/*  92 */     String trackId = request.getTrack_id();
/*  93 */     if (StringUtils.isEmpty(trackId)) {
/*  94 */       response = new GoogleRPKResponse();
/*  95 */       response.setResponseCode("5103");
/*  96 */       response.setResponseMsg("Track ID in the request is either empty or invalid.");
/*  97 */       return response;
/*     */     } 
/*     */ 
/*     */     
/* 101 */     String googleCsr = request.getGoogleCsr();
/* 102 */     if (StringUtils.isEmpty(googleCsr)) {
/* 103 */       response = new GoogleRPKResponse();
/* 104 */       response.setResponseCode("5104");
/* 105 */       response.setResponseMsg("Google CSR in the request is either empty or invalid.");
/* 106 */       return response;
/*     */     } 
/*     */     
/* 109 */     return response;
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\googlerpk\service\impl\GoogleRPKServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */