<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenovo.iqs.dao.IbTImeiSnMbMapper">
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTImeiSnMb">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="imei_code" jdbcType="VARCHAR" property="imeiCode" />
    <result column="serial_number" jdbcType="VARCHAR" property="serialNumber" />
    <result column="parts_type" jdbcType="VARCHAR" property="partsType" />
    <result column="imei_send_datetime" jdbcType="TIMESTAMP" property="imeiSendDatetime" />
    <result column="used_flag" jdbcType="VARCHAR" property="usedFlag" />
    <result column="last_change" jdbcType="TIMESTAMP" property="lastChange" />
  </resultMap>
  <sql id="Base_Column_List">
    id, imei_code, serial_number, parts_type, imei_send_datetime, used_flag, last_change
  </sql>
  <select id="selectByImei" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ib_t_imei_sn_mb
    <where>
    	<if test="imeiCode != null and imeiCode != ''" >
	        and imei_code = #{imeiCode,jdbcType=VARCHAR}
    	</if>
    	<if test="(imeiCode == null or imeiCode == '')" >
        	1 = 2
    	</if>
    </where>
  </select>
  <select id="selectBySn" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ib_t_imei_sn_mb
    <where>
    	<if test="serialNumber != null and serialNumber != ''" >
        	and serial_number = #{serialNumber,jdbcType=VARCHAR}
    	</if>
    	<if test="(serialNumber == null or serialNumber == '')" >
        	1 = 2
    	</if>
    </where>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ib_t_imei_sn_mb
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from ib_t_imei_sn_mb
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTImeiSnMb">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_imei_sn_mb (imei_code, serial_number, parts_type, 
      imei_send_datetime, used_flag, last_change
      )
    values (#{imeiCode,jdbcType=VARCHAR}, #{serialNumber,jdbcType=VARCHAR}, #{partsType,jdbcType=VARCHAR}, 
      #{imeiSendDatetime,jdbcType=TIMESTAMP}, #{usedFlag,jdbcType=VARCHAR}, #{lastChange,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTImeiSnMb">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_imei_sn_mb
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="imeiCode != null">
        imei_code,
      </if>
      <if test="serialNumber != null">
        serial_number,
      </if>
      <if test="partsType != null">
        parts_type,
      </if>
      <if test="imeiSendDatetime != null">
        imei_send_datetime,
      </if>
      <if test="usedFlag != null">
        used_flag,
      </if>
      <if test="lastChange != null">
        last_change,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="imeiCode != null">
        #{imeiCode,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="partsType != null">
        #{partsType,jdbcType=VARCHAR},
      </if>
      <if test="imeiSendDatetime != null">
        #{imeiSendDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="usedFlag != null">
        #{usedFlag,jdbcType=VARCHAR},
      </if>
      <if test="lastChange != null">
        #{lastChange,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTImeiSnMb">
    update ib_t_imei_sn_mb
    <set>
      <if test="imeiCode != null">
        imei_code = #{imeiCode,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        serial_number = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="partsType != null">
        parts_type = #{partsType,jdbcType=VARCHAR},
      </if>
      <if test="imeiSendDatetime != null">
        imei_send_datetime = #{imeiSendDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="usedFlag != null">
        used_flag = #{usedFlag,jdbcType=VARCHAR},
      </if>
      <if test="lastChange != null">
        last_change = #{lastChange,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTImeiSnMb">
    update ib_t_imei_sn_mb
    set imei_code = #{imeiCode,jdbcType=VARCHAR},
      serial_number = #{serialNumber,jdbcType=VARCHAR},
      parts_type = #{partsType,jdbcType=VARCHAR},
      imei_send_datetime = #{imeiSendDatetime,jdbcType=TIMESTAMP},
      used_flag = #{usedFlag,jdbcType=VARCHAR},
      last_change = #{lastChange,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>