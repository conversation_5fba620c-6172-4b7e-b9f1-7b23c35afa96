/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.bean;
/*    */ import com.lenovo.iqs.datablocksign.bean.PkiNodeLockingResponse;
/*    */ 
/*    */ public class PkiNLHTTPSMessage {
/*    */   int status_code;
/*    */   
/*    */   public int getStatus_code() {
/*  8 */     return this.status_code;
/*    */   }
/*    */   PkiNodeLockingResponse response;
/*    */   public void setStatus_code(int status_code) {
/* 12 */     this.status_code = status_code;
/*    */   }
/*    */   
/*    */   public PkiNodeLockingResponse getResponse() {
/* 16 */     return this.response;
/*    */   }
/*    */   
/*    */   public void setResponse(PkiNodeLockingResponse response) {
/* 20 */     this.response = response;
/*    */   }
/*    */   
/*    */   public boolean isRequestSuccessful() {
/* 24 */     return (this.status_code == 200);
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\bean\PkiNLHTTPSMessage.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */