/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTUpdIdenAttributes implements Serializable { private String imeiNumber; private String serialNumber; private Integer orderNumber; private Integer lineNumber; private String model; private String simNumber; private String tanapa; private String tanapaSuffix; private String firmwareVersion; private String cdmaEsn;
/*    */   private String cdmaPesn;
/*    */   private String msl;
/*    */   private String otksl;
/*    */   
/*  7 */   public void setImeiNumber(String imeiNumber) { this.imeiNumber = imeiNumber; } private String authKey; private String servicePasscode; private String lock4; private String lock5; private String edfPartNbr; private String apc; private String bluetoothAddress; private String regionId; private String systemId; private String createdBy; private Date createdDate; private String modifiedBy; private Date modifiedDate; private static final long serialVersionUID = 1L; public void setSerialNumber(String serialNumber) { this.serialNumber = serialNumber; } public void setOrderNumber(Integer orderNumber) { this.orderNumber = orderNumber; } public void setLineNumber(Integer lineNumber) { this.lineNumber = lineNumber; } public void setModel(String model) { this.model = model; } public void setSimNumber(String simNumber) { this.simNumber = simNumber; } public void setTanapa(String tanapa) { this.tanapa = tanapa; } public void setTanapaSuffix(String tanapaSuffix) { this.tanapaSuffix = tanapaSuffix; } public void setFirmwareVersion(String firmwareVersion) { this.firmwareVersion = firmwareVersion; } public void setCdmaEsn(String cdmaEsn) { this.cdmaEsn = cdmaEsn; } public void setCdmaPesn(String cdmaPesn) { this.cdmaPesn = cdmaPesn; } public void setMsl(String msl) { this.msl = msl; } public void setOtksl(String otksl) { this.otksl = otksl; } public void setAuthKey(String authKey) { this.authKey = authKey; } public void setServicePasscode(String servicePasscode) { this.servicePasscode = servicePasscode; } public void setLock4(String lock4) { this.lock4 = lock4; } public void setLock5(String lock5) { this.lock5 = lock5; } public void setEdfPartNbr(String edfPartNbr) { this.edfPartNbr = edfPartNbr; } public void setApc(String apc) { this.apc = apc; } public void setBluetoothAddress(String bluetoothAddress) { this.bluetoothAddress = bluetoothAddress; } public void setRegionId(String regionId) { this.regionId = regionId; } public void setSystemId(String systemId) { this.systemId = systemId; } public void setCreatedBy(String createdBy) { this.createdBy = createdBy; } public void setCreatedDate(Date createdDate) { this.createdDate = createdDate; } public void setModifiedBy(String modifiedBy) { this.modifiedBy = modifiedBy; } public void setModifiedDate(Date modifiedDate) { this.modifiedDate = modifiedDate; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdIdenAttributes)) return false;  com.lenovo.iqs.entity.IbTUpdIdenAttributes other = (com.lenovo.iqs.entity.IbTUpdIdenAttributes)o; if (!other.canEqual(this)) return false;  Object this$imeiNumber = getImeiNumber(), other$imeiNumber = other.getImeiNumber(); if ((this$imeiNumber == null) ? (other$imeiNumber != null) : !this$imeiNumber.equals(other$imeiNumber)) return false;  Object this$serialNumber = getSerialNumber(), other$serialNumber = other.getSerialNumber(); if ((this$serialNumber == null) ? (other$serialNumber != null) : !this$serialNumber.equals(other$serialNumber)) return false;  Object this$orderNumber = getOrderNumber(), other$orderNumber = other.getOrderNumber(); if ((this$orderNumber == null) ? (other$orderNumber != null) : !this$orderNumber.equals(other$orderNumber)) return false;  Object this$lineNumber = getLineNumber(), other$lineNumber = other.getLineNumber(); if ((this$lineNumber == null) ? (other$lineNumber != null) : !this$lineNumber.equals(other$lineNumber)) return false;  Object this$model = getModel(), other$model = other.getModel(); if ((this$model == null) ? (other$model != null) : !this$model.equals(other$model)) return false;  Object this$simNumber = getSimNumber(), other$simNumber = other.getSimNumber(); if ((this$simNumber == null) ? (other$simNumber != null) : !this$simNumber.equals(other$simNumber)) return false;  Object this$tanapa = getTanapa(), other$tanapa = other.getTanapa(); if ((this$tanapa == null) ? (other$tanapa != null) : !this$tanapa.equals(other$tanapa)) return false;  Object this$tanapaSuffix = getTanapaSuffix(), other$tanapaSuffix = other.getTanapaSuffix(); if ((this$tanapaSuffix == null) ? (other$tanapaSuffix != null) : !this$tanapaSuffix.equals(other$tanapaSuffix)) return false;  Object this$firmwareVersion = getFirmwareVersion(), other$firmwareVersion = other.getFirmwareVersion(); if ((this$firmwareVersion == null) ? (other$firmwareVersion != null) : !this$firmwareVersion.equals(other$firmwareVersion)) return false;  Object this$cdmaEsn = getCdmaEsn(), other$cdmaEsn = other.getCdmaEsn(); if ((this$cdmaEsn == null) ? (other$cdmaEsn != null) : !this$cdmaEsn.equals(other$cdmaEsn)) return false;  Object this$cdmaPesn = getCdmaPesn(), other$cdmaPesn = other.getCdmaPesn(); if ((this$cdmaPesn == null) ? (other$cdmaPesn != null) : !this$cdmaPesn.equals(other$cdmaPesn)) return false;  Object this$msl = getMsl(), other$msl = other.getMsl(); if ((this$msl == null) ? (other$msl != null) : !this$msl.equals(other$msl)) return false;  Object this$otksl = getOtksl(), other$otksl = other.getOtksl(); if ((this$otksl == null) ? (other$otksl != null) : !this$otksl.equals(other$otksl)) return false;  Object this$authKey = getAuthKey(), other$authKey = other.getAuthKey(); if ((this$authKey == null) ? (other$authKey != null) : !this$authKey.equals(other$authKey)) return false;  Object this$servicePasscode = getServicePasscode(), other$servicePasscode = other.getServicePasscode(); if ((this$servicePasscode == null) ? (other$servicePasscode != null) : !this$servicePasscode.equals(other$servicePasscode)) return false;  Object this$lock4 = getLock4(), other$lock4 = other.getLock4(); if ((this$lock4 == null) ? (other$lock4 != null) : !this$lock4.equals(other$lock4)) return false;  Object this$lock5 = getLock5(), other$lock5 = other.getLock5(); if ((this$lock5 == null) ? (other$lock5 != null) : !this$lock5.equals(other$lock5)) return false;  Object this$edfPartNbr = getEdfPartNbr(), other$edfPartNbr = other.getEdfPartNbr(); if ((this$edfPartNbr == null) ? (other$edfPartNbr != null) : !this$edfPartNbr.equals(other$edfPartNbr)) return false;  Object this$apc = getApc(), other$apc = other.getApc(); if ((this$apc == null) ? (other$apc != null) : !this$apc.equals(other$apc)) return false;  Object this$bluetoothAddress = getBluetoothAddress(), other$bluetoothAddress = other.getBluetoothAddress(); if ((this$bluetoothAddress == null) ? (other$bluetoothAddress != null) : !this$bluetoothAddress.equals(other$bluetoothAddress)) return false;  Object this$regionId = getRegionId(), other$regionId = other.getRegionId(); if ((this$regionId == null) ? (other$regionId != null) : !this$regionId.equals(other$regionId)) return false;  Object this$systemId = getSystemId(), other$systemId = other.getSystemId(); if ((this$systemId == null) ? (other$systemId != null) : !this$systemId.equals(other$systemId)) return false;  Object this$createdBy = getCreatedBy(), other$createdBy = other.getCreatedBy(); if ((this$createdBy == null) ? (other$createdBy != null) : !this$createdBy.equals(other$createdBy)) return false;  Object this$createdDate = getCreatedDate(), other$createdDate = other.getCreatedDate(); if ((this$createdDate == null) ? (other$createdDate != null) : !this$createdDate.equals(other$createdDate)) return false;  Object this$modifiedBy = getModifiedBy(), other$modifiedBy = other.getModifiedBy(); if ((this$modifiedBy == null) ? (other$modifiedBy != null) : !this$modifiedBy.equals(other$modifiedBy)) return false;  Object this$modifiedDate = getModifiedDate(), other$modifiedDate = other.getModifiedDate(); return !((this$modifiedDate == null) ? (other$modifiedDate != null) : !this$modifiedDate.equals(other$modifiedDate)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdIdenAttributes; } public int hashCode() { int PRIME = 59; result = 1; Object $imeiNumber = getImeiNumber(); result = result * 59 + (($imeiNumber == null) ? 43 : $imeiNumber.hashCode()); Object $serialNumber = getSerialNumber(); result = result * 59 + (($serialNumber == null) ? 43 : $serialNumber.hashCode()); Object $orderNumber = getOrderNumber(); result = result * 59 + (($orderNumber == null) ? 43 : $orderNumber.hashCode()); Object $lineNumber = getLineNumber(); result = result * 59 + (($lineNumber == null) ? 43 : $lineNumber.hashCode()); Object $model = getModel(); result = result * 59 + (($model == null) ? 43 : $model.hashCode()); Object $simNumber = getSimNumber(); result = result * 59 + (($simNumber == null) ? 43 : $simNumber.hashCode()); Object $tanapa = getTanapa(); result = result * 59 + (($tanapa == null) ? 43 : $tanapa.hashCode()); Object $tanapaSuffix = getTanapaSuffix(); result = result * 59 + (($tanapaSuffix == null) ? 43 : $tanapaSuffix.hashCode()); Object $firmwareVersion = getFirmwareVersion(); result = result * 59 + (($firmwareVersion == null) ? 43 : $firmwareVersion.hashCode()); Object $cdmaEsn = getCdmaEsn(); result = result * 59 + (($cdmaEsn == null) ? 43 : $cdmaEsn.hashCode()); Object $cdmaPesn = getCdmaPesn(); result = result * 59 + (($cdmaPesn == null) ? 43 : $cdmaPesn.hashCode()); Object $msl = getMsl(); result = result * 59 + (($msl == null) ? 43 : $msl.hashCode()); Object $otksl = getOtksl(); result = result * 59 + (($otksl == null) ? 43 : $otksl.hashCode()); Object $authKey = getAuthKey(); result = result * 59 + (($authKey == null) ? 43 : $authKey.hashCode()); Object $servicePasscode = getServicePasscode(); result = result * 59 + (($servicePasscode == null) ? 43 : $servicePasscode.hashCode()); Object $lock4 = getLock4(); result = result * 59 + (($lock4 == null) ? 43 : $lock4.hashCode()); Object $lock5 = getLock5(); result = result * 59 + (($lock5 == null) ? 43 : $lock5.hashCode()); Object $edfPartNbr = getEdfPartNbr(); result = result * 59 + (($edfPartNbr == null) ? 43 : $edfPartNbr.hashCode()); Object $apc = getApc(); result = result * 59 + (($apc == null) ? 43 : $apc.hashCode()); Object $bluetoothAddress = getBluetoothAddress(); result = result * 59 + (($bluetoothAddress == null) ? 43 : $bluetoothAddress.hashCode()); Object $regionId = getRegionId(); result = result * 59 + (($regionId == null) ? 43 : $regionId.hashCode()); Object $systemId = getSystemId(); result = result * 59 + (($systemId == null) ? 43 : $systemId.hashCode()); Object $createdBy = getCreatedBy(); result = result * 59 + (($createdBy == null) ? 43 : $createdBy.hashCode()); Object $createdDate = getCreatedDate(); result = result * 59 + (($createdDate == null) ? 43 : $createdDate.hashCode()); Object $modifiedBy = getModifiedBy(); result = result * 59 + (($modifiedBy == null) ? 43 : $modifiedBy.hashCode()); Object $modifiedDate = getModifiedDate(); return result * 59 + (($modifiedDate == null) ? 43 : $modifiedDate.hashCode()); } public String toString() { return "IbTUpdIdenAttributes(imeiNumber=" + getImeiNumber() + ", serialNumber=" + getSerialNumber() + ", orderNumber=" + getOrderNumber() + ", lineNumber=" + getLineNumber() + ", model=" + getModel() + ", simNumber=" + getSimNumber() + ", tanapa=" + getTanapa() + ", tanapaSuffix=" + getTanapaSuffix() + ", firmwareVersion=" + getFirmwareVersion() + ", cdmaEsn=" + getCdmaEsn() + ", cdmaPesn=" + getCdmaPesn() + ", msl=" + getMsl() + ", otksl=" + getOtksl() + ", authKey=" + getAuthKey() + ", servicePasscode=" + getServicePasscode() + ", lock4=" + getLock4() + ", lock5=" + getLock5() + ", edfPartNbr=" + getEdfPartNbr() + ", apc=" + getApc() + ", bluetoothAddress=" + getBluetoothAddress() + ", regionId=" + getRegionId() + ", systemId=" + getSystemId() + ", createdBy=" + getCreatedBy() + ", createdDate=" + getCreatedDate() + ", modifiedBy=" + getModifiedBy() + ", modifiedDate=" + getModifiedDate() + ")"; }
/*    */    public String getImeiNumber() {
/*  9 */     return this.imeiNumber;
/*    */   } public String getSerialNumber() {
/* 11 */     return this.serialNumber;
/*    */   } public Integer getOrderNumber() {
/* 13 */     return this.orderNumber;
/*    */   } public Integer getLineNumber() {
/* 15 */     return this.lineNumber;
/*    */   } public String getModel() {
/* 17 */     return this.model;
/*    */   } public String getSimNumber() {
/* 19 */     return this.simNumber;
/*    */   } public String getTanapa() {
/* 21 */     return this.tanapa;
/*    */   } public String getTanapaSuffix() {
/* 23 */     return this.tanapaSuffix;
/*    */   } public String getFirmwareVersion() {
/* 25 */     return this.firmwareVersion;
/*    */   } public String getCdmaEsn() {
/* 27 */     return this.cdmaEsn;
/*    */   } public String getCdmaPesn() {
/* 29 */     return this.cdmaPesn;
/*    */   } public String getMsl() {
/* 31 */     return this.msl;
/*    */   } public String getOtksl() {
/* 33 */     return this.otksl;
/*    */   } public String getAuthKey() {
/* 35 */     return this.authKey;
/*    */   } public String getServicePasscode() {
/* 37 */     return this.servicePasscode;
/*    */   } public String getLock4() {
/* 39 */     return this.lock4;
/*    */   } public String getLock5() {
/* 41 */     return this.lock5;
/*    */   } public String getEdfPartNbr() {
/* 43 */     return this.edfPartNbr;
/*    */   } public String getApc() {
/* 45 */     return this.apc;
/*    */   } public String getBluetoothAddress() {
/* 47 */     return this.bluetoothAddress;
/*    */   } public String getRegionId() {
/* 49 */     return this.regionId;
/*    */   } public String getSystemId() {
/* 51 */     return this.systemId;
/*    */   } public String getCreatedBy() {
/* 53 */     return this.createdBy;
/*    */   } public Date getCreatedDate() {
/* 55 */     return this.createdDate;
/*    */   } public String getModifiedBy() {
/* 57 */     return this.modifiedBy;
/*    */   } public Date getModifiedDate() {
/* 59 */     return this.modifiedDate;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdIdenAttributes.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */