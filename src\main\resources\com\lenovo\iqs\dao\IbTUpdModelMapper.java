package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTUpdModel;
import com.lenovo.iqs.entity.IbTUpdModelKey;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTUpdModelMapper {
  int deleteByPrimaryKey(IbTUpdModelKey paramIbTUpdModelKey);
  
  int insert(IbTUpdModel paramIbTUpdModel);
  
  IbTUpdModel selectByPrimaryKey(IbTUpdModelKey paramIbTUpdModelKey);
  
  int updateByPrimaryKey(IbTUpdModel paramIbTUpdModel);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTUpdModelMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */