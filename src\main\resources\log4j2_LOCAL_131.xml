<?xml version="1.0" encoding="UTF-8"?>
<!--设置log4j2的自身log级别为warn -->
<!--<configuration status="warn">-->
<configuration status="debug">
	<appenders>
		<console name="Console" target="SYSTEM_OUT">
			<PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss:SSS} [%t] [%-5level] - %l - %m%n" />
		</console>

		<RollingFile name="RollingFileInfo" fileName="${sys:user.home}/logs/hpaasvc/info.log"
					 filePattern="${sys:user.home}/logs/hpaasvc/$${date:yyyy-MM}/info-%d{yyyy-MM-dd}-%i.log">
			<Filters>
				<ThresholdFilter level="INFO" />
				<ThresholdFilter level="WARN" onMatch="DENY" onMismatch="NEUTRAL" />
			</Filters>
			<PatternLayout pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n" />
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="100 MB" />
			</Policies>
		</RollingFile>

		<RollingFile name="RollingFileWarn" fileName="${sys:user.home}/logs/hpaasvc/warn.log"
					 filePattern="${sys:user.home}/logs/hpaasvc/$${date:yyyy-MM}/warn-%d{yyyy-MM-dd}-%i.log">
			<Filters>
				<ThresholdFilter level="WARN" />
				<ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL" />
			</Filters>
			<PatternLayout pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n" />
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="100 MB" />
			</Policies>
		</RollingFile>

		<RollingFile name="RollingFileError" fileName="${sys:user.home}/logs/hpaasvc/error.log"
					 filePattern="${sys:user.home}/logs/hpaasvc/$${date:yyyy-MM}/error-%d{yyyy-MM-dd}-%i.log">
			<ThresholdFilter level="ERROR" />
			<PatternLayout pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n" />
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="100 MB" />
			</Policies>
		</RollingFile>

		<RollingFile name="RollingFileDebug" fileName="${sys:user.home}/logs/hpaasvc/debug.log"
					 filePattern="${sys:user.home}/logs/hpaasvc/$${date:yyyy-MM}/debug-%d{yyyy-MM-dd}-%i.log">
			<ThresholdFilter level="DEBUG" />
			<PatternLayout pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n" />
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="100 MB" />
			</Policies>
		</RollingFile>
	</appenders>

	<loggers>
		<!--过滤掉spring和mybatis的一些无用的debug信息 -->
		<logger name="org.springframework" level="WARN" />
		<logger name="org.apache.ibatis" level="WARN" />

		<root level="DEBUG">
			<appender-ref ref="Console" />
			<appender-ref ref="RollingFileInfo" />
			<appender-ref ref="RollingFileWarn" />
			<appender-ref ref="RollingFileError" />
			<appender-ref ref="RollingFileDebug" />
		</root>
	</loggers>
</configuration>