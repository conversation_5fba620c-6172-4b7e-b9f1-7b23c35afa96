/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.bean;
/*    */ 
/*    */ import com.fasterxml.jackson.annotation.JsonProperty;
/*    */ 
/*    */ public class PkiNodeLockingResponse {
/*    */   @JsonProperty("tnlDbsResponse")
/*    */   private TnlDbsResponse tnlDbsResponse;
/*    */   
/*    */   public String toString() {
/* 10 */     return "PkiNodeLockingResponse(tnlDbsResponse=" + getTnlDbsResponse() + ")"; } public int hashCode() { int PRIME = 59; result = 1; Object $tnlDbsResponse = getTnlDbsResponse(); return result * 59 + (($tnlDbsResponse == null) ? 43 : $tnlDbsResponse.hashCode()); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.datablocksign.bean.PkiNodeLockingResponse; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.datablocksign.bean.PkiNodeLockingResponse)) return false;  com.lenovo.iqs.datablocksign.bean.PkiNodeLockingResponse other = (com.lenovo.iqs.datablocksign.bean.PkiNodeLockingResponse)o; if (!other.canEqual(this)) return false;  Object this$tnlDbsResponse = getTnlDbsResponse(), other$tnlDbsResponse = other.getTnlDbsResponse(); return !((this$tnlDbsResponse == null) ? (other$tnlDbsResponse != null) : !this$tnlDbsResponse.equals(other$tnlDbsResponse)); } public void setTnlDbsResponse(TnlDbsResponse tnlDbsResponse) { this.tnlDbsResponse = tnlDbsResponse; }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public TnlDbsResponse getTnlDbsResponse() {
/* 22 */     return this.tnlDbsResponse;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\bean\PkiNodeLockingResponse.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */