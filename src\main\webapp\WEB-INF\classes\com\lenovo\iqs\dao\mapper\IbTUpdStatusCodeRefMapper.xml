<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdStatusCodeRefMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdStatusCodeRef" >
    <id column="STATUS_CODE" property="statusCode" jdbcType="VARCHAR" />
    <result column="START_DATE" property="startDate" jdbcType="DATE" />
    <result column="END_DATE" property="endDate" jdbcType="DATE" />
    <result column="DESCRIPTION" property="description" jdbcType="VARCHAR" />
    <result column="STATUS_TYPE" property="statusType" jdbcType="VARCHAR" />
    <result column="CANCEL_CODE" property="cancelCode" jdbcType="VARCHAR" />
    <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR" />
    <result column="CREATION_DATETIME" property="creationDatetime" jdbcType="DATE" />
    <result column="LAST_MOD_USER" property="lastModUser" jdbcType="VARCHAR" />
    <result column="LAST_MOD_DATE" property="lastModDate" jdbcType="DATE" />
  </resultMap>
  <sql id="Base_Column_List" >
    STATUS_CODE, START_DATE, END_DATE, DESCRIPTION, STATUS_TYPE, CANCEL_CODE, CREATED_BY, 
    CREATION_DATETIME, LAST_MOD_USER, LAST_MOD_DATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_upd_status_code_ref
    where STATUS_CODE = #{statusCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from ib_t_upd_status_code_ref
    where STATUS_CODE = #{statusCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdStatusCodeRef" >
    insert into ib_t_upd_status_code_ref (STATUS_CODE, START_DATE, END_DATE, 
      DESCRIPTION, STATUS_TYPE, CANCEL_CODE, 
      CREATED_BY, CREATION_DATETIME, LAST_MOD_USER, 
      LAST_MOD_DATE)
    values (#{statusCode,jdbcType=VARCHAR}, #{startDate,jdbcType=DATE}, #{endDate,jdbcType=DATE}, 
      #{description,jdbcType=VARCHAR}, #{statusType,jdbcType=VARCHAR}, #{cancelCode,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{creationDatetime,jdbcType=DATE}, #{lastModUser,jdbcType=VARCHAR}, 
      #{lastModDate,jdbcType=DATE})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdStatusCodeRef" >
    insert into ib_t_upd_status_code_ref
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="statusCode != null" >
        STATUS_CODE,
      </if>
      <if test="startDate != null" >
        START_DATE,
      </if>
      <if test="endDate != null" >
        END_DATE,
      </if>
      <if test="description != null" >
        DESCRIPTION,
      </if>
      <if test="statusType != null" >
        STATUS_TYPE,
      </if>
      <if test="cancelCode != null" >
        CANCEL_CODE,
      </if>
      <if test="createdBy != null" >
        CREATED_BY,
      </if>
      <if test="creationDatetime != null" >
        CREATION_DATETIME,
      </if>
      <if test="lastModUser != null" >
        LAST_MOD_USER,
      </if>
      <if test="lastModDate != null" >
        LAST_MOD_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="statusCode != null" >
        #{statusCode,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null" >
        #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null" >
        #{endDate,jdbcType=DATE},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="statusType != null" >
        #{statusType,jdbcType=VARCHAR},
      </if>
      <if test="cancelCode != null" >
        #{cancelCode,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="creationDatetime != null" >
        #{creationDatetime,jdbcType=DATE},
      </if>
      <if test="lastModUser != null" >
        #{lastModUser,jdbcType=VARCHAR},
      </if>
      <if test="lastModDate != null" >
        #{lastModDate,jdbcType=DATE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTUpdStatusCodeRef" >
    update ib_t_upd_status_code_ref
    <set >
      <if test="startDate != null" >
        START_DATE = #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null" >
        END_DATE = #{endDate,jdbcType=DATE},
      </if>
      <if test="description != null" >
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="statusType != null" >
        STATUS_TYPE = #{statusType,jdbcType=VARCHAR},
      </if>
      <if test="cancelCode != null" >
        CANCEL_CODE = #{cancelCode,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="creationDatetime != null" >
        CREATION_DATETIME = #{creationDatetime,jdbcType=DATE},
      </if>
      <if test="lastModUser != null" >
        LAST_MOD_USER = #{lastModUser,jdbcType=VARCHAR},
      </if>
      <if test="lastModDate != null" >
        LAST_MOD_DATE = #{lastModDate,jdbcType=DATE},
      </if>
    </set>
    where STATUS_CODE = #{statusCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdStatusCodeRef" >
    update ib_t_upd_status_code_ref
    set START_DATE = #{startDate,jdbcType=DATE},
      END_DATE = #{endDate,jdbcType=DATE},
      DESCRIPTION = #{description,jdbcType=VARCHAR},
      STATUS_TYPE = #{statusType,jdbcType=VARCHAR},
      CANCEL_CODE = #{cancelCode,jdbcType=VARCHAR},
      CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      CREATION_DATETIME = #{creationDatetime,jdbcType=DATE},
      LAST_MOD_USER = #{lastModUser,jdbcType=VARCHAR},
      LAST_MOD_DATE = #{lastModDate,jdbcType=DATE}
    where STATUS_CODE = #{statusCode,jdbcType=VARCHAR}
  </update>
</mapper>