# 📋 Resumen General del Proyecto IQS (Intelligent Query System)

## 🎯 **Información General**
- **Nombre**: IQS (Intelligent Query System)
- **Versión**: 0.0.1-SNAPSHOT
- **Organización**: Lenovo/Motorola
- **Tipo**: Aplicación Web Java Enterprise (.war)
- **Fecha de Build**: 24 de Diciembre, 2023

## 🏗️ **Arquitectura General**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Integración   │
│   Web Java      │◄──►│   Spring MVC    │◄──►│   SAP JCo       │
│   (JSP/HTML)    │    │   REST/SOAP     │    │   MySQL         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   Procesamiento │
                       │   Scripts Python│
                       │   (ParseLog)    │
                       └─────────────────┘
```

## 🎯 **Propósito Principal**
El sistema IQS es una plataforma empresarial diseñada para:

1. **Gestión de Dispositivos Móviles**: Procesamiento y análisis de información de dispositivos Motorola/Lenovo
2. **Servicios de Desbloqueo**: Manejo de SIMLOCK y desbloqueo de dispositivos
3. **Integración SAP**: Conexión con sistemas empresariales SAP para consultas y actualizaciones
4. **Análisis de Logs**: Procesamiento automatizado de logs de dispositivos usando Python
5. **Servicios Web**: Exposición de APIs REST y SOAP para integración con otros sistemas

## 🛠️ **Stack Tecnológico**

### **Backend Java**
- **Framework**: Spring Framework 4.2.1
- **Web Services**: Apache CXF 3.1.3
- **ORM**: MyBatis 3.3.0
- **Validación**: Hibernate Validator 5.2.1
- **Logging**: Log4j2 2.17.1
- **JSON**: FastJSON 1.2.3, Jackson 2.10.0
- **Batch Processing**: Spring Batch 3.0.6

### **Base de Datos**
- **Principal**: MySQL 8.0.33
- **Pool de Conexiones**: Druid 1.0.14
- **Monitoreo**: Druid Web Console
- **Secundaria**: SQL Server (para PCG e iBase)

### **Integración**
- **SAP**: SAP JCo (Java Connector)
- **HTTP Client**: OkHttp 3.11.0
- **Criptografía**: Bouncy Castle 1.51

### **Procesamiento**
- **Python**: Scripts para análisis de logs
- **Librerías Python**: cryptography, base64, struct

### **Monitoreo**
- **JavaMelody**: 1.79.0 para monitoreo de aplicación
- **Druid**: Para monitoreo de base de datos

## 🌐 **Módulos Principales**

### 1. **RSU (Remote Secure Update)**
- Gestión de actualizaciones seguras remotas
- Integración con servicios GPS/Trustonic

### 2. **DataBlock Sign**
- Firma digital de bloques de datos
- Manejo de certificados X.509

### 3. **SIM Unlock**
- Servicios de desbloqueo de SIM
- Procesamiento de códigos de desbloqueo

### 4. **WeChat Integration**
- Integración con servicios WeChat
- APIs específicas para el mercado chino

### 5. **Google RPK**
- Gestión de claves públicas de Google
- Servicios de verificación

### 6. **SAP Integration**
- Conexión con sistemas SAP
- Consultas iBase y actualizaciones

## 🔧 **Configuración de Entornos**

### **Producción**
- **Base de Datos**: AWS RDS MySQL
- **Host**: iqs-prod.cojemua46qv2.us-east-1.rds.amazonaws.com
- **Puerto**: 3306

### **SAP CSP**
- **Host**: **********
- **Sistema**: CSP
- **Cliente**: 301

### **Servicios Externos**
- **GPS Trustonic**: wsgw.motorola.com:443
- **iBase**: csp.lenovo.com

## 📊 **Características Especiales**

### **Seguridad**
- Filtros de codificación UTF-8
- Manejo de certificados digitales
- Conexiones SSL/TLS
- Autenticación SAP

### **Escalabilidad**
- Pool de conexiones configurables
- Batch processing para grandes volúmenes
- Monitoreo en tiempo real

### **Integración**
- APIs REST y SOAP
- Conectores SAP nativos
- Scripts Python embebidos
- Múltiples bases de datos

## 🎯 **Casos de Uso Principales**
1. **Consulta de Garantía**: Verificación de estado de garantía de dispositivos
2. **Desbloqueo de Dispositivos**: Procesamiento de solicitudes de desbloqueo
3. **Actualizaciones Remotas**: Gestión de actualizaciones OTA
4. **Análisis de Logs**: Procesamiento automatizado de logs de dispositivos
5. **Integración Empresarial**: Sincronización con sistemas SAP

## 📈 **Métricas y Monitoreo**
- **JavaMelody**: Monitoreo de rendimiento de aplicación
- **Druid Console**: Estadísticas de base de datos
- **Log4j2**: Logging estructurado y configurable
- **Spring Actuator**: Endpoints de salud y métricas

## 🔄 **Flujo de Datos Típico**
1. **Entrada**: Solicitud via REST/SOAP API
2. **Validación**: Hibernate Validator + Spring Validation
3. **Procesamiento**: Lógica de negocio en servicios Spring
4. **Integración**: Consultas SAP o procesamiento Python
5. **Persistencia**: MyBatis + MySQL/SQL Server
6. **Respuesta**: JSON/XML estructurado

Este sistema representa una solución empresarial robusta para la gestión integral de dispositivos móviles con integración profunda a sistemas empresariales.
