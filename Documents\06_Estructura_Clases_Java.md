# IQS - Estructura de Clases Java

## 📦 Organización de Paquetes

```
com.lenovo.iqs/
├── dao/                    # Data Access Objects
├── datablocksign/          # Firma digital de bloques de datos
├── entity/                 # Entidades de dominio
├── exceptions/             # Excepciones personalizadas
├── googlerpk/             # Gestión de claves públicas Google
├── rsu/                   # Remote SIM Unlock
├── sap/                   # Integración SAP
├── simlock/               # Gestión de SIMLOCK
├── utils/                 # Utilidades generales
├── wechat/                # Integración WeChat
└── ws/                    # Web Services
```

## 🏗️ Módulo DataBlock Sign

### **Propósito**
Manejo de firma digital y validación de bloques de datos de dispositivos móviles.

### **Clases Principales**

#### **DataBlockSignController**
```java
@RestController
@RequestMapping("/datablocksign")
public class DataBlockSignController {
    
    @PostMapping("/sign")
    public ResponseEntity<ClientResponse> signDataBlock(@RequestBody RequestBean request) {
        // Lógica de firma digital
    }
    
    @PostMapping("/verify")
    public ResponseEntity<Boolean> verifySignature(@RequestBody VerificationRequest request) {
        // Verificación de firma
    }
}
```

#### **ClientResponse**
```java
public class ClientResponse {
    private int errorCode;
    private String errorMessage;
    private String signedData;
    private String timestamp;
    private CertificateInfo certificateInfo;
    
    // Getters y setters
}
```

#### **RequestBean**
```java
public class RequestBean {
    private String dataBlock;
    private String deviceId;
    private String clientType;
    private Map<String, Object> parameters;
    
    // Getters y setters
}
```

### **Servicios de Cliente por Tipo**

#### **CIDClientTypeServiceImpl**
```java
@Service("ClientType-0x02")
public class CIDClientTypeServiceImpl extends AbstractClientTypeService {
    
    @Autowired
    private SAPRfcs sapRfcs;
    
    @Override
    public ClientResponse processRequest(RequestBean request) {
        // Procesamiento específico para CID (Customer ID)
        return processDataBlock(request);
    }
}
```

#### **AbstractClientTypeService**
```java
public abstract class AbstractClientTypeService {
    
    protected abstract ClientResponse processRequest(RequestBean request);
    
    protected boolean validateDataBlock(String dataBlock) {
        // Validación común de bloques de datos
    }
    
    protected String generateSignature(String data, PrivateKey key) {
        // Generación de firma digital
    }
}
```

## 🔓 Módulo RSU (Remote SIM Unlock)

### **Propósito**
Gestión de desbloqueo remoto de tarjetas SIM para dispositivos móviles.

### **Clases Principales**

#### **RSUController**
```java
@RestController
@RequestMapping("/rsu")
public class RSUController {
    
    @PostMapping("/unlock")
    public ResponseEntity<UnlockResponse> unlockSIM(@RequestBody UnlockRequest request) {
        // Lógica de desbloqueo de SIM
    }
    
    @GetMapping("/status/{imei}")
    public ResponseEntity<UnlockStatus> getUnlockStatus(@PathVariable String imei) {
        // Consulta de estado de desbloqueo
    }
}
```

#### **UnlockRequest**
```java
public class UnlockRequest {
    private String imei;
    private String model;
    private String carrier;
    private String requestType;
    private Map<String, String> deviceInfo;
    
    // Getters y setters
}
```

#### **UnlockResponse**
```java
public class UnlockResponse {
    private String unlockCode;
    private String status;
    private Date timestamp;
    private String validationCode;
    private List<String> instructions;
    
    // Getters y setters
}
```

### **Servicios RSU**

#### **RSUService**
```java
@Service
public class RSUService {
    
    @Autowired
    private GPSService gpsService;
    
    public UnlockResponse processUnlockRequest(UnlockRequest request) {
        // 1. Validar IMEI
        // 2. Consultar GPS/Trustonic
        // 3. Generar código de desbloqueo
        // 4. Registrar transacción
    }
    
    private boolean validateIMEI(String imei) {
        // Validación de formato IMEI
    }
}
```

## 📱 Módulo SIMLOCK

### **Propósito**
Gestión general de políticas y estados de SIMLOCK.

### **Clases Principales**

#### **SimUnlockController**
```java
@RestController
@RequestMapping("/simunlock")
public class SimUnlockController {
    
    @PostMapping("/request")
    public ResponseEntity<SimUnlockResponse> requestUnlock(@RequestBody SimUnlockRequest request) {
        // Solicitud de desbloqueo
    }
    
    @GetMapping("/policy/{carrier}")
    public ResponseEntity<UnlockPolicy> getUnlockPolicy(@PathVariable String carrier) {
        // Consulta de políticas de carrier
    }
}
```

#### **UnlockPolicy**
```java
public class UnlockPolicy {
    private String carrier;
    private int waitingPeriodDays;
    private List<String> eligibilityCriteria;
    private boolean requiresCustomerService;
    private Map<String, Object> additionalRequirements;
    
    // Getters y setters
}
```

## 🔑 Módulo Google RPK

### **Propósito**
Gestión de claves públicas de Google para validación de dispositivos Android.

### **Clases Principales**

#### **GoogleRPKController**
```java
@RestController
@RequestMapping("/googlerpk")
public class GoogleRPKController {
    
    @PostMapping("/register")
    public ResponseEntity<RPKResponse> registerPublicKey(@RequestBody RPKRequest request) {
        // Registro de clave pública
    }
    
    @PostMapping("/validate")
    public ResponseEntity<ValidationResult> validateKey(@RequestBody KeyValidationRequest request) {
        // Validación de clave
    }
}
```

#### **RPKRequest**
```java
public class RPKRequest {
    private String publicKey;
    private String deviceId;
    private String attestationChain;
    private Map<String, Object> deviceProperties;
    
    // Getters y setters
}
```

## 💬 Módulo WeChat

### **Propósito**
Integración con la plataforma WeChat para servicios de soporte.

### **Clases Principales**

#### **WeChatController**
```java
@RestController
@RequestMapping("/wechat")
public class WeChatController {
    
    @PostMapping("/webhook")
    public ResponseEntity<String> handleWebhook(@RequestBody WeChatMessage message) {
        // Manejo de mensajes WeChat
    }
    
    @GetMapping("/verify")
    public ResponseEntity<String> verifyWebhook(@RequestParam Map<String, String> params) {
        // Verificación de webhook
    }
}
```

#### **WeChatMessage**
```java
public class WeChatMessage {
    private String fromUser;
    private String toUser;
    private String messageType;
    private String content;
    private Date timestamp;
    
    // Getters y setters
}
```

## 🗄️ Módulo DAO (Data Access Objects)

### **Estructura Base**

#### **BaseDAO**
```java
public interface BaseDAO<T, ID> {
    T findById(ID id);
    List<T> findAll();
    void save(T entity);
    void update(T entity);
    void delete(ID id);
}
```

#### **DeviceDAO**
```java
@Repository
public interface DeviceDAO extends BaseDAO<Device, String> {
    
    @Select("SELECT * FROM devices WHERE imei = #{imei}")
    Device findByIMEI(@Param("imei") String imei);
    
    @Select("SELECT * FROM devices WHERE model = #{model}")
    List<Device> findByModel(@Param("model") String model);
    
    @Insert("INSERT INTO devices (imei, model, carrier, status) VALUES (#{imei}, #{model}, #{carrier}, #{status})")
    void insertDevice(Device device);
}
```

## 🏷️ Módulo Entity

### **Entidades Principales**

#### **Device**
```java
@Entity
@Table(name = "devices")
public class Device {
    @Id
    private String imei;
    
    @Column(name = "model")
    private String model;
    
    @Column(name = "carrier")
    private String carrier;
    
    @Column(name = "simlock_status")
    private String simlockStatus;
    
    @Column(name = "created_date")
    private Date createdDate;
    
    // Getters y setters
}
```

#### **UnlockTransaction**
```java
@Entity
@Table(name = "unlock_transactions")
public class UnlockTransaction {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "imei")
    private String imei;
    
    @Column(name = "unlock_code")
    private String unlockCode;
    
    @Column(name = "status")
    private String status;
    
    @Column(name = "request_date")
    private Date requestDate;
    
    @Column(name = "completion_date")
    private Date completionDate;
    
    // Getters y setters
}
```

## 🛠️ Módulo Utils

### **Utilidades Generales**

#### **CryptoUtils**
```java
@Component
public class CryptoUtils {
    
    public String generateHash(String input, String algorithm) {
        // Generación de hash
    }
    
    public boolean verifySignature(String data, String signature, PublicKey publicKey) {
        // Verificación de firma digital
    }
    
    public String encryptData(String data, SecretKey key) {
        // Encriptación de datos
    }
}
```

#### **ValidationUtils**
```java
@Component
public class ValidationUtils {
    
    public boolean isValidIMEI(String imei) {
        // Validación de IMEI con algoritmo Luhn
    }
    
    public boolean isValidSerialNumber(String serialNumber) {
        // Validación de número de serie
    }
    
    public boolean isValidCarrier(String carrier) {
        // Validación de código de carrier
    }
}
```

## 🚨 Módulo Exceptions

### **Excepciones Personalizadas**

#### **IQSException**
```java
public class IQSException extends Exception {
    private String errorCode;
    private String userMessage;
    
    public IQSException(String errorCode, String message, String userMessage) {
        super(message);
        this.errorCode = errorCode;
        this.userMessage = userMessage;
    }
    
    // Getters y setters
}
```

#### **ValidationException**
```java
public class ValidationException extends IQSException {
    private String fieldName;
    private Object fieldValue;
    
    public ValidationException(String fieldName, Object fieldValue, String message) {
        super("VALIDATION_ERROR", message, "Invalid input provided");
        this.fieldName = fieldName;
        this.fieldValue = fieldValue;
    }
}
```

## 🔧 Configuración de Componentes

### **Anotaciones Spring**
- `@RestController`: Controladores REST
- `@Service`: Servicios de negocio
- `@Repository`: Acceso a datos
- `@Component`: Componentes generales
- `@Configuration`: Clases de configuración

### **Inyección de Dependencias**
```java
@Service
public class DeviceService {
    
    @Autowired
    private DeviceDAO deviceDAO;
    
    @Autowired
    private ValidationUtils validationUtils;
    
    @Autowired
    private CryptoUtils cryptoUtils;
    
    // Métodos de servicio
}
```
