<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdServicelinkInboundHistoryMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdServicelinkInboundHistory" >
    <result column="auto_id" property="autoId" jdbcType="INTEGER" />
    <result column="serial_no" property="serialNo" jdbcType="VARCHAR" />
    <result column="srno_type" property="srnoType" jdbcType="VARCHAR" />
    <result column="warranty_claim_no" property="warrantyClaimNo" jdbcType="VARCHAR" />
    <result column="clms_date_recvd" property="clmsDateRecvd" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="batch_number" property="batchNumber" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdServicelinkInboundHistory" >
    insert into ib_t_upd_servicelink_inbound_history (auto_id, serial_no, srno_type, 
      warranty_claim_no, clms_date_recvd, create_time, 
      batch_number)
    values (#{autoId,jdbcType=INTEGER}, #{serialNo,jdbcType=VARCHAR}, #{srnoType,jdbcType=VARCHAR}, 
      #{warrantyClaimNo,jdbcType=VARCHAR}, #{clmsDateRecvd,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{batchNumber,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdServicelinkInboundHistory" >
    insert into ib_t_upd_servicelink_inbound_history
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="autoId != null" >
        auto_id,
      </if>
      <if test="serialNo != null" >
        serial_no,
      </if>
      <if test="srnoType != null" >
        srno_type,
      </if>
      <if test="warrantyClaimNo != null" >
        warranty_claim_no,
      </if>
      <if test="clmsDateRecvd != null" >
        clms_date_recvd,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="batchNumber != null" >
        batch_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="autoId != null" >
        #{autoId,jdbcType=INTEGER},
      </if>
      <if test="serialNo != null" >
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="srnoType != null" >
        #{srnoType,jdbcType=VARCHAR},
      </if>
      <if test="warrantyClaimNo != null" >
        #{warrantyClaimNo,jdbcType=VARCHAR},
      </if>
      <if test="clmsDateRecvd != null" >
        #{clmsDateRecvd,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNumber != null" >
        #{batchNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>