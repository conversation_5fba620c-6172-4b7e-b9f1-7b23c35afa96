/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.bean;
/*    */ 
/*    */ 
/*    */ public class Config implements Serializable {
/*    */   private String key;
/*    */   private String value;
/*    */   
/*  8 */   public void setKey(String key) { this.key = key; } public void setValue(String value) { this.value = value; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.datablocksign.bean.Config)) return false;  com.lenovo.iqs.datablocksign.bean.Config other = (com.lenovo.iqs.datablocksign.bean.Config)o; if (!other.canEqual(this)) return false;  Object this$key = getKey(), other$key = other.getKey(); if ((this$key == null) ? (other$key != null) : !this$key.equals(other$key)) return false;  Object this$value = getValue(), other$value = other.getValue(); return !((this$value == null) ? (other$value != null) : !this$value.equals(other$value)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.datablocksign.bean.Config; } public int hashCode() { int PRIME = 59; result = 1; Object $key = getKey(); result = result * 59 + (($key == null) ? 43 : $key.hashCode()); Object $value = getValue(); return result * 59 + (($value == null) ? 43 : $value.hashCode()); } public String toString() { return "Config(key=" + getKey() + ", value=" + getValue() + ")"; }
/*    */    public String getKey() {
/* 10 */     return this.key;
/*    */   } public String getValue() {
/* 12 */     return this.value;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\bean\Config.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */