#!/usr/bin/env python3
"""
Script para extraer todas las tablas de base de datos de los archivos mapper XML
"""

import os
import re
import xml.etree.ElementTree as ET
from collections import defaultdict

def extract_tables_from_xml(file_path):
    """Extrae nombres de tablas de un archivo mapper XML"""
    tables = set()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Buscar patrones SQL que contengan nombres de tablas
        patterns = [
            r'FROM\s+(\w+)',
            r'from\s+(\w+)',
            r'INTO\s+(\w+)',
            r'into\s+(\w+)',
            r'UPDATE\s+(\w+)',
            r'update\s+(\w+)',
            r'DELETE\s+FROM\s+(\w+)',
            r'delete\s+from\s+(\w+)',
            r'INSERT\s+INTO\s+(\w+)',
            r'insert\s+into\s+(\w+)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                # Filtrar nombres que claramente son tablas (empiezan con ib_t_ o qrtz_)
                if match.lower().startswith(('ib_t_', 'qrtz_')) or 'ib_t_' in match.lower():
                    tables.add(match.lower())
                    
    except Exception as e:
        print(f"Error procesando {file_path}: {e}")
        
    return tables

def extract_columns_from_xml(file_path):
    """Extrae información de columnas de los resultMaps"""
    columns_info = defaultdict(list)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Buscar resultMaps para obtener información de columnas
        resultmap_pattern = r'<resultMap[^>]*type="([^"]*)"[^>]*>(.*?)</resultMap>'
        resultmaps = re.findall(resultmap_pattern, content, re.DOTALL | re.IGNORECASE)
        
        for entity_type, resultmap_content in resultmaps:
            # Extraer columnas del resultMap
            column_pattern = r'<(?:id|result)\s+column="([^"]*)"[^>]*property="([^"]*)"[^>]*jdbcType="([^"]*)"'
            columns = re.findall(column_pattern, resultmap_content, re.IGNORECASE)
            
            if columns:
                table_name = entity_type.split('.')[-1].lower()
                # Convertir nombre de entidad a nombre de tabla
                table_name = re.sub(r'([a-z])([A-Z])', r'\1_\2', table_name).lower()
                if not table_name.startswith('ib_t_'):
                    table_name = 'ib_t_' + table_name.replace('ibt', '')
                    
                columns_info[table_name] = columns
                
    except Exception as e:
        print(f"Error extrayendo columnas de {file_path}: {e}")
        
    return columns_info

def main():
    """Función principal"""
    mapper_dir = "WEB-INF/classes/com/lenovo/iqs/dao/mapper"
    datablock_mapper = "WEB-INF/classes/com/lenovo/iqs/datablocksign/dao/mapper/DatablockSignMapper.xml"
    rsu_mapper = "WEB-INF/classes/com/lenovo/iqs/rsu/dao/mapper/RsuWebserviceMapper.xml"
    
    all_tables = set()
    all_columns = defaultdict(list)
    
    # Procesar mappers en el directorio principal
    if os.path.exists(mapper_dir):
        for filename in os.listdir(mapper_dir):
            if filename.endswith('.xml'):
                file_path = os.path.join(mapper_dir, filename)
                tables = extract_tables_from_xml(file_path)
                columns = extract_columns_from_xml(file_path)
                
                all_tables.update(tables)
                all_columns.update(columns)
                
                if tables:
                    print(f"\n📁 {filename}:")
                    for table in sorted(tables):
                        print(f"   - {table}")
    
    # Procesar DatablockSignMapper
    if os.path.exists(datablock_mapper):
        tables = extract_tables_from_xml(datablock_mapper)
        columns = extract_columns_from_xml(datablock_mapper)
        all_tables.update(tables)
        all_columns.update(columns)
        
        if tables:
            print(f"\n📁 DatablockSignMapper.xml:")
            for table in sorted(tables):
                print(f"   - {table}")
    
    # Procesar RsuWebserviceMapper
    if os.path.exists(rsu_mapper):
        tables = extract_tables_from_xml(rsu_mapper)
        all_tables.update(tables)
        
        if tables:
            print(f"\n📁 RsuWebserviceMapper.xml:")
            for table in sorted(tables):
                print(f"   - {table}")
    
    # Resumen final
    print(f"\n🎯 RESUMEN FINAL:")
    print(f"📊 Total de tablas encontradas: {len(all_tables)}")
    print(f"\n📋 LISTA COMPLETA DE TABLAS:")
    
    for table in sorted(all_tables):
        print(f"   ✓ {table}")
    
    # Generar script SQL básico
    print(f"\n🔧 GENERANDO SCRIPT SQL BÁSICO...")
    
    with open('create_tables.sql', 'w') as f:
        f.write("-- Script generado automáticamente para crear tablas IQS\n")
        f.write("-- Basado en análisis de mappers XML\n\n")
        f.write("USE iqs_local;\n\n")
        
        for table in sorted(all_tables):
            f.write(f"-- Tabla: {table}\n")
            if table in all_columns and all_columns[table]:
                f.write(f"CREATE TABLE IF NOT EXISTS {table} (\n")
                columns = all_columns[table]
                for i, (col_name, prop_name, jdbc_type) in enumerate(columns):
                    mysql_type = convert_jdbc_to_mysql(jdbc_type)
                    comma = "," if i < len(columns) - 1 else ""
                    f.write(f"    {col_name} {mysql_type}{comma}\n")
                f.write(");\n\n")
            else:
                f.write(f"-- CREATE TABLE IF NOT EXISTS {table} (\n")
                f.write(f"--     id INT AUTO_INCREMENT PRIMARY KEY,\n")
                f.write(f"--     -- Agregar columnas según necesidades\n")
                f.write(f"--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n")
                f.write(f"-- );\n\n")
    
    print(f"✅ Script SQL generado: create_tables.sql")

def convert_jdbc_to_mysql(jdbc_type):
    """Convierte tipos JDBC a tipos MySQL"""
    type_mapping = {
        'VARCHAR': 'VARCHAR(255)',
        'INTEGER': 'INT',
        'TIMESTAMP': 'TIMESTAMP',
        'DATE': 'DATE',
        'CHAR': 'CHAR(1)',
        'BLOB': 'BLOB',
        'TEXT': 'TEXT',
        'DECIMAL': 'DECIMAL(10,2)',
        'BIGINT': 'BIGINT'
    }
    
    return type_mapping.get(jdbc_type.upper(), 'VARCHAR(255)')

if __name__ == "__main__":
    main()
