/*    */ package WEB-INF.classes.com.lenovo.iqs.exceptions;
/*    */ public class BusinessException extends RuntimeException {
/*    */   public String errorCode;
/*    */   public String errorMessage;
/*    */   
/*    */   private BusinessException() {}
/*    */   
/*  8 */   public void setErrorCode(String errorCode) { this.errorCode = errorCode; } public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.exceptions.BusinessException)) return false;  com.lenovo.iqs.exceptions.BusinessException other = (com.lenovo.iqs.exceptions.BusinessException)o; if (!other.canEqual(this)) return false;  Object this$errorCode = getErrorCode(), other$errorCode = other.getErrorCode(); if ((this$errorCode == null) ? (other$errorCode != null) : !this$errorCode.equals(other$errorCode)) return false;  Object this$errorMessage = getErrorMessage(), other$errorMessage = other.getErrorMessage(); return !((this$errorMessage == null) ? (other$errorMessage != null) : !this$errorMessage.equals(other$errorMessage)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.exceptions.BusinessException; } public int hashCode() { int PRIME = 59; result = 1; Object $errorCode = getErrorCode(); result = result * 59 + (($errorCode == null) ? 43 : $errorCode.hashCode()); Object $errorMessage = getErrorMessage(); return result * 59 + (($errorMessage == null) ? 43 : $errorMessage.hashCode()); } public String toString() { return "BusinessException(errorCode=" + getErrorCode() + ", errorMessage=" + getErrorMessage() + ")"; }
/*    */   
/* 10 */   public String getErrorCode() { return this.errorCode; } public String getErrorMessage() {
/* 11 */     return this.errorMessage;
/*    */   }
/*    */   public BusinessException(String errorCode, String errorMessage) {
/* 14 */     this.errorCode = errorCode;
/* 15 */     this.errorMessage = errorMessage;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\exceptions\BusinessException.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */