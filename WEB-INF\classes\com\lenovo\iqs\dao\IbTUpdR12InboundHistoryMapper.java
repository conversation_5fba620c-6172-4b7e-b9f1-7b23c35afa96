package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTUpdR12InboundHistory;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTUpdR12InboundHistoryMapper {
  int insert(IbTUpdR12InboundHistory paramIbTUpdR12InboundHistory);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTUpdR12InboundHistoryMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */