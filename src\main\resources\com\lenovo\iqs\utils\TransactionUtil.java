/*    */ package WEB-INF.classes.com.lenovo.iqs.utils;
/*    */ 
/*    */ import com.lenovo.iqs.utils.SpringHelper;
/*    */ import org.springframework.jdbc.datasource.DataSourceTransactionManager;
/*    */ import org.springframework.transaction.TransactionDefinition;
/*    */ import org.springframework.transaction.TransactionStatus;
/*    */ import org.springframework.transaction.support.DefaultTransactionDefinition;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public final class TransactionUtil
/*    */ {
/*    */   public static <T> T runInTransaction(Runnable<T> runnable) {
/* 15 */     DataSourceTransactionManager transactionManager = (DataSourceTransactionManager)SpringHelper.getBean("transactionManager", DataSourceTransactionManager.class);
/*    */ 
/*    */     
/* 18 */     DefaultTransactionDefinition def = new DefaultTransactionDefinition();
/*    */     
/* 20 */     def.setPropagationBehavior(3);
/*    */     
/* 22 */     TransactionStatus status = transactionManager.getTransaction((TransactionDefinition)def);
/*    */     try {
/* 24 */       T retVal = (T)runnable.run();
/* 25 */       transactionManager.commit(status);
/* 26 */       return retVal;
/* 27 */     } catch (Exception e) {
/* 28 */       transactionManager.rollback(status);
/* 29 */       throw e;
/*    */     } 
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iq\\utils\TransactionUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */