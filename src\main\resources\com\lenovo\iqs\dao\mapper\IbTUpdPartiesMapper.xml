<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdPartiesMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdParties" >
    <id column="ID" property="id" jdbcType="INTEGER" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="TYPE" property="type" jdbcType="INTEGER" />
    <result column="enable" property="enable" jdbcType="INTEGER" />
    <result column="COUNTRY_CODE" property="countryCode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, NAME, TYPE, enable, COUNTRY_CODE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_upd_parties
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from ib_t_upd_parties
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdParties" >
    insert into ib_t_upd_parties (ID, NAME, TYPE, 
      enable, COUNTRY_CODE)
    values (#{id,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{enable,jdbcType=INTEGER}, #{countryCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdParties" >
    insert into ib_t_upd_parties
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="name != null" >
        NAME,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="enable != null" >
        enable,
      </if>
      <if test="countryCode != null" >
        COUNTRY_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="enable != null" >
        #{enable,jdbcType=INTEGER},
      </if>
      <if test="countryCode != null" >
        #{countryCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTUpdParties" >
    update ib_t_upd_parties
    <set >
      <if test="name != null" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        TYPE = #{type,jdbcType=INTEGER},
      </if>
      <if test="enable != null" >
        enable = #{enable,jdbcType=INTEGER},
      </if>
      <if test="countryCode != null" >
        COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdParties" >
    update ib_t_upd_parties
    set NAME = #{name,jdbcType=VARCHAR},
      TYPE = #{type,jdbcType=INTEGER},
      enable = #{enable,jdbcType=INTEGER},
      COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
</mapper>