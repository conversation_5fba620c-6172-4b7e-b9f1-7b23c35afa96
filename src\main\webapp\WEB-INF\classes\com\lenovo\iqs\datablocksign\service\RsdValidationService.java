package WEB-INF.classes.com.lenovo.iqs.datablocksign.service;

import com.lenovo.iqs.datablocksign.bean.RSDValidationRequest;
import com.lenovo.iqs.datablocksign.bean.RSDValidationResponse;

public interface RsdValidationService {
  RSDValidationResponse validateRequest(RSDValidationRequest paramRSDValidationRequest) throws Exception;
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\service\RsdValidationService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */