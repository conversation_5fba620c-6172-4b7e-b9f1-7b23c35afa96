<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdCountryWarrantyExceptionMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdCountryWarrantyException" >
    <id column="auto_id" property="autoId" jdbcType="BIGINT" />
    <result column="iso_code" property="isoCode" jdbcType="CHAR" />
    <result column="warranty_code" property="warrantyCode" jdbcType="CHAR" />
    <result column="carrier_model_no" property="carrierModelNo" jdbcType="CHAR" />
    <result column="ship_to_customer" property="shipToCustomer" jdbcType="CHAR" />
    <result column="trans_model_no" property="transModelNo" jdbcType="CHAR" />
    <result column="apc" property="apc" jdbcType="CHAR" />
    <result column="start_ship_date" property="startShipDate" jdbcType="DATE" />
    <result column="end_ship_date" property="endShipDate" jdbcType="DATE" />
    <result column="last_mod_date" property="lastModDate" jdbcType="TIMESTAMP" />
    <result column="last_mod_by" property="lastModBy" jdbcType="CHAR" />
    <result column="creation_datetime" property="creationDatetime" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="CHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    auto_id, iso_code, warranty_code, carrier_model_no, ship_to_customer, trans_model_no, 
    apc, start_ship_date, end_ship_date, last_mod_date, last_mod_by, creation_datetime, 
    created_by
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_upd_country_warranty_exception
    where auto_id = #{autoId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ib_t_upd_country_warranty_exception
    where auto_id = #{autoId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdCountryWarrantyException" >
    insert into ib_t_upd_country_warranty_exception (auto_id, iso_code, warranty_code, 
      carrier_model_no, ship_to_customer, trans_model_no, 
      apc, start_ship_date, end_ship_date, 
      last_mod_date, last_mod_by, creation_datetime, 
      created_by)
    values (#{autoId,jdbcType=BIGINT}, #{isoCode,jdbcType=CHAR}, #{warrantyCode,jdbcType=CHAR}, 
      #{carrierModelNo,jdbcType=CHAR}, #{shipToCustomer,jdbcType=CHAR}, #{transModelNo,jdbcType=CHAR}, 
      #{apc,jdbcType=CHAR}, #{startShipDate,jdbcType=DATE}, #{endShipDate,jdbcType=DATE}, 
      #{lastModDate,jdbcType=TIMESTAMP}, #{lastModBy,jdbcType=CHAR}, #{creationDatetime,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdCountryWarrantyException" >
    insert into ib_t_upd_country_warranty_exception
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="autoId != null" >
        auto_id,
      </if>
      <if test="isoCode != null" >
        iso_code,
      </if>
      <if test="warrantyCode != null" >
        warranty_code,
      </if>
      <if test="carrierModelNo != null" >
        carrier_model_no,
      </if>
      <if test="shipToCustomer != null" >
        ship_to_customer,
      </if>
      <if test="transModelNo != null" >
        trans_model_no,
      </if>
      <if test="apc != null" >
        apc,
      </if>
      <if test="startShipDate != null" >
        start_ship_date,
      </if>
      <if test="endShipDate != null" >
        end_ship_date,
      </if>
      <if test="lastModDate != null" >
        last_mod_date,
      </if>
      <if test="lastModBy != null" >
        last_mod_by,
      </if>
      <if test="creationDatetime != null" >
        creation_datetime,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="autoId != null" >
        #{autoId,jdbcType=BIGINT},
      </if>
      <if test="isoCode != null" >
        #{isoCode,jdbcType=CHAR},
      </if>
      <if test="warrantyCode != null" >
        #{warrantyCode,jdbcType=CHAR},
      </if>
      <if test="carrierModelNo != null" >
        #{carrierModelNo,jdbcType=CHAR},
      </if>
      <if test="shipToCustomer != null" >
        #{shipToCustomer,jdbcType=CHAR},
      </if>
      <if test="transModelNo != null" >
        #{transModelNo,jdbcType=CHAR},
      </if>
      <if test="apc != null" >
        #{apc,jdbcType=CHAR},
      </if>
      <if test="startShipDate != null" >
        #{startShipDate,jdbcType=DATE},
      </if>
      <if test="endShipDate != null" >
        #{endShipDate,jdbcType=DATE},
      </if>
      <if test="lastModDate != null" >
        #{lastModDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModBy != null" >
        #{lastModBy,jdbcType=CHAR},
      </if>
      <if test="creationDatetime != null" >
        #{creationDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTUpdCountryWarrantyException" >
    update ib_t_upd_country_warranty_exception
    <set >
      <if test="isoCode != null" >
        iso_code = #{isoCode,jdbcType=CHAR},
      </if>
      <if test="warrantyCode != null" >
        warranty_code = #{warrantyCode,jdbcType=CHAR},
      </if>
      <if test="carrierModelNo != null" >
        carrier_model_no = #{carrierModelNo,jdbcType=CHAR},
      </if>
      <if test="shipToCustomer != null" >
        ship_to_customer = #{shipToCustomer,jdbcType=CHAR},
      </if>
      <if test="transModelNo != null" >
        trans_model_no = #{transModelNo,jdbcType=CHAR},
      </if>
      <if test="apc != null" >
        apc = #{apc,jdbcType=CHAR},
      </if>
      <if test="startShipDate != null" >
        start_ship_date = #{startShipDate,jdbcType=DATE},
      </if>
      <if test="endShipDate != null" >
        end_ship_date = #{endShipDate,jdbcType=DATE},
      </if>
      <if test="lastModDate != null" >
        last_mod_date = #{lastModDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModBy != null" >
        last_mod_by = #{lastModBy,jdbcType=CHAR},
      </if>
      <if test="creationDatetime != null" >
        creation_datetime = #{creationDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=CHAR},
      </if>
    </set>
    where auto_id = #{autoId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdCountryWarrantyException" >
    update ib_t_upd_country_warranty_exception
    set iso_code = #{isoCode,jdbcType=CHAR},
      warranty_code = #{warrantyCode,jdbcType=CHAR},
      carrier_model_no = #{carrierModelNo,jdbcType=CHAR},
      ship_to_customer = #{shipToCustomer,jdbcType=CHAR},
      trans_model_no = #{transModelNo,jdbcType=CHAR},
      apc = #{apc,jdbcType=CHAR},
      start_ship_date = #{startShipDate,jdbcType=DATE},
      end_ship_date = #{endShipDate,jdbcType=DATE},
      last_mod_date = #{lastModDate,jdbcType=TIMESTAMP},
      last_mod_by = #{lastModBy,jdbcType=CHAR},
      creation_datetime = #{creationDatetime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=CHAR}
    where auto_id = #{autoId,jdbcType=BIGINT}
  </update>
</mapper>