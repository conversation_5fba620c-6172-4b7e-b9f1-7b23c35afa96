/*     */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.controller;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.lenovo.iqs.dao.IbTAccessLogMapper;
/*     */ import com.lenovo.iqs.datablocksign.Constants.ClientTypeEnum;
/*     */ import com.lenovo.iqs.datablocksign.bean.ClientResponse;
/*     */ import com.lenovo.iqs.datablocksign.bean.RequestBean;
/*     */ import com.lenovo.iqs.datablocksign.service.ClientTypeService;
/*     */ import com.lenovo.iqs.entity.IbTAccessLog;
/*     */ import com.lenovo.iqs.exceptions.PKIException;
/*     */ import com.lenovo.iqs.utils.SpringHelper;
/*     */ import java.util.Date;
/*     */ import javax.jws.WebMethod;
/*     */ import javax.jws.WebResult;
/*     */ import javax.jws.WebService;
/*     */ import javax.jws.soap.SOAPBinding;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import org.apache.cxf.common.util.StringUtils;
/*     */ import org.apache.cxf.message.Message;
/*     */ import org.apache.cxf.phase.PhaseInterceptorChain;
/*     */ import org.apache.log4j.Logger;
/*     */ import org.springframework.beans.factory.annotation.Autowired;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @WebService(name = "IQS_DataBlockSign_1.0", serviceName = "IQS_DataBlockSign_1.0", targetNamespace = "http://ibase.lenovo.com/webservices/IQS_DataBlockSign_1.0")
/*     */ @SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
/*     */ public class DataBlockSignController
/*     */ {
/*  38 */   private static final Logger log = Logger.getLogger(com.lenovo.iqs.datablocksign.controller.DataBlockSignController.class);
/*     */ 
/*     */   
/*     */   public static final String serviceBeanNamePre = "ClientType";
/*     */   
/*     */   @Autowired
/*     */   private IbTAccessLogMapper accessLogMapper;
/*     */ 
/*     */   
/*     */   @WebMethod(action = "", operationName = "signDataBlock")
/*     */   @WebResult(name = "signDataBlockResponse", targetNamespace = "http://ibase.lenovo.com/webservices/IQS_DataBlockSign_1.0")
/*     */   public ClientResponse signDataBlock(RequestBean requestBean) {
/*  50 */     Date startTime = new Date();
/*  51 */     String errorMsg = "";
/*  52 */     log.info("DatablockSign请求参数:" + JSON.toJSONString(requestBean));
/*  53 */     String clientType = requestBean.getIstrClientReqType();
/*  54 */     IbTAccessLog accessLog = new IbTAccessLog();
/*     */     
/*  56 */     ClientResponse clientResponse = null;
/*     */     try {
/*  58 */       Message message = PhaseInterceptorChain.getCurrentMessage();
/*  59 */       HttpServletRequest httpRequest = (HttpServletRequest)message.get("HTTP.REQUEST");
/*     */       
/*  61 */       String userId = httpRequest.getHeader("coreid");
/*  62 */       String publicIp = httpRequest.getHeader("publicIP");
/*  63 */       requestBean.setUserId(StringUtils.isEmpty(userId) ? "" : userId);
/*  64 */       requestBean.setPublicIP(StringUtils.isEmpty(publicIp) ? "" : publicIp);
/*  65 */     } catch (Exception ex) {
/*  66 */       log.error("Eror while getting userId and public IP");
/*     */     } 
/*     */     
/*     */     try {
/*  70 */       float dbs_version = getDatablockVersionInFloat(requestBean);
/*  71 */       if ((dbs_version > 3.5D && requestBean.getIstrNodeLockParam() != null) || dbs_version == 3.5D) {
/*     */         
/*  73 */         ClientTypeService clientTypeService = (ClientTypeService)SpringHelper.getBean("ClientType-" + (
/*  74 */             ClientTypeEnum.SIMLock.clientType.equalsIgnoreCase(clientType) ? ClientTypeEnum.CID.clientType : clientType), ClientTypeService.class);
/*  75 */         clientResponse = clientTypeService.process(requestBean);
/*     */       } else {
/*  77 */         log.error("Node Lock Param not present");
/*  78 */         clientResponse = new ClientResponse();
/*  79 */         clientResponse.setIstrStatusCode("8107");
/*  80 */         clientResponse.setIstrStatusData("Node Locking Message Not Present.");
/*     */       } 
/*  82 */       log.info("DatablockSign 返回的结果：" + JSON.toJSONString(clientResponse));
/*  83 */     } catch (PKIException e) {
/*  84 */       log.error("PKI访问异常;old_imei:" + requestBean.getIstrOldIMEI() + " new_imei:" + requestBean.getIstrNewIMEI(), (Throwable)e);
/*  85 */       errorMsg = e.getMessage();
/*  86 */       clientResponse = new ClientResponse();
/*  87 */       clientResponse.setIstrStatusCode("8105");
/*  88 */       clientResponse.setIstrStatusData("PKI connection failure.");
/*  89 */     } catch (Exception e) {
/*  90 */       log.error("DataBlockSige 处理异常:old_imei:" + requestBean.getIstrOldIMEI() + " new_imei:" + requestBean.getIstrNewIMEI(), e);
/*  91 */       errorMsg = e.getMessage();
/*  92 */       clientResponse = new ClientResponse();
/*  93 */       clientResponse.setIstrStatusCode("8106");
/*  94 */       clientResponse.setIstrStatusData("Exception while processing the request");
/*     */     } 
/*  96 */     Date endTime = new Date();
/*     */     try {
/*  98 */       accessLog.setAccessTime(new Date());
/*  99 */       accessLog.setKeyword(requestBean.getIstrNewIMEI() + "-" + requestBean.getIstrOldIMEI());
/* 100 */       accessLog.setClassName(getClass().getName());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 110 */       accessLog.setAccess_parameter(JSONObject.toJSONString(requestBean));
/* 111 */       accessLog.setResult(clientResponse.getIstrStatusCode() + "_" + clientResponse.getIstrStatusData() + "costTime:" + (endTime.getTime() - startTime.getTime()) + ";errorMsg:" + ((errorMsg.length() > 300) ? errorMsg.substring(0, 300) : errorMsg));
/* 112 */       this.accessLogMapper.insert(accessLog);
/* 113 */     } catch (Exception e) {
/* 114 */       log.error("DatablockSign add log error, old_imei:" + requestBean.getIstrOldIMEI() + " new_imei:" + requestBean.getIstrNewIMEI(), e);
/*     */     } 
/*     */     
/* 117 */     String status_before_return = "";
/* 118 */     status_before_return = clientResponse.getIstrStatusCode();
/* 119 */     if (!status_before_return.equalsIgnoreCase("8049") && !status_before_return.equalsIgnoreCase("8022") && status_before_return.startsWith("7")) {
/* 120 */       log.debug("Before final return");
/* 121 */       String[] error = "9999,Request Failed".split(",");
/* 122 */       clientResponse.setIstrStatusCode(error[0]);
/* 123 */       clientResponse.setIstrStatusData(error[1]);
/* 124 */       log.debug("done here");
/*     */     } 
/* 126 */     return clientResponse;
/*     */   }
/*     */   
/*     */   private String getDatablockVersion(RequestBean requestBean) {
/* 130 */     Message message = PhaseInterceptorChain.getCurrentMessage();
/* 131 */     HttpServletRequest httpRequest = (HttpServletRequest)message.get("HTTP.REQUEST");
/* 132 */     String curretRequestURI = httpRequest.getRequestURI();
/* 133 */     String datablockSignVersion = curretRequestURI.substring(curretRequestURI.lastIndexOf("_") + 1);
/* 134 */     log.info("DatablockSign curretRequestURI: " + curretRequestURI + " for serialno:" + requestBean.getIstrOldIMEI());
/* 135 */     return datablockSignVersion;
/*     */   }
/*     */   
/*     */   private float getDatablockVersionInFloat(RequestBean requestBean) {
/* 139 */     return Float.parseFloat(getDatablockVersion(requestBean));
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\controller\DataBlockSignController.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */