<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jaxws="http://cxf.apache.org/jaxws"
	xmlns:http-conf="http://cxf.apache.org/transports/http/configuration"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
      	http://cxf.apache.org/jaxws http://cxf.apache.org/schemas/jaxws.xsd
      	http://cxf.apache.org/transports/http/configuration 
      	http://cxf.apache.org/schemas/configuration/http-conf.xsd
		http://cxf.apache.org/jaxrs http://cxf.apache.org/schemas/jaxrs.xsd"
	   xmlns:jaxrs="http://cxf.apache.org/jaxrs">
      	
	<import resource="classpath*:META-INF/cxf/cxf.xml" />

<!--	<bean id="ibTUpdSnReposWebServiceImpl"-->
<!--		class="com.lenovo.iqs.webquery.warrantyCheck.ws.impl.IbTUpdSnReposWebServiceImpl" />-->
<!--	<jaxws:endpoint id="findIbTUpdSnReposbySerialno"-->
<!--		implementor="#ibTUpdSnReposWebServiceImpl" address="/ibTUpdSnReposWebServiceImplWSDL"></jaxws:endpoint>-->

	<jaxrs:server address="/rest" id="RESTWebService">
		<jaxrs:serviceBeans>
			<ref bean="rsuController"/>    <!--如果还有其它的服务，继续在这里引用-->
			<ref bean="weChatController"/>
			<ref bean="simUnlockController"/>
			<ref bean="googleRPKController"/>
<!--			<ref bean="UPDGetWarrantyServiceRest"/>-->
		</jaxrs:serviceBeans>

		<jaxrs:providers>
			<bean class="org.codehaus.jackson.jaxrs.JacksonJsonProvider" />
			<bean class="org.apache.cxf.jaxrs.provider.JAXBElementProvider" />
		</jaxrs:providers>
	</jaxrs:server>

<!--	<jaxws:endpoint id="DataBlockSign3.3"-->
<!--					implementor="com.lenovo.iqs.datablocksign.controller.DataBlockSignController"-->
<!--					address="/IQS_DataBlockSign_3.3">-->
<!--	</jaxws:endpoint>-->

<!--	<jaxws:endpoint id="DataBlockSign3.4"-->
<!--					implementor="com.lenovo.iqs.datablocksign.controller.DataBlockSignController"-->
<!--					address="/IQS_DataBlockSign_3.4">-->
<!--	</jaxws:endpoint>-->

	<jaxws:endpoint id="DataBlockSign3.5"
					implementor="com.lenovo.iqs.datablocksign.controller.DataBlockSignController"
					address="/IQS_DataBlockSign_3.5">
	</jaxws:endpoint>

	<!-- Node locking server version-->
	<jaxws:endpoint id="DataBlockSign3.6"
					implementor="com.lenovo.iqs.datablocksign.controller.DataBlockSignController"
					address="/IQS_DataBlockSign_3.6">
	</jaxws:endpoint>


	<!--	<jaxws:endpoint id="DeviceUnlock"-->
<!--					implementor="com.lenovo.iqs.deviceunlock.controller.DeviceUnlockController"-->
<!--					address="/IQS_DeviceUnlock_1.0">-->
<!--	</jaxws:endpoint>-->

<!--	<jaxws:endpoint id="UPDGetWarranty"-->
<!--		implementor="com.lenovo.iqs.ws.upd.warranty.service.impl.UPDGetWarrantyServiceImpl"-->
<!--		address="/IQS_WARRANTY_CHECK_1.0">-->
<!--		<jaxws:inInterceptors>-->
<!--			<bean class="org.apache.cxf.ws.security.wss4j.WSS4JInInterceptor">-->
<!--				<constructor-arg>-->
<!--					<map>-->
<!--						<entry key="action" value="UsernameToken" />-->
<!--						<entry key="passwordType" value="PasswordText" />-->
<!--						<entry key="user" value="FHDServer" />-->
<!--						<entry key="passwordCallbackRef">-->
<!--							<ref bean="iqsWarrantyCheckPasswordCallback" />-->
<!--						</entry>-->
<!--					</map>-->
<!--				</constructor-arg>-->
<!--			</bean>-->
<!--		</jaxws:inInterceptors>-->
<!--	</jaxws:endpoint>-->
<!--	-->
<!--	<jaxws:endpoint id="SnValidation"-->
<!--		implementor="com.lenovo.iqs.ws.upd.warranty.service.impl.IqsSnValidationServiceImpl"-->
<!--		address="/IQS_SN_VALIDATION_1.0">-->
<!--		<jaxws:inInterceptors>-->
<!--			<bean class="org.apache.cxf.ws.security.wss4j.WSS4JInInterceptor">-->
<!--				<constructor-arg>-->
<!--					<map>-->
<!--						<entry key="action" value="UsernameToken" />-->
<!--						<entry key="passwordType" value="PasswordText" />-->
<!--						<entry key="user" value="FHDServer" />-->
<!--						<entry key="passwordCallbackRef">-->
<!--							<ref bean="iqsWarrantyCheckPasswordCallback" />-->
<!--						</entry>-->
<!--					</map>-->
<!--				</constructor-arg>-->
<!--			</bean>-->
<!--		</jaxws:inInterceptors>-->
<!--	</jaxws:endpoint>-->

<!--	<jaxws:client id="ZIBPBATCHQUERYBYSN"-->
<!--		serviceClass="com.lenovo.iqs.ibase.resend.service.ZIBPBATCHQUERYBYSN"-->
<!--		username="${ibase.webservice.username}" password="${ibase.webservice.password}"-->
<!--		address="${ibase.webservice.resendUrl}" />-->

<!--	<jaxws:client id="ZIBPBATCHQUERYRESULTBYSN"-->
<!--		serviceClass="com.lenovo.iqs.report.lclaim.ibase.ZIBPBATCHQUERYRESULTBYSN"-->
<!--		address="${ibase.webservice.lclaim-batch-quary}" username="${ibase.webservice.username}"-->
<!--		password="${ibase.webservice.password}" />-->

	<http-conf:conduit name="*.http-conduit">
		<http-conf:client ConnectionTimeout="30000"
			ReceiveTimeout="600000" />
	</http-conf:conduit>
</beans>
