-- Script SQL corregido para crear las tablas principales de IQS
-- TODAS LAS TABLAS TIENEN ID AUTO_INCREMENT COMO CLAVE PRIMARIA
-- Fecha: 2025-01-07

USE iqs_local;

-- =====================================================
-- TABLAS PRINCIPALES PARA EL FUNCIONAMIENTO DE IQS
-- =====================================================

-- 1. Tabla para DatablockSignMapper.xml - White List MASCS
CREATE TABLE IF NOT EXISTS ib_t_white_mascs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    MASC_ID VARCHAR(50),
    ACTIVE_LOCK_CODE VARCHAR(255),
    NWSCP_LOCK_CODE VARCHAR(255),
    SSCP_LOCK_CODE VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_masc_id (MASC_ID)
);

-- 2. Tabla para DatablockSignMapper.xml - White List IMEI
CREATE TABLE IF NOT EXISTS ib_t_white_imei (
    id INT AUTO_INCREMENT PRIMARY KEY,
    SERIAL_NO VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_serial_no (SERIAL_NO)
);

-- 3. Tabla para IbTAccessLogMapper.xml - Log de accesos
CREATE TABLE IF NOT EXISTS ib_t_access_log (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    access_time TIMESTAMP,
    keyword VARCHAR(255),
    access_parameter TEXT,
    class_name VARCHAR(255),
    result TEXT
);

-- 4. Tabla para IbTZuserInfoMapper.xml - Información de usuarios
CREATE TABLE IF NOT EXISTS ib_t_zuser_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    USER_ID VARCHAR(50),
    PASSWORD VARCHAR(255),
    LAST_CHANGE_TIME TIMESTAMP,
    LAST_CHANGE_BY VARCHAR(50),
    ISVALID VARCHAR(1) DEFAULT 'Y',
    USER_TYPE VARCHAR(20),
    SYSTEM VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (USER_ID),
    UNIQUE KEY unique_user_id (USER_ID)
);

-- 5. Tabla para IbTZuserFailLogMapper.xml - Log de fallos de login
CREATE TABLE IF NOT EXISTS ib_t_zuser_fail_log (
    auto_id INT AUTO_INCREMENT PRIMARY KEY,
    ip VARCHAR(45),
    userid VARCHAR(50),
    pw VARCHAR(255),
    access_time TIMESTAMP,
    logtype VARCHAR(20)
);

-- 6. Tabla para IbTZuserLockMapper.xml - Bloqueos de usuarios
CREATE TABLE IF NOT EXISTS ib_t_zuser_lock (
    auto_id INT AUTO_INCREMENT PRIMARY KEY,
    ip VARCHAR(45),
    userid VARCHAR(50),
    lock_time TIMESTAMP,
    unlock_time TIMESTAMP
);

-- 7. Tabla para IbTUpdConfigMapper.xml - Configuraciones del sistema
CREATE TABLE IF NOT EXISTS ib_t_upd_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    SECTION VARCHAR(100),
    config_key VARCHAR(100),
    config_value TEXT,
    value1 TEXT,
    date_ended TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_section_key (SECTION, config_key)
);

-- 8. Tabla para IbTImeiSnMapper.xml - IMEI y Serial Numbers
CREATE TABLE IF NOT EXISTS ib_t_imei_sn (
    id INT AUTO_INCREMENT PRIMARY KEY,
    imei_code VARCHAR(20),
    serial_number VARCHAR(50),
    product_id VARCHAR(50),
    imei_send_datetime TIMESTAMP,
    partition_char VARCHAR(10),
    last_change_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_imei (imei_code),
    INDEX idx_serial (serial_number)
);

-- 9. Tabla para IbTUpdModelMapper.xml - Modelos de dispositivos
CREATE TABLE IF NOT EXISTS ib_t_upd_model (
    id INT AUTO_INCREMENT PRIMARY KEY,
    NAME VARCHAR(100),
    COUNTRY_CODE VARCHAR(10),
    DESCRIPTION TEXT,
    MARKDEL INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_name_country (NAME, COUNTRY_CODE)
);

-- 10. Tabla para IbTUpdCountryMapper.xml - Países
CREATE TABLE IF NOT EXISTS ib_t_upd_country (
    id INT AUTO_INCREMENT PRIMARY KEY,
    COUNTRY_CODE VARCHAR(10),
    DESCRIPTION VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_country_code (COUNTRY_CODE),
    UNIQUE KEY unique_country_code (COUNTRY_CODE)
);

-- =====================================================
-- DATOS INICIALES PARA TESTING
-- =====================================================

-- Insertar usuario de prueba
INSERT IGNORE INTO ib_t_zuser_info (USER_ID, PASSWORD, ISVALID, USER_TYPE, SYSTEM) VALUES
('admin', 'admin123', 'Y', 'ADMIN', 'IQS'),
('test_user', 'test123', 'Y', 'USER', 'IQS');

-- Insertar configuraciones básicas
INSERT IGNORE INTO ib_t_upd_config (SECTION, config_key, config_value) VALUES
('DATABLOCK_SIGN', 'PKI_SERVER_IP', '127.0.0.1'),
('DATABLOCK_SIGN', 'PKI_SERVER_PORT', '8443'),
('DATABLOCK_SIGN', 'FORBIDEN_IMEI', '000000000000000'),
('DATABLOCK_SIGN', 'EXCEPTION_IMEI', '111111111111111'),
('DATABLOCK_SIGN', 'RSD_VALIDATION_FLAGS', 'ENABLED'),
('RSUSERVICE', 'MNO', 'TMO'),
('RSUMODEL_MAP', 'MOTO_G', 'MOTO_G_MAPPED');

-- Insertar datos de prueba para white lists
INSERT IGNORE INTO ib_t_white_mascs (MASC_ID, ACTIVE_LOCK_CODE, NWSCP_LOCK_CODE, SSCP_LOCK_CODE) VALUES
('TEST_MASC_001', 'ACTIVE123', 'NWSCP123', 'SSCP123'),
('TEST_MASC_002', 'ACTIVE456', 'NWSCP456', 'SSCP456'),
('PROD_MASC_001', 'PROD_ACTIVE', 'PROD_NWSCP', 'PROD_SSCP');

INSERT IGNORE INTO ib_t_white_imei (SERIAL_NO) VALUES
('123456789012345'),
('123456789012346'),
('123456789012347'),
('987654321098765'),
('555666777888999');

-- Insertar países de prueba
INSERT IGNORE INTO ib_t_upd_country (COUNTRY_CODE, DESCRIPTION) VALUES
('US', 'United States'),
('MX', 'Mexico'),
('BR', 'Brazil'),
('CA', 'Canada'),
('AR', 'Argentina');

-- Insertar modelos de prueba
INSERT IGNORE INTO ib_t_upd_model (NAME, COUNTRY_CODE, DESCRIPTION) VALUES
('MOTO_G', 'US', 'Motorola G Series'),
('MOTO_E', 'US', 'Motorola E Series'),
('MOTO_X', 'US', 'Motorola X Series'),
('MOTO_G', 'MX', 'Motorola G Series Mexico'),
('MOTO_E', 'MX', 'Motorola E Series Mexico');

COMMIT;

-- =====================================================
-- VERIFICACIÓN
-- =====================================================
SELECT 'Tablas principales IQS creadas exitosamente' as status;

-- Mostrar tablas creadas
SHOW TABLES LIKE 'ib_t_%';

-- Verificar datos insertados
SELECT 'Verificando datos insertados...' as info;
SELECT COUNT(*) as white_mascs_count FROM ib_t_white_mascs;
SELECT COUNT(*) as white_imei_count FROM ib_t_white_imei;
SELECT COUNT(*) as zuser_info_count FROM ib_t_zuser_info;
SELECT COUNT(*) as upd_config_count FROM ib_t_upd_config;
SELECT COUNT(*) as countries_count FROM ib_t_upd_country;
SELECT COUNT(*) as models_count FROM ib_t_upd_model;
