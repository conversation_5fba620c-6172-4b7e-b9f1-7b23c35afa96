<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTBpMasterMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTBpMaster" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="bp_id" property="bpId" jdbcType="VARCHAR" />
    <result column="company_code" property="companyCode" jdbcType="VARCHAR" />
    <result column="first_name" property="firstName" jdbcType="VARCHAR" />
    <result column="last_name" property="lastName" jdbcType="VARCHAR" />
    <result column="street" property="street" jdbcType="VARCHAR" />
    <result column="city" property="city" jdbcType="VARCHAR" />
    <result column="country" property="country" jdbcType="VARCHAR" />
    <result column="telephone" property="telephone" jdbcType="VARCHAR" />
    <result column="postal_code" property="postalCode" jdbcType="VARCHAR" />
    <result column="email" property="email" jdbcType="VARCHAR" />
    <result column="last_change_time" property="lastChangeTime" jdbcType="TIMESTAMP" />
    <result column="preserve1" property="preserve1" jdbcType="VARCHAR" />
    <result column="preserve2" property="preserve2" jdbcType="VARCHAR" />
    <result column="preserve3" property="preserve3" jdbcType="VARCHAR" />
    <result column="house_number" property="houseNumber" jdbcType="VARCHAR" />
    <result column="street2" property="street2" jdbcType="VARCHAR" />
    <result column="street3" property="street3" jdbcType="VARCHAR" />
    <result column="region" property="region" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, bp_id, company_code, first_name, last_name, street, city, country, telephone, 
    postal_code, email, last_change_time, preserve1, preserve2, preserve3, house_number, 
    street2, street3, region
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_bp_master
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from ib_t_bp_master
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTBpMaster" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_bp_master (bp_id, company_code, first_name, 
      last_name, street, city, 
      country, telephone, postal_code, 
      email, last_change_time, preserve1, 
      preserve2, preserve3, house_number, 
      street2, street3, region
      )
    values (#{bpId,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, #{firstName,jdbcType=VARCHAR}, 
      #{lastName,jdbcType=VARCHAR}, #{street,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{country,jdbcType=VARCHAR}, #{telephone,jdbcType=VARCHAR}, #{postalCode,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR}, #{lastChangeTime,jdbcType=TIMESTAMP}, #{preserve1,jdbcType=VARCHAR}, 
      #{preserve2,jdbcType=VARCHAR}, #{preserve3,jdbcType=VARCHAR}, #{houseNumber,jdbcType=VARCHAR}, 
      #{street2,jdbcType=VARCHAR}, #{street3,jdbcType=VARCHAR}, #{region,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTBpMaster" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_bp_master
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="bpId != null" >
        bp_id,
      </if>
      <if test="companyCode != null" >
        company_code,
      </if>
      <if test="firstName != null" >
        first_name,
      </if>
      <if test="lastName != null" >
        last_name,
      </if>
      <if test="street != null" >
        street,
      </if>
      <if test="city != null" >
        city,
      </if>
      <if test="country != null" >
        country,
      </if>
      <if test="telephone != null" >
        telephone,
      </if>
      <if test="postalCode != null" >
        postal_code,
      </if>
      <if test="email != null" >
        email,
      </if>
      <if test="lastChangeTime != null" >
        last_change_time,
      </if>
      <if test="preserve1 != null" >
        preserve1,
      </if>
      <if test="preserve2 != null" >
        preserve2,
      </if>
      <if test="preserve3 != null" >
        preserve3,
      </if>
      <if test="houseNumber != null" >
        house_number,
      </if>
      <if test="street2 != null" >
        street2,
      </if>
      <if test="street3 != null" >
        street3,
      </if>
      <if test="region != null" >
        region,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="bpId != null" >
        #{bpId,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null" >
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="firstName != null" >
        #{firstName,jdbcType=VARCHAR},
      </if>
      <if test="lastName != null" >
        #{lastName,jdbcType=VARCHAR},
      </if>
      <if test="street != null" >
        #{street,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="country != null" >
        #{country,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="postalCode != null" >
        #{postalCode,jdbcType=VARCHAR},
      </if>
      <if test="email != null" >
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="lastChangeTime != null" >
        #{lastChangeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preserve1 != null" >
        #{preserve1,jdbcType=VARCHAR},
      </if>
      <if test="preserve2 != null" >
        #{preserve2,jdbcType=VARCHAR},
      </if>
      <if test="preserve3 != null" >
        #{preserve3,jdbcType=VARCHAR},
      </if>
      <if test="houseNumber != null" >
        #{houseNumber,jdbcType=VARCHAR},
      </if>
      <if test="street2 != null" >
        #{street2,jdbcType=VARCHAR},
      </if>
      <if test="street3 != null" >
        #{street3,jdbcType=VARCHAR},
      </if>
      <if test="region != null" >
        #{region,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTBpMaster" >
    update ib_t_bp_master
    <set >
      <if test="bpId != null" >
        bp_id = #{bpId,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null" >
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="firstName != null" >
        first_name = #{firstName,jdbcType=VARCHAR},
      </if>
      <if test="lastName != null" >
        last_name = #{lastName,jdbcType=VARCHAR},
      </if>
      <if test="street != null" >
        street = #{street,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="country != null" >
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        telephone = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="postalCode != null" >
        postal_code = #{postalCode,jdbcType=VARCHAR},
      </if>
      <if test="email != null" >
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="lastChangeTime != null" >
        last_change_time = #{lastChangeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preserve1 != null" >
        preserve1 = #{preserve1,jdbcType=VARCHAR},
      </if>
      <if test="preserve2 != null" >
        preserve2 = #{preserve2,jdbcType=VARCHAR},
      </if>
      <if test="preserve3 != null" >
        preserve3 = #{preserve3,jdbcType=VARCHAR},
      </if>
      <if test="houseNumber != null" >
        house_number = #{houseNumber,jdbcType=VARCHAR},
      </if>
      <if test="street2 != null" >
        street2 = #{street2,jdbcType=VARCHAR},
      </if>
      <if test="street3 != null" >
        street3 = #{street3,jdbcType=VARCHAR},
      </if>
      <if test="region != null" >
        region = #{region,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTBpMaster" >
    update ib_t_bp_master
    set bp_id = #{bpId,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      first_name = #{firstName,jdbcType=VARCHAR},
      last_name = #{lastName,jdbcType=VARCHAR},
      street = #{street,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      country = #{country,jdbcType=VARCHAR},
      telephone = #{telephone,jdbcType=VARCHAR},
      postal_code = #{postalCode,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      last_change_time = #{lastChangeTime,jdbcType=TIMESTAMP},
      preserve1 = #{preserve1,jdbcType=VARCHAR},
      preserve2 = #{preserve2,jdbcType=VARCHAR},
      preserve3 = #{preserve3,jdbcType=VARCHAR},
      house_number = #{houseNumber,jdbcType=VARCHAR},
      street2 = #{street2,jdbcType=VARCHAR},
      street3 = #{street3,jdbcType=VARCHAR},
      region = #{region,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>