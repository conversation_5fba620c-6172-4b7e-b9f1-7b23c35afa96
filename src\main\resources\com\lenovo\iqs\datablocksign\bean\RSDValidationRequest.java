/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.bean;
/*    */ 
/*    */ import com.fasterxml.jackson.annotation.JsonProperty;
/*    */ import com.fasterxml.jackson.databind.ObjectMapper;
/*    */ import java.io.ByteArrayOutputStream;
/*    */ import java.io.IOException;
/*    */ import java.io.OutputStream;
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class RSDValidationRequest
/*    */   implements Serializable {
/*    */   @JsonProperty("etokenip")
/*    */   private String etokenip;
/*    */   @JsonProperty("publicip")
/*    */   private String publicip;
/*    */   
/*    */   public void setEtokenip(String etokenip) {
/* 18 */     this.etokenip = etokenip; } @JsonProperty("username") private String username; @JsonProperty("requesttype") private String requesttype; @JsonProperty("cid") private String cid; public void setPublicip(String publicip) { this.publicip = publicip; } public void setUsername(String username) { this.username = username; } public void setRequesttype(String requesttype) { this.requesttype = requesttype; } public void setCid(String cid) { this.cid = cid; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.datablocksign.bean.RSDValidationRequest)) return false;  com.lenovo.iqs.datablocksign.bean.RSDValidationRequest other = (com.lenovo.iqs.datablocksign.bean.RSDValidationRequest)o; if (!other.canEqual(this)) return false;  Object this$etokenip = getEtokenip(), other$etokenip = other.getEtokenip(); if ((this$etokenip == null) ? (other$etokenip != null) : !this$etokenip.equals(other$etokenip)) return false;  Object this$publicip = getPublicip(), other$publicip = other.getPublicip(); if ((this$publicip == null) ? (other$publicip != null) : !this$publicip.equals(other$publicip)) return false;  Object this$username = getUsername(), other$username = other.getUsername(); if ((this$username == null) ? (other$username != null) : !this$username.equals(other$username)) return false;  Object this$requesttype = getRequesttype(), other$requesttype = other.getRequesttype(); if ((this$requesttype == null) ? (other$requesttype != null) : !this$requesttype.equals(other$requesttype)) return false;  Object this$cid = getCid(), other$cid = other.getCid(); return !((this$cid == null) ? (other$cid != null) : !this$cid.equals(other$cid)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.datablocksign.bean.RSDValidationRequest; } public int hashCode() { int PRIME = 59; result = 1; Object $etokenip = getEtokenip(); result = result * 59 + (($etokenip == null) ? 43 : $etokenip.hashCode()); Object $publicip = getPublicip(); result = result * 59 + (($publicip == null) ? 43 : $publicip.hashCode()); Object $username = getUsername(); result = result * 59 + (($username == null) ? 43 : $username.hashCode()); Object $requesttype = getRequesttype(); result = result * 59 + (($requesttype == null) ? 43 : $requesttype.hashCode()); Object $cid = getCid(); return result * 59 + (($cid == null) ? 43 : $cid.hashCode()); } public String toString() { return "RSDValidationRequest(etokenip=" + getEtokenip() + ", publicip=" + getPublicip() + ", username=" + getUsername() + ", requesttype=" + getRequesttype() + ", cid=" + getCid() + ")"; }
/*    */   
/*    */   public String getEtokenip() {
/* 21 */     return this.etokenip;
/*    */   } public String getPublicip() {
/* 23 */     return this.publicip;
/*    */   } public String getUsername() {
/* 25 */     return this.username;
/*    */   } public String getRequesttype() {
/* 27 */     return this.requesttype;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getCid() {
/* 34 */     return this.cid;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public RSDValidationRequest(String etokenIP, String publicIP, String userName, String reqType, String cid) {
/* 45 */     this.etokenip = etokenIP;
/* 46 */     this.publicip = publicIP;
/* 47 */     this.username = userName;
/* 48 */     this.requesttype = reqType;
/* 49 */     this.cid = cid;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public RSDValidationRequest() {}
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static void main(String[] args) {
/* 65 */     com.lenovo.iqs.datablocksign.bean.RSDValidationRequest req = new com.lenovo.iqs.datablocksign.bean.RSDValidationRequest();
/* 66 */     req.setEtokenip("*******");
/* 67 */     ObjectMapper mapper = new ObjectMapper();
/* 68 */     OutputStream jos = new ByteArrayOutputStream();
/*    */     try {
/* 70 */       mapper.writeValue(jos, req);
/* 71 */     } catch (IOException e) {
/* 72 */       e.printStackTrace();
/*    */     } 
/* 74 */     System.out.println(jos.toString());
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\bean\RSDValidationRequest.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */