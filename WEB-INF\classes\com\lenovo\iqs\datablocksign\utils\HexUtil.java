/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.utils;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HexUtil
/*    */ {
/*    */   public static String toTwoDigitHexString(byte num) {
/*  8 */     String hex = Integer.toHexString(num);
/*  9 */     return (hex.length() == 1) ? ("0" + hex) : hex;
/*    */   }
/*    */   
/*    */   public static String toTwoDigitHexString(byte[] nums) {
/* 13 */     String hex = "";
/* 14 */     for (int i = 0; i < nums.length; i++) {
/* 15 */       String temp = Integer.toHexString(nums[i] & 0xFF);
/* 16 */       hex = hex + ((temp.length() == 1) ? ("0" + temp) : temp);
/*    */     } 
/* 18 */     return hex;
/*    */   }
/*    */   
/*    */   public static String CalcImeiCRC(String strIMEI) {
/* 22 */     if (strIMEI.length() != 14) {
/* 23 */       return strIMEI;
/*    */     }
/* 25 */     int check_digit = 0;
/* 26 */     int temp_val = 0;
/* 27 */     for (int counter = 0; counter < 14; counter++) {
/* 28 */       if (counter % 2 == 0) {
/* 29 */         check_digit += (new Integer("" + strIMEI.charAt(counter))).intValue();
/*    */       } else {
/*    */         
/* 32 */         temp_val = (new Integer("" + strIMEI.charAt(counter))).intValue() * 2;
/* 33 */         if (temp_val > 9) {
/* 34 */           temp_val -= 10;
/* 35 */           check_digit += 1 + temp_val;
/*    */         } else {
/*    */           
/* 38 */           check_digit += temp_val;
/*    */         } 
/*    */       } 
/*    */     } 
/* 42 */     check_digit %= 10;
/* 43 */     if (check_digit != 0) {
/* 44 */       check_digit = 10 - check_digit;
/*    */     }
/* 46 */     StringBuffer imeiStr = new StringBuffer();
/* 47 */     imeiStr.append(strIMEI);
/* 48 */     imeiStr.append("" + check_digit);
/* 49 */     return imeiStr.toString();
/*    */   }
/*    */   
/*    */   public static byte[] convert2ReverseBCD(String s) {
/* 53 */     byte[] b = null;
/* 54 */     int n = s.length();
/* 55 */     if (!s.equals("0")) {
/* 56 */       b = new byte[n / 2];
/* 57 */       int i = 0;
/* 58 */       for (int j = 0; i < s.length(); j++) {
/* 59 */         char c1 = s.charAt(i + 1);
/* 60 */         int s1 = Character.getNumericValue(c1);
/* 61 */         char c2 = s.charAt(i);
/* 62 */         int s2 = Character.getNumericValue(c2);
/* 63 */         int h = 16 * s1 + s2;
/* 64 */         b[j] = (byte)h;
/* 65 */         i += 2;
/*    */       } 
/*    */     } 
/* 68 */     return b;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksig\\utils\HexUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */