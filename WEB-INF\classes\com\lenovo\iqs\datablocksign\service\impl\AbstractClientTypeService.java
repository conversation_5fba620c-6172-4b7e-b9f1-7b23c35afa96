package WEB-INF.classes.com.lenovo.iqs.datablocksign.service.impl;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.lenovo.iqs.dao.IbTUpdConfigMapper;
import com.lenovo.iqs.datablocksign.Constants.ClientTypeEnum;
import com.lenovo.iqs.datablocksign.bean.ClientResponse;
import com.lenovo.iqs.datablocksign.bean.Config;
import com.lenovo.iqs.datablocksign.bean.DataBlock;
import com.lenovo.iqs.datablocksign.bean.ParseResponse;
import com.lenovo.iqs.datablocksign.bean.PkiNLHTTPSMessage;
import com.lenovo.iqs.datablocksign.bean.PkiNodeLockingResponse;
import com.lenovo.iqs.datablocksign.bean.RSDRules;
import com.lenovo.iqs.datablocksign.bean.RSDValidationRequest;
import com.lenovo.iqs.datablocksign.bean.RSDValidationResponse;
import com.lenovo.iqs.datablocksign.bean.RequestBean;
import com.lenovo.iqs.datablocksign.service.RsdValidationService;
import com.lenovo.iqs.datablocksign.utils.HexUtil;
import com.lenovo.iqs.utils.SpringHelper;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.apache.cxf.common.util.StringUtils;
import org.apache.cxf.message.Message;
import org.apache.cxf.phase.PhaseInterceptorChain;
import org.apache.logging.log4j.LogManager;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractClientTypeService implements ClientTypeService {
  private static final Logger log = LogManager.getLogger(com.lenovo.iqs.datablocksign.service.impl.AbstractClientTypeService.class);

  public static final String REQUEST_PROCESSOR_UID = "request_processor_uid";

  @Autowired
  private PkiConnectionService pkiConnectionService;

  @Autowired
  private IbTUpdConfigMapper ibTUpdConfigMapper;@Autowired
  private SAPRfcs sapRfcs;@Autowired
  private RsdValidationService rsdValidator;
  private static final String BACKUP_PKI_SERVER = "**************";
  private static final String DEFAUL_PKI_SERVER = "***********";

  protected ClientResponse validateReqParam(RequestBean requestBean) {
    String hexCRC = CRCUtils.caculateHexCRC(getBytesWOCRC(requestBean));
    ClientResponse response = null;

    log.info("rb here-->  " + requestBean.toString());

    String clientIP = requestBean.getIstrClientIP();
    String clientType = requestBean.getIstrClientReqType();
    String rsdLogId = requestBean.getIstrRsdLogId();

    String blockRsdEmpty = this.ibTUpdConfigMapper.selectOneRecord("DATABLOCK_SIGN", "BLOCK_RSD_EMPTY");

    if (StringUtils.isEmpty(rsdLogId) && "Y".equalsIgnoreCase(blockRsdEmpty)) {
      response = new ClientResponse();
      String[] error = "7056,RSD_LOGID is empty and configured blocking requeset".split(",");
      response.setIstrStatusCode(error[0]);
      response.setIstrStatusData(error[1]);
      return response;
    }

    if (ClientTypeEnum.isCheckForbidden(clientType)) {
      boolean isPrepaidAllowed;

      try {
        dataBlockParseNew(requestBean, getDatablockVersion(requestBean));
      }
      catch(Exception e) {
        log.error("serial_no : " + requestBean.getIstrOldIMEI() + " pase requestparam error!");
        log.error(e);
        response = new ClientResponse();
        String[] error = "7053,Datablock parsing error".split(",");
        response.setIstrStatusCode(error[0]);
        response.setIstrStatusData(error[1]);
        return response;
      }

      if (StringUtils.isEmpty(requestBean.getDataBlock()) || requestBean.getDataBlock().indexOf("error_code") > -1) {
        response = new ClientResponse();
        String[] error = "7056,datablock in request is invalid".split(",");
        response.setIstrStatusCode(error[0]);
        response.setIstrStatusData(error[1]);
        return response;
      }

      JSONObject jsonObject = JSON.parseObject(requestBean.getDataBlock());
      log.info(requestBean.getDataBlock());
      log.info("DEBUG");
      log.info(requestBean);
      String etokenIP = jsonObject.getString("etoken_ip");
      if (StringUtils.isEmpty(etokenIP) || !etokenIP.equals(requestBean.getIstrClientIP())) {
        String[] error = "7055,etoken missing or etoken in datablock does not match".split(",");
        response = new ClientResponse();
        response.setIstrStatusCode(error[0]);
        response.setIstrStatusData(error[1]);
        return response;
      }

      String userID = jsonObject.getString("UserID");
      if (StringUtils.isEmpty(userID) || !userID.equalsIgnoreCase(requestBean.getUserId())) {
        String[] error = "7062,UserId missing or UserId in datablock does not match".split(",");
        response = new ClientResponse();
        response.setIstrStatusCode(error[0]);
        response.setIstrStatusData(error[1]);
        return response;
      }

      if (StringUtils.isEmpty(requestBean.getIstrMASCID()) || requestBean.getIstrMASCID().length() < 8) {
        String[] error = "7063, Mascid does not meet length requirement".split(",");
        response = new ClientResponse();
        response.setIstrStatusCode(error[0]);
        response.setIstrStatusData(error[1]);
        return response;
      }

      if ("3.4".equalsIgnoreCase(getDatablockVersion(requestBean))) {
        log.info(" dbs version 3.4 block executed");
        if (ClientTypeEnum.IMEI.clientType.equalsIgnoreCase(clientType) || ClientTypeEnum.CID.clientType.equalsIgnoreCase(clientType)) {
          String serialFromDatablock = getSerialNoFromDatablock(requestBean, clientType);
          if (StringUtils.isEmpty(serialFromDatablock)) {
            log.info("DatablockSign validate error: request serial is " + requestBean.getIstrOldIMEI() + "; serial from datablock is " + serialFromDatablock);
            response = new ClientResponse();
            String[] error = "7050, Serial from DataBlock is not match with request serial".split(",");
            response.setIstrStatusCode(error[0]);
            response.setIstrStatusData(error[1]);
            return response;
          }
        }
      } else {
        log.info(" dbs version 3.5 block executed");
        if (ClientTypeEnum.IMEI.clientType.equalsIgnoreCase(clientType) || ClientTypeEnum.CID.clientType.equalsIgnoreCase(clientType)) {
          log.info("parser result before the change --> " + requestBean.getDataBlock());

          String db_array = jsonObject.getString("datablocks");
          log.info(db_array + "--->  data blocks");
          JsonArray jArray = (new JsonParser()).parse(db_array).getAsJsonArray();
          List < String > IMEI_FROM_DB = new ArrayList < >();
          for (int i = 0; i < jArray.size(); i++) {
            JsonObject db = jArray.get(i).getAsJsonObject();
            IMEI_FROM_DB.add(db.get("serial_number").getAsString().substring(1, 15));
          }

          log.info(IMEI_FROM_DB + "--->  IMEI_FROM_DB from blocks");

          String imei_from_Req = requestBean.getIstrOldIMEI().substring(0, 14);
          String new_imei_from_Req = requestBean.getIstrNewIMEI().substring(0, 14);
          log.info(imei_from_Req);
          log.info(new_imei_from_Req);
          boolean db_match = false;
          for (String imei_from_db: IMEI_FROM_DB) {
            if (!db_match) {
              db_match = (imei_from_db.matches(imei_from_Req) || imei_from_db.matches(new_imei_from_Req));
            }
          }

          log.info(db_match + "----> result of imei check");
          if (StringUtils.isEmpty(IMEI_FROM_DB) || !db_match) {
            log.info("new block by genius");
            log.info("DatablockSign validate error: request serial is " + requestBean.getIstrOldIMEI() + "; serial from datablock is " + IMEI_FROM_DB);
            response = new ClientResponse();
            String[] error = "7050, Serial from DataBlock is not match with request serial".split(",");
            response.setIstrStatusCode(error[0]);
            response.setIstrStatusData(error[1]);
            return response;
          }
        }
      }

      String imei = requestBean.getIstrOldIMEI();
      String processorId = jsonObject.getString("request_processor_uid");

      String mappingImei = "-1";
      boolean pidForbidden = true;

      try {
        mappingImei = this.sapRfcs.getIMEIByProcessorId(processorId);
        requestBean.setProcessorIdMappingImei(mappingImei);
        log.info("processsorId: " + processorId + "; MappingIMEI:" + mappingImei);
      } catch(Exception e) {
        log.info("getIMEIByProcessorId error for IMEI:" + imei + " pid: " + processorId);
        log.info("Trying to get IMEI list from the PID");

        try {
          List < String > eImei = new ArrayList < >();
          eImei = this.sapRfcs.getIMEIListByProcessorId(processorId);
          log.info("IMEI from PID : " + eImei);
          if (eImei.size() == 1) {
            mappingImei = eImei.get(0);
          } else if (eImei.size() > 0) {
            log.info("imei : " + imei);
            Iterator < String > iterator = eImei.iterator();
            String tempImei = "";
            while (iterator.hasNext()) {
              tempImei = iterator.next();
              log.info("tempImei : " + tempImei);
              if (tempImei.trim().equalsIgnoreCase(imei.trim())) {
                mappingImei = tempImei;
                break;
              }
            }
          }
          if (mappingImei == "-1" && eImei.size() > 0) {
            Iterator < String > iterator = eImei.iterator();
            String tempImei = "";
            while (iterator.hasNext()) {
              tempImei = iterator.next();
              if (this.ibTUpdConfigMapper.countForbiddenIMEI(tempImei) <= 0 || this.ibTUpdConfigMapper.countExceptionIMEI(tempImei) != 0) {
                log.info(tempImei + " found as non-prepaid or in exception table.");
                pidForbidden = false;
                mappingImei = tempImei;
                break;
              }
            }
          }
          requestBean.setProcessorIdMappingImei(mappingImei);
          log.info("processsorId: " + processorId + "; MappingIMEI:" + mappingImei);
        } catch(Exception ex) {
          log.info("getIMEIByProcessorId error for IMEI:" + imei + " pid: " + processorId);
          log.error("getIMEIByProcessorId error for IMEI:" + imei + " pid: " + processorId, ex);
        }
      }

      if (this.ibTUpdConfigMapper.countBySectionAndKey("DATABLOCK_SIGN", "SKIP_RSD_VALIDATION") == 0) {
        log.info("Switch not found. Using RSD Validation");

        String cid = "";
        if (requestBean.getIstrClientReqType().equalsIgnoreCase("0x02")) {
          try {
            cid = parserOutputReader(requestBean.getDataBlock());
          }
          catch(IOException e) {

            e.printStackTrace();
            log.error("serial_no : " + requestBean.getIstrOldIMEI() + " pase requestparam error!");
            log.error(e);
            response = new ClientResponse();
            String[] error = "7053,Datablock parsing error".split(",");
            response.setIstrStatusCode(error[0]);
            response.setIstrStatusData("NEW RSD Implementation parsing error");
            return response;
          }
        }

        RSDValidationRequest request = buildValidationRequest(etokenIP, requestBean.getPublicIP(), requestBean.getIstrClientReqType(), requestBean.getUserId(), cid);
        log.info(request);

        try {
          RSDValidationResponse rsdresponse = this.rsdValidator.validateRequest(request);
          log.info("validation step completed");
          requestBean.setRsdResponse(rsdresponse.toString());
          List < Config > configs = this.ibTUpdConfigMapper.getConfigRules();
          RSDRules RSDValidation = new RSDRules(configs, rsdresponse);
          if (!RSDValidation.isEtokenAllowed()) {
            log.info("Error while validating request by RSD Etoken not allowed" + imei);
            response = new ClientResponse();
            String[] error = "7058, Etoken Not allowed as per RSD validation".split(",");
            response.setIstrStatusCode(error[0]);
            response.setIstrStatusData(error[1]);
            return response;
          }
          if (!RSDValidation.isPublicIPAllowed()) {
            log.info("Error while validating request by RSD publicip not allowed" + imei);
            response = new ClientResponse();
            String[] error = "7059, publicip Not allowed as per RSD validation".split(",");
            response.setIstrStatusCode(error[0]);
            response.setIstrStatusData(error[1]);
            return response;
          }
          if (!RSDValidation.isUserIdAllowed()) {
            log.info("Error while validating request by RSD userid not allowed" + imei);
            response = new ClientResponse();
            String[] error = "7060, userid Not allowed as per RSD validation".split(",");
            response.setIstrStatusCode(error[0]);
            response.setIstrStatusData(error[1]);
            return response;
          }
          if (!RSDValidation.isReqTypeAllowed()) {
            log.info("Error while validating request by RSD reType not allowed for user/etoken" + imei);
            response = new ClientResponse();
            String[] error = "7061, reqType Not allowed as per RSD validation".split(",");
            response.setIstrStatusCode(error[0]);
            response.setIstrStatusData(error[1]);
            return response;
          }
          isPrepaidAllowed = RSDValidation.isPrepaidAllowed();
        } catch(Exception e) {
          log.error(e.getMessage());
          log.info("Error while validating request by RSD" + imei);
          requestBean.setRsdResponse(e.getMessage());
          response = new ClientResponse();
          String[] error = "7057, RSD Validation service failed with exception".split(",");
          response.setIstrStatusCode(error[0]);
          response.setIstrStatusData(error[1]);
          return response;
        }
      } else {
        log.info("Switch found. Using RSD Validation");
        isPrepaidAllowed = (this.ibTUpdConfigMapper.countBySectionAndKey("EtokenIP", etokenIP) != 0);
        log.info("isPrepaidallowed--> " + isPrepaidAllowed);
      }

      if (!isPrepaidAllowed) {
        if ("-1".equalsIgnoreCase(mappingImei)) {
          String blockMappingIMEI = this.ibTUpdConfigMapper.selectOneRecord("DATABLOCK_SIGN", "BLOCK_MAPPINGIMEI_EMPTY");
          if ("Y".equalsIgnoreCase(blockMappingIMEI)) {
            String[] error = "7054,Did not find PID Mapping IMEI".split(",");
            response = new ClientResponse();
            response.setIstrStatusCode(error[0]);
            response.setIstrStatusData(error[1]);
            return response;
          }
        } else {
          imei = mappingImei;
        }

        if (pidForbidden && this.ibTUpdConfigMapper.countForbiddenIMEI(imei) > 0 && this.ibTUpdConfigMapper.countExceptionIMEI(imei) == 0) {
          log.info("DatablockSign validate error: request serial is " + requestBean.getIstrOldIMEI() + "is forbidden!");
          requestBean.setError("IMEI: " + imei + " in exclude list");
          response = new ClientResponse();
          String[] error = "7051,Serial is Forbidden".split(",");
          response.setIstrStatusCode(error[0]);
          response.setIstrStatusData(error[1]);
          return response;
        }
      } else {
        requestBean.setEtokenInException(true);
      }
    }

    if (!hexCRC.equalsIgnoreCase(requestBean.getIstrCrc32())) {
      response = new ClientResponse();
      String[] error = "8023,CRC Check Failed".split(",");
      response.setIstrStatusCode(error[0]);
      response.setIstrStatusData(error[1]);
      return response;
    }

    if (ClientTypeEnum.IMEI.clientType.equals(clientType) || ClientTypeEnum.SIMLock.clientType.equals(clientType) || ClientTypeEnum.WhiteListSerial.clientType.equals(clientType) || ClientTypeEnum.JanusOrIPRMKey.clientType.equals(clientType)) {
      if (StringUtils.isEmpty(requestBean.getIstrOldIMEI()) || StringUtils.isEmpty(requestBean.getIstrNewIMEI()) || StringUtils.isEmpty(requestBean.getIstrPassChgRequd())) {
        response = new ClientResponse();
        String[] error = "8045,oldIMEI or newIMEI or passChangereq is null".split(",");
        response.setIstrStatusCode(error[0]);
        response.setIstrStatusData(error[1]);
        return response;
      }
    }
    String serialNoType = getSerialNoType(requestBean);
    if (ClientTypeEnum.WhiteListSerial.clientType.equals(clientType)) {
      IbTUpdConfigMapper mapper = (IbTUpdConfigMapper) SpringHelper.getBean(IbTUpdConfigMapper.class);
      int cnt = mapper.countBySectionAndKey("DBS_WHITE_LIST_SN", serialNoType);
      if (cnt == 0) {
        response = new ClientResponse();
        String[] error = "8002,Not a valid Serial Number Type".split(",");
        response.setIstrStatusCode(error[0]);
        response.setIstrStatusData(error[1]);
        return response;
      }
    } else if (!"00".equals(serialNoType) && !"04".equals(serialNoType) && !"05".equals(serialNoType)) {
      response = new ClientResponse();
      String[] error = "8002,Not a valid Serial Number Type".split(",");
      response.setIstrStatusCode(error[0]);
      response.setIstrStatusData(error[1]);
      return response;
    }

    if (!ClientTypeEnum.contains(clientType)) {
      response = new ClientResponse();
      String[] error = "8026,Invalid Data Block Signing Request Type".split(",");
      response.setIstrStatusCode(error[0]);
      response.setIstrStatusData(error[1]);
      return response;
    }

    return null;
  }

  private void dataBlockParseNew(RequestBean requestBean, String dbs_version_no) throws Exception {
    String ret = "";
    Message message = PhaseInterceptorChain.getCurrentMessage();
    HttpServletRequest httpRequest = (HttpServletRequest) message.get("HTTP.REQUEST");

    String path = "";

    log.info("dbs_version_no is -->" + dbs_version_no);
    path = httpRequest.getSession().getServletContext().getRealPath("/WEB-INF/ParseLog_v03.py");
    log.info("ParseLog Path: " + path);
    String base64Input = Base64.getEncoder().encodeToString(requestBean.getIstrReqParam());
    String command = "python2.7 " + path + " " + base64Input;
    log.info("ParseLog command: " + command);
    log.info("ParseLog base64 input: " + base64Input);
    Process p = Runtime.getRuntime().exec(command);
    BufferedReader in =new BufferedReader(new InputStreamReader(p.getInputStream()));
    BufferedReader err = new BufferedReader(new InputStreamReader(p.getErrorStream()));
    String line;
    while ((line = in.readLine()) != null) {
      ret = ret + line;
      ret = ret + System.lineSeparator();
    }
    log.info("ParseLog output: " + ret);

    // Log any errors from Python script
    String errorLine;
    String errors = "";
    while ((errorLine = err.readLine()) != null) {
      errors = errors + errorLine + System.lineSeparator();
    }
    if (!errors.isEmpty()) {
      log.error("ParseLog errors: " + errors);
    }

    requestBean.setDataBlock(ret);
  }

  protected String getSerialNo(RequestBean requestBean) {
    int serialNoiSze = requestBean.getIstrReqParam()[5];
    byte[] byteSerialNoArray = new byte[serialNoiSze];
    System.arraycopy(requestBean.getIstrReqParam(), 6, byteSerialNoArray, 0, serialNoiSze);
    String hexValue = HexUtil.toTwoDigitHexString(byteSerialNoArray);
    String strIMEI = "";
    for (int i = 0; i < hexValue.length() - 2; i += 2) {
      strIMEI = strIMEI + hexValue.charAt(i) + hexValue.charAt(i + 3);
    }
    return HexUtil.CalcImeiCRC(strIMEI);
  }

  protected String getSerialNoFromDatablock(RequestBean requestBean, String clienType) {
    String hexReqParam = Hex.encodeHexString(requestBean.getIstrReqParam());
    String imeiInHex = "";
    if ("0x02".equalsIgnoreCase(clienType)) {
      imeiInHex = hexReqParam.substring(348, 364);
    } else if ("0x00".equalsIgnoreCase(clienType)) {
      imeiInHex = hexReqParam.substring(336, 352);
    }
    String imei = "";
    for (int i = 0; i < imeiInHex.length(); i += 2) {
      char v1 = imeiInHex.charAt(i);
      char v2 = imeiInHex.charAt(i + 1);
      if (i != 0) {
        imei = imei + Character.toString(v2);
      }
      imei = imei + Character.toString(v1);
    }

    String meid = "";
    for (int j = imeiInHex.length() - 4; j >= 0; j -= 4) {
      int end1 = Math.min(j + 2, imeiInHex.length() - 1);
      int end2 = Math.min(j + 4, imeiInHex.length() - 1);
      String s1 = imeiInHex.substring(j, end1);
      String s2 = imeiInHex.substring(j + 2, end2);
      meid = meid.concat(s2);
      meid = meid.concat(s1);
    }

    String requestSerialNo = requestBean.getIstrOldIMEI().substring(0, 14);
    if (requestSerialNo.equalsIgnoreCase(imeiInHex.substring(0, 14))) return imeiInHex;
    if (requestSerialNo.equalsIgnoreCase(imei.substring(0, 14))) return imei;
    if (requestSerialNo.equalsIgnoreCase(meid.substring(1, 15))) {
      return meid;
    }
    return "";
  }

  public byte[] getBytesWOCRC(RequestBean requestBean) {
    String istrClientIP = requestBean.getIstrClientIP();
    String istrMASCID = requestBean.getIstrMASCID();
    String istrClientReqType = requestBean.getIstrClientReqType();
    String istrOldIMEI = requestBean.getIstrOldIMEI();
    String istrNewIMEI = requestBean.getIstrNewIMEI();
    String istrPassChgRequd = requestBean.getIstrPassChgRequd();
    byte[] istrReqParam = requestBean.getIstrReqParam();
    String istrRsdLogId = requestBean.getIstrRsdLogId();

    int rsdLogIdLength = StringUtils.isEmpty(istrRsdLogId) ? 0 : istrRsdLogId.length();

    byte[] byteValues = new byte[istrClientIP.length() + istrMASCID.length() + istrClientReqType.length() + istrOldIMEI.length() + istrNewIMEI.length() + istrPassChgRequd.length() + istrReqParam.length + rsdLogIdLength];

    int destPos = 0;

    System.arraycopy(istrClientIP.getBytes(), 0, byteValues, destPos, (istrClientIP.getBytes()).length);
    destPos = (istrClientIP.getBytes()).length;

    System.arraycopy(istrMASCID.getBytes(), 0, byteValues, destPos, (istrMASCID.getBytes()).length);
    destPos += (istrMASCID.getBytes()).length;

    System.arraycopy(istrClientReqType.getBytes(), 0, byteValues, destPos, (istrClientReqType.getBytes()).length);
    destPos += (istrClientReqType.getBytes()).length;

    System.arraycopy(istrOldIMEI.getBytes(), 0, byteValues, destPos, (istrOldIMEI.getBytes()).length);
    destPos += (istrOldIMEI.getBytes()).length;

    System.arraycopy(istrNewIMEI.getBytes(), 0, byteValues, destPos, (istrNewIMEI.getBytes()).length);
    destPos += (istrNewIMEI.getBytes()).length;

    System.arraycopy(istrPassChgRequd.getBytes(), 0, byteValues, destPos, (istrPassChgRequd.getBytes()).length);
    destPos += (istrPassChgRequd.getBytes()).length;

    if (rsdLogIdLength != 0) {
      System.arraycopy(istrRsdLogId.getBytes(), 0, byteValues, destPos, (istrRsdLogId.getBytes()).length);
      destPos += (istrRsdLogId.getBytes()).length;
    }

    System.arraycopy(istrReqParam, 0, byteValues, destPos, istrReqParam.length);
    destPos += istrReqParam.length;

    return byteValues;
  }

  public String getSerialNoType(RequestBean requestBean) {
    String serialNoType = Integer.toHexString(requestBean.getIstrReqParam()[4]);
    return (serialNoType.length() == 1) ? ("0" + serialNoType) : serialNoType;
  }

  public int getDataBlockType(RequestBean requestBean) {
    byte[] istrReqParam = requestBean.getIstrReqParam();
    String istrClientReqType = requestBean.getIstrClientReqType();

    String protocolVersion = Integer.toHexString(istrReqParam[2]);
    protocolVersion = (protocolVersion.length() == 1) ? ("0" + protocolVersion) : protocolVersion;

    int dataBlockType = 0;
    if (ClientTypeEnum.JanusOrIPRMKey.clientType.equals(istrClientReqType)) {
      int numOfPkiDataTypes = Integer.parseInt("" + istrReqParam[5], 16);
      byte[] pkiTypeArray = null;

      if ("02".equals(protocolVersion)) {
        pkiTypeArray = new byte[32];
        System.arraycopy(istrReqParam, 6, pkiTypeArray, 0, pkiTypeArray.length);
        for (int k = 0, j = 1; k < pkiTypeArray.length && j <= numOfPkiDataTypes; k += 2, j++) {
          String pkiType = HexUtil.toTwoDigitHexString(pkiTypeArray[k]) + HexUtil.toTwoDigitHexString(pkiTypeArray[k + 1]);
          IbTUpdConfigMapper mapper = (IbTUpdConfigMapper) SpringHelper.getBean(IbTUpdConfigMapper.class);
          int cnt = mapper.countBySectionAndKey("DBS_PKI_TYPE", pkiType);
          if (cnt == 0) {
            dataBlockType = 7;
            return dataBlockType;
          }
        }
      }
      if ("03".equals(protocolVersion)) {
        pkiTypeArray = new byte[64];
        System.arraycopy(istrReqParam, 6, pkiTypeArray, 0, pkiTypeArray.length);
        for (int k = 0, j = 1; k < pkiTypeArray.length && j <= numOfPkiDataTypes; k += 4, j++) {
          String pkiType = HexUtil.toTwoDigitHexString(pkiTypeArray[k + 2]) + HexUtil.toTwoDigitHexString(pkiTypeArray[k + 3]);
          IbTUpdConfigMapper mapper = (IbTUpdConfigMapper) SpringHelper.getBean(IbTUpdConfigMapper.class);
          int cnt = mapper.countBySectionAndKey("DBS_PKI_TYPE", pkiType);
          if (cnt == 0) {
            dataBlockType = 7;
            return dataBlockType;
          }
        }
      }
      dataBlockType = 6;
      return dataBlockType;
    }
    byte[] blockTypeByteArray = new byte[2];
    String blockType = "";
    if ("01".equals(protocolVersion)) {
      blockTypeByteArray = Arrays.copyOfRange(istrReqParam, 128, 130);
    } else if ("02".equals(protocolVersion) || "03".equals(protocolVersion)) {
      blockTypeByteArray = Arrays.copyOfRange(istrReqParam, 160, 162);
    }
    for (int i = 0; i < blockTypeByteArray.length; i++) {
      int val = blockTypeByteArray[i] & 0xFF;
      String temp = Integer.toHexString(val);
      blockType = blockType + ((temp.length() == 1) ? ("0" + temp) : temp);
    }
    if (blockType.equalsIgnoreCase("000F")) {
      dataBlockType = 1;
    } else if (blockType.equalsIgnoreCase("0033")) {
      dataBlockType = 2;
    } else if (blockType.equalsIgnoreCase("00F0")) {
      dataBlockType = 3;
    } else if (blockType.equalsIgnoreCase("00A5")) {
      dataBlockType = 4;
    } else if (blockType.equalsIgnoreCase("005A")) {
      dataBlockType = 5;
    } else if (blockType.equalsIgnoreCase("00C3") || blockType.equalsIgnoreCase("0096") || blockType.equalsIgnoreCase("0069")) {
      dataBlockType = 8;
    } else if (blockType.equalsIgnoreCase("030A")) {
      dataBlockType = 9;
    } else if (blockType.equalsIgnoreCase("0305")) {
      dataBlockType = 10;
    } else if (blockType.equalsIgnoreCase("0303")) {
      dataBlockType = 11;
    } else if (blockType.equalsIgnoreCase("0309")) {
      dataBlockType = 12;
    } else if (blockType.equalsIgnoreCase("030C")) {
      dataBlockType = 13;
    } else if (blockType.equalsIgnoreCase("0C03")) {

      dataBlockType = 14;
    }

    return dataBlockType;
  }

  protected ClientResponse callPKIAndProcessResult(RequestBean requestBean) throws Exception {
    ClientResponse clientResponse = new ClientResponse();
    String pkiIp = "***********";
    String datablockSignVersion = getDatablockVersion(requestBean);
    float dbsversion = getDatablockVersionInFloat(requestBean);

    List < String > pkiIps = this.ibTUpdConfigMapper.SelectBySectionAndKey("DATABLOCK_SIGN" + datablockSignVersion, "PKI_SERVER_IP");
    if (!CollectionUtils.isEmpty(pkiIps)) {
      pkiIp = pkiIps.get(0);
    }
    byte[] pkiResponse = new byte[2];
    byte[] tnlResponse = new byte[2];
    log.info("DatablockSign accsee PKI IP: " + pkiIp + " for serialno:" + requestBean.getIstrOldIMEI());
    if (dbsversion <= 3.5D) {
      pkiResponse = this.pkiConnectionService.forwardToPKI(requestBean, pkiIp);
    } else {
      PkiNLHTTPSMessage nlhttpsMessage = this.pkiConnectionService.forwardToNLPKI(requestBean, pkiIp);
      if (nlhttpsMessage.isRequestSuccessful()) {
        log.info("nlrequest is successfull");
        PkiNodeLockingResponse.TnlDbsResponse response = nlhttpsMessage.getResponse().getTnlDbsResponse();

        pkiResponse = Base64.getDecoder().decode(response.getDbsResponseMessage());
        tnlResponse = Base64.getDecoder().decode(response.getTnlResponseMessage());

      }
      else {

        log.error("NL PKI SERVER error");
        String[] error = "8205,NL PKI server connection failure.".split(",");
        clientResponse.setIstrStatusCode(error[0]);
        clientResponse.setIstrStatusData(error[1]);
        return clientResponse;
      }
    }

    log.info("DatablockSign PKI Return result for serialno:" + requestBean.getIstrOldIMEI() + Arrays.toString(pkiResponse));

    if (pkiResponse == null || pkiResponse.length == 0) {
      String[] error = "8105,PKI connection failure.".split(",");
      clientResponse.setIstrStatusCode(error[0]);
      clientResponse.setIstrStatusData(error[1]);
      return clientResponse;
    }
    String clientType = requestBean.getIstrClientReqType();
    byte[] byteStatusArray = new byte[2];
    System.arraycopy(pkiResponse, 4, byteStatusArray, 0, 2);
    String status = HexUtil.toTwoDigitHexString(byteStatusArray);
    log.info(requestBean.getIstrNewIMEI() + "----->" + clientType);
    log.debug("Status of request--> " + status);

    if (ClientTypeEnum.JanusOrIPRMKey.clientType.equals(clientType)) {
      if ("0000".equals(status)) {
        String[] error = "8049,Janus Key/IPRM Programming has been successfully completed".split(",");
        clientResponse.setIstrStatusCode(error[0]);
        clientResponse.setIstrStatusData(error[1]);
        clientResponse.setPkiResponse(pkiResponse);
      } else if ("0001".equals(status)) {
        String[] error = "8048,PKI Reply Failed".split(",");
        clientResponse.setIstrStatusCode(error[0]);
        clientResponse.setIstrStatusData(error[1] + ";" + "PKI_Data_Supply_Depleted");
      } else if ("0005".equals(status)) {
        String[] error = "8048,PKI Reply Failed".split(",");
        clientResponse.setIstrStatusCode(error[0]);
        clientResponse.setIstrStatusData(error[1] + ";" + "Unrecognized_PKI_Data_Type");
      } else if ("000B".equals(status)) {
        String[] error = "8048,PKI Reply Failed".split(",");
        clientResponse.setIstrStatusCode(error[0]);
        clientResponse.setIstrStatusData(error[1] + ";" + "Response_Oversized");
      } else if ("0009".equals(status) || "000A".equals(status)) {
        String[] error = "8048,PKI Reply Failed".split(",");
        clientResponse.setIstrStatusCode(error[0]);
        clientResponse.setIstrStatusData(error[1] + ";" + "RESERVED");
      }

    } else if ("0000".equals(status)) {
      log.info("status here is --->   " + status);
      String[] error = requestBean.isEtokenInException() ? "8022,Data Block Signing has been successfully completed due to exception".split(",") : "8022,Data Block Signing has been successfully completed".split(",");
      clientResponse.setIstrStatusCode(error[0]);
      clientResponse.setIstrStatusData(error[1]);
      clientResponse.setPkiResponse(pkiResponse);
    } else if ("0005".equals(status)) {
      String[] error = "8018,Data Block Signing completed with Error.".split(",");
      clientResponse.setIstrStatusCode(error[0]);
      clientResponse.setIstrStatusData(error[1] + ";" + "Unrecognized_PKI_Data_Type");
    } else if ("000B".equals(status)) {
      String[] error = "8018,Data Block Signing completed with Error.".split(",");
      clientResponse.setIstrStatusCode(error[0]);
      clientResponse.setIstrStatusData(error[1] + ";" + "Selected_Key_Not_Available");
    } else if ("0009".equals(status)) {
      String[] error = "8018,Data Block Signing completed with Error.".split(",");
      clientResponse.setIstrStatusCode(error[0]);
      clientResponse.setIstrStatusData(error[1] + ";" + "UPD_Connect_Error");
    } else if ("000A".equals(status)) {
      String[] error = "8018,Data Block Signing completed with Error.".split(",");
      clientResponse.setIstrStatusCode(error[0]);
      clientResponse.setIstrStatusData(error[1] + ";" + "SBK_Not_Available");
    } else if ("0002".equals(status)) {
      String[] error = "8018,Data Block Signing completed with Error.".split(",");
      clientResponse.setIstrStatusCode(error[0]);
      clientResponse.setIstrStatusData(error[1] + ";" + "CRC_Failed");
    } else if ("0003".equals(status)) {
      String[] error = "8018,Data Block Signing completed with Error.".split(",");
      clientResponse.setIstrStatusCode(error[0]);
      clientResponse.setIstrStatusData(error[1] + ";" + "Protocol_Version_Mismatch");
    } else if ("0004".equals(status)) {
      String[] error = "8018,Data Block Signing completed with Error.".split(",");
      clientResponse.setIstrStatusCode(error[0]);
      clientResponse.setIstrStatusData(error[1] + ";" + "Incorrect_Message_Length");
    } else if ("0006".equals(status)) {
      String[] error = "8018,Data Block Signing completed with Error.".split(",");
      clientResponse.setIstrStatusCode(error[0]);
      clientResponse.setIstrStatusData(error[1] + ";" + "Format_Error");
    } else if ("0007".equals(status)) {
      String[] error = "8018,Data Block Signing completed with Error.".split(",");
      clientResponse.setIstrStatusCode(error[0]);
      clientResponse.setIstrStatusData(error[1] + ";" + "Internal_Error");
    } else if ("0008".equals(status)) {
      String[] error = "8018,Data Block Signing completed with Error.".split(",");
      clientResponse.setIstrStatusCode(error[0]);
      clientResponse.setIstrStatusData(error[1] + ";" + "INVALID_MSG_ID");
    } else if ("000C".equals(status)) {
      String[] error = "8018,Data Block Signing completed with Error.".split(",");
      clientResponse.setIstrStatusCode(error[0]);

      clientResponse.setIstrStatusData(error[1] + ";" + "Not all Datablock types in the request is built for version 3");
    } else {
      log.error("status here is --->   " + status);
      String[] error = requestBean.isEtokenInException() ? "8022,Data Block Signing has been successfully completed due to exception".split(",") : "8022,Data Block Signing has been successfully completed".split(",");

      clientResponse.setPkiResponse(pkiResponse);
      clientResponse.setIstrStatusCode(error[0]);
      clientResponse.setIstrStatusData(error[1]);
    }

    if (dbsversion > 3.5D) {
      clientResponse.setIstrNodeLockingResponse(tnlResponse);
    }
    return clientResponse;
  }

  public String getDatablockVersion(RequestBean requestBean) {
    Message message = PhaseInterceptorChain.getCurrentMessage();
    HttpServletRequest httpRequest = (HttpServletRequest) message.get("HTTP.REQUEST");
    String curretRequestURI = httpRequest.getRequestURI();
    String datablockSignVersion = curretRequestURI.substring(curretRequestURI.lastIndexOf("_") + 1);
    log.info("DatablockSign curretRequestURI: " + curretRequestURI + " for serialno:" + requestBean.getIstrOldIMEI());
    return datablockSignVersion;
  }

  public float getDatablockVersionInFloat(RequestBean requestBean) {
    return Float.parseFloat(getDatablockVersion(requestBean));
  }

  public String generateTranscationId(String mascID) {
    String timePrefix = (new SimpleDateFormat("yyyyMMddHHmmssSS")).format(new Date());
    return timePrefix + "-" + mascID;
  }

  public RSDValidationRequest buildValidationRequest(String eip, String publicip, String reqtype, String username, String cid) {
    return new RSDValidationRequest(eip, publicip, username, reqtype, cid);
  }

  private String parserOutputReader(String res) throws IOException {
    ObjectMapper mapper = new ObjectMapper();

    ParseResponse parseResponse = null;
    try {
      parseResponse = (ParseResponse) mapper.readValue(res, ParseResponse.class);
    } catch(IOException e) {
      e.printStackTrace();
      log.error("Error While parsing json response from RSD");
      log.error(e.getMessage());
      throw e;
    }

    return ((DataBlock) parseResponse.getDatablocks().get(0)).getCustomer_identifier();
  }
}