/*    */ package WEB-INF.classes.com.lenovo.iqs.simlock.bean;
/*    */ public class KSUnlockRequest { private String newIMEI; private String MASCID; private String clientIP; private String clientReqType; private String rsd_log_id;
/*    */   private String pname;
/*    */   
/*  5 */   public void setNewIMEI(String newIMEI) { this.newIMEI = newIMEI; } private String cpuid; private String buildType; private String userId; private String publicIP; private String rsdResponse; private boolean etokenInException; public void setMASCID(String MASCID) { this.MASCID = MASCID; } public void setClientIP(String clientIP) { this.clientIP = clientIP; } public void setClientReqType(String clientReqType) { this.clientReqType = clientReqType; } public void setRsd_log_id(String rsd_log_id) { this.rsd_log_id = rsd_log_id; } public void setPname(String pname) { this.pname = pname; } public void setCpuid(String cpuid) { this.cpuid = cpuid; } public void setBuildType(String buildType) { this.buildType = buildType; } public void setUserId(String userId) { this.userId = userId; } public void setPublicIP(String publicIP) { this.publicIP = publicIP; } public void setRsdResponse(String rsdResponse) { this.rsdResponse = rsdResponse; } public void setEtokenInException(boolean etokenInException) { this.etokenInException = etokenInException; } public void setError(String error) { this.error = error; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.simlock.bean.KSUnlockRequest)) return false;  com.lenovo.iqs.simlock.bean.KSUnlockRequest other = (com.lenovo.iqs.simlock.bean.KSUnlockRequest)o; if (!other.canEqual(this)) return false;  Object this$newIMEI = getNewIMEI(), other$newIMEI = other.getNewIMEI(); if ((this$newIMEI == null) ? (other$newIMEI != null) : !this$newIMEI.equals(other$newIMEI)) return false;  Object this$MASCID = getMASCID(), other$MASCID = other.getMASCID(); if ((this$MASCID == null) ? (other$MASCID != null) : !this$MASCID.equals(other$MASCID)) return false;  Object this$clientIP = getClientIP(), other$clientIP = other.getClientIP(); if ((this$clientIP == null) ? (other$clientIP != null) : !this$clientIP.equals(other$clientIP)) return false;  Object this$clientReqType = getClientReqType(), other$clientReqType = other.getClientReqType(); if ((this$clientReqType == null) ? (other$clientReqType != null) : !this$clientReqType.equals(other$clientReqType)) return false;  Object this$rsd_log_id = getRsd_log_id(), other$rsd_log_id = other.getRsd_log_id(); if ((this$rsd_log_id == null) ? (other$rsd_log_id != null) : !this$rsd_log_id.equals(other$rsd_log_id)) return false;  Object this$pname = getPname(), other$pname = other.getPname(); if ((this$pname == null) ? (other$pname != null) : !this$pname.equals(other$pname)) return false;  Object this$cpuid = getCpuid(), other$cpuid = other.getCpuid(); if ((this$cpuid == null) ? (other$cpuid != null) : !this$cpuid.equals(other$cpuid)) return false;  Object this$buildType = getBuildType(), other$buildType = other.getBuildType(); if ((this$buildType == null) ? (other$buildType != null) : !this$buildType.equals(other$buildType)) return false;  Object this$userId = getUserId(), other$userId = other.getUserId(); if ((this$userId == null) ? (other$userId != null) : !this$userId.equals(other$userId)) return false;  Object this$publicIP = getPublicIP(), other$publicIP = other.getPublicIP(); if ((this$publicIP == null) ? (other$publicIP != null) : !this$publicIP.equals(other$publicIP)) return false;  Object this$rsdResponse = getRsdResponse(), other$rsdResponse = other.getRsdResponse(); if ((this$rsdResponse == null) ? (other$rsdResponse != null) : !this$rsdResponse.equals(other$rsdResponse)) return false;  if (isEtokenInException() != other.isEtokenInException()) return false;  Object this$error = getError(), other$error = other.getError(); return !((this$error == null) ? (other$error != null) : !this$error.equals(other$error)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.simlock.bean.KSUnlockRequest; } public int hashCode() { int PRIME = 59; result = 1; Object $newIMEI = getNewIMEI(); result = result * 59 + (($newIMEI == null) ? 43 : $newIMEI.hashCode()); Object $MASCID = getMASCID(); result = result * 59 + (($MASCID == null) ? 43 : $MASCID.hashCode()); Object $clientIP = getClientIP(); result = result * 59 + (($clientIP == null) ? 43 : $clientIP.hashCode()); Object $clientReqType = getClientReqType(); result = result * 59 + (($clientReqType == null) ? 43 : $clientReqType.hashCode()); Object $rsd_log_id = getRsd_log_id(); result = result * 59 + (($rsd_log_id == null) ? 43 : $rsd_log_id.hashCode()); Object $pname = getPname(); result = result * 59 + (($pname == null) ? 43 : $pname.hashCode()); Object $cpuid = getCpuid(); result = result * 59 + (($cpuid == null) ? 43 : $cpuid.hashCode()); Object $buildType = getBuildType(); result = result * 59 + (($buildType == null) ? 43 : $buildType.hashCode()); Object $userId = getUserId(); result = result * 59 + (($userId == null) ? 43 : $userId.hashCode()); Object $publicIP = getPublicIP(); result = result * 59 + (($publicIP == null) ? 43 : $publicIP.hashCode()); Object $rsdResponse = getRsdResponse(); result = result * 59 + (($rsdResponse == null) ? 43 : $rsdResponse.hashCode()); result = result * 59 + (isEtokenInException() ? 79 : 97); Object $error = getError(); return result * 59 + (($error == null) ? 43 : $error.hashCode()); } public String toString() { return "KSUnlockRequest(newIMEI=" + getNewIMEI() + ", MASCID=" + getMASCID() + ", clientIP=" + getClientIP() + ", clientReqType=" + getClientReqType() + ", rsd_log_id=" + getRsd_log_id() + ", pname=" + getPname() + ", cpuid=" + getCpuid() + ", buildType=" + getBuildType() + ", userId=" + getUserId() + ", publicIP=" + getPublicIP() + ", rsdResponse=" + getRsdResponse() + ", etokenInException=" + isEtokenInException() + ", error=" + getError() + ")"; }
/*    */   
/*  7 */   public String getNewIMEI() { return this.newIMEI; }
/*  8 */   public String getMASCID() { return this.MASCID; }
/*  9 */   public String getClientIP() { return this.clientIP; }
/* 10 */   public String getClientReqType() { return this.clientReqType; } public String getRsd_log_id() {
/* 11 */     return this.rsd_log_id;
/*    */   }
/*    */   
/* 14 */   public String getPname() { return this.pname; }
/* 15 */   public String getCpuid() { return this.cpuid; } public String getBuildType() {
/* 16 */     return this.buildType;
/*    */   }
/*    */   
/*    */   public String getUserId() {
/* 20 */     return this.userId;
/* 21 */   } public String getPublicIP() { return this.publicIP; }
/* 22 */   public String getRsdResponse() { return this.rsdResponse; } public boolean isEtokenInException() {
/* 23 */     return this.etokenInException;
/* 24 */   } private String error = ""; public String getError() { return this.error; }
/*    */    }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\simlock\bean\KSUnlockRequest.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */