<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTWtyReportOutboundErrorMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTWtyReportOutboundError" >
    <result column="wty_id" property="wtyId" jdbcType="VARCHAR" />
    <result column="product_id" property="productId" jdbcType="VARCHAR" />
    <result column="ship_to_country" property="shipToCountry" jdbcType="VARCHAR" />
    <result column="hierarchy" property="hierarchy" jdbcType="VARCHAR" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
    <result column="wty_start_date" property="wtyStartDate" jdbcType="VARCHAR" />
    <result column="wty_dura" property="wtyDura" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="batch_number" property="batchNumber" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List">
    product_id, ship_to_country, hierarchy, quantity, wty_start_date, wty_dura, create_time, batch_number
  </sql>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTWtyReportOutboundError" >
    insert into ib_t_wty_report_outbound (product_id, ship_to_country, hierarchy, 
      quantity, wty_start_date, wty_dura, 
      create_time, batch_number)
    values (#{productId,jdbcType=VARCHAR}, #{shipToCountry,jdbcType=VARCHAR}, #{hierarchy,jdbcType=VARCHAR}, 
      #{quantity,jdbcType=INTEGER}, #{wtyStartDate,jdbcType=VARCHAR}, #{wtyDura,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{batchNumber,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTWtyReportOutboundError" >
    insert into ib_t_wty_report_outbound
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="productId != null" >
        product_id,
      </if>
      <if test="shipToCountry != null" >
        ship_to_country,
      </if>
      <if test="hierarchy != null" >
        hierarchy,
      </if>
      <if test="quantity != null" >
        quantity,
      </if>
      <if test="wtyStartDate != null" >
        wty_start_date,
      </if>
      <if test="wtyDura != null" >
        wty_dura,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="batchNumber != null" >
        batch_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="productId != null" >
        #{productId,jdbcType=VARCHAR},
      </if>
      <if test="shipToCountry != null" >
        #{shipToCountry,jdbcType=VARCHAR},
      </if>
      <if test="hierarchy != null" >
        #{hierarchy,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null" >
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="wtyStartDate != null" >
        #{wtyStartDate,jdbcType=VARCHAR},
      </if>
      <if test="wtyDura != null" >
        #{wtyDura,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNumber != null" >
        #{batchNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>