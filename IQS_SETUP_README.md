# IQS (Integrated Quality System) - Configuración Ubuntu WSL

## 🎉 ¡Configuración Completada!

Su sistema IQS ha sido configurado exitosamente en Ubuntu WSL con los siguientes componentes:

### 📋 Componentes Instalados

- ✅ **Java 8 JDK** (OpenJDK 1.8.0_452)
- ✅ **MySQL Server 8.0** (Base de datos)
- ✅ **Python 3.12** (Para scripts auxiliares)
- ✅ **Base de datos IQS** configurada

### 🗄️ Configuración de Base de Datos

- **Servidor**: localhost:3306
- **Base de datos**: iqs_db
- **Usuario**: iqs_user
- **Contraseña**: iqs_password
- **Codificación**: UTF8MB4

### 🚀 Cómo Iniciar IQS

1. **Navegue al directorio de IQS**:
   ```bash
   cd /mnt/e/Moto_S3_Downloads/2025/moto-rsd-prod-secure/Wars/iqs-0.0.1-SNAPSHOT.war.src
   ```

2. **Ejecute el script de inicio**:
   ```bash
   sudo start-iqs.sh
   ```

3. **Acceda a la aplicación**:
   - URL: http://localhost:8080
   - El sistema se iniciará automáticamente

### 🔧 Comandos Útiles

#### Gestión de MySQL
```bash
# Verificar estado de MySQL
sudo systemctl status mysql

# Iniciar MySQL
sudo systemctl start mysql

# Detener MySQL
sudo systemctl stop mysql

# Conectar a la base de datos
mysql -u iqs_user -piqs_password iqs_db
```

#### Gestión de IQS
```bash
# Iniciar IQS en puerto personalizado
IQS_PORT=8090 sudo start-iqs.sh

# Ver logs del sistema
journalctl -f

# Verificar procesos Java
ps aux | grep java
```

### 📁 Estructura de Archivos

```
/mnt/e/Moto_S3_Downloads/2025/moto-rsd-prod-secure/Wars/iqs-0.0.1-SNAPSHOT.war.src/
├── *.war                    # Archivo principal de IQS
├── WEB-INF/                 # Configuración web
├── META-INF/                # Metadatos
└── IQS_SETUP_README.md      # Esta documentación
```

### 🛠️ Solución de Problemas

#### Error de conexión a base de datos
```bash
# Verificar que MySQL esté ejecutándose
sudo systemctl status mysql

# Reiniciar MySQL si es necesario
sudo systemctl restart mysql

# Verificar conectividad
mysql -u iqs_user -piqs_password -e "SHOW DATABASES;"
```

#### Error de Java
```bash
# Verificar versión de Java
java -version

# Verificar JAVA_HOME
echo $JAVA_HOME
```

#### Puerto ocupado
```bash
# Verificar qué proceso usa el puerto 8080
sudo netstat -tlnp | grep :8080

# Usar puerto alternativo
IQS_PORT=8090 sudo start-iqs.sh
```

### 📞 Información de Soporte

- **Versión de IQS**: 0.0.1-SNAPSHOT
- **Plataforma**: Ubuntu 24.04 LTS (WSL)
- **Java**: OpenJDK 1.8.0_452
- **Base de datos**: MySQL 8.0.42

### 🔐 Credenciales por Defecto

**Base de datos**:
- Usuario: iqs_user
- Contraseña: iqs_password

**Aplicación** (consulte la documentación de IQS para credenciales de aplicación)

---

**Nota**: Este sistema ha sido configurado para desarrollo/pruebas. Para producción, 
asegúrese de cambiar las contraseñas por defecto y configurar medidas de seguridad adicionales.

