<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdModelMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdModel" >
    <id column="NAME" property="name" jdbcType="VARCHAR" />
    <id column="COUNTRY_CODE" property="countryCode" jdbcType="VARCHAR" />
    <result column="DESCRIPTION" property="description" jdbcType="VARCHAR" />
    <result column="MARKDEL" property="markdel" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    NAME, COUNTRY_CODE, DESCRIPTION, MARKDEL
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.lenovo.iqs.entity.IbTUpdModelKey" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_upd_model
    where NAME = #{name,jdbcType=VARCHAR}
      and COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdModelKey" >
    delete from ib_t_upd_model
    where NAME = #{name,jdbcType=VARCHAR}
      and COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdModel" >
    insert into ib_t_upd_model (NAME, COUNTRY_CODE, DESCRIPTION, 
      MARKDEL)
    values (#{name,jdbcType=VARCHAR}, #{countryCode,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{markdel,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdModel" >
    insert into ib_t_upd_model
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="name != null" >
        NAME,
      </if>
      <if test="countryCode != null" >
        COUNTRY_CODE,
      </if>
      <if test="description != null" >
        DESCRIPTION,
      </if>
      <if test="markdel != null" >
        MARKDEL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="countryCode != null" >
        #{countryCode,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="markdel != null" >
        #{markdel,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTUpdModel" >
    update ib_t_upd_model
    <set >
      <if test="description != null" >
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="markdel != null" >
        MARKDEL = #{markdel,jdbcType=INTEGER},
      </if>
    </set>
    where NAME = #{name,jdbcType=VARCHAR}
      and COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdModel" >
    update ib_t_upd_model
    set DESCRIPTION = #{description,jdbcType=VARCHAR},
      MARKDEL = #{markdel,jdbcType=INTEGER}
    where NAME = #{name,jdbcType=VARCHAR}
      and COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
  </update>
</mapper>