package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTUpdSwwarrPeriodRef;
import com.lenovo.iqs.entity.IbTUpdSwwarrPeriodRefKey;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTUpdSwwarrPeriodRefMapper {
  int deleteByPrimaryKey(IbTUpdSwwarrPeriodRefKey paramIbTUpdSwwarrPeriodRefKey);
  
  int insert(IbTUpdSwwarrPeriodRef paramIbTUpdSwwarrPeriodRef);
  
  IbTUpdSwwarrPeriodRef selectByPrimaryKey(IbTUpdSwwarrPeriodRefKey paramIbTUpdSwwarrPeriodRefKey);
  
  int updateByPrimaryKey(IbTUpdSwwarrPeriodRef paramIbTUpdSwwarrPeriodRef);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTUpdSwwarrPeriodRefMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */