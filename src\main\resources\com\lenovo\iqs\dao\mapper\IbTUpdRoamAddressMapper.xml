<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdRoamAddressMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdRoamAddress" >
    <id column="ADDRESS_ID" property="addressId" jdbcType="VARCHAR" />
    <result column="ADDRESS1" property="address1" jdbcType="VARCHAR" />
    <result column="ADDRESS2" property="address2" jdbcType="VARCHAR" />
    <result column="ADDRESS3" property="address3" jdbcType="VARCHAR" />
    <result column="ADDRESS4" property="address4" jdbcType="VARCHAR" />
    <result column="CITY" property="city" jdbcType="VARCHAR" />
    <result column="POSTAL_CODE" property="postalCode" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="VARCHAR" />
    <result column="PROVINCE" property="province" jdbcType="VARCHAR" />
    <result column="CNTY" property="cnty" jdbcType="VARCHAR" />
    <result column="COUNTRY" property="country" jdbcType="VARCHAR" />
    <result column="LAST_UPDATE_DATE" property="lastUpdateDate" jdbcType="DATE" />
    <result column="CUST_NUMBER" property="custNumber" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ADDRESS_ID, ADDRESS1, ADDRESS2, ADDRESS3, ADDRESS4, CITY, POSTAL_CODE, STATE, PROVINCE, 
    CNTY, COUNTRY, LAST_UPDATE_DATE, CUST_NUMBER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_upd_roam_address
    where ADDRESS_ID = #{addressId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from ib_t_upd_roam_address
    where ADDRESS_ID = #{addressId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdRoamAddress" >
    insert into ib_t_upd_roam_address (ADDRESS_ID, ADDRESS1, ADDRESS2, 
      ADDRESS3, ADDRESS4, CITY, 
      POSTAL_CODE, STATE, PROVINCE, 
      CNTY, COUNTRY, LAST_UPDATE_DATE, 
      CUST_NUMBER)
    values (#{addressId,jdbcType=VARCHAR}, #{address1,jdbcType=VARCHAR}, #{address2,jdbcType=VARCHAR}, 
      #{address3,jdbcType=VARCHAR}, #{address4,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{postalCode,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, 
      #{cnty,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=DATE}, 
      #{custNumber,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdRoamAddress" >
    insert into ib_t_upd_roam_address
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="addressId != null" >
        ADDRESS_ID,
      </if>
      <if test="address1 != null" >
        ADDRESS1,
      </if>
      <if test="address2 != null" >
        ADDRESS2,
      </if>
      <if test="address3 != null" >
        ADDRESS3,
      </if>
      <if test="address4 != null" >
        ADDRESS4,
      </if>
      <if test="city != null" >
        CITY,
      </if>
      <if test="postalCode != null" >
        POSTAL_CODE,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="province != null" >
        PROVINCE,
      </if>
      <if test="cnty != null" >
        CNTY,
      </if>
      <if test="country != null" >
        COUNTRY,
      </if>
      <if test="lastUpdateDate != null" >
        LAST_UPDATE_DATE,
      </if>
      <if test="custNumber != null" >
        CUST_NUMBER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="addressId != null" >
        #{addressId,jdbcType=VARCHAR},
      </if>
      <if test="address1 != null" >
        #{address1,jdbcType=VARCHAR},
      </if>
      <if test="address2 != null" >
        #{address2,jdbcType=VARCHAR},
      </if>
      <if test="address3 != null" >
        #{address3,jdbcType=VARCHAR},
      </if>
      <if test="address4 != null" >
        #{address4,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="postalCode != null" >
        #{postalCode,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="cnty != null" >
        #{cnty,jdbcType=VARCHAR},
      </if>
      <if test="country != null" >
        #{country,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        #{lastUpdateDate,jdbcType=DATE},
      </if>
      <if test="custNumber != null" >
        #{custNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTUpdRoamAddress" >
    update ib_t_upd_roam_address
    <set >
      <if test="address1 != null" >
        ADDRESS1 = #{address1,jdbcType=VARCHAR},
      </if>
      <if test="address2 != null" >
        ADDRESS2 = #{address2,jdbcType=VARCHAR},
      </if>
      <if test="address3 != null" >
        ADDRESS3 = #{address3,jdbcType=VARCHAR},
      </if>
      <if test="address4 != null" >
        ADDRESS4 = #{address4,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        CITY = #{city,jdbcType=VARCHAR},
      </if>
      <if test="postalCode != null" >
        POSTAL_CODE = #{postalCode,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        PROVINCE = #{province,jdbcType=VARCHAR},
      </if>
      <if test="cnty != null" >
        CNTY = #{cnty,jdbcType=VARCHAR},
      </if>
      <if test="country != null" >
        COUNTRY = #{country,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        LAST_UPDATE_DATE = #{lastUpdateDate,jdbcType=DATE},
      </if>
      <if test="custNumber != null" >
        CUST_NUMBER = #{custNumber,jdbcType=VARCHAR},
      </if>
    </set>
    where ADDRESS_ID = #{addressId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdRoamAddress" >
    update ib_t_upd_roam_address
    set ADDRESS1 = #{address1,jdbcType=VARCHAR},
      ADDRESS2 = #{address2,jdbcType=VARCHAR},
      ADDRESS3 = #{address3,jdbcType=VARCHAR},
      ADDRESS4 = #{address4,jdbcType=VARCHAR},
      CITY = #{city,jdbcType=VARCHAR},
      POSTAL_CODE = #{postalCode,jdbcType=VARCHAR},
      STATE = #{state,jdbcType=VARCHAR},
      PROVINCE = #{province,jdbcType=VARCHAR},
      CNTY = #{cnty,jdbcType=VARCHAR},
      COUNTRY = #{country,jdbcType=VARCHAR},
      LAST_UPDATE_DATE = #{lastUpdateDate,jdbcType=DATE},
      CUST_NUMBER = #{custNumber,jdbcType=VARCHAR}
    where ADDRESS_ID = #{addressId,jdbcType=VARCHAR}
  </update>
</mapper>