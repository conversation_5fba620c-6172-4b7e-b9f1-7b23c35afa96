<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenovo.iqs.dao.IbTImeiSnMapper">
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTImeiSn">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="imei_code" jdbcType="VARCHAR" property="imeiCode" />
    <result column="serial_number" jdbcType="VARCHAR" property="serialNumber" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="imei_send_datetime" jdbcType="TIMESTAMP" property="imeiSendDatetime" />
    <result column="partition_char" jdbcType="VARCHAR" property="partitionChar" />
    <result column="last_change_time" jdbcType="TIMESTAMP" property="lastChangeTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, imei_code, serial_number, product_id, imei_send_datetime, partition_char, last_change_time
  </sql>
  <select id="selectByImei" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ib_t_imei_sn
    <where>
    	<if test="imeiCode != null and imeiCode != ''" >
        	imei_code = #{imeiCode,jdbcType=VARCHAR}
    	</if>
    	<if test="(imeiCode == null or imeiCode == '')" >
	        1 = 2
    	</if>
    </where>
  </select>
  <select id="selectBySn" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ib_t_imei_sn
    <where>
    <if test="serialNumber != null and serialNumber != ''" >
        serial_number = #{serialNumber,jdbcType=VARCHAR}
    </if>
    <if test="(serialNumber == null or serialNumber == '')" >
        1 = 2
    </if>
    </where>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ib_t_imei_sn
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from ib_t_imei_sn
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTImeiSn">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_imei_sn (imei_code, serial_number, product_id, 
      imei_send_datetime, partition_char, last_change_time
      )
    values (#{imeiCode,jdbcType=VARCHAR}, #{serialNumber,jdbcType=VARCHAR}, #{productId,jdbcType=VARCHAR}, 
      #{imeiSendDatetime,jdbcType=TIMESTAMP}, #{partitionChar,jdbcType=VARCHAR}, #{lastChangeTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTImeiSn">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_imei_sn
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="imeiCode != null">
        imei_code,
      </if>
      <if test="serialNumber != null">
        serial_number,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="imeiSendDatetime != null">
        imei_send_datetime,
      </if>
      <if test="partitionChar != null">
        partition_char,
      </if>
      <if test="lastChangeTime != null">
        last_change_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="imeiCode != null">
        #{imeiCode,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=VARCHAR},
      </if>
      <if test="imeiSendDatetime != null">
        #{imeiSendDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="partitionChar != null">
        #{partitionChar,jdbcType=VARCHAR},
      </if>
      <if test="lastChangeTime != null">
        #{lastChangeTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTImeiSn">
    update ib_t_imei_sn
    <set>
      <if test="imeiCode != null">
        imei_code = #{imeiCode,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        serial_number = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=VARCHAR},
      </if>
      <if test="imeiSendDatetime != null">
        imei_send_datetime = #{imeiSendDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="partitionChar != null">
        partition_char = #{partitionChar,jdbcType=VARCHAR},
      </if>
      <if test="lastChangeTime != null">
        last_change_time = #{lastChangeTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTImeiSn">
    update ib_t_imei_sn
    set imei_code = #{imeiCode,jdbcType=VARCHAR},
      serial_number = #{serialNumber,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=VARCHAR},
      imei_send_datetime = #{imeiSendDatetime,jdbcType=TIMESTAMP},
      partition_char = #{partitionChar,jdbcType=VARCHAR},
      last_change_time = #{lastChangeTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>