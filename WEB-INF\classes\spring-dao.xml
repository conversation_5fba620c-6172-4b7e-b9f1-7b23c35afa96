<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
	http://www.springframework.org/schema/beans 
	http://www.springframework.org/schema/beans/spring-beans-4.0.xsd 
	http://www.springframework.org/schema/tx 
	http://www.springframework.org/schema/tx/spring-tx-4.0.xsd">

	<!-- <bean name="dataSource" class="com.mysql.jdbc.jdbc2.optional.MysqlDataSource"> -->
	<bean name="dataSource" class="com.mysql.cj.jdbc.MysqlDataSource">
		<property name="url" value="${jdbc.url}" />
		<property name="user" value="${jdbc.username}" />
		<property name="password" value="${jdbc.password}" />
	</bean>

	<!--Inbound and Config 数据源 -->
	<bean name="inboundAndConfDataSource" class="com.alibaba.druid.pool.DruidDataSource">
		<property name="driverClassName" value="${config.driverClassName}" />
		<property name="url" value="${config.url}" />
		<property name="username" value="${config.username}" />
		<property name="password" value="${config.password}" />
		<property name="maxActive" value="60" />
		<property name="minIdle" value="10" />
		<property name="validationQuery" value="select 1" />
		<property name="filters" value="stat" />
        <property name="testOnBorrow" value="true" />
	</bean>

	<!--Druid数据源 -->
	<bean name="PCGDataSource" class="com.alibaba.druid.pool.DruidDataSource">
		<property name="driverClassName" value="${pcgjdbc.driverClassName}" />
		<property name="url" value="${pcgjdbc.url}" />
		<property name="username" value="${pcgjdbc.username}" />
        <property name="password" value="${pcgjdbc.password}" />
		<property name="removeAbandoned" value="false" />
		<property name="removeAbandonedTimeout" value="1800" />
		<property name="maxActive" value="3000" />
		<property name="minIdle" value="10" />
		<property name="validationQuery" value="select 1" />
		<property name="filters" value="stat" />
        <property name="testOnBorrow" value="true" />
	</bean>

	<bean id="PCGJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
		<constructor-arg ref="PCGDataSource" />
	</bean>
	
	<bean name="SPPDataSource" class="com.alibaba.druid.pool.DruidDataSource">
		<property name="driverClassName" value="${spp.driverClassName}" />
		<property name="url" value="${spp.url}" />
		<property name="username" value="${spp.username}" />
        <property name="password" value="${spp.password}" />
		<property name="removeAbandoned" value="false" />
		<property name="removeAbandonedTimeout" value="1800" />
		<property name="maxActive" value="3000" />
		<property name="minIdle" value="10" />
		<property name="validationQuery" value="select 1" />
		<property name="filters" value="stat" />
        <property name="testOnBorrow" value="true" />
	</bean>
	
	<bean id="SPPJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
		<constructor-arg ref="SPPDataSource" />
	</bean>

	<bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
		<!-- basePackage指定要扫描的包，在此包之下的映射器都会被搜索到。可指定多个包，包与包之间用逗号或分号分隔 -->
		<property name="basePackage" value="com.lenovo.iqs.**.dao" />
		<!-- 只搜索有Repository注解的接口 -->
		<property name="annotationClass" value="org.springframework.stereotype.Repository" />
	</bean>

	<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
		<property name="configLocation" value="classpath:sqlMapConfig.xml" />
		<property name="dataSource" ref="dataSource" />
		<!-- 只有classpath*的写法能够在jUnit时扫描到MyBatis的Mapper文件 -->
		<property name="mapperLocations"
			value="classpath*:com/lenovo/iqs/**/mapper/*Mapper.xml" />
	</bean>

	<!-- 配置事务管理器 org.springframework.jdbc.datasource.DataSourceTransactionManager -->
	<bean id="transactionManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="dataSource" />
	</bean>

	<bean
		class="org.springframework.transaction.support.DefaultTransactionDefinition">
		<property name="propagationBehaviorName" value="PROPAGATION_REQUIRED" />
	</bean>

	<!-- 注解方式配置事务 -->
	<tx:annotation-driven transaction-manager="transactionManager"
		proxy-target-class="true" />

	<bean class="org.mybatis.spring.SqlSessionTemplate">
		<constructor-arg ref="sqlSessionFactory" />
	</bean>


</beans>