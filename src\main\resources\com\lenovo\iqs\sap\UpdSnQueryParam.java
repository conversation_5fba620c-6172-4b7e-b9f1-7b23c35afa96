/*   */ package WEB-INF.classes.com.lenovo.iqs.sap;
/*   */ public class UpdSnQueryParam {
/*   */   private String serialNo;
/*   */   
/* 5 */   public void setSerialNo(String serialNo) { this.serialNo = serialNo; } private String serialNoType; private String flag; public void setSerialNoType(String serialNoType) { this.serialNoType = serialNoType; } public void setFlag(String flag) { this.flag = flag; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.sap.UpdSnQueryParam)) return false;  com.lenovo.iqs.sap.UpdSnQueryParam other = (com.lenovo.iqs.sap.UpdSnQueryParam)o; if (!other.canEqual(this)) return false;  Object this$serialNo = getSerialNo(), other$serialNo = other.getSerialNo(); if ((this$serialNo == null) ? (other$serialNo != null) : !this$serialNo.equals(other$serialNo)) return false;  Object this$serialNoType = getSerialNoType(), other$serialNoType = other.getSerialNoType(); if ((this$serialNoType == null) ? (other$serialNoType != null) : !this$serialNoType.equals(other$serialNoType)) return false;  Object this$flag = getFlag(), other$flag = other.getFlag(); return !((this$flag == null) ? (other$flag != null) : !this$flag.equals(other$flag)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.sap.UpdSnQueryParam; } public int hashCode() { int PRIME = 59; result = 1; Object $serialNo = getSerialNo(); result = result * 59 + (($serialNo == null) ? 43 : $serialNo.hashCode()); Object $serialNoType = getSerialNoType(); result = result * 59 + (($serialNoType == null) ? 43 : $serialNoType.hashCode()); Object $flag = getFlag(); return result * 59 + (($flag == null) ? 43 : $flag.hashCode()); } public String toString() { return "UpdSnQueryParam(serialNo=" + getSerialNo() + ", serialNoType=" + getSerialNoType() + ", flag=" + getFlag() + ")"; }
/*   */   
/* 7 */   public String getSerialNo() { return this.serialNo; }
/* 8 */   public String getSerialNoType() { return this.serialNoType; } public String getFlag() {
/* 9 */     return this.flag;
/*   */   }
/*   */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\sap\UpdSnQueryParam.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */