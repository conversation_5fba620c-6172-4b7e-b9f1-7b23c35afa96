<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenovo.iqs.dao.IbTImeiQueryLogMapper">
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTImeiQueryLog">
    <id column="query_id" jdbcType="BIGINT" property="queryId" />
    <result column="iobject_found" jdbcType="VARCHAR" property="iobjectFound" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="serial_number" jdbcType="VARCHAR" property="serialNumber" />
    <result column="imei_number" jdbcType="VARCHAR" property="imeiNumber" />
    <result column="exist" jdbcType="VARCHAR" property="exist" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="search_datetime" jdbcType="TIMESTAMP" property="searchDatetime" />
  </resultMap>
  <sql id="Base_Column_List">
    query_id, iobject_found, user_id, serial_number, imei_number, exist, type, search_datetime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ib_t_imei_query_log
    where query_id = #{queryId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ib_t_imei_query_log
    where query_id = #{queryId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTImeiQueryLog">
    <selectKey keyProperty="queryId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_imei_query_log (iobject_found, user_id, serial_number, 
      imei_number, exist, type, 
      search_datetime)
    values (#{iobjectFound,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{serialNumber,jdbcType=VARCHAR}, 
      #{imeiNumber,jdbcType=VARCHAR}, #{exist,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{searchDatetime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTImeiQueryLog">
    <selectKey keyProperty="queryId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_imei_query_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="iobjectFound != null">
        iobject_found,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="serialNumber != null">
        serial_number,
      </if>
      <if test="imeiNumber != null">
        imei_number,
      </if>
      <if test="exist != null">
        exist,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="searchDatetime != null">
        search_datetime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="iobjectFound != null">
        #{iobjectFound,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="imeiNumber != null">
        #{imeiNumber,jdbcType=VARCHAR},
      </if>
      <if test="exist != null">
        #{exist,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="searchDatetime != null">
        #{searchDatetime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTImeiQueryLog">
    update ib_t_imei_query_log
    <set>
      <if test="iobjectFound != null">
        iobject_found = #{iobjectFound,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        serial_number = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="imeiNumber != null">
        imei_number = #{imeiNumber,jdbcType=VARCHAR},
      </if>
      <if test="exist != null">
        exist = #{exist,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="searchDatetime != null">
        search_datetime = #{searchDatetime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where query_id = #{queryId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTImeiQueryLog">
    update ib_t_imei_query_log
    set iobject_found = #{iobjectFound,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      serial_number = #{serialNumber,jdbcType=VARCHAR},
      imei_number = #{imeiNumber,jdbcType=VARCHAR},
      exist = #{exist,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      search_datetime = #{searchDatetime,jdbcType=TIMESTAMP}
    where query_id = #{queryId,jdbcType=BIGINT}
  </update>
</mapper> 