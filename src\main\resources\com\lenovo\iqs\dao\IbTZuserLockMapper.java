package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTZuserLock;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTZuserLockMapper {
  int deleteByPrimaryKey(Integer paramInteger);
  
  int insert(IbTZuserLock paramIbTZuserLock);
  
  IbTZuserLock selectByPrimaryKey(Integer paramInteger);
  
  int updateByPrimaryKey(IbTZuserLock paramIbTZuserLock);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTZuserLockMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */