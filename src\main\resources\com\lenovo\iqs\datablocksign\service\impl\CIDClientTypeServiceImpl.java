/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.service.impl;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.lenovo.iqs.datablocksign.bean.ClientResponse;
/*    */ import com.lenovo.iqs.datablocksign.bean.RequestBean;
/*    */ import com.lenovo.iqs.datablocksign.service.impl.AbstractClientTypeService;
/*    */ import com.lenovo.iqs.sap.SAPRfcs;
/*    */ import org.apache.log4j.Logger;
/*    */ import org.springframework.beans.factory.annotation.Autowired;
/*    */ import org.springframework.stereotype.Service;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Service("ClientType-0x02")
/*    */ public class CIDClientTypeServiceImpl
/*    */   extends AbstractClientTypeService
/*    */ {
/* 19 */   private static final Logger log = Logger.getLogger(com.lenovo.iqs.datablocksign.service.impl.CIDClientTypeServiceImpl.class);
/*    */   
/*    */   @Autowired
/*    */   private SAPRfcs sapRfcs;
/*    */ 
/*    */   
/*    */   public ClientResponse process(RequestBean requestBean) throws Exception {
/* 26 */     String transcationId = generateTranscationId(requestBean.getIstrMASCID());
/* 27 */     log.info("Request Bean before validation - " + requestBean.toString());
/* 28 */     ClientResponse clientResponse = validateReqParam(requestBean);
/* 29 */     log.info("Request Bean after validation - " + requestBean.toString());
/* 30 */     if (clientResponse != null) {
/* 31 */       clientResponse.setIstrTransactionID(transcationId);
/* 32 */       return clientResponse;
/*    */     } 
/*    */     
/* 35 */     int dataBlockType = getDataBlockType(requestBean);
/* 36 */     System.out.println(dataBlockType);
/* 37 */     if (dataBlockType != 3 && dataBlockType != 2 && dataBlockType != 10 && dataBlockType != 14) {
/* 38 */       clientResponse = new ClientResponse();
/* 39 */       clientResponse.setIstrTransactionID(transcationId);
/* 40 */       String[] error = "8026,Invalid Data Block Signing Request Type".split(",");
/* 41 */       clientResponse.setIstrStatusCode(error[0]);
/* 42 */       clientResponse.setIstrStatusData(error[1]);
/* 43 */       return clientResponse;
/*    */     } 
/*    */     
/* 46 */     String oldImei = requestBean.getIstrOldIMEI();
/* 47 */     boolean isOldImeiExist = this.sapRfcs.isSerialExists("IMEI", oldImei, "PART");
/* 48 */     if (!isOldImeiExist) {
/* 49 */       clientResponse = new ClientResponse();
/* 50 */       clientResponse.setIstrTransactionID(transcationId);
/* 51 */       String[] error = "8009,Serial Number not available in IBASE".split(",");
/* 52 */       clientResponse.setIstrStatusCode(error[0]);
/* 53 */       clientResponse.setIstrStatusData(error[1]);
/* 54 */       return clientResponse;
/*    */     } 
/*    */     
/* 57 */     clientResponse = callPKIAndProcessResult(requestBean);
/* 58 */     log.info("CID ClientREpose:" + JSON.toJSONString(clientResponse));
/* 59 */     clientResponse.setIstrTransactionID(transcationId);
/* 60 */     return clientResponse;
/*    */   }
/*    */   
/*    */   public static void main(String[] a) {
/* 64 */     RequestBean rb = new RequestBean();
/* 65 */     rb.setIstrReqParam("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".getBytes());
/* 66 */     rb.setIstrClientIP("***********");
/* 67 */     rb.setIstrMASCID("EMEA-7777777-3705");
/* 68 */     rb.setIstrClientReqType("0x02");
/* 69 */     rb.setIstrOldIMEI("356939300018776");
/* 70 */     rb.setIstrNewIMEI("356939300018776");
/* 71 */     rb.setIstrPassChgRequd("0x00");
/* 72 */     rb.setIstrRsdLogId("964A0E74458C4BE58CDEEA2780904C12");
/* 73 */     com.lenovo.iqs.datablocksign.service.impl.CIDClientTypeServiceImpl cidp = new com.lenovo.iqs.datablocksign.service.impl.CIDClientTypeServiceImpl();
/*    */     try {
/* 75 */       cidp.process(rb);
/* 76 */     } catch (Exception e) {
/* 77 */       e.printStackTrace();
/*    */     } 
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\service\impl\CIDClientTypeServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */