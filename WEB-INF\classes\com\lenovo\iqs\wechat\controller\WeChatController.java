/*    */ package WEB-INF.classes.com.lenovo.iqs.wechat.controller;
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.lenovo.iqs.dao.IbTAccessLogMapper;
/*    */ import com.lenovo.iqs.entity.IbTAccessLog;
/*    */ import com.lenovo.iqs.wechat.bean.WeChatRequest;
/*    */ import com.lenovo.iqs.wechat.bean.WeChatResponse;
/*    */ import com.lenovo.iqs.wechat.service.WeChatService;
/*    */ import java.util.Date;
/*    */ import javax.ws.rs.GET;
/*    */ import javax.ws.rs.POST;
/*    */ import javax.ws.rs.Path;
/*    */ import javax.ws.rs.Produces;
/*    */ import org.apache.cxf.jaxrs.model.wadl.Description;
/*    */ import org.apache.logging.log4j.LogManager;
/*    */ import org.apache.logging.log4j.Logger;
/*    */ import org.springframework.beans.factory.annotation.Autowired;
/*    */ 
/*    */ @Path("/weChat")
/*    */ @Service
/*    */ public class WeChatController {
/* 21 */   private static final Logger log = LogManager.getLogger(com.lenovo.iqs.wechat.controller.WeChatController.class);
/*    */   
/*    */   @Autowired
/*    */   private WeChatService weChatService;
/*    */   
/*    */   @Autowired
/*    */   private IbTAccessLogMapper accessLogMapper;
/*    */ 
/*    */   
/*    */   @POST
/*    */   @GET
/*    */   @Path("/weChatService")
/*    */   @Produces({"application/json"})
/*    */   @Consumes({"application/json"})
/*    */   public WeChatResponse weChatService(@Description("WeChatRequest") WeChatRequest weChatRequest) {
/* 36 */     Date startTime = new Date();
/* 37 */     String errorMsg = "";
/*    */     
/* 39 */     WeChatResponse response = null;
/*    */     
/*    */     try {
/* 42 */       response = this.weChatService.processWeChatRequest(weChatRequest);
/* 43 */     } catch (Exception e) {
/* 44 */       log.error("WeChat webservice error,input serial_no:" + weChatRequest.getSerialNo(), e);
/* 45 */       errorMsg = e.getMessage();
/* 46 */       response = new WeChatResponse();
/* 47 */       response.setResponseCode("8106");
/* 48 */       response.setResponseMsg("Exception while processing the request");
/*    */     } 
/* 50 */     Date endTime = new Date();
/*    */     try {
/* 52 */       IbTAccessLog accessLog = new IbTAccessLog();
/* 53 */       accessLog.setAccessTime(new Date());
/* 54 */       accessLog.setKeyword(weChatRequest.getSerialNo());
/* 55 */       accessLog.setClassName(getClass().getName());
/* 56 */       accessLog.setAccess_parameter(JSON.toJSONString(weChatRequest));
/* 57 */       accessLog.setResult(response.getResponseCode() + "-" + response.getResponseMsg() + "costTime:" + (endTime.getTime() - startTime.getTime()) + "errorMsg:" + ((errorMsg.length() > 300) ? errorMsg.substring(0, 300) : errorMsg));
/*    */       
/* 59 */       this.accessLogMapper.insert(accessLog);
/* 60 */     } catch (Exception e) {
/* 61 */       log.error("WeChat webservice add log error,input serial_no:" + weChatRequest.getSerialNo());
/*    */     } 
/* 63 */     return response;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\wechat\controller\WeChatController.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */