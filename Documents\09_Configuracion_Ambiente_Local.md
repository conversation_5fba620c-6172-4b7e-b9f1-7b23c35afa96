# IQS - Configuración de Ambiente Local para Desarrollo

## 🏠 Descripción General

Esta guía te permitirá recrear completamente el proyecto IQS en tu ambiente local para desarrollo y pruebas, incluyendo todas las dependencias y servicios necesarios.

## 📋 Prerrequisitos

### **Software Base Requerido**
- **Java 8** (JDK 1.8)
- **Apache Maven 3.6+**
- **Apache Tomcat 8.5+**
- **MySQL 8.0+**
- **Python 3.8+**
- **Git**
- **IDE** (IntelliJ IDEA, Eclipse, o VS Code)

### **Herramientas Opcionales**
- **Docker** (para contenedores)
- **MySQL Workbench** (administración DB)
- **Postman** (testing APIs)
- **SoapUI** (testing SOAP services)

## 🗄️ Configuración de Base de Datos Local

### **1. Instalación MySQL**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server mysql-client

# Windows (usando Chocolatey)
choco install mysql

# macOS (usando Homebrew)
brew install mysql
```

### **2. Configuración de Base de Datos**
```sql
-- Crear base de datos principal
CREATE DATABASE iqs_local CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Crear usuario para desarrollo
CREATE USER 'iqs_dev'@'localhost' IDENTIFIED BY 'iqs_dev_password';
GRANT ALL PRIVILEGES ON iqs_local.* TO 'iqs_dev'@'localhost';
FLUSH PRIVILEGES;

-- Usar la base de datos
USE iqs_local;
```

### **3. Crear Tablas Quartz**
```sql
-- Ejecutar el script de tablas Quartz
SOURCE /path/to/project/WEB-INF/classes/sqlscript/tables_mysql.sql;

-- Crear tablas adicionales para IQS
CREATE TABLE devices (
    imei VARCHAR(15) PRIMARY KEY,
    model VARCHAR(50),
    carrier VARCHAR(50),
    simlock_status VARCHAR(20),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE unlock_transactions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    imei VARCHAR(15),
    unlock_code VARCHAR(20),
    status VARCHAR(20),
    request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completion_date TIMESTAMP NULL
);
```

## 🐍 Configuración de Python

### **1. Instalación de Python y Dependencias**
```bash
# Verificar versión de Python
python --version

# Crear ambiente virtual
python -m venv iqs_env

# Activar ambiente virtual
# Windows
iqs_env\Scripts\activate
# Linux/macOS
source iqs_env/bin/activate

# Instalar dependencias
pip install cryptography
pip install requests
pip install python-dateutil
```

### **2. Configurar Scripts Python**
```bash
# Copiar scripts Python a directorio local
mkdir python_scripts
cp WEB-INF/ParseLog*.py python_scripts/
cp WEB-INF/testparser.py python_scripts/

# Hacer scripts ejecutables (Linux/macOS)
chmod +x python_scripts/*.py
```

## ☕ Configuración del Proyecto Java

### **1. Estructura del Proyecto Maven**
```
iqs-local/
├── pom.xml
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/lenovo/iqs/
│   │   ├── resources/
│   │   │   ├── config-local.properties
│   │   │   ├── applicationContext.xml
│   │   │   ├── spring-mvc.xml
│   │   │   ├── spring-dao.xml
│   │   │   └── log4j2-local.xml
│   │   └── webapp/
│   │       └── WEB-INF/
│   │           └── web.xml
│   └── test/
│       └── java/
└── python_scripts/
```

### **2. Configuración Local (config-local.properties)**
```properties
# Base de datos local MySQL
jdbc.driverClassName=com.mysql.cj.jdbc.MysqlDataSource
jdbc.url=*************************************?useSSL=false&serverTimezone=UTC
jdbc.username=iqs_dev
jdbc.password=iqs_dev_password

# Configuración para testing (misma DB)
config.driverClassName=com.mysql.cj.jdbc.MysqlDataSource
config.url=*************************************?useSSL=false&serverTimezone=UTC
config.username=iqs_dev
config.password=iqs_dev_password

# Servicios externos (URLs de testing/mock)
GPS_URL=http://localhost:8080/mock/gps
ibase_url=http://localhost:8080/mock/ibase

# SAP Configuration (Mock para desarrollo local)
ibase.sap.appServerHost=localhost
ibase.sap.systemID=DEV
ibase.sap.client=100
ibase.sap.user=dev_user
ibase.sap.password=dev_password

# Python scripts path
python.scripts.path=/path/to/python_scripts
python.executable=python
```

### **3. POM.xml Simplificado para Local**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.lenovo</groupId>
    <artifactId>iqs-local</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>war</packaging>
    
    <properties>
        <java.version>1.8</java.version>
        <spring.version>4.2.1.RELEASE</spring.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>
    
    <dependencies>
        <!-- Spring Framework -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${spring.version}</version>
        </dependency>
        
        <!-- MySQL Driver -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
        </dependency>
        
        <!-- Druid Connection Pool -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.0.14</version>
        </dependency>
        
        <!-- MyBatis -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.3.0</version>
        </dependency>
        
        <!-- Apache CXF -->
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-frontend-jaxrs</artifactId>
            <version>3.1.3</version>
        </dependency>
        
        <!-- Jackson JSON -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.10.0</version>
        </dependency>
        
        <!-- Log4j2 -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>2.17.1</version>
        </dependency>
        
        <!-- Testing -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.tomcat.maven</groupId>
                <artifactId>tomcat7-maven-plugin</artifactId>
                <version>2.2</version>
                <configuration>
                    <port>8080</port>
                    <path>/iqs</path>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
```

## 🔧 Configuración de Tomcat Local

### **1. Instalación de Tomcat**
```bash
# Descargar Tomcat 8.5
wget https://downloads.apache.org/tomcat/tomcat-8/v8.5.93/bin/apache-tomcat-8.5.93.tar.gz
tar -xzf apache-tomcat-8.5.93.tar.gz
mv apache-tomcat-8.5.93 /opt/tomcat

# O usar Maven plugin (más fácil)
mvn tomcat7:run
```

### **2. Configuración de Tomcat (server.xml)**
```xml
<!-- Agregar DataSource en context.xml -->
<Context>
    <Resource name="jdbc/iqs" 
              auth="Container" 
              type="javax.sql.DataSource"
              maxTotal="100" 
              maxIdle="30" 
              maxWaitMillis="10000"
              username="iqs_dev" 
              password="iqs_dev_password" 
              driverClassName="com.mysql.cj.jdbc.Driver"
              url="*************************************"/>
</Context>
```

## 🧪 Servicios Mock para Testing

### **1. Crear Controlador Mock**
```java
@RestController
@RequestMapping("/mock")
public class MockController {
    
    @PostMapping("/gps")
    public ResponseEntity<String> mockGPS(@RequestBody String request) {
        // Simular respuesta GPS
        return ResponseEntity.ok("{\"status\":\"success\",\"unlockCode\":\"12345678\"}");
    }
    
    @PostMapping("/ibase")
    public ResponseEntity<String> mockIBase(@RequestBody String request) {
        // Simular respuesta iBase
        return ResponseEntity.ok("{\"warranty\":\"valid\",\"model\":\"MOTO_G\"}");
    }
    
    @PostMapping("/sap")
    public ResponseEntity<String> mockSAP(@RequestBody String request) {
        // Simular respuesta SAP
        return ResponseEntity.ok("{\"productInfo\":\"valid\",\"serialNumber\":\"123456\"}");
    }
}
```

### **2. Datos de Prueba**
```sql
-- Insertar datos de prueba
INSERT INTO devices (imei, model, carrier, simlock_status) VALUES
('123456789012345', 'MOTO_G', 'VERIZON', 'LOCKED'),
('123456789012346', 'MOTO_E', 'ATT', 'UNLOCKED'),
('123456789012347', 'MOTO_X', 'TMOBILE', 'LOCKED');

INSERT INTO unlock_transactions (imei, unlock_code, status) VALUES
('123456789012345', '12345678', 'COMPLETED'),
('123456789012346', '87654321', 'PENDING');
```

## 🚀 Comandos para Ejecutar

### **1. Compilar y Ejecutar**
```bash
# Compilar proyecto
mvn clean compile

# Ejecutar tests
mvn test

# Ejecutar con Tomcat embedded
mvn tomcat7:run

# O crear WAR y deployar
mvn clean package
cp target/iqs-local.war /opt/tomcat/webapps/
```

### **2. URLs de Acceso Local**
```
# Aplicación principal
http://localhost:8080/iqs

# Servicios REST
http://localhost:8080/iqs/webservice/rest/

# Monitoreo Druid
http://localhost:8080/iqs/druid/

# Health Check
http://localhost:8080/iqs/health
```

## 🧪 Testing de APIs

### **1. Ejemplos con curl**
```bash
# Test RSU Unlock
curl -X POST http://localhost:8080/iqs/webservice/rest/rsu/unlock \
  -H "Content-Type: application/json" \
  -d '{"imei":"123456789012345","model":"MOTO_G","carrier":"VERIZON"}'

# Test Python Parser
curl -X POST http://localhost:8080/iqs/webservice/rest/parser/parse \
  -H "Content-Type: application/json" \
  -d '{"logData":"base64encodeddata"}'
```

### **2. Postman Collection**
```json
{
  "info": {
    "name": "IQS Local Testing"
  },
  "item": [
    {
      "name": "RSU Unlock",
      "request": {
        "method": "POST",
        "url": "http://localhost:8080/iqs/webservice/rest/rsu/unlock",
        "header": [{"key": "Content-Type", "value": "application/json"}],
        "body": {
          "raw": "{\"imei\":\"123456789012345\",\"model\":\"MOTO_G\"}"
        }
      }
    }
  ]
}
```

## 🐳 Alternativa con Docker

### **1. Dockerfile**
```dockerfile
FROM openjdk:8-jdk-alpine
VOLUME /tmp
COPY target/iqs-local.war app.war
ENTRYPOINT ["java","-jar","/app.war"]
```

### **2. docker-compose.yml**
```yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: iqs_local
      MYSQL_USER: iqs_dev
      MYSQL_PASSWORD: iqs_dev_password
    ports:
      - "3306:3306"
  
  iqs-app:
    build: .
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    environment:
      - SPRING_PROFILES_ACTIVE=docker
```

## 🔍 Troubleshooting Local

### **Problemas Comunes**
1. **Puerto 8080 ocupado**: Cambiar puerto en pom.xml
2. **MySQL connection refused**: Verificar servicio MySQL
3. **Python scripts no encontrados**: Verificar paths en properties
4. **Dependencias faltantes**: Ejecutar `mvn dependency:resolve`

### **Logs de Desarrollo**
```bash
# Ver logs de Tomcat
tail -f /opt/tomcat/logs/catalina.out

# Ver logs de aplicación
tail -f logs/iqs-local.log
```
