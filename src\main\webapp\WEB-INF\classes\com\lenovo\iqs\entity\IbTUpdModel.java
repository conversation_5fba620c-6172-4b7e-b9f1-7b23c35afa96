/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ 
/*    */ public class IbTUpdModel extends IbTUpdModelKey implements Serializable {
/*    */   private String description;
/*    */   
/*  6 */   public void setDescription(String description) { this.description = description; } private Integer markdel; private static final long serialVersionUID = 1L; public void setMarkdel(Integer markdel) { this.markdel = markdel; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdModel)) return false;  com.lenovo.iqs.entity.IbTUpdModel other = (com.lenovo.iqs.entity.IbTUpdModel)o; if (!other.canEqual(this)) return false;  Object this$description = getDescription(), other$description = other.getDescription(); if ((this$description == null) ? (other$description != null) : !this$description.equals(other$description)) return false;  Object this$markdel = getMarkdel(), other$markdel = other.getMarkdel(); return !((this$markdel == null) ? (other$markdel != null) : !this$markdel.equals(other$markdel)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdModel; } public int hashCode() { int PRIME = 59; result = 1; Object $description = getDescription(); result = result * 59 + (($description == null) ? 43 : $description.hashCode()); Object $markdel = getMarkdel(); return result * 59 + (($markdel == null) ? 43 : $markdel.hashCode()); } public String toString() { return "IbTUpdModel(description=" + getDescription() + ", markdel=" + getMarkdel() + ")"; }
/*    */    public String getDescription() {
/*  8 */     return this.description;
/*    */   } public Integer getMarkdel() {
/* 10 */     return this.markdel;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdModel.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */