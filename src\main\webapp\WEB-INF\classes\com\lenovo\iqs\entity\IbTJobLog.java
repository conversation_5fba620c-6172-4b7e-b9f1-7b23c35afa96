/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTJobLog implements Serializable { private Integer errorlogId;
/*    */   private Integer jobstatusId;
/*    */   private String jobId;
/*    */   private String statusType;
/*    */   
/*  7 */   public void setErrorlogId(Integer errorlogId) { this.errorlogId = errorlogId; } private String messageType; private String remark; private Date createTime; private String batchNumber; private static final long serialVersionUID = 1L; public void setJobstatusId(Integer jobstatusId) { this.jobstatusId = jobstatusId; } public void setJobId(String jobId) { this.jobId = jobId; } public void setStatusType(String statusType) { this.statusType = statusType; } public void setMessageType(String messageType) { this.messageType = messageType; } public void setRemark(String remark) { this.remark = remark; } public void setCreateTime(Date createTime) { this.createTime = createTime; } public void setBatchNumber(String batchNumber) { this.batchNumber = batchNumber; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTJobLog)) return false;  com.lenovo.iqs.entity.IbTJobLog other = (com.lenovo.iqs.entity.IbTJobLog)o; if (!other.canEqual(this)) return false;  Object this$errorlogId = getErrorlogId(), other$errorlogId = other.getErrorlogId(); if ((this$errorlogId == null) ? (other$errorlogId != null) : !this$errorlogId.equals(other$errorlogId)) return false;  Object this$jobstatusId = getJobstatusId(), other$jobstatusId = other.getJobstatusId(); if ((this$jobstatusId == null) ? (other$jobstatusId != null) : !this$jobstatusId.equals(other$jobstatusId)) return false;  Object this$jobId = getJobId(), other$jobId = other.getJobId(); if ((this$jobId == null) ? (other$jobId != null) : !this$jobId.equals(other$jobId)) return false;  Object this$statusType = getStatusType(), other$statusType = other.getStatusType(); if ((this$statusType == null) ? (other$statusType != null) : !this$statusType.equals(other$statusType)) return false;  Object this$messageType = getMessageType(), other$messageType = other.getMessageType(); if ((this$messageType == null) ? (other$messageType != null) : !this$messageType.equals(other$messageType)) return false;  Object this$remark = getRemark(), other$remark = other.getRemark(); if ((this$remark == null) ? (other$remark != null) : !this$remark.equals(other$remark)) return false;  Object this$createTime = getCreateTime(), other$createTime = other.getCreateTime(); if ((this$createTime == null) ? (other$createTime != null) : !this$createTime.equals(other$createTime)) return false;  Object this$batchNumber = getBatchNumber(), other$batchNumber = other.getBatchNumber(); return !((this$batchNumber == null) ? (other$batchNumber != null) : !this$batchNumber.equals(other$batchNumber)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTJobLog; } public int hashCode() { int PRIME = 59; result = 1; Object $errorlogId = getErrorlogId(); result = result * 59 + (($errorlogId == null) ? 43 : $errorlogId.hashCode()); Object $jobstatusId = getJobstatusId(); result = result * 59 + (($jobstatusId == null) ? 43 : $jobstatusId.hashCode()); Object $jobId = getJobId(); result = result * 59 + (($jobId == null) ? 43 : $jobId.hashCode()); Object $statusType = getStatusType(); result = result * 59 + (($statusType == null) ? 43 : $statusType.hashCode()); Object $messageType = getMessageType(); result = result * 59 + (($messageType == null) ? 43 : $messageType.hashCode()); Object $remark = getRemark(); result = result * 59 + (($remark == null) ? 43 : $remark.hashCode()); Object $createTime = getCreateTime(); result = result * 59 + (($createTime == null) ? 43 : $createTime.hashCode()); Object $batchNumber = getBatchNumber(); return result * 59 + (($batchNumber == null) ? 43 : $batchNumber.hashCode()); } public String toString() { return "IbTJobLog(errorlogId=" + getErrorlogId() + ", jobstatusId=" + getJobstatusId() + ", jobId=" + getJobId() + ", statusType=" + getStatusType() + ", messageType=" + getMessageType() + ", remark=" + getRemark() + ", createTime=" + getCreateTime() + ", batchNumber=" + getBatchNumber() + ")"; }
/*    */    public Integer getErrorlogId() {
/*  9 */     return this.errorlogId;
/*    */   } public Integer getJobstatusId() {
/* 11 */     return this.jobstatusId;
/*    */   } public String getJobId() {
/* 13 */     return this.jobId;
/*    */   } public String getStatusType() {
/* 15 */     return this.statusType;
/*    */   } public String getMessageType() {
/* 17 */     return this.messageType;
/*    */   } public String getRemark() {
/* 19 */     return this.remark;
/*    */   } public Date getCreateTime() {
/* 21 */     return this.createTime;
/*    */   } public String getBatchNumber() {
/* 23 */     return this.batchNumber;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTJobLog.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */