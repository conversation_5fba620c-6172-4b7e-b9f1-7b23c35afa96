/*    */ package WEB-INF.classes.com.lenovo.iqs.simlock.bean;
/*    */ 
/*    */ public class KeysResponse {
/*    */   private String responseCode;
/*    */   
/*  6 */   public void setResponseCode(String responseCode) { this.responseCode = responseCode; } private String responseMsg; private String certBlob; public void setResponseMsg(String responseMsg) { this.responseMsg = responseMsg; } public void setCertBlob(String certBlob) { this.certBlob = certBlob; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.simlock.bean.KeysResponse)) return false;  com.lenovo.iqs.simlock.bean.KeysResponse other = (com.lenovo.iqs.simlock.bean.KeysResponse)o; if (!other.canEqual(this)) return false;  Object this$responseCode = getResponseCode(), other$responseCode = other.getResponseCode(); if ((this$responseCode == null) ? (other$responseCode != null) : !this$responseCode.equals(other$responseCode)) return false;  Object this$responseMsg = getResponseMsg(), other$responseMsg = other.getResponseMsg(); if ((this$responseMsg == null) ? (other$responseMsg != null) : !this$responseMsg.equals(other$responseMsg)) return false;  Object this$certBlob = getCertBlob(), other$certBlob = other.getCertBlob(); return !((this$certBlob == null) ? (other$certBlob != null) : !this$certBlob.equals(other$certBlob)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.simlock.bean.KeysResponse; } public int hashCode() { int PRIME = 59; result = 1; Object $responseCode = getResponseCode(); result = result * 59 + (($responseCode == null) ? 43 : $responseCode.hashCode()); Object $responseMsg = getResponseMsg(); result = result * 59 + (($responseMsg == null) ? 43 : $responseMsg.hashCode()); Object $certBlob = getCertBlob(); return result * 59 + (($certBlob == null) ? 43 : $certBlob.hashCode()); } public String toString() { return "KeysResponse(responseCode=" + getResponseCode() + ", responseMsg=" + getResponseMsg() + ", certBlob=" + getCertBlob() + ")"; }
/*    */ 
/*    */   
/*  9 */   public String getResponseCode() { return this.responseCode; }
/* 10 */   public String getResponseMsg() { return this.responseMsg; } public String getCertBlob() {
/* 11 */     return this.certBlob;
/*    */   }
/*    */   public static com.lenovo.iqs.simlock.bean.KeysResponse build(String data) throws Exception {
/* 14 */     com.lenovo.iqs.simlock.bean.KeysResponse rsuResponse = new com.lenovo.iqs.simlock.bean.KeysResponse();
/* 15 */     String errorCode = XmlUtils.getContentByTag(data, "errorCode");
/* 16 */     String errorMessage = XmlUtils.getContentByTag(data, "errorMessage");
/* 17 */     String certBlob = XmlUtils.getContentByTag(data, "certBlob");
/* 18 */     if ("0".equalsIgnoreCase(errorCode)) {
/* 19 */       errorMessage = "Success";
/*    */     }
/*    */     
/* 22 */     rsuResponse.setResponseCode(errorCode);
/* 23 */     rsuResponse.setResponseMsg(StringUtils.isEmpty(errorMessage) ? "" : errorMessage);
/* 24 */     rsuResponse.setCertBlob(StringUtils.isEmpty(certBlob) ? "" : certBlob);
/*    */     
/* 26 */     return rsuResponse;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\simlock\bean\KeysResponse.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */