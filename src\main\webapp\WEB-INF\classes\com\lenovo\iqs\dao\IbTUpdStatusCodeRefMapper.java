package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTUpdStatusCodeRef;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTUpdStatusCodeRefMapper {
  int deleteByPrimaryKey(String paramString);
  
  int insert(IbTUpdStatusCodeRef paramIbTUpdStatusCodeRef);
  
  IbTUpdStatusCodeRef selectByPrimaryKey(String paramString);
  
  int updateByPrimaryKey(IbTUpdStatusCodeRef paramIbTUpdStatusCodeRef);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTUpdStatusCodeRefMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */