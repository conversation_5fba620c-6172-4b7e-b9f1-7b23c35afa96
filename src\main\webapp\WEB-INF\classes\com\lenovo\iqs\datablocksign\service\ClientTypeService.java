package WEB-INF.classes.com.lenovo.iqs.datablocksign.service;

import com.lenovo.iqs.datablocksign.bean.ClientResponse;
import com.lenovo.iqs.datablocksign.bean.RequestBean;

public interface ClientTypeService {
  ClientResponse process(RequestBean paramRequestBean) throws Exception;
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\service\ClientTypeService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */