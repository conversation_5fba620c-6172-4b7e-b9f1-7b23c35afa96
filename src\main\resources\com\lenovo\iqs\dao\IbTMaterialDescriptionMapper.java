package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTMaterialDescription;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTMaterialDescriptionMapper {
  int deleteByPrimaryKey(Integer paramInteger);
  
  int insert(IbTMaterialDescription paramIbTMaterialDescription);
  
  IbTMaterialDescription selectByPrimaryKey(Integer paramInteger);
  
  int updateByPrimaryKey(IbTMaterialDescription paramIbTMaterialDescription);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTMaterialDescriptionMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */