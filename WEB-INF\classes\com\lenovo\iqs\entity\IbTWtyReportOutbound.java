/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTWtyReportOutbound implements Serializable { private String wtyId; private String productId;
/*    */   private String shipToCountry;
/*    */   private String hierarchy;
/*    */   private Integer quantity;
/*    */   
/*  7 */   public void setWtyId(String wtyId) { this.wtyId = wtyId; } private String wtyStartDate; private String wtyDura; private Date createTime; private String batchNumber; private static final long serialVersionUID = 1L; public void setProductId(String productId) { this.productId = productId; } public void setShipToCountry(String shipToCountry) { this.shipToCountry = shipToCountry; } public void setHierarchy(String hierarchy) { this.hierarchy = hierarchy; } public void setQuantity(Integer quantity) { this.quantity = quantity; } public void setWtyStartDate(String wtyStartDate) { this.wtyStartDate = wtyStartDate; } public void setWtyDura(String wtyDura) { this.wtyDura = wtyDura; } public void setCreateTime(Date createTime) { this.createTime = createTime; } public void setBatchNumber(String batchNumber) { this.batchNumber = batchNumber; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTWtyReportOutbound)) return false;  com.lenovo.iqs.entity.IbTWtyReportOutbound other = (com.lenovo.iqs.entity.IbTWtyReportOutbound)o; if (!other.canEqual(this)) return false;  Object this$wtyId = getWtyId(), other$wtyId = other.getWtyId(); if ((this$wtyId == null) ? (other$wtyId != null) : !this$wtyId.equals(other$wtyId)) return false;  Object this$productId = getProductId(), other$productId = other.getProductId(); if ((this$productId == null) ? (other$productId != null) : !this$productId.equals(other$productId)) return false;  Object this$shipToCountry = getShipToCountry(), other$shipToCountry = other.getShipToCountry(); if ((this$shipToCountry == null) ? (other$shipToCountry != null) : !this$shipToCountry.equals(other$shipToCountry)) return false;  Object this$hierarchy = getHierarchy(), other$hierarchy = other.getHierarchy(); if ((this$hierarchy == null) ? (other$hierarchy != null) : !this$hierarchy.equals(other$hierarchy)) return false;  Object this$quantity = getQuantity(), other$quantity = other.getQuantity(); if ((this$quantity == null) ? (other$quantity != null) : !this$quantity.equals(other$quantity)) return false;  Object this$wtyStartDate = getWtyStartDate(), other$wtyStartDate = other.getWtyStartDate(); if ((this$wtyStartDate == null) ? (other$wtyStartDate != null) : !this$wtyStartDate.equals(other$wtyStartDate)) return false;  Object this$wtyDura = getWtyDura(), other$wtyDura = other.getWtyDura(); if ((this$wtyDura == null) ? (other$wtyDura != null) : !this$wtyDura.equals(other$wtyDura)) return false;  Object this$createTime = getCreateTime(), other$createTime = other.getCreateTime(); if ((this$createTime == null) ? (other$createTime != null) : !this$createTime.equals(other$createTime)) return false;  Object this$batchNumber = getBatchNumber(), other$batchNumber = other.getBatchNumber(); return !((this$batchNumber == null) ? (other$batchNumber != null) : !this$batchNumber.equals(other$batchNumber)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTWtyReportOutbound; } public int hashCode() { int PRIME = 59; result = 1; Object $wtyId = getWtyId(); result = result * 59 + (($wtyId == null) ? 43 : $wtyId.hashCode()); Object $productId = getProductId(); result = result * 59 + (($productId == null) ? 43 : $productId.hashCode()); Object $shipToCountry = getShipToCountry(); result = result * 59 + (($shipToCountry == null) ? 43 : $shipToCountry.hashCode()); Object $hierarchy = getHierarchy(); result = result * 59 + (($hierarchy == null) ? 43 : $hierarchy.hashCode()); Object $quantity = getQuantity(); result = result * 59 + (($quantity == null) ? 43 : $quantity.hashCode()); Object $wtyStartDate = getWtyStartDate(); result = result * 59 + (($wtyStartDate == null) ? 43 : $wtyStartDate.hashCode()); Object $wtyDura = getWtyDura(); result = result * 59 + (($wtyDura == null) ? 43 : $wtyDura.hashCode()); Object $createTime = getCreateTime(); result = result * 59 + (($createTime == null) ? 43 : $createTime.hashCode()); Object $batchNumber = getBatchNumber(); return result * 59 + (($batchNumber == null) ? 43 : $batchNumber.hashCode()); } public String toString() { return "IbTWtyReportOutbound(wtyId=" + getWtyId() + ", productId=" + getProductId() + ", shipToCountry=" + getShipToCountry() + ", hierarchy=" + getHierarchy() + ", quantity=" + getQuantity() + ", wtyStartDate=" + getWtyStartDate() + ", wtyDura=" + getWtyDura() + ", createTime=" + getCreateTime() + ", batchNumber=" + getBatchNumber() + ")"; }
/*    */   
/*    */   public String getWtyId() {
/* 10 */     return this.wtyId;
/*    */   } public String getProductId() {
/* 12 */     return this.productId;
/*    */   } public String getShipToCountry() {
/* 14 */     return this.shipToCountry;
/*    */   } public String getHierarchy() {
/* 16 */     return this.hierarchy;
/*    */   } public Integer getQuantity() {
/* 18 */     return this.quantity;
/*    */   } public String getWtyStartDate() {
/* 20 */     return this.wtyStartDate;
/*    */   } public String getWtyDura() {
/* 22 */     return this.wtyDura;
/*    */   } public Date getCreateTime() {
/* 24 */     return this.createTime;
/*    */   } public String getBatchNumber() {
/* 26 */     return this.batchNumber;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTWtyReportOutbound.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */