/*     */ package WEB-INF.classes.com.lenovo.iqs.wechat.service.impl;
/*     */ 
/*     */ import com.lenovo.iqs.utils.HttpClientUtils;
/*     */ import com.lenovo.iqs.wechat.bean.WeChatRequest;
/*     */ import com.lenovo.iqs.wechat.bean.WeChatResponse;
/*     */ import com.lenovo.iqs.wechat.service.WeChatService;
/*     */ import com.lenovo.iqs.ws.upd.warranty.util.MeidUtils;
/*     */ import com.lenovo.iqs.ws.upd.warranty.util.SerialValidateTAC;
/*     */ import org.apache.cxf.common.util.StringUtils;
/*     */ import org.springframework.beans.factory.annotation.Value;
/*     */ import org.springframework.stereotype.Service;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Service
/*     */ public class WeChatServiceImpl
/*     */   implements WeChatService
/*     */ {
/*     */   @Value("${GPS_URL}")
/*  21 */   private String gpsWeChatUrl = "";
/*     */   
/*     */   @Value("${GPS_USER}")
/*     */   private String authUser;
/*     */   
/*     */   @Value("${GPS_PWD}")
/*     */   private String authPwd;
/*  28 */   private final String requestParams = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:rsu=\"http://rsu.programmingservice.cfc.nextest.globaltest.motorolamobility.com/\"><soapenv:Header/><soapenv:Body><rsu:SaveWeChatDataForService><trackid>{trackid}</trackid><attkpubkey>{attkpubkey}</attkpubkey><attkpubkeyuid>{attkpubkeyuid}</attkpubkeyuid><attkbrandname>{attkbrandname}</attkbrandname><attkproductmodel>{attkproductmodel}</attkproductmodel><attksecuritylevel>{attksecuritylevel}</attksecuritylevel></rsu:SaveWeChatDataForService></soapenv:Body></soapenv:Envelope>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public WeChatResponse processWeChatRequest(WeChatRequest weChatRequest) throws Exception {
/*  46 */     WeChatResponse weChatResponse = null;
/*     */     
/*  48 */     weChatResponse = validateRequestParam(weChatRequest);
/*  49 */     if (weChatResponse != null) {
/*  50 */       return weChatResponse;
/*     */     }
/*     */     
/*  53 */     String soapParams = buildParams(weChatRequest);
/*  54 */     String response = HttpClientUtils.postWebservice(this.gpsWeChatUrl, soapParams, "", this.authUser, this.authPwd);
/*     */     
/*  56 */     weChatResponse = WeChatResponse.build(response);
/*  57 */     return weChatResponse;
/*     */   }
/*     */   
/*     */   private String buildParams(WeChatRequest weChatRequest) {
/*  61 */     return "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:rsu=\"http://rsu.programmingservice.cfc.nextest.globaltest.motorolamobility.com/\"><soapenv:Header/><soapenv:Body><rsu:SaveWeChatDataForService><trackid>{trackid}</trackid><attkpubkey>{attkpubkey}</attkpubkey><attkpubkeyuid>{attkpubkeyuid}</attkpubkeyuid><attkbrandname>{attkbrandname}</attkbrandname><attkproductmodel>{attkproductmodel}</attkproductmodel><attksecuritylevel>{attksecuritylevel}</attksecuritylevel></rsu:SaveWeChatDataForService></soapenv:Body></soapenv:Envelope>".replace("{trackid}", weChatRequest.getTrackId())
/*  62 */       .replace("{attkpubkey}", weChatRequest.getAttkPubKey())
/*  63 */       .replace("{attkpubkeyuid}", weChatRequest.getDeviceIds())
/*  64 */       .replace("{attkbrandname}", weChatRequest.getBrandName())
/*  65 */       .replace("{attkproductmodel}", weChatRequest.getDeviceModel())
/*  66 */       .replace("{attksecuritylevel}", weChatRequest.getSecurityLevel());
/*     */   }
/*     */ 
/*     */   
/*     */   private WeChatResponse validateRequestParam(WeChatRequest weChatRequest) throws Exception {
/*  71 */     String serialNo = weChatRequest.getSerialNo();
/*  72 */     String serialNoType = weChatRequest.getSerialType();
/*  73 */     WeChatResponse weChatResponse = null;
/*     */     
/*  75 */     if (StringUtils.isEmpty(serialNo)) {
/*  76 */       weChatResponse = new WeChatResponse();
/*  77 */       weChatResponse.setResponseCode("5070");
/*  78 */       weChatResponse.setResponseMsg("Invalid serialNumber");
/*     */     } 
/*  80 */     if (StringUtils.isEmpty(serialNoType)) {
/*  81 */       weChatResponse = new WeChatResponse();
/*  82 */       weChatResponse.setResponseCode("5071");
/*  83 */       weChatResponse.setResponseMsg("Invalid serialNumberType");
/*     */     } 
/*  85 */     if ("IMEI".equalsIgnoreCase(serialNoType) || "MEID".equalsIgnoreCase(serialNoType) || "MSH"
/*  86 */       .equalsIgnoreCase(serialNoType) || "UID".equalsIgnoreCase(serialNoType)) {
/*  87 */       serialNo = validateSerialWithType(serialNo, serialNoType);
/*  88 */       if (StringUtils.isEmpty(serialNo)) {
/*  89 */         weChatResponse = new WeChatResponse();
/*  90 */         weChatResponse.setResponseCode("5070");
/*  91 */         weChatResponse.setResponseMsg("Invalid serialNumber");
/*     */       } else {
/*  93 */         weChatRequest.setSerialNo(serialNo);
/*     */       } 
/*     */     } else {
/*  96 */       weChatResponse = new WeChatResponse();
/*  97 */       weChatResponse.setResponseCode("5077");
/*  98 */       weChatResponse.setResponseMsg("Serial number type should be either MEID or IMEI or MSN or UID");
/*     */     } 
/*     */     
/* 101 */     String deviceModel = weChatRequest.getDeviceModel();
/* 102 */     if (StringUtils.isEmpty(deviceModel)) {
/* 103 */       weChatResponse = new WeChatResponse();
/* 104 */       weChatResponse.setResponseCode("5072");
/* 105 */       weChatResponse.setResponseMsg("Invalid deviceModel");
/*     */     } 
/*     */     
/* 108 */     String deviceId = weChatRequest.getDeviceIds();
/* 109 */     if (StringUtils.isEmpty(deviceId)) {
/* 110 */       weChatResponse = new WeChatResponse();
/* 111 */       weChatResponse.setResponseCode("5073");
/* 112 */       weChatResponse.setResponseMsg("Invalid deviceId");
/*     */     } 
/*     */     
/* 115 */     String securityLevel = weChatRequest.getSecurityLevel();
/* 116 */     if (StringUtils.isEmpty(securityLevel)) {
/* 117 */       weChatResponse = new WeChatResponse();
/* 118 */       weChatResponse.setResponseCode("5074");
/* 119 */       weChatResponse.setResponseMsg("Invalid security level");
/*     */     } 
/*     */     
/* 122 */     String publicKey = weChatRequest.getAttkPubKey();
/* 123 */     if (StringUtils.isEmpty(publicKey)) {
/* 124 */       weChatResponse = new WeChatResponse();
/* 125 */       weChatResponse.setResponseCode("5075");
/* 126 */       weChatResponse.setResponseMsg("Invalid public key");
/*     */     } 
/*     */     
/* 129 */     String brandName = weChatRequest.getBrandName();
/* 130 */     if (StringUtils.isEmpty(brandName)) {
/* 131 */       weChatResponse = new WeChatResponse();
/* 132 */       weChatResponse.setResponseCode("5076");
/* 133 */       weChatResponse.setResponseMsg("Invalid brand name");
/*     */     } 
/*     */     
/* 136 */     return weChatResponse;
/*     */   }
/*     */   
/*     */   private String validateSerialWithType(String serialNo, String serialNoType) throws Exception {
/* 140 */     if (serialNoType.equalsIgnoreCase("MEID")) {
/* 141 */       serialNo = MeidUtils.validateMEID(serialNo);
/*     */     }
/* 143 */     String computeSerialNoType = SerialValidateTAC.getSerialNoType(serialNo);
/* 144 */     if (!computeSerialNoType.equalsIgnoreCase(serialNoType)) {
/* 145 */       return null;
/*     */     }
/* 147 */     return serialNo;
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\wechat\service\impl\WeChatServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */