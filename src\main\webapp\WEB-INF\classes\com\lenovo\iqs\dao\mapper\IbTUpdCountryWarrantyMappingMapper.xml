<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdCountryWarrantyMappingMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdCountryWarrantyMapping" >
    <id column="auto_id" property="autoId" jdbcType="BIGINT" />
    <result column="iso_code" property="isoCode" jdbcType="CHAR" />
    <result column="def_warranty_code" property="defWarrantyCode" jdbcType="CHAR" />
    <result column="last_mod_date" property="lastModDate" jdbcType="TIMESTAMP" />
    <result column="last_mod_by" property="lastModBy" jdbcType="CHAR" />
    <result column="creation_datetime" property="creationDatetime" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="CHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    auto_id, iso_code, def_warranty_code, last_mod_date, last_mod_by, creation_datetime, 
    created_by
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_upd_country_warranty_mapping
    where auto_id = #{autoId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ib_t_upd_country_warranty_mapping
    where auto_id = #{autoId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdCountryWarrantyMapping" >
    insert into ib_t_upd_country_warranty_mapping (auto_id, iso_code, def_warranty_code, 
      last_mod_date, last_mod_by, creation_datetime, 
      created_by)
    values (#{autoId,jdbcType=BIGINT}, #{isoCode,jdbcType=CHAR}, #{defWarrantyCode,jdbcType=CHAR}, 
      #{lastModDate,jdbcType=TIMESTAMP}, #{lastModBy,jdbcType=CHAR}, #{creationDatetime,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdCountryWarrantyMapping" >
    insert into ib_t_upd_country_warranty_mapping
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="autoId != null" >
        auto_id,
      </if>
      <if test="isoCode != null" >
        iso_code,
      </if>
      <if test="defWarrantyCode != null" >
        def_warranty_code,
      </if>
      <if test="lastModDate != null" >
        last_mod_date,
      </if>
      <if test="lastModBy != null" >
        last_mod_by,
      </if>
      <if test="creationDatetime != null" >
        creation_datetime,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="autoId != null" >
        #{autoId,jdbcType=BIGINT},
      </if>
      <if test="isoCode != null" >
        #{isoCode,jdbcType=CHAR},
      </if>
      <if test="defWarrantyCode != null" >
        #{defWarrantyCode,jdbcType=CHAR},
      </if>
      <if test="lastModDate != null" >
        #{lastModDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModBy != null" >
        #{lastModBy,jdbcType=CHAR},
      </if>
      <if test="creationDatetime != null" >
        #{creationDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTUpdCountryWarrantyMapping" >
    update ib_t_upd_country_warranty_mapping
    <set >
      <if test="isoCode != null" >
        iso_code = #{isoCode,jdbcType=CHAR},
      </if>
      <if test="defWarrantyCode != null" >
        def_warranty_code = #{defWarrantyCode,jdbcType=CHAR},
      </if>
      <if test="lastModDate != null" >
        last_mod_date = #{lastModDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModBy != null" >
        last_mod_by = #{lastModBy,jdbcType=CHAR},
      </if>
      <if test="creationDatetime != null" >
        creation_datetime = #{creationDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=CHAR},
      </if>
    </set>
    where auto_id = #{autoId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdCountryWarrantyMapping" >
    update ib_t_upd_country_warranty_mapping
    set iso_code = #{isoCode,jdbcType=CHAR},
      def_warranty_code = #{defWarrantyCode,jdbcType=CHAR},
      last_mod_date = #{lastModDate,jdbcType=TIMESTAMP},
      last_mod_by = #{lastModBy,jdbcType=CHAR},
      creation_datetime = #{creationDatetime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=CHAR}
    where auto_id = #{autoId,jdbcType=BIGINT}
  </update>
</mapper>