/*     */ package WEB-INF.classes.com.lenovo.iqs.ws.upd.warranty.util;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.File;
/*     */ import java.io.FileReader;
/*     */ import java.util.Hashtable;
/*     */ import java.util.StringTokenizer;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AppProps
/*     */ {
/*     */   private static Hashtable htProps;
/*     */   private static Hashtable htYear;
/*     */   private static Hashtable htMonth;
/*     */   private static com.lenovo.iqs.ws.upd.warranty.util.AppProps myApp;
/*     */   
/*     */   private AppProps() {}
/*     */   
/*     */   private AppProps(String file) throws Exception {
/*  37 */     htYear = loadYears();
/*  38 */     htMonth = loadMonths();
/*     */   }
/*     */ 
/*     */   
/*     */   private Hashtable loadYears() throws Exception {
/*  43 */     Hashtable<Object, Object> htyr = new Hashtable<>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  71 */     htyr.put("A", "2000");
/*  72 */     htyr.put("B", "2001");
/*  73 */     htyr.put("C", "2002");
/*  74 */     htyr.put("D", "2003");
/*  75 */     htyr.put("E", "2004");
/*  76 */     htyr.put("F", "2005");
/*  77 */     htyr.put("G", "2006");
/*  78 */     htyr.put("H", "2007");
/*  79 */     htyr.put("J", "2008");
/*  80 */     htyr.put("K", "2008");
/*  81 */     htyr.put("L", "2009");
/*  82 */     htyr.put("M", "2009");
/*  83 */     htyr.put("N", "2010");
/*  84 */     htyr.put("P", "2010");
/*  85 */     htyr.put("Q", "2011");
/*  86 */     htyr.put("R", "2011");
/*  87 */     htyr.put("S", "2012");
/*  88 */     htyr.put("T", "2012");
/*  89 */     htyr.put("V", "2013");
/*  90 */     htyr.put("W", "2013");
/*  91 */     htyr.put("X", "2014");
/*  92 */     htyr.put("Z", "2014");
/*     */     
/*  94 */     return htyr;
/*     */   }
/*     */ 
/*     */   
/*     */   private Hashtable loadMonths() throws Exception {
/*  99 */     Hashtable<Object, Object> htmnth = new Hashtable<>();
/* 100 */     htmnth.put("A", "01");
/* 101 */     htmnth.put("B", "01");
/* 102 */     htmnth.put("C", "02");
/* 103 */     htmnth.put("D", "02");
/* 104 */     htmnth.put("E", "03");
/* 105 */     htmnth.put("F", "03");
/* 106 */     htmnth.put("G", "04");
/* 107 */     htmnth.put("H", "04");
/* 108 */     htmnth.put("J", "05");
/* 109 */     htmnth.put("K", "05");
/* 110 */     htmnth.put("L", "06");
/* 111 */     htmnth.put("M", "06");
/* 112 */     htmnth.put("N", "07");
/* 113 */     htmnth.put("P", "07");
/* 114 */     htmnth.put("Q", "08");
/* 115 */     htmnth.put("R", "08");
/* 116 */     htmnth.put("S", "09");
/* 117 */     htmnth.put("T", "09");
/* 118 */     htmnth.put("U", "10");
/* 119 */     htmnth.put("V", "10");
/* 120 */     htmnth.put("W", "11");
/* 121 */     htmnth.put("X", "11");
/* 122 */     htmnth.put("Y", "12");
/* 123 */     htmnth.put("Z", "12");
/* 124 */     return htmnth;
/*     */   }
/*     */ 
/*     */   
/*     */   private Hashtable loadProperties(String file) throws Exception {
/* 129 */     Hashtable<Object, Object> htOTAProps = new Hashtable<>();
/*     */     
/* 131 */     String canonicalPath = System.getProperty("user.dir");
/* 132 */     canonicalPath = canonicalPath.substring(0, canonicalPath.lastIndexOf(File.separator));
/* 133 */     String path = File.separator + "webapps" + File.separator + "axis" + File.separator + "WEB-INF" + File.separator + "classes" + File.separator + "com" + File.separator + "mot" + File.separator + "pcs" + File.separator + "upd" + File.separator + "conf";
/*     */     
/* 135 */     String readP = canonicalPath + path;
/* 136 */     System.out.println("read Path =" + readP);
/* 137 */     File cfgFile = new File(readP, file);
/* 138 */     BufferedReader br = new BufferedReader(new FileReader(cfgFile));
/* 139 */     String str = br.readLine();
/*     */     
/* 141 */     while (str != null) {
/*     */       
/* 143 */       if (str.startsWith("#")) {
/*     */         
/* 145 */         str = br.readLine();
/*     */         continue;
/*     */       } 
/* 148 */       StringTokenizer strTok = new StringTokenizer(str, "=");
/* 149 */       if (strTok.hasMoreTokens())
/*     */       {
/* 151 */         htOTAProps.put(strTok.nextToken(), strTok.nextToken().trim());
/*     */       }
/* 153 */       str = br.readLine();
/*     */     } 
/* 155 */     br.close();
/* 156 */     return htOTAProps;
/*     */   }
/*     */ 
/*     */   
/*     */   public static Hashtable getProps() {
/* 161 */     return htProps;
/*     */   }
/*     */ 
/*     */   
/*     */   public static String getMonth(String key) {
/* 166 */     return (String)htMonth.get(key);
/*     */   }
/*     */ 
/*     */   
/*     */   public static String getYear(String key) {
/* 171 */     return (String)htYear.get(key);
/*     */   }
/*     */ 
/*     */   
/*     */   public static Hashtable getYears() {
/* 176 */     return htYear;
/*     */   }
/*     */ 
/*     */   
/*     */   public static Hashtable getMonths() {
/* 181 */     return htMonth;
/*     */   }
/*     */ 
/*     */   
/*     */   public static void init(String file) throws Exception {
/* 186 */     if (myApp == null)
/* 187 */       myApp = new com.lenovo.iqs.ws.upd.warranty.util.AppProps(file); 
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\w\\upd\warrant\\util\AppProps.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */