<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTZuserInfoMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTZuserInfo" >
    <id column="USER_ID" property="userId" jdbcType="VARCHAR" />
    <result column="PASSWORD" property="password" jdbcType="VARCHAR" />
    <result column="LAST_CHANGE_TIME" property="lastChangeTime" jdbcType="TIMESTAMP" />
    <result column="LAST_CHANGE_BY" property="lastChangeBy" jdbcType="VARCHAR" />
    <result column="ISVALID" property="isvalid" jdbcType="VARCHAR" />
    <result column="USER_TYPE" property="userType" jdbcType="VARCHAR" />
    <result column="SYSTEM" property="system" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    USER_ID, PASSWORD, LAST_CHANGE_TIME, LAST_CHANGE_BY, ISVALID, USER_TYPE, SYSTEM
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_zuser_info
    where USER_ID = #{userId,jdbcType=VARCHAR}
  </select>
  <select id="selectByUP" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_zuser_info
    where ISVALID &lt;&gt; 'N' AND USER_ID = #{username,jdbcType=VARCHAR} and PASSWORD = #{password,jdbcType=VARCHAR} 
  </select>
  <select id="selectMatchCaseByUP" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_zuser_info
    where ISVALID &lt;&gt; 'N' AND binary USER_ID = #{username,jdbcType=VARCHAR} and binary PASSWORD = #{password,jdbcType=VARCHAR} 
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from ib_t_zuser_info
    where USER_ID = #{userId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTZuserInfo" >
    <selectKey resultType="java.lang.String" keyProperty="userId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_zuser_info (PASSWORD, LAST_CHANGE_TIME, LAST_CHANGE_BY, 
      ISVALID, USER_TYPE, SYSTEM)
    values (#{password,jdbcType=VARCHAR}, #{lastChangeTime,jdbcType=TIMESTAMP}, #{lastChangeBy,jdbcType=VARCHAR}, 
      #{isvalid,jdbcType=VARCHAR}, #{userType,jdbcType=VARCHAR}, #{system,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTZuserInfo" >
    <selectKey resultType="java.lang.String" keyProperty="userId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_zuser_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="password != null" >
        PASSWORD,
      </if>
      <if test="lastChangeTime != null" >
        LAST_CHANGE_TIME,
      </if>
      <if test="lastChangeBy != null" >
        LAST_CHANGE_BY,
      </if>
      <if test="isvalid != null" >
        ISVALID,
      </if>
      <if test="userType != null" >
        USER_TYPE,
      </if>
      <if test="system != null" >
        SYSTEM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="password != null" >
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="lastChangeTime != null" >
        #{lastChangeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastChangeBy != null" >
        #{lastChangeBy,jdbcType=VARCHAR},
      </if>
      <if test="isvalid != null" >
        #{isvalid,jdbcType=VARCHAR},
      </if>
      <if test="userType != null" >
        #{userType,jdbcType=VARCHAR},
      </if>
      <if test="system != null" >
        #{system,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTZuserInfo" >
    update ib_t_zuser_info
    <set >
      <if test="password != null" >
        PASSWORD = #{password,jdbcType=VARCHAR},
      </if>
      <if test="lastChangeTime != null" >
        LAST_CHANGE_TIME = #{lastChangeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastChangeBy != null" >
        LAST_CHANGE_BY = #{lastChangeBy,jdbcType=VARCHAR},
      </if>
      <if test="isvalid != null" >
        ISVALID = #{isvalid,jdbcType=VARCHAR},
      </if>
      <if test="userType != null" >
        USER_TYPE = #{userType,jdbcType=VARCHAR},
      </if>
      <if test="system != null" >
        SYSTEM = #{system,jdbcType=VARCHAR},
      </if>
    </set>
    where USER_ID = #{userId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTZuserInfo" >
    update ib_t_zuser_info
    set PASSWORD = #{password,jdbcType=VARCHAR},
      LAST_CHANGE_TIME = #{lastChangeTime,jdbcType=TIMESTAMP},
      LAST_CHANGE_BY = #{lastChangeBy,jdbcType=VARCHAR},
      ISVALID = #{isvalid,jdbcType=VARCHAR},
      USER_TYPE = #{userType,jdbcType=VARCHAR},
      SYSTEM = #{system,jdbcType=VARCHAR}
    where USER_ID = #{userId,jdbcType=VARCHAR}
  </update>
</mapper>