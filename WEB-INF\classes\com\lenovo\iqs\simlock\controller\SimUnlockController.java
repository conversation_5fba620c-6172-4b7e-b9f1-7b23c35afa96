/*     */ package WEB-INF.classes.com.lenovo.iqs.simlock.controller;
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.lenovo.iqs.entity.IbTAccessLog;
/*     */ import com.lenovo.iqs.simlock.bean.KSUnlockRequest;
/*     */ import com.lenovo.iqs.simlock.bean.KSUnlockResponse;
/*     */ import com.lenovo.iqs.simlock.bean.KeysRequest;
/*     */ import com.lenovo.iqs.simlock.bean.KeysResponse;
/*     */ import com.lenovo.iqs.simlock.bean.SimunlockRequest;
/*     */ import com.lenovo.iqs.simlock.bean.SimunlockResponse;
/*     */ import java.util.Date;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.ws.rs.Consumes;
/*     */ import javax.ws.rs.POST;
/*     */ import javax.ws.rs.Path;
/*     */ import javax.ws.rs.Produces;
/*     */ import org.apache.cxf.common.util.StringUtils;
/*     */ import org.apache.cxf.message.Message;
/*     */ import org.apache.cxf.phase.PhaseInterceptorChain;
/*     */ 
/*     */ @Controller
/*     */ @Path("/simunlock")
/*     */ public class SimUnlockController {
/*  23 */   private static final Logger log = LogManager.getLogger(com.lenovo.iqs.simlock.controller.SimUnlockController.class);
/*     */   
/*     */   @Autowired
/*     */   private SimunlockService unlockService;
/*     */   
/*     */   @Autowired
/*     */   private IbTAccessLogMapper accessLogMapper;
/*     */ 
/*     */   
/*     */   @POST
/*     */   @GET
/*     */   @Path("/callsimunlockservice")
/*     */   @Produces({"application/json"})
/*     */   @Consumes({"application/json"})
/*     */   public SimunlockResponse SubsidyUnlockService(SimunlockRequest rsuRequest) {
/*  38 */     Date startTime = new Date();
/*  39 */     String errorMsg = "";
/*     */     
/*  41 */     SimunlockResponse rsuResponse = null;
/*  42 */     Message message = PhaseInterceptorChain.getCurrentMessage();
/*  43 */     HttpServletRequest httpRequest = (HttpServletRequest)message.get("HTTP.REQUEST");
/*  44 */     String userId = httpRequest.getHeader("coreid");
/*  45 */     String publicIp = httpRequest.getHeader("publicIP");
/*  46 */     log.info("userId --> " + userId);
/*  47 */     log.info("publicip --> " + publicIp);
/*  48 */     rsuRequest.setUserId(StringUtils.isEmpty(userId) ? "" : userId);
/*  49 */     rsuRequest.setPublicIP(StringUtils.isEmpty(publicIp) ? "" : publicIp);
/*     */     
/*     */     try {
/*  52 */       rsuResponse = this.unlockService.processRsuRequest(rsuRequest, userId, publicIp);
/*  53 */     } catch (Exception e) {
/*  54 */       log.error("simunlock webservice error,input serial_no:" + rsuRequest.getNewIMEI(), e);
/*  55 */       errorMsg = e.getMessage();
/*  56 */       rsuResponse = new SimunlockResponse();
/*  57 */       rsuResponse.setResponseCode("8106");
/*  58 */       rsuResponse.setResponseMsg("Exception while processing the request");
/*     */     } 
/*  60 */     Date endTime = new Date();
/*     */     
/*     */     try {
/*  63 */       IbTAccessLog accessLog = new IbTAccessLog();
/*  64 */       accessLog.setAccessTime(new Date());
/*  65 */       accessLog.setKeyword(rsuRequest.getNewIMEI());
/*  66 */       accessLog.setClassName(getClass().getName() + ".subsidyunlock");
/*  67 */       accessLog.setAccess_parameter(JSON.toJSONString(rsuRequest));
/*  68 */       accessLog.setResult(rsuResponse.getResponseCode() + "-" + rsuResponse.getResponseMsg() + "costTime:" + (endTime.getTime() - startTime.getTime()) + ";errorMsg:" + ((errorMsg.length() > 300) ? errorMsg.substring(0, 300) : errorMsg));
/*  69 */       this.accessLogMapper.insert(accessLog);
/*  70 */     } catch (Exception e) {
/*  71 */       log.error("simunlock webservice add log error, input serial_no:" + rsuRequest.getNewIMEI(), e);
/*     */     } 
/*     */     
/*  74 */     return rsuResponse;
/*     */   }
/*     */ 
/*     */   
/*     */   @POST
/*     */   @Path("/dispatchkeys")
/*     */   @Produces({"application/json"})
/*     */   @Consumes({"application/json"})
/*     */   public KeysResponse dispatchKeys(KeysRequest rsuRequest) {
/*  83 */     Date startTime = new Date();
/*  84 */     String errorMsg = "";
/*     */     
/*  86 */     KeysResponse rsuResponse = null;
/*  87 */     Message message = PhaseInterceptorChain.getCurrentMessage();
/*  88 */     HttpServletRequest httpRequest = (HttpServletRequest)message.get("HTTP.REQUEST");
/*  89 */     String userId = httpRequest.getHeader("coreid");
/*  90 */     String publicIp = httpRequest.getHeader("publicIP");
/*  91 */     log.info("userId --> " + userId);
/*  92 */     log.info("publicip --> " + publicIp);
/*  93 */     rsuRequest.setUserId(StringUtils.isEmpty(userId) ? "" : userId);
/*  94 */     rsuRequest.setPublicIP(StringUtils.isEmpty(publicIp) ? "" : publicIp);
/*     */     
/*     */     try {
/*  97 */       rsuResponse = this.unlockService.processRsuRequest(rsuRequest, userId, publicIp);
/*  98 */     } catch (Exception e) {
/*  99 */       log.error("simunlock webservice error,input serial_no:" + rsuRequest.getNewIMEI(), e);
/* 100 */       errorMsg = e.getMessage();
/* 101 */       rsuResponse = new KeysResponse();
/* 102 */       rsuResponse.setResponseCode("8106");
/* 103 */       rsuResponse.setResponseMsg("Exception while processing the request");
/*     */     } 
/* 105 */     Date endTime = new Date();
/*     */     
/*     */     try {
/* 108 */       IbTAccessLog accessLog = new IbTAccessLog();
/* 109 */       accessLog.setAccessTime(new Date());
/* 110 */       accessLog.setKeyword(rsuRequest.getNewIMEI());
/* 111 */       accessLog.setClassName(getClass().getName() + ".certsDispatch");
/* 112 */       accessLog.setAccess_parameter(JSON.toJSONString(rsuRequest));
/* 113 */       accessLog.setResult(rsuResponse.getResponseCode() + "-" + rsuResponse.getResponseMsg() + "costTime:" + (endTime.getTime() - startTime.getTime()) + ";errorMsg:" + ((errorMsg.length() > 300) ? errorMsg.substring(0, 300) : errorMsg));
/* 114 */       this.accessLogMapper.insert(accessLog);
/* 115 */     } catch (Exception e) {
/* 116 */       log.error("simunlock webservice add log error, input serial_no:" + rsuRequest.getNewIMEI(), e);
/*     */     } 
/*     */     
/* 119 */     return rsuResponse;
/*     */   }
/*     */   
/*     */   @POST
/*     */   @Path("/killswitch")
/*     */   @Produces({"application/json"})
/*     */   @Consumes({"application/json"})
/*     */   public KSUnlockResponse mmKillSwitch(KSUnlockRequest rsuRequest) {
/* 127 */     Date startTime = new Date();
/* 128 */     String errorMsg = "";
/*     */     
/* 130 */     KSUnlockResponse rsuResponse = null;
/* 131 */     Message message = PhaseInterceptorChain.getCurrentMessage();
/* 132 */     HttpServletRequest httpRequest = (HttpServletRequest)message.get("HTTP.REQUEST");
/* 133 */     String userId = httpRequest.getHeader("coreid");
/* 134 */     String publicIp = httpRequest.getHeader("publicIP");
/* 135 */     log.info("userId --> " + userId);
/* 136 */     log.info("publicip --> " + publicIp);
/* 137 */     rsuRequest.setUserId(StringUtils.isEmpty(userId) ? "" : userId);
/* 138 */     rsuRequest.setPublicIP(StringUtils.isEmpty(publicIp) ? "" : publicIp);
/*     */     
/*     */     try {
/* 141 */       rsuResponse = this.unlockService.processKsUnlock(rsuRequest);
/* 142 */     } catch (Exception e) {
/* 143 */       log.error("simunlock webservice error,input serial_no:" + rsuRequest.getNewIMEI(), e);
/* 144 */       errorMsg = e.getMessage();
/* 145 */       rsuResponse = new KSUnlockResponse();
/* 146 */       rsuResponse.setResponseCode("8106");
/* 147 */       rsuResponse.setResponseMsg("Exception while processing the request");
/*     */     } 
/* 149 */     Date endTime = new Date();
/*     */     
/*     */     try {
/* 152 */       IbTAccessLog accessLog = new IbTAccessLog();
/* 153 */       accessLog.setAccessTime(new Date());
/* 154 */       accessLog.setKeyword(rsuRequest.getNewIMEI());
/* 155 */       accessLog.setClassName(getClass().getName() + ".ksunlock");
/* 156 */       accessLog.setAccess_parameter(JSON.toJSONString(rsuRequest));
/* 157 */       accessLog.setResult(rsuResponse.getResponseCode() + "-" + rsuResponse.getResponseMsg() + "costTime:" + (endTime.getTime() - startTime.getTime()) + ";errorMsg:" + ((errorMsg.length() > 300) ? errorMsg.substring(0, 300) : errorMsg));
/* 158 */       this.accessLogMapper.insert(accessLog);
/* 159 */     } catch (Exception e) {
/* 160 */       log.error("simunlock webservice add log error, input serial_no:" + rsuRequest.getNewIMEI(), e);
/*     */     } 
/*     */     
/* 163 */     return rsuResponse;
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\simlock\controller\SimUnlockController.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */