<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd">

	<!-- 自动扫描(自动注入) -->
	<context:component-scan base-package="com.lenovo.iqs">
		<context:exclude-filter type="annotation"
			expression="org.springframework.stereotype.Controller" />
	</context:component-scan>

	<!-- 读取properties文件（注意:读取多个文件的时候，需要写一起） -->
	<bean id="propertyConfigurer"
		class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="locations">
			<list>
				<value>classpath:config.properties</value>
			</list>
		</property>
		<property name="fileEncoding" value="UTF-8" />
	</bean>

    <!-- 导入classes及其子目录下的全部spring-开头的配置文件 -->
    <import resource="classpath:**/spring-*" />

	<!-- java melody -->
	<bean id="monitoringAdvisor" class="net.bull.javamelody.MonitoringSpringAdvisor">
		<property name="pointcut">
			<bean class="net.bull.javamelody.MonitoredWithAnnotationPointcut"/>
		</property>
	</bean>

	<bean id="monitoringControllerAndServiceAdvisor" class="net.bull.javamelody.MonitoringSpringAdvisor">
		<property name="pointcut">
			<bean class="net.bull.javamelody.MonitoredSpringControllerAndServicePointcut"/>
		</property>
	</bean>

	<bean class="org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator"/>

	<bean id="springDataSourceBeanPostProcessor" class="net.bull.javamelody.SpringDataSourceBeanPostProcessor">
		<property name="excludedDatasources">
			<set>
				<value>PCGDataSource</value>
				<value>SPPDataSource</value>
			</set>
		</property>
	</bean>

	<bean id="springRestTemplateBeanPostProcessor" class="net.bull.javamelody.SpringRestTemplateBeanPostProcessor" />

	<bean id="javamelodySpringContext" class="net.bull.javamelody.SpringContext" />

</beans>