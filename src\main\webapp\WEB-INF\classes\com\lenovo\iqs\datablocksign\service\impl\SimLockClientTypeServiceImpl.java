/*     */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.service.impl;
/*     */ 
/*     */ import com.lenovo.iqs.datablocksign.bean.ClientResponse;
/*     */ import com.lenovo.iqs.datablocksign.bean.RequestBean;
/*     */ import com.lenovo.iqs.datablocksign.dao.DatablockSignMapper;
/*     */ import com.lenovo.iqs.datablocksign.service.impl.AbstractClientTypeService;
/*     */ import com.lenovo.iqs.datablocksign.utils.CRCUtils;
/*     */ import com.lenovo.iqs.datablocksign.utils.HexUtil;
/*     */ import com.lenovo.iqs.entity.IbTUpdSnRepos;
/*     */ import com.lenovo.iqs.sap.SAPRfcs;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.cxf.common.util.StringUtils;
/*     */ import org.apache.log4j.Logger;
/*     */ import org.springframework.beans.factory.annotation.Autowired;
/*     */ import org.springframework.stereotype.Service;
/*     */ 
/*     */ @Service("ClientType-0x01")
/*     */ public class SimLockClientTypeServiceImpl
/*     */   extends AbstractClientTypeService
/*     */ {
/*  23 */   private static final Logger log = Logger.getLogger(com.lenovo.iqs.datablocksign.service.impl.SimLockClientTypeServiceImpl.class);
/*     */   
/*     */   @Autowired
/*     */   private SAPRfcs sapRfcs;
/*     */   
/*     */   @Autowired
/*     */   private DatablockSignMapper datablockSignMapper;
/*     */ 
/*     */   
/*     */   public ClientResponse process(RequestBean requestBean) throws Exception {
/*  33 */     String transcationId = generateTranscationId(requestBean.getIstrMASCID());
/*     */     
/*  35 */     ClientResponse clientResponse = validateReqParam(requestBean);
/*  36 */     if (clientResponse != null) {
/*  37 */       clientResponse.setIstrTransactionID(transcationId);
/*     */     }
/*     */     
/*  40 */     int dataBlockType = getDataBlockType(requestBean);
/*  41 */     log.info("Inside Simlock DB, dbtype found is " + dataBlockType);
/*  42 */     if (dataBlockType != 2 && dataBlockType != 10 && dataBlockType != 14) {
/*  43 */       clientResponse = new ClientResponse();
/*  44 */       clientResponse.setIstrTransactionID(transcationId);
/*  45 */       String[] error = "8026,Invalid Data Block Signing Request Type".split(",");
/*  46 */       clientResponse.setIstrStatusCode(error[0]);
/*  47 */       clientResponse.setIstrStatusData(error[1]);
/*  48 */       return clientResponse;
/*     */     } 
/*     */     
/*  51 */     String mascId = requestBean.getIstrMASCID();
/*  52 */     String oldImei = requestBean.getIstrOldIMEI();
/*  53 */     int mascCnt = this.datablockSignMapper.countWihiteMascs(mascId).intValue();
/*  54 */     boolean isOldImeiExist = this.sapRfcs.isSerialExists("IMEI", oldImei, "PART");
/*  55 */     if (!StringUtils.isEmpty(mascId) && mascCnt > 1 && !isOldImeiExist) {
/*  56 */       clientResponse = new ClientResponse();
/*  57 */       String[] error = "8032,IMEI from PFE MASC is not White Listed IMEI".split(",");
/*  58 */       clientResponse.setIstrStatusCode(error[0]);
/*  59 */       clientResponse.setIstrStatusData(error[1]);
/*  60 */       return clientResponse;
/*     */     } 
/*  62 */     if (dataBlockType == 10) {
/*  63 */       String str = requestBean.getIstrPassChgRequd();
/*  64 */       List<IbTUpdSnRepos> list = this.sapRfcs.callUpdSnReposFunction(getSerialNo(requestBean), "IMEI", IbTUpdSnRepos.class, "PART");
/*  65 */       IbTUpdSnRepos ibTUpdSnRepos = list.get(0);
/*  66 */       if (!str.equals("0x01")) {
/*  67 */         String imcIicketIdType = getImcIicketIdType(requestBean);
/*  68 */         String password = "";
/*  69 */         if ("10".equals(imcIicketIdType)) {
/*     */           
/*  71 */           password = ibTUpdSnRepos.getAttribute30();
/*  72 */         } else if ("11".equals(imcIicketIdType) || "12".equals(imcIicketIdType) || "13".equals(imcIicketIdType) || "14".equals(imcIicketIdType)) {
/*     */           
/*  74 */           password = ibTUpdSnRepos.getAttribute31();
/*     */         } 
/*  76 */         if (StringUtils.isEmpty(password)) {
/*  77 */           clientResponse = new ClientResponse();
/*  78 */           String[] error = "8012,Password Retrieval Failed".split(",");
/*  79 */           clientResponse.setIstrStatusCode(error[0]);
/*  80 */           clientResponse.setIstrStatusData(error[1]);
/*  81 */           clientResponse.setIstrTransactionID(transcationId);
/*  82 */           return clientResponse;
/*     */         } 
/*  84 */         char[] charNewspArray = password.toCharArray();
/*  85 */         updateIMCSImLockPassword(requestBean, charNewspArray);
/*  86 */         CRCUtils.updateCRCCode(requestBean.getIstrReqParam());
/*     */       } 
/*  88 */       clientResponse = callPKIAndProcessResult(requestBean);
/*  89 */       return clientResponse;
/*     */     } 
/*     */     
/*  92 */     int simLockSubType = computeSimLockSubType(requestBean);
/*  93 */     if (simLockSubType != 1 && simLockSubType != 3 && simLockSubType != 7) {
/*  94 */       clientResponse = new ClientResponse();
/*  95 */       String[] error = "8012,Password Retrieval Failed".split(",");
/*  96 */       clientResponse.setIstrStatusCode(error[0]);
/*  97 */       clientResponse.setIstrStatusData(error[1]);
/*  98 */       clientResponse.setIstrTransactionID(transcationId);
/*  99 */       return clientResponse;
/*     */     } 
/* 101 */     String passChgRequd = requestBean.getIstrPassChgRequd();
/* 102 */     if ("0x01".equals(passChgRequd)) {
/* 103 */       Map<String, String> map = this.datablockSignMapper.queryWhiteMascs(mascId);
/* 104 */       String activeCode = map.get("ACTIVE_LOCK_CODE");
/* 105 */       String newScp = map.get("NWSCP_LOCK_CODE");
/* 106 */       String sscp = map.get("SSCP_LOCK_CODE");
/*     */       
/* 108 */       int subType = 0;
/* 109 */       int flag = 0;
/* 110 */       List<String> list = new ArrayList<>();
/* 111 */       if (simLockSubType == 1) {
/* 112 */         subType = 1;
/* 113 */         list.add(activeCode);
/* 114 */         list.add("");
/* 115 */         list.add("");
/* 116 */       } else if (simLockSubType == 3) {
/* 117 */         subType = 2;
/* 118 */         list.add(activeCode);
/* 119 */         list.add(newScp);
/* 120 */         list.add("");
/* 121 */       } else if (simLockSubType == 7) {
/* 122 */         subType = 3;
/* 123 */         list.add(activeCode);
/* 124 */         list.add(newScp);
/* 125 */         list.add(sscp);
/*     */       } 
/* 127 */       for (int i = 0; i < subType; i++) {
/* 128 */         if (StringUtils.isEmpty(list.get(i))) {
/* 129 */           clientResponse = new ClientResponse();
/* 130 */           clientResponse.setIstrTransactionID(transcationId);
/* 131 */           String[] error = "8035,Unable to retrieve Lock Code".split(",");
/* 132 */           clientResponse.setIstrStatusCode(error[0]);
/* 133 */           clientResponse.setIstrStatusData(error[1]);
/* 134 */           return clientResponse;
/*     */         } 
/*     */       } 
/* 137 */       flag = this.sapRfcs.updatePassword(getSerialNo(requestBean), list.get(0), list.get(1), list.get(2));
/* 138 */       if (flag != 1) {
/* 139 */         clientResponse = new ClientResponse();
/* 140 */         clientResponse.setIstrTransactionID(transcationId);
/* 141 */         String[] error = "8106,Exception while processing the request".split(",");
/* 142 */         clientResponse.setIstrStatusCode(error[0]);
/* 143 */         clientResponse.setIstrStatusData(error[1]);
/* 144 */         return clientResponse;
/*     */       } 
/* 146 */       String password = list.get(subType - 1);
/* 147 */       byte[] byteArray = HexUtil.convert2ReverseBCD(password);
/* 148 */       updateSIMLockPassword(requestBean, byteArray, subType);
/* 149 */       CRCUtils.updateCRCCode(requestBean.getIstrReqParam());
/*     */       
/* 151 */       clientResponse = callPKIAndProcessResult(requestBean);
/* 152 */       return clientResponse;
/*     */     } 
/*     */     
/* 155 */     List<IbTUpdSnRepos> snReposList = this.sapRfcs.callUpdSnReposFunction(getSerialNo(requestBean), "IMEI", IbTUpdSnRepos.class, "PART");
/* 156 */     IbTUpdSnRepos snRepos = snReposList.get(0);
/* 157 */     if (simLockSubType == 1) {
/* 158 */       clientResponse = callPKIAndProcessResult(requestBean);
/* 159 */       return clientResponse;
/* 160 */     }  if (simLockSubType == 3) {
/* 161 */       String newScpPassword = snRepos.getAttribute30();
/* 162 */       if (StringUtils.isEmpty(newScpPassword)) {
/* 163 */         clientResponse = new ClientResponse();
/* 164 */         clientResponse.setIstrTransactionID(transcationId);
/* 165 */         String[] error = "8012,Password Retrieval Failed".split(",");
/* 166 */         clientResponse.setIstrStatusCode(error[0]);
/* 167 */         clientResponse.setIstrStatusData(error[1]);
/* 168 */         return clientResponse;
/*     */       } 
/* 170 */       byte[] byteArray = HexUtil.convert2ReverseBCD(newScpPassword);
/* 171 */       updateSIMLockPassword(requestBean, byteArray, 2);
/* 172 */       CRCUtils.updateCRCCode(requestBean.getIstrReqParam());
/*     */       
/* 174 */       clientResponse = callPKIAndProcessResult(requestBean);
/* 175 */       return clientResponse;
/* 176 */     }  if (simLockSubType == 7) {
/* 177 */       String newScpPassword = snRepos.getAttribute30();
/* 178 */       String sscpPassword = snRepos.getAttribute30();
/* 179 */       if (StringUtils.isEmpty(newScpPassword) || StringUtils.isEmpty(sscpPassword)) {
/* 180 */         clientResponse = new ClientResponse();
/* 181 */         clientResponse.setIstrTransactionID(transcationId);
/* 182 */         String[] error = "8012,Password Retrieval Failed".split(",");
/* 183 */         clientResponse.setIstrStatusCode(error[0]);
/* 184 */         clientResponse.setIstrStatusData(error[1]);
/* 185 */         return clientResponse;
/*     */       } 
/* 187 */       byte[] byteArray = HexUtil.convert2ReverseBCD(newScpPassword);
/* 188 */       updateSIMLockPassword(requestBean, byteArray, 2);
/* 189 */       byteArray = HexUtil.convert2ReverseBCD(sscpPassword);
/* 190 */       updateSIMLockPassword(requestBean, byteArray, 3);
/* 191 */       CRCUtils.updateCRCCode(requestBean.getIstrReqParam());
/*     */       
/* 193 */       clientResponse = callPKIAndProcessResult(requestBean);
/* 194 */       return clientResponse;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 199 */     return null;
/*     */   }
/*     */   
/*     */   private void updateSIMLockPassword(RequestBean requestBean, byte[] pstrSIMLockPassword, int subType) {
/* 203 */     byte[] reqParam = requestBean.getIstrReqParam();
/*     */     
/* 205 */     int mainStartIndex = 128 + (subType - 1) * 364 + 344, i = mainStartIndex; int j;
/* 206 */     for (j = 0; j < pstrSIMLockPassword.length; j++) {
/* 207 */       reqParam[i] = pstrSIMLockPassword[j];
/* 208 */       i++;
/*     */     } 
/* 210 */     for (j = 0; j < 20 - pstrSIMLockPassword.length; j++) {
/* 211 */       reqParam[mainStartIndex + pstrSIMLockPassword.length + j] = 0;
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   private int computeSimLockSubType(RequestBean requestBean) {
/* 217 */     byte[] reqParam = requestBean.getIstrReqParam();
/* 218 */     String protocolVersion = Integer.toHexString(reqParam[3]);
/* 219 */     protocolVersion = (protocolVersion.length() == 1) ? ("0" + protocolVersion) : protocolVersion;
/* 220 */     int DataBlockSartIndex = 0;
/* 221 */     int simLockStartIndex = 0;
/* 222 */     if ("02".equals(protocolVersion)) {
/* 223 */       DataBlockSartIndex = 158;
/* 224 */       simLockStartIndex = 160;
/*     */     }
/* 226 */     else if ("01".equals(protocolVersion)) {
/* 227 */       DataBlockSartIndex = 126;
/* 228 */       simLockStartIndex = 128;
/*     */     } else {
/* 230 */       return 0;
/*     */     } 
/* 232 */     byte[] byteNumDataBlocks = new byte[2];
/* 233 */     byte[] byteSimLockSubType = new byte[364];
/* 234 */     int simLockSubType = 0;
/*     */     
/* 236 */     System.arraycopy(reqParam, DataBlockSartIndex, byteNumDataBlocks, 0, byteNumDataBlocks.length);
/* 237 */     int numDataBlocks = Integer.parseInt("" + byteNumDataBlocks[0] + byteNumDataBlocks[1], 16);
/* 238 */     for (int i = 0; i < numDataBlocks; i++) {
/* 239 */       System.arraycopy(reqParam, simLockStartIndex, byteSimLockSubType, 0, byteSimLockSubType.length);
/* 240 */       int step = i * byteSimLockSubType.length;
/* 241 */       String strSimLockSubType = HexUtil.toTwoDigitHexString(byteSimLockSubType[40 + step]) + HexUtil.toTwoDigitHexString(byteSimLockSubType[41 + step]);
/* 242 */       if ("0000".equals(strSimLockSubType)) {
/* 243 */         simLockSubType++;
/* 244 */       } else if ("0001".equals(strSimLockSubType)) {
/* 245 */         simLockSubType += 2;
/* 246 */       } else if ("0002".equals(strSimLockSubType)) {
/* 247 */         simLockSubType += 4;
/*     */       } 
/*     */     } 
/* 250 */     return simLockSubType;
/*     */   }
/*     */   
/*     */   private void updateIMCSImLockPassword(RequestBean requestBean, char[] charNewspArray) {
/* 254 */     byte[] reqParam = requestBean.getIstrReqParam();
/* 255 */     String protocolVersion = Integer.toHexString(reqParam[3]);
/* 256 */     protocolVersion = (protocolVersion.length() == 1) ? ("0" + protocolVersion) : protocolVersion;
/*     */     
/* 258 */     int mainStartIndex = "02".equals(protocolVersion) ? 220 : 188;
/* 259 */     int i = mainStartIndex; int j;
/* 260 */     for (j = 0; j < charNewspArray.length; j++) {
/* 261 */       reqParam[i] = (byte)charNewspArray[j];
/* 262 */       i++;
/*     */     } 
/* 264 */     for (j = 0; j < 16 - charNewspArray.length; j++) {
/* 265 */       reqParam[mainStartIndex + charNewspArray.length + j] = 0;
/*     */     }
/*     */   }
/*     */   
/*     */   private String getImcIicketIdType(RequestBean requestBean) {
/* 270 */     byte[] reqParam = requestBean.getIstrReqParam();
/* 271 */     String protocolVersion = Integer.toHexString(reqParam[3]);
/* 272 */     protocolVersion = (protocolVersion.length() == 1) ? ("0" + protocolVersion) : protocolVersion;
/* 273 */     String imcIicketIdType = "";
/* 274 */     if ("02".equals(protocolVersion)) {
/* 275 */       imcIicketIdType = HexUtil.toTwoDigitHexString(reqParam[240]);
/*     */     } else {
/* 277 */       imcIicketIdType = HexUtil.toTwoDigitHexString(reqParam[208]);
/*     */     } 
/* 279 */     return imcIicketIdType;
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\service\impl\SimLockClientTypeServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */