/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.service.impl;
/*    */ 
/*    */ import com.lenovo.iqs.datablocksign.bean.ClientResponse;
/*    */ import com.lenovo.iqs.datablocksign.bean.RequestBean;
/*    */ import com.lenovo.iqs.datablocksign.dao.DatablockSignMapper;
/*    */ import com.lenovo.iqs.datablocksign.service.impl.AbstractClientTypeService;
/*    */ import org.springframework.beans.factory.annotation.Autowired;
/*    */ import org.springframework.stereotype.Service;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Service("ClientType-0x06")
/*    */ public class STEClientTypeServiceImpl
/*    */   extends AbstractClientTypeService
/*    */ {
/*    */   @Autowired
/*    */   private DatablockSignMapper datablockSignMapper;
/*    */   
/*    */   public ClientResponse process(RequestBean requestBean) throws Exception {
/* 21 */     String transcationId = generateTranscationId(requestBean.getIstrMASCID());
/* 22 */     ClientResponse clientResponse = validateReqParam(requestBean);
/* 23 */     if (clientResponse != null) {
/* 24 */       clientResponse.setIstrTransactionID(transcationId);
/* 25 */       return clientResponse;
/*    */     } 
/*    */     
/* 28 */     if (getDataBlockType(requestBean) != 8) {
/* 29 */       clientResponse = new ClientResponse();
/* 30 */       clientResponse.setIstrTransactionID(transcationId);
/* 31 */       String[] error = "8026,Invalid Data Block Signing Request Type".split(",");
/* 32 */       clientResponse.setIstrStatusCode(error[0]);
/* 33 */       clientResponse.setIstrStatusData(error[1]);
/* 34 */       return clientResponse;
/*    */     } 
/*    */     
/* 37 */     clientResponse = callPKIAndProcessResult(requestBean);
/* 38 */     clientResponse.setIstrTransactionID(transcationId);
/*    */     
/* 40 */     return clientResponse;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\service\impl\STEClientTypeServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */