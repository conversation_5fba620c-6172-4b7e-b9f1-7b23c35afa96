/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.service.impl;
/*    */ 
/*    */ import com.lenovo.iqs.datablocksign.bean.ClientResponse;
/*    */ import com.lenovo.iqs.datablocksign.bean.RequestBean;
/*    */ import com.lenovo.iqs.datablocksign.dao.DatablockSignMapper;
/*    */ import com.lenovo.iqs.datablocksign.service.impl.AbstractClientTypeService;
/*    */ import org.apache.cxf.common.util.StringUtils;
/*    */ import org.springframework.beans.factory.annotation.Autowired;
/*    */ import org.springframework.stereotype.Service;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Service("ClientType-0xFF")
/*    */ public class WhiteSerialClientTypeServiceImpl
/*    */   extends AbstractClientTypeService
/*    */ {
/*    */   @Autowired
/*    */   private DatablockSignMapper datablockSignMapper;
/*    */   
/*    */   public ClientResponse process(RequestBean requestBean) throws Exception {
/* 22 */     String transcationId = generateTranscationId(requestBean.getIstrMASCID());
/* 23 */     ClientResponse clientResponse = validateReqParam(requestBean);
/* 24 */     if (clientResponse != null) {
/* 25 */       clientResponse.setIstrTransactionID(transcationId);
/* 26 */       return clientResponse;
/*    */     } 
/*    */     
/* 29 */     clientResponse = new ClientResponse();
/* 30 */     clientResponse.setIstrTransactionID(transcationId);
/* 31 */     String serialNo = getSerialNo(requestBean);
/* 32 */     if (!StringUtils.isEmpty(serialNo) && this.datablockSignMapper.countWhiteImei(serialNo).intValue() > 0) {
/* 33 */       String[] error = "8037,Serial Number is White Listed".split(",");
/* 34 */       clientResponse.setIstrStatusCode(error[0]);
/* 35 */       clientResponse.setIstrStatusData(error[1]);
/*    */     } else {
/* 37 */       String[] error = "8036,Serial Number is not White Listed".split(",");
/* 38 */       clientResponse.setIstrStatusCode(error[0]);
/* 39 */       clientResponse.setIstrStatusData(error[1]);
/*    */     } 
/*    */     
/* 42 */     return clientResponse;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\service\impl\WhiteSerialClientTypeServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */