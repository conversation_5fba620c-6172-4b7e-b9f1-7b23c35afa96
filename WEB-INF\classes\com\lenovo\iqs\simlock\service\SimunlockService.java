package WEB-INF.classes.com.lenovo.iqs.simlock.service;

import com.lenovo.iqs.simlock.bean.KSUnlockRequest;
import com.lenovo.iqs.simlock.bean.KSUnlockResponse;
import com.lenovo.iqs.simlock.bean.KeysRequest;
import com.lenovo.iqs.simlock.bean.KeysResponse;
import com.lenovo.iqs.simlock.bean.SimunlockRequest;
import com.lenovo.iqs.simlock.bean.SimunlockResponse;

public interface SimunlockService {
  SimunlockResponse processRsuRequest(SimunlockRequest paramSimunlockRequest, String paramString1, String paramString2) throws Exception;
  
  KeysResponse processRsuRequest(KeysRequest paramKeysRequest, String paramString1, String paramString2) throws Exception;
  
  KSUnlockResponse processKsUnlock(KSUnlockRequest paramKSUnlockRequest) throws Exception;
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\simlock\service\SimunlockService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */