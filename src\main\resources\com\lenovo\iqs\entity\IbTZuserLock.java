/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTZuserLock implements Serializable {
/*    */   private Integer autoId;
/*    */   private String ip;
/*    */   private String userid;
/*    */   
/*  7 */   public void setAutoId(Integer autoId) { this.autoId = autoId; } private Date lockTime; private Date unlockTime; private static final long serialVersionUID = 1L; public void setIp(String ip) { this.ip = ip; } public void setUserid(String userid) { this.userid = userid; } public void setLockTime(Date lockTime) { this.lockTime = lockTime; } public void setUnlockTime(Date unlockTime) { this.unlockTime = unlockTime; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTZuserLock)) return false;  com.lenovo.iqs.entity.IbTZuserLock other = (com.lenovo.iqs.entity.IbTZuserLock)o; if (!other.canEqual(this)) return false;  Object this$autoId = getAutoId(), other$autoId = other.getAutoId(); if ((this$autoId == null) ? (other$autoId != null) : !this$autoId.equals(other$autoId)) return false;  Object this$ip = getIp(), other$ip = other.getIp(); if ((this$ip == null) ? (other$ip != null) : !this$ip.equals(other$ip)) return false;  Object this$userid = getUserid(), other$userid = other.getUserid(); if ((this$userid == null) ? (other$userid != null) : !this$userid.equals(other$userid)) return false;  Object this$lockTime = getLockTime(), other$lockTime = other.getLockTime(); if ((this$lockTime == null) ? (other$lockTime != null) : !this$lockTime.equals(other$lockTime)) return false;  Object this$unlockTime = getUnlockTime(), other$unlockTime = other.getUnlockTime(); return !((this$unlockTime == null) ? (other$unlockTime != null) : !this$unlockTime.equals(other$unlockTime)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTZuserLock; } public int hashCode() { int PRIME = 59; result = 1; Object $autoId = getAutoId(); result = result * 59 + (($autoId == null) ? 43 : $autoId.hashCode()); Object $ip = getIp(); result = result * 59 + (($ip == null) ? 43 : $ip.hashCode()); Object $userid = getUserid(); result = result * 59 + (($userid == null) ? 43 : $userid.hashCode()); Object $lockTime = getLockTime(); result = result * 59 + (($lockTime == null) ? 43 : $lockTime.hashCode()); Object $unlockTime = getUnlockTime(); return result * 59 + (($unlockTime == null) ? 43 : $unlockTime.hashCode()); } public String toString() { return "IbTZuserLock(autoId=" + getAutoId() + ", ip=" + getIp() + ", userid=" + getUserid() + ", lockTime=" + getLockTime() + ", unlockTime=" + getUnlockTime() + ")"; }
/*    */    public Integer getAutoId() {
/*  9 */     return this.autoId;
/*    */   } public String getIp() {
/* 11 */     return this.ip;
/*    */   } public String getUserid() {
/* 13 */     return this.userid;
/*    */   } public Date getLockTime() {
/* 15 */     return this.lockTime;
/*    */   } public Date getUnlockTime() {
/* 17 */     return this.unlockTime;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTZuserLock.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */