/*    */ package WEB-INF.classes.com.lenovo.iqs.utils;
/*    */ 
/*    */ import com.google.common.cache.Cache;
/*    */ import com.google.common.cache.CacheBuilder;
/*    */ import java.util.concurrent.ExecutionException;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class XmlUtils
/*    */ {
/* 14 */   private static Cache<String, Pattern> regexCache = CacheBuilder.newBuilder().build();
/*    */   
/*    */   public static String getContentByTag(String xml, String tagName) throws ExecutionException {
/* 17 */     Pattern pattern = (Pattern)regexCache.get(tagName, () -> Pattern.compile(String.format("(?i)<%1$s>.+</?%1$s>", new Object[] { tagName })));
/*    */ 
/*    */     
/* 20 */     Matcher matcher = pattern.matcher(xml);
/* 21 */     if (matcher.find()) {
/* 22 */       return matcher.group().replaceAll("(?i)</?" + tagName + ">", "");
/*    */     }
/* 24 */     return null;
/*    */   }
/*    */   
/*    */   public static boolean isExistTag(String xml, String tagName) throws ExecutionException {
/* 28 */     Pattern pattern = (Pattern)regexCache.get(tagName, () -> Pattern.compile(String.format("(?i)<%1$s[^>]*/>", new Object[] { tagName })));
/* 29 */     return pattern.matcher(xml).find();
/*    */   }
/*    */   
/*    */   public static void main(String[] args) throws ExecutionException {
/* 33 */     String xml = " <?xml version=\"1.0\" ?><S:Envelope xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\"><S:Body><ns2:RequestUnlockPasswordResponse xmlns:ns2=\"http://rsu.programmingservice.cfc.nextest.globaltest.motorolamobility.com/\"><return><errorInfo><errorCode>0</errorCode><errorMessage>success</errorMessage></errorInfo><msg>48C7A0FADF1D9ADA9886BCFB0786B8527EE8F65BD52852802E39C30DE5DF1E8CE5BEFDB900EFA2B00C29675461CAE34D2F8A5C52BB61DE6F5F5F472DEA5DD7705C9D5A92E0D1DCC16AA25D14F0229E2323479BCE2C4B6E260D48481EF3710CF32ACD5966E83D7B8589F6A438BFE6BE6E761023EB6EBAA77DCD516EA04C0DA9F18FB1A1C11E17CCDBBBD09B093DE73963DA98311571A29DAD3F5EF5120D2C801C549C03BB21CE0B41ADF52F813A6F4886E0A4B931F4B6662AF5D4C7612EE6FC9C8A2A839A2EA5602A1AA7BDE27BC82934F35CF55743F7CE833BCB658F934D815BC115DCC6AE45A722D02931B31D3EC3064D4E495132406895E905B845B566F3DA</msg><password>150b086db1</password></return></ns2:RequestUnlockPasswordResponse></S:Body></S:Envelope>";
/*    */     
/* 35 */     System.out.println(getContentByTag(xml, "password"));
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iq\\utils\XmlUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */