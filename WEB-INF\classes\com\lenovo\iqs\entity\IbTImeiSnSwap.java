/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTImeiSnSwap implements Serializable { private Integer id; private String oriSn;
/*    */   private String oldSn;
/*    */   private String oldImei1;
/*    */   private String oldImei2;
/*    */   
/*  7 */   public void setId(Integer id) { this.id = id; } private String newImei1; private String newImei2; private String newSn; private String upUser; private Date lastChange; private static final long serialVersionUID = 1L; public void setOriSn(String oriSn) { this.oriSn = oriSn; } public void setOldSn(String oldSn) { this.oldSn = oldSn; } public void setOldImei1(String oldImei1) { this.oldImei1 = oldImei1; } public void setOldImei2(String oldImei2) { this.oldImei2 = oldImei2; } public void setNewImei1(String newImei1) { this.newImei1 = newImei1; } public void setNewImei2(String newImei2) { this.newImei2 = newImei2; } public void setNewSn(String newSn) { this.newSn = newSn; } public void setUpUser(String upUser) { this.upUser = upUser; } public void setLastChange(Date lastChange) { this.lastChange = lastChange; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTImeiSnSwap)) return false;  com.lenovo.iqs.entity.IbTImeiSnSwap other = (com.lenovo.iqs.entity.IbTImeiSnSwap)o; if (!other.canEqual(this)) return false;  Object this$id = getId(), other$id = other.getId(); if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id)) return false;  Object this$oriSn = getOriSn(), other$oriSn = other.getOriSn(); if ((this$oriSn == null) ? (other$oriSn != null) : !this$oriSn.equals(other$oriSn)) return false;  Object this$oldSn = getOldSn(), other$oldSn = other.getOldSn(); if ((this$oldSn == null) ? (other$oldSn != null) : !this$oldSn.equals(other$oldSn)) return false;  Object this$oldImei1 = getOldImei1(), other$oldImei1 = other.getOldImei1(); if ((this$oldImei1 == null) ? (other$oldImei1 != null) : !this$oldImei1.equals(other$oldImei1)) return false;  Object this$oldImei2 = getOldImei2(), other$oldImei2 = other.getOldImei2(); if ((this$oldImei2 == null) ? (other$oldImei2 != null) : !this$oldImei2.equals(other$oldImei2)) return false;  Object this$newImei1 = getNewImei1(), other$newImei1 = other.getNewImei1(); if ((this$newImei1 == null) ? (other$newImei1 != null) : !this$newImei1.equals(other$newImei1)) return false;  Object this$newImei2 = getNewImei2(), other$newImei2 = other.getNewImei2(); if ((this$newImei2 == null) ? (other$newImei2 != null) : !this$newImei2.equals(other$newImei2)) return false;  Object this$newSn = getNewSn(), other$newSn = other.getNewSn(); if ((this$newSn == null) ? (other$newSn != null) : !this$newSn.equals(other$newSn)) return false;  Object this$upUser = getUpUser(), other$upUser = other.getUpUser(); if ((this$upUser == null) ? (other$upUser != null) : !this$upUser.equals(other$upUser)) return false;  Object this$lastChange = getLastChange(), other$lastChange = other.getLastChange(); return !((this$lastChange == null) ? (other$lastChange != null) : !this$lastChange.equals(other$lastChange)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTImeiSnSwap; } public int hashCode() { int PRIME = 59; result = 1; Object $id = getId(); result = result * 59 + (($id == null) ? 43 : $id.hashCode()); Object $oriSn = getOriSn(); result = result * 59 + (($oriSn == null) ? 43 : $oriSn.hashCode()); Object $oldSn = getOldSn(); result = result * 59 + (($oldSn == null) ? 43 : $oldSn.hashCode()); Object $oldImei1 = getOldImei1(); result = result * 59 + (($oldImei1 == null) ? 43 : $oldImei1.hashCode()); Object $oldImei2 = getOldImei2(); result = result * 59 + (($oldImei2 == null) ? 43 : $oldImei2.hashCode()); Object $newImei1 = getNewImei1(); result = result * 59 + (($newImei1 == null) ? 43 : $newImei1.hashCode()); Object $newImei2 = getNewImei2(); result = result * 59 + (($newImei2 == null) ? 43 : $newImei2.hashCode()); Object $newSn = getNewSn(); result = result * 59 + (($newSn == null) ? 43 : $newSn.hashCode()); Object $upUser = getUpUser(); result = result * 59 + (($upUser == null) ? 43 : $upUser.hashCode()); Object $lastChange = getLastChange(); return result * 59 + (($lastChange == null) ? 43 : $lastChange.hashCode()); } public String toString() { return "IbTImeiSnSwap(id=" + getId() + ", oriSn=" + getOriSn() + ", oldSn=" + getOldSn() + ", oldImei1=" + getOldImei1() + ", oldImei2=" + getOldImei2() + ", newImei1=" + getNewImei1() + ", newImei2=" + getNewImei2() + ", newSn=" + getNewSn() + ", upUser=" + getUpUser() + ", lastChange=" + getLastChange() + ")"; }
/*    */    public Integer getId() {
/*  9 */     return this.id;
/*    */   } public String getOriSn() {
/* 11 */     return this.oriSn;
/*    */   } public String getOldSn() {
/* 13 */     return this.oldSn;
/*    */   } public String getOldImei1() {
/* 15 */     return this.oldImei1;
/*    */   } public String getOldImei2() {
/* 17 */     return this.oldImei2;
/*    */   } public String getNewImei1() {
/* 19 */     return this.newImei1;
/*    */   } public String getNewImei2() {
/* 21 */     return this.newImei2;
/*    */   } public String getNewSn() {
/* 23 */     return this.newSn;
/*    */   } public String getUpUser() {
/* 25 */     return this.upUser;
/*    */   } public Date getLastChange() {
/* 27 */     return this.lastChange;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTImeiSnSwap.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */