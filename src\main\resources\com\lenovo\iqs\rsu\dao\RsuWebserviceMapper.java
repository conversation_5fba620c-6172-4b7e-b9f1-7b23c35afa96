package WEB-INF.classes.com.lenovo.iqs.rsu.dao;

import java.util.List;
import org.springframework.stereotype.Repository;

@Repository
public interface RsuWebserviceMapper {
  List<String> getAllowedTMOValues();
  
  String getCorrectModel(String paramString);
  
  String getSipForModel(String paramString);
  
  String getSocForModel(String paramString);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\rsu\dao\RsuWebserviceMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */