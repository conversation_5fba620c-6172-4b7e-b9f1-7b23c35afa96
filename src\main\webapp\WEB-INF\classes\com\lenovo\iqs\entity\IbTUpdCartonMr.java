/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTUpdCartonMr implements Serializable { private String cartonId;
/*    */   private String wipDj;
/*    */   private Integer cartonQty;
/*    */   private String flag;
/*    */   
/*  7 */   public void setCartonId(String cartonId) { this.cartonId = cartonId; } private String attribute1; private Date recExtrDatetime; private Date lastUpdatetimeDatetime; private String recSrc; private static final long serialVersionUID = 1L; public void setWipDj(String wipDj) { this.wipDj = wipDj; } public void setCartonQty(Integer cartonQty) { this.cartonQty = cartonQty; } public void setFlag(String flag) { this.flag = flag; } public void setAttribute1(String attribute1) { this.attribute1 = attribute1; } public void setRecExtrDatetime(Date recExtrDatetime) { this.recExtrDatetime = recExtrDatetime; } public void setLastUpdatetimeDatetime(Date lastUpdatetimeDatetime) { this.lastUpdatetimeDatetime = lastUpdatetimeDatetime; } public void setRecSrc(String recSrc) { this.recSrc = recSrc; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdCartonMr)) return false;  com.lenovo.iqs.entity.IbTUpdCartonMr other = (com.lenovo.iqs.entity.IbTUpdCartonMr)o; if (!other.canEqual(this)) return false;  Object this$cartonId = getCartonId(), other$cartonId = other.getCartonId(); if ((this$cartonId == null) ? (other$cartonId != null) : !this$cartonId.equals(other$cartonId)) return false;  Object this$wipDj = getWipDj(), other$wipDj = other.getWipDj(); if ((this$wipDj == null) ? (other$wipDj != null) : !this$wipDj.equals(other$wipDj)) return false;  Object this$cartonQty = getCartonQty(), other$cartonQty = other.getCartonQty(); if ((this$cartonQty == null) ? (other$cartonQty != null) : !this$cartonQty.equals(other$cartonQty)) return false;  Object this$flag = getFlag(), other$flag = other.getFlag(); if ((this$flag == null) ? (other$flag != null) : !this$flag.equals(other$flag)) return false;  Object this$attribute1 = getAttribute1(), other$attribute1 = other.getAttribute1(); if ((this$attribute1 == null) ? (other$attribute1 != null) : !this$attribute1.equals(other$attribute1)) return false;  Object this$recExtrDatetime = getRecExtrDatetime(), other$recExtrDatetime = other.getRecExtrDatetime(); if ((this$recExtrDatetime == null) ? (other$recExtrDatetime != null) : !this$recExtrDatetime.equals(other$recExtrDatetime)) return false;  Object this$lastUpdatetimeDatetime = getLastUpdatetimeDatetime(), other$lastUpdatetimeDatetime = other.getLastUpdatetimeDatetime(); if ((this$lastUpdatetimeDatetime == null) ? (other$lastUpdatetimeDatetime != null) : !this$lastUpdatetimeDatetime.equals(other$lastUpdatetimeDatetime)) return false;  Object this$recSrc = getRecSrc(), other$recSrc = other.getRecSrc(); return !((this$recSrc == null) ? (other$recSrc != null) : !this$recSrc.equals(other$recSrc)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdCartonMr; } public int hashCode() { int PRIME = 59; result = 1; Object $cartonId = getCartonId(); result = result * 59 + (($cartonId == null) ? 43 : $cartonId.hashCode()); Object $wipDj = getWipDj(); result = result * 59 + (($wipDj == null) ? 43 : $wipDj.hashCode()); Object $cartonQty = getCartonQty(); result = result * 59 + (($cartonQty == null) ? 43 : $cartonQty.hashCode()); Object $flag = getFlag(); result = result * 59 + (($flag == null) ? 43 : $flag.hashCode()); Object $attribute1 = getAttribute1(); result = result * 59 + (($attribute1 == null) ? 43 : $attribute1.hashCode()); Object $recExtrDatetime = getRecExtrDatetime(); result = result * 59 + (($recExtrDatetime == null) ? 43 : $recExtrDatetime.hashCode()); Object $lastUpdatetimeDatetime = getLastUpdatetimeDatetime(); result = result * 59 + (($lastUpdatetimeDatetime == null) ? 43 : $lastUpdatetimeDatetime.hashCode()); Object $recSrc = getRecSrc(); return result * 59 + (($recSrc == null) ? 43 : $recSrc.hashCode()); } public String toString() { return "IbTUpdCartonMr(cartonId=" + getCartonId() + ", wipDj=" + getWipDj() + ", cartonQty=" + getCartonQty() + ", flag=" + getFlag() + ", attribute1=" + getAttribute1() + ", recExtrDatetime=" + getRecExtrDatetime() + ", lastUpdatetimeDatetime=" + getLastUpdatetimeDatetime() + ", recSrc=" + getRecSrc() + ")"; }
/*    */    public String getCartonId() {
/*  9 */     return this.cartonId;
/*    */   } public String getWipDj() {
/* 11 */     return this.wipDj;
/*    */   } public Integer getCartonQty() {
/* 13 */     return this.cartonQty;
/*    */   } public String getFlag() {
/* 15 */     return this.flag;
/*    */   } public String getAttribute1() {
/* 17 */     return this.attribute1;
/*    */   } public Date getRecExtrDatetime() {
/* 19 */     return this.recExtrDatetime;
/*    */   } public Date getLastUpdatetimeDatetime() {
/* 21 */     return this.lastUpdatetimeDatetime;
/*    */   } public String getRecSrc() {
/* 23 */     return this.recSrc;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdCartonMr.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */