<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTJobInfoMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTJobInfo" >
    <result column="job_id" property="jobId" jdbcType="VARCHAR" />
    <result column="job_des" property="jobDes" jdbcType="VARCHAR" />
    <result column="email_loading" property="emailLoading" jdbcType="VARCHAR" />
    <result column="email_processing" property="emailProcessing" jdbcType="VARCHAR" />
    <result column="job_type" property="jobType" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="last_change_time" property="lastChangeTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List">
    job_id, job_des, email_loading, email_processing, job_type, remark, last_change_time
  </sql>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTJobInfo" >
    insert into ib_t_job_info (job_id, job_des, email_loading, 
      email_processing, job_type, remark, 
      last_change_time)
    values (#{jobId,jdbcType=VARCHAR}, #{jobDes,jdbcType=VARCHAR}, #{emailLoading,jdbcType=VARCHAR}, 
      #{emailProcessing,jdbcType=VARCHAR}, #{jobType,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{lastChangeTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTJobInfo" >
    insert into ib_t_job_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="jobId != null" >
        job_id,
      </if>
      <if test="jobDes != null" >
        job_des,
      </if>
      <if test="emailLoading != null" >
        email_loading,
      </if>
      <if test="emailProcessing != null" >
        email_processing,
      </if>
      <if test="jobType != null" >
        job_type,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="lastChangeTime != null" >
        last_change_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="jobId != null" >
        #{jobId,jdbcType=VARCHAR},
      </if>
      <if test="jobDes != null" >
        #{jobDes,jdbcType=VARCHAR},
      </if>
      <if test="emailLoading != null" >
        #{emailLoading,jdbcType=VARCHAR},
      </if>
      <if test="emailProcessing != null" >
        #{emailProcessing,jdbcType=VARCHAR},
      </if>
      <if test="jobType != null" >
        #{jobType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="lastChangeTime != null" >
        #{lastChangeTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="selectByJobId" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_job_info
    where job_id = #{jobId,jdbcType=VARCHAR}
  </select>
</mapper>