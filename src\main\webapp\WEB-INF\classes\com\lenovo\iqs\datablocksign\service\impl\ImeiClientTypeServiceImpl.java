/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.service.impl;
/*    */ 
/*    */ import com.lenovo.iqs.datablocksign.bean.ClientResponse;
/*    */ import com.lenovo.iqs.datablocksign.bean.RequestBean;
/*    */ import com.lenovo.iqs.datablocksign.service.impl.AbstractClientTypeService;
/*    */ import com.lenovo.iqs.sap.SAPRfcs;
/*    */ import java.util.Date;
/*    */ import org.springframework.beans.factory.annotation.Autowired;
/*    */ import org.springframework.stereotype.Service;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Service("ClientType-0x00")
/*    */ public class ImeiClientTypeServiceImpl
/*    */   extends AbstractClientTypeService
/*    */ {
/*    */   @Autowired
/*    */   private SAPRfcs sapRfcs;
/*    */   
/*    */   public ClientResponse process(RequestBean requestBean) throws Exception {
/* 23 */     String transcationId = generateTranscationId(requestBean.getIstrMASCID());
/* 24 */     ClientResponse clientResponse = validateReqParam(requestBean);
/* 25 */     if (clientResponse != null) {
/* 26 */       clientResponse.setIstrTransactionID(transcationId);
/* 27 */       return clientResponse;
/*    */     } 
/* 29 */     int dataBlockType = getDataBlockType(requestBean);
/* 30 */     if (dataBlockType != 1 && dataBlockType != 9) {
/* 31 */       clientResponse = new ClientResponse();
/* 32 */       clientResponse.setIstrTransactionID(transcationId);
/* 33 */       String[] error = "8026,Invalid Data Block Signing Request Type".split(",");
/* 34 */       clientResponse.setIstrStatusCode(error[0]);
/* 35 */       clientResponse.setIstrStatusData(error[1]);
/* 36 */       return clientResponse;
/*    */     } 
/*    */     
/* 39 */     String oldImei = requestBean.getIstrOldIMEI();
/* 40 */     String newImei = requestBean.getIstrNewIMEI();
/* 41 */     String mascId = requestBean.getIstrMASCID();
/* 42 */     boolean isOldImeiExist = this.sapRfcs.isSerialExists("IMEI", oldImei, "PART");
/* 43 */     if (!isOldImeiExist) {
/* 44 */       clientResponse = new ClientResponse();
/* 45 */       clientResponse.setIstrTransactionID(transcationId);
/* 46 */       String[] error = "8009,Serial Number not available in IBASE".split(",");
/* 47 */       clientResponse.setIstrStatusCode(error[0]);
/* 48 */       clientResponse.setIstrStatusData(error[1]);
/* 49 */       return clientResponse;
/*    */     } 
/*    */     
/* 52 */     if (!oldImei.equals(newImei)) {
/* 53 */       boolean isNewImeiExist = this.sapRfcs.isSerialExists("IMEI", newImei, "PART");
/* 54 */       if (isNewImeiExist) {
/* 55 */         clientResponse = new ClientResponse();
/* 56 */         String[] error = "8011,Updation of New IMEI is Failure".split(",");
/* 57 */         clientResponse.setIstrStatusCode(error[0]);
/* 58 */         clientResponse.setIstrStatusData(error[1]);
/*    */       } else {
/*    */         
/* 61 */         this.sapRfcs.swapImei("IMEI", "BOARD", "MST", oldImei, newImei, mascId, new Date());
/*    */       } 
/*    */     } 
/*    */     
/* 65 */     clientResponse = callPKIAndProcessResult(requestBean);
/* 66 */     clientResponse.setIstrTransactionID(transcationId);
/* 67 */     return clientResponse;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\service\impl\ImeiClientTypeServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */