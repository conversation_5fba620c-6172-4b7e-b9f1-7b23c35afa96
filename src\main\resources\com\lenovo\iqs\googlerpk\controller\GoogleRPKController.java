/*    */ package WEB-INF.classes.com.lenovo.iqs.googlerpk.controller;
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.lenovo.iqs.dao.IbTAccessLogMapper;
/*    */ import com.lenovo.iqs.entity.IbTAccessLog;
/*    */ import com.lenovo.iqs.googlerpk.bean.GoogleRPKRequest;
/*    */ import com.lenovo.iqs.googlerpk.bean.GoogleRPKResponse;
/*    */ import com.lenovo.iqs.googlerpk.service.GoogleRPKService;
/*    */ import java.util.Date;
/*    */ import javax.ws.rs.POST;
/*    */ import javax.ws.rs.Path;
/*    */ import org.apache.logging.log4j.LogManager;
/*    */ import org.apache.logging.log4j.Logger;
/*    */ import org.springframework.beans.factory.annotation.Autowired;
/*    */ import org.springframework.stereotype.Controller;
/*    */ 
/*    */ @Controller
/*    */ @Path("/googleRPKService")
/*    */ public class GoogleRPKController {
/* 19 */   private static final Logger log = LogManager.getLogger(com.lenovo.iqs.googlerpk.controller.GoogleRPKController.class);
/*    */   
/*    */   @Autowired
/*    */   private GoogleRPKService googleRPKService;
/*    */   
/*    */   @Autowired
/*    */   private IbTAccessLogMapper accessLogMapper;
/*    */ 
/*    */   
/*    */   @POST
/*    */   @GET
/*    */   @Path("/callGoogleRPKService")
/*    */   @Produces({"application/json"})
/*    */   @Consumes({"application/json"})
/*    */   public GoogleRPKResponse uploadGoogleRPKService(GoogleRPKRequest googleRPKRequest) {
/* 34 */     Date startTime = new Date();
/* 35 */     String errorMsg = "";
/* 36 */     log.debug("Input googleRPRequest : " + googleRPKRequest.toString());
/* 37 */     GoogleRPKResponse googleRPKResponse = null;
/*    */     
/*    */     try {
/* 40 */       googleRPKResponse = this.googleRPKService.processGoogleRPKRequest(googleRPKRequest);
/* 41 */     } catch (Exception e) {
/* 42 */       log.error("Google RPK webservice error,input serial_no:" + googleRPKRequest.getSerialNo(), e);
/* 43 */       errorMsg = e.getMessage();
/* 44 */       googleRPKResponse = new GoogleRPKResponse();
/* 45 */       googleRPKResponse.setResponseCode("8106");
/* 46 */       googleRPKResponse.setResponseMsg("Exception while processing the request");
/*    */     } 
/* 48 */     Date endTime = new Date();
/*    */     
/*    */     try {
/* 51 */       IbTAccessLog accessLog = new IbTAccessLog();
/* 52 */       accessLog.setAccessTime(new Date());
/* 53 */       accessLog.setKeyword(googleRPKRequest.getSerialNo());
/* 54 */       accessLog.setClassName(getClass().getName());
/* 55 */       accessLog.setAccess_parameter(JSON.toJSONString(googleRPKRequest));
/* 56 */       accessLog.setResult(googleRPKResponse.getResponseCode() + "-" + googleRPKResponse.getResponseMsg() + "costTime:" + (endTime.getTime() - startTime.getTime()) + ";errorMsg:" + ((errorMsg.length() > 300) ? errorMsg.substring(0, 300) : errorMsg));
/*    */       
/* 58 */       this.accessLogMapper.insert(accessLog);
/* 59 */     } catch (Exception e) {
/* 60 */       log.error("Google RPK webservice add log error, input serial_no:" + googleRPKRequest.getSerialNo(), e);
/*    */     } 
/*    */     
/* 63 */     return googleRPKResponse;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\googlerpk\controller\GoogleRPKController.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */