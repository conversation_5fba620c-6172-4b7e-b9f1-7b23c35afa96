/*    */ package WEB-INF.classes.com.lenovo.iqs.rsu.controller;
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.lenovo.iqs.dao.IbTAccessLogMapper;
/*    */ import com.lenovo.iqs.entity.IbTAccessLog;
/*    */ import com.lenovo.iqs.rsu.bean.RsuRequest;
/*    */ import com.lenovo.iqs.rsu.bean.RsuResponse;
/*    */ import com.lenovo.iqs.rsu.service.RsuService;
/*    */ import java.util.Date;
/*    */ import javax.ws.rs.POST;
/*    */ import javax.ws.rs.Path;
/*    */ import org.apache.logging.log4j.LogManager;
/*    */ import org.apache.logging.log4j.Logger;
/*    */ import org.springframework.beans.factory.annotation.Autowired;
/*    */ import org.springframework.stereotype.Controller;
/*    */ 
/*    */ @Controller
/*    */ @Path("/rsuService")
/*    */ public class RsuController {
/* 19 */   private static final Logger log = LogManager.getLogger(com.lenovo.iqs.rsu.controller.RsuController.class);
/*    */   
/*    */   @Autowired
/*    */   private RsuService rsuService;
/*    */   
/*    */   @Autowired
/*    */   private IbTAccessLogMapper accessLogMapper;
/*    */ 
/*    */   
/*    */   @POST
/*    */   @GET
/*    */   @Path("/callrsuService")
/*    */   @Produces({"application/json"})
/*    */   @Consumes({"application/json"})
/*    */   public RsuResponse uploadRSUService(RsuRequest rsuRequest) {
/* 34 */     Date startTime = new Date();
/* 35 */     String errorMsg = "";
/*    */     
/* 37 */     RsuResponse rsuResponse = null;
/*    */     
/*    */     try {
/* 40 */       rsuResponse = this.rsuService.processRsuRequest(rsuRequest);
/* 41 */     } catch (Exception e) {
/* 42 */       log.error("RSU webservice error,input serial_no:" + rsuRequest.getSerialNo(), e);
/* 43 */       errorMsg = e.getMessage();
/* 44 */       rsuResponse = new RsuResponse();
/* 45 */       rsuResponse.setResponseCode("8106");
/* 46 */       rsuResponse.setResponseMsg("Exception while processing the request");
/*    */     } 
/* 48 */     Date endTime = new Date();
/*    */     
/*    */     try {
/* 51 */       IbTAccessLog accessLog = new IbTAccessLog();
/* 52 */       accessLog.setAccessTime(new Date());
/* 53 */       accessLog.setKeyword(rsuRequest.getSerialNo());
/* 54 */       accessLog.setClassName(getClass().getName());
/* 55 */       accessLog.setAccess_parameter(JSON.toJSONString(rsuRequest));
/* 56 */       accessLog.setResult(rsuResponse.getResponseCode() + "-" + rsuResponse.getResponseMsg() + "costTime:" + (endTime.getTime() - startTime.getTime()) + ";errorMsg:" + ((errorMsg.length() > 300) ? errorMsg.substring(0, 300) : errorMsg));
/*    */       
/* 58 */       this.accessLogMapper.insert(accessLog);
/* 59 */     } catch (Exception e) {
/* 60 */       log.error("Rsu webservice add log error, input serial_no:" + rsuRequest.getSerialNo(), e);
/*    */     } 
/*    */     
/* 63 */     return rsuResponse;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\rsu\controller\RsuController.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */