package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTUpdRoamAddress;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTUpdRoamAddressMapper {
  int deleteByPrimaryKey(String paramString);
  
  int insert(IbTUpdRoamAddress paramIbTUpdRoamAddress);
  
  IbTUpdRoamAddress selectByPrimaryKey(String paramString);
  
  int updateByPrimaryKey(IbTUpdRoamAddress paramIbTUpdRoamAddress);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTUpdRoamAddressMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */