package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTUpdCountryWarrantyMapping;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTUpdCountryWarrantyMappingMapper {
  int deleteByPrimaryKey(Long paramLong);
  
  int insert(IbTUpdCountryWarrantyMapping paramIbTUpdCountryWarrantyMapping);
  
  IbTUpdCountryWarrantyMapping selectByPrimaryKey(Long paramLong);
  
  int updateByPrimaryKey(IbTUpdCountryWarrantyMapping paramIbTUpdCountryWarrantyMapping);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTUpdCountryWarrantyMappingMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */