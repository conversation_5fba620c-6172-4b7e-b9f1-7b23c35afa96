b0VIM 8.2      M��_�2 �   rgb764                                  RGB764-03                               ~rgb764/Workspace/iqs/conf/production/log4j2_LOCAL_131.xml                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        
 3210    #"! U                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 tp           F                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             ad  v  �     F       �  �  �  g  Z  +  �  �  �  n      �
  �
  �
  C
  5
  
  �  �  �  �  j    �  �  �  �  >  0    �
  �
  �
  �
  c
  
  �	  �	  �	  k	  ;	  ,	  	  	  �  `  ;  �  �  �  �  �  x  j  i  ^    �  �  �  �  {  Q  '  �  �  �  �  �  �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        </configuration> 	</loggers> 		</root> 			<appender-ref ref="RollingFileDebug" /> 			<appender-ref ref="RollingFileError" /> 			<appender-ref ref="RollingFileWarn" /> 			<appender-ref ref="RollingFileInfo" /> 			<appender-ref ref="Console" /> 		<root level="DEBUG">  		<logger name="org.apache.ibatis" level="WARN" /> 		<logger name="org.springframework" level="WARN" /> 		<!--过滤掉spring和mybatis的一些无用的debug信息 --> 	<loggers>  	</appenders> 		</RollingFile> 			</Policies> 				<SizeBasedTriggeringPolicy size="100 MB" /> 				<TimeBasedTriggeringPolicy /> 			<Policies> 			<PatternLayout pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n" /> 			<ThresholdFilter level="DEBUG" /> 					 filePattern="${sys:user.home}/logs/hpaasvc/$${date:yyyy-MM}/debug-%d{yyyy-MM-dd}-%i.log"> 		<RollingFile name="RollingFileDebug" fileName="${sys:user.home}/logs/hpaasvc/debug.log"  		</RollingFile> 			</Policies> 				<SizeBasedTriggeringPolicy size="100 MB" /> 				<TimeBasedTriggeringPolicy /> 			<Policies> 			<PatternLayout pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n" /> 			<ThresholdFilter level="ERROR" /> 					 filePattern="${sys:user.home}/logs/hpaasvc/$${date:yyyy-MM}/error-%d{yyyy-MM-dd}-%i.log"> 		<RollingFile name="RollingFileError" fileName="${sys:user.home}/logs/hpaasvc/error.log"  		</RollingFile> 			</Policies> 				<SizeBasedTriggeringPolicy size="100 MB" /> 				<TimeBasedTriggeringPolicy /> 			<Policies> 			<PatternLayout pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n" /> 			</Filters> 				<ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL" /> 				<ThresholdFilter level="WARN" /> 			<Filters> 					 filePattern="${sys:user.home}/logs/hpaasvc/$${date:yyyy-MM}/warn-%d{yyyy-MM-dd}-%i.log"> 		<RollingFile name="RollingFileWarn" fileName="${sys:user.home}/logs/hpaasvc/warn.log"  		</RollingFile> 			</Policies> 				<SizeBasedTriggeringPolicy size="100 MB" /> 				<TimeBasedTriggeringPolicy /> 			<Policies> 			<PatternLayout pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n" /> 			</Filters> 				<ThresholdFilter level="WARN" onMatch="DENY" onMismatch="NEUTRAL" /> 				<ThresholdFilter level="INFO" /> 			<Filters> 					 filePattern="${sys:user.home}/logs/hpaasvc/$${date:yyyy-MM}/info-%d{yyyy-MM-dd}-%i.log"> 		<RollingFile name="RollingFileInfo" fileName="${sys:user.home}/logs/hpaasvc/info.log"  		</console> 			<PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss:SSS} [%t] [%-5level] - %l - %m%n" /> 		<console name="Console" target="SYSTEM_OUT"> 	<appenders> <configuration status="debug"> <!--<configuration status="warn">--> <!--设置log4j2的自身log级别为warn --> <?xml version="1.0" encoding="UTF-8"?> 