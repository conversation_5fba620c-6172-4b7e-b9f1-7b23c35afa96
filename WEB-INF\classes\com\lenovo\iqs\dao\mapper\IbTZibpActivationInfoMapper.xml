<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTZibpActivationInfoMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTZibpActivationInfo" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="client" property="client" jdbcType="INTEGER" />
    <result column="serial_no1" property="serialNo1" jdbcType="VARCHAR" />
    <result column="serial_no2" property="serialNo2" jdbcType="VARCHAR" />
    <result column="track_id" property="trackId" jdbcType="VARCHAR" />
    <result column="msn" property="msn" jdbcType="VARCHAR" />
    <result column="country_iso_code" property="countryIsoCode" jdbcType="VARCHAR" />
    <result column="hsn" property="hsn" jdbcType="VARCHAR" />
    <result column="activation_date" property="activationDate" jdbcType="TIMESTAMP" />
    <result column="last_mod_by" property="lastModBy" jdbcType="VARCHAR" />
    <result column="last_mod_datetime" property="lastModDatetime" jdbcType="TIMESTAMP" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_datetime" property="createDatetime" jdbcType="TIMESTAMP" />
    <result column="update_status" property="updateStatus" jdbcType="VARCHAR" />
    <result column="error_message" property="errorMessage" jdbcType="VARCHAR" />
    <result column="batch_number" property="batchNumber" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, client, serial_no1, serial_no2, track_id, msn, country_iso_code, 
    hsn, activation_date, last_mod_by, last_mod_datetime, create_by, create_datetime, 
    update_status, error_message, batch_number
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_zibp_activation_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from ib_t_zibp_activation_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTZibpActivationInfo" >
    insert into ib_t_zibp_activation_info (id, client, serial_no1, 
      serial_no2, track_id, 
      msn, country_iso_code, hsn, 
      activation_date, last_mod_by, last_mod_datetime, 
      create_by, create_datetime, update_status, 
      error_message, batch_number)
    values (#{id,jdbcType=INTEGER}, #{client,jdbcType=INTEGER}, #{serialNo1,jdbcType=VARCHAR}, 
      #{serialNo2,jdbcType=VARCHAR}, #{trackId,jdbcType=VARCHAR}, 
      #{msn,jdbcType=VARCHAR}, #{countryIsoCode,jdbcType=VARCHAR}, #{hsn,jdbcType=VARCHAR}, 
      #{activationDate,jdbcType=TIMESTAMP}, #{lastModBy,jdbcType=VARCHAR}, #{lastModDatetime,jdbcType=TIMESTAMP}, 
      #{createBy,jdbcType=VARCHAR}, #{createDatetime,jdbcType=TIMESTAMP}, #{updateStatus,jdbcType=VARCHAR}, 
      #{errorMessage,jdbcType=VARCHAR}, #{batchNumber,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTZibpActivationInfo" >
    insert into ib_t_zibp_activation_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="client != null" >
        client,
      </if>
      <if test="serialNo1 != null" >
        serial_no1,
      </if>
      <if test="serialNo2 != null" >
        serial_no2,
      </if>
      <if test="trackId != null" >
        track_id,
      </if>
      <if test="msn != null" >
        msn,
      </if>
      <if test="countryIsoCode != null" >
        country_iso_code,
      </if>
      <if test="hsn != null" >
        hsn,
      </if>
      <if test="activationDate != null" >
        activation_date,
      </if>
      <if test="lastModBy != null" >
        last_mod_by,
      </if>
      <if test="lastModDatetime != null" >
        last_mod_datetime,
      </if>
      <if test="createBy != null" >
        create_by,
      </if>
      <if test="createDatetime != null" >
        create_datetime,
      </if>
      <if test="updateStatus != null" >
        update_status,
      </if>
      <if test="errorMessage != null" >
        error_message,
      </if>
      <if test="batchNumber != null" >
        batch_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="client != null" >
        #{client,jdbcType=INTEGER},
      </if>
      <if test="serialNo1 != null" >
        #{serialNo1,jdbcType=VARCHAR},
      </if>
      <if test="serialNo2 != null" >
        #{serialNo2,jdbcType=VARCHAR},
      </if>
      <if test="trackId != null" >
        #{trackId,jdbcType=VARCHAR},
      </if>
      <if test="msn != null" >
        #{msn,jdbcType=VARCHAR},
      </if>
      <if test="countryIsoCode != null" >
        #{countryIsoCode,jdbcType=VARCHAR},
      </if>
      <if test="hsn != null" >
        #{hsn,jdbcType=VARCHAR},
      </if>
      <if test="activationDate != null" >
        #{activationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModBy != null" >
        #{lastModBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModDatetime != null" >
        #{lastModDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null" >
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createDatetime != null" >
        #{createDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateStatus != null" >
        #{updateStatus,jdbcType=VARCHAR},
      </if>
      <if test="errorMessage != null" >
        #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null" >
        #{batchNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTZibpActivationInfo" >
    update ib_t_zibp_activation_info
    <set >
      <if test="client != null" >
        client = #{client,jdbcType=INTEGER},
      </if>
      <if test="serialNo1 != null" >
        serial_no1 = #{serialNo1,jdbcType=VARCHAR},
      </if>
      <if test="serialNo2 != null" >
        serial_no2 = #{serialNo2,jdbcType=VARCHAR},
      </if>
      <if test="trackId != null" >
        track_id = #{trackId,jdbcType=VARCHAR},
      </if>
      <if test="msn != null" >
        msn = #{msn,jdbcType=VARCHAR},
      </if>
      <if test="countryIsoCode != null" >
        country_iso_code = #{countryIsoCode,jdbcType=VARCHAR},
      </if>
      <if test="hsn != null" >
        hsn = #{hsn,jdbcType=VARCHAR},
      </if>
      <if test="activationDate != null" >
        activation_date = #{activationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModBy != null" >
        last_mod_by = #{lastModBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModDatetime != null" >
        last_mod_datetime = #{lastModDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createDatetime != null" >
        create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateStatus != null" >
        update_status = #{updateStatus,jdbcType=VARCHAR},
      </if>
      <if test="errorMessage != null" >
        error_message = #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null" >
        batch_number = #{batchNumber,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTZibpActivationInfo" >
    update ib_t_zibp_activation_info
    set client = #{client,jdbcType=INTEGER},
      serial_no1 = #{serialNo1,jdbcType=VARCHAR},
      serial_no2 = #{serialNo2,jdbcType=VARCHAR},
      track_id = #{trackId,jdbcType=VARCHAR},
      msn = #{msn,jdbcType=VARCHAR},
      country_iso_code = #{countryIsoCode,jdbcType=VARCHAR},
      hsn = #{hsn,jdbcType=VARCHAR},
      activation_date = #{activationDate,jdbcType=TIMESTAMP},
      last_mod_by = #{lastModBy,jdbcType=VARCHAR},
      last_mod_datetime = #{lastModDatetime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
      update_status = #{updateStatus,jdbcType=VARCHAR},
      error_message = #{errorMessage,jdbcType=VARCHAR},
      batch_number = #{batchNumber,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>