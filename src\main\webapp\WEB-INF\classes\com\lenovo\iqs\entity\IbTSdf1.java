/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTSdf1 implements Serializable { private Integer id; private String wtyId; private String language; private String wtType;
/*    */   private String wtyCategory;
/*    */   private String wtyDescription;
/*    */   
/*  6 */   public void setId(Integer id) { this.id = id; } private String wtyText; private String wtyDura; private String duraUnit; private String accInd; private String acindDescr; private static final long serialVersionUID = 1L; public void setWtyId(String wtyId) { this.wtyId = wtyId; } public void setLanguage(String language) { this.language = language; } public void setWtType(String wtType) { this.wtType = wtType; } public void setWtyCategory(String wtyCategory) { this.wtyCategory = wtyCategory; } public void setWtyDescription(String wtyDescription) { this.wtyDescription = wtyDescription; } public void setWtyText(String wtyText) { this.wtyText = wtyText; } public void setWtyDura(String wtyDura) { this.wtyDura = wtyDura; } public void setDuraUnit(String duraUnit) { this.duraUnit = duraUnit; } public void setAccInd(String accInd) { this.accInd = accInd; } public void setAcindDescr(String acindDescr) { this.acindDescr = acindDescr; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTSdf1)) return false;  com.lenovo.iqs.entity.IbTSdf1 other = (com.lenovo.iqs.entity.IbTSdf1)o; if (!other.canEqual(this)) return false;  Object this$id = getId(), other$id = other.getId(); if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id)) return false;  Object this$wtyId = getWtyId(), other$wtyId = other.getWtyId(); if ((this$wtyId == null) ? (other$wtyId != null) : !this$wtyId.equals(other$wtyId)) return false;  Object this$language = getLanguage(), other$language = other.getLanguage(); if ((this$language == null) ? (other$language != null) : !this$language.equals(other$language)) return false;  Object this$wtType = getWtType(), other$wtType = other.getWtType(); if ((this$wtType == null) ? (other$wtType != null) : !this$wtType.equals(other$wtType)) return false;  Object this$wtyCategory = getWtyCategory(), other$wtyCategory = other.getWtyCategory(); if ((this$wtyCategory == null) ? (other$wtyCategory != null) : !this$wtyCategory.equals(other$wtyCategory)) return false;  Object this$wtyDescription = getWtyDescription(), other$wtyDescription = other.getWtyDescription(); if ((this$wtyDescription == null) ? (other$wtyDescription != null) : !this$wtyDescription.equals(other$wtyDescription)) return false;  Object this$wtyText = getWtyText(), other$wtyText = other.getWtyText(); if ((this$wtyText == null) ? (other$wtyText != null) : !this$wtyText.equals(other$wtyText)) return false;  Object this$wtyDura = getWtyDura(), other$wtyDura = other.getWtyDura(); if ((this$wtyDura == null) ? (other$wtyDura != null) : !this$wtyDura.equals(other$wtyDura)) return false;  Object this$duraUnit = getDuraUnit(), other$duraUnit = other.getDuraUnit(); if ((this$duraUnit == null) ? (other$duraUnit != null) : !this$duraUnit.equals(other$duraUnit)) return false;  Object this$accInd = getAccInd(), other$accInd = other.getAccInd(); if ((this$accInd == null) ? (other$accInd != null) : !this$accInd.equals(other$accInd)) return false;  Object this$acindDescr = getAcindDescr(), other$acindDescr = other.getAcindDescr(); return !((this$acindDescr == null) ? (other$acindDescr != null) : !this$acindDescr.equals(other$acindDescr)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTSdf1; } public int hashCode() { int PRIME = 59; result = 1; Object $id = getId(); result = result * 59 + (($id == null) ? 43 : $id.hashCode()); Object $wtyId = getWtyId(); result = result * 59 + (($wtyId == null) ? 43 : $wtyId.hashCode()); Object $language = getLanguage(); result = result * 59 + (($language == null) ? 43 : $language.hashCode()); Object $wtType = getWtType(); result = result * 59 + (($wtType == null) ? 43 : $wtType.hashCode()); Object $wtyCategory = getWtyCategory(); result = result * 59 + (($wtyCategory == null) ? 43 : $wtyCategory.hashCode()); Object $wtyDescription = getWtyDescription(); result = result * 59 + (($wtyDescription == null) ? 43 : $wtyDescription.hashCode()); Object $wtyText = getWtyText(); result = result * 59 + (($wtyText == null) ? 43 : $wtyText.hashCode()); Object $wtyDura = getWtyDura(); result = result * 59 + (($wtyDura == null) ? 43 : $wtyDura.hashCode()); Object $duraUnit = getDuraUnit(); result = result * 59 + (($duraUnit == null) ? 43 : $duraUnit.hashCode()); Object $accInd = getAccInd(); result = result * 59 + (($accInd == null) ? 43 : $accInd.hashCode()); Object $acindDescr = getAcindDescr(); return result * 59 + (($acindDescr == null) ? 43 : $acindDescr.hashCode()); } public String toString() { return "IbTSdf1(id=" + getId() + ", wtyId=" + getWtyId() + ", language=" + getLanguage() + ", wtType=" + getWtType() + ", wtyCategory=" + getWtyCategory() + ", wtyDescription=" + getWtyDescription() + ", wtyText=" + getWtyText() + ", wtyDura=" + getWtyDura() + ", duraUnit=" + getDuraUnit() + ", accInd=" + getAccInd() + ", acindDescr=" + getAcindDescr() + ")"; }
/*    */    public Integer getId() {
/*  8 */     return this.id;
/*    */   } public String getWtyId() {
/* 10 */     return this.wtyId;
/*    */   } public String getLanguage() {
/* 12 */     return this.language;
/*    */   } public String getWtType() {
/* 14 */     return this.wtType;
/*    */   } public String getWtyCategory() {
/* 16 */     return this.wtyCategory;
/*    */   } public String getWtyDescription() {
/* 18 */     return this.wtyDescription;
/*    */   } public String getWtyText() {
/* 20 */     return this.wtyText;
/*    */   } public String getWtyDura() {
/* 22 */     return this.wtyDura;
/*    */   } public String getDuraUnit() {
/* 24 */     return this.duraUnit;
/*    */   } public String getAccInd() {
/* 26 */     return this.accInd;
/*    */   } public String getAcindDescr() {
/* 28 */     return this.acindDescr;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTSdf1.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */