<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdSwwarrPeriodRefMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdSwwarrPeriodRef" >
    <id column="APC_CODE" property="apcCode" jdbcType="VARCHAR" />
    <id column="COUNTRY_CODE" property="countryCode" jdbcType="VARCHAR" />
    <result column="SW_UPG_EXP_PERIOD" property="swUpgExpPeriod" jdbcType="INTEGER" />
    <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR" />
    <result column="CREATION_DATETIME" property="creationDatetime" jdbcType="DATE" />
    <result column="LAST_MOD_BY" property="lastModBy" jdbcType="VARCHAR" />
    <result column="LAST_MOD_DATETIME" property="lastModDatetime" jdbcType="DATE" />
  </resultMap>
  <sql id="Base_Column_List" >
    APC_CODE, COUNTRY_CODE, SW_UPG_EXP_PERIOD, CREATED_BY, CREATION_DATETIME, LAST_MOD_BY, 
    LAST_MOD_DATETIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.lenovo.iqs.entity.IbTUpdSwwarrPeriodRefKey" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_upd_swwarr_period_ref
    where APC_CODE = #{apcCode,jdbcType=VARCHAR}
      and COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdSwwarrPeriodRefKey" >
    delete from ib_t_upd_swwarr_period_ref
    where APC_CODE = #{apcCode,jdbcType=VARCHAR}
      and COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdSwwarrPeriodRef" >
    insert into ib_t_upd_swwarr_period_ref (APC_CODE, COUNTRY_CODE, SW_UPG_EXP_PERIOD, 
      CREATED_BY, CREATION_DATETIME, LAST_MOD_BY, 
      LAST_MOD_DATETIME)
    values (#{apcCode,jdbcType=VARCHAR}, #{countryCode,jdbcType=VARCHAR}, #{swUpgExpPeriod,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=VARCHAR}, #{creationDatetime,jdbcType=DATE}, #{lastModBy,jdbcType=VARCHAR}, 
      #{lastModDatetime,jdbcType=DATE})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdSwwarrPeriodRef" >
    insert into ib_t_upd_swwarr_period_ref
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="apcCode != null" >
        APC_CODE,
      </if>
      <if test="countryCode != null" >
        COUNTRY_CODE,
      </if>
      <if test="swUpgExpPeriod != null" >
        SW_UPG_EXP_PERIOD,
      </if>
      <if test="createdBy != null" >
        CREATED_BY,
      </if>
      <if test="creationDatetime != null" >
        CREATION_DATETIME,
      </if>
      <if test="lastModBy != null" >
        LAST_MOD_BY,
      </if>
      <if test="lastModDatetime != null" >
        LAST_MOD_DATETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="apcCode != null" >
        #{apcCode,jdbcType=VARCHAR},
      </if>
      <if test="countryCode != null" >
        #{countryCode,jdbcType=VARCHAR},
      </if>
      <if test="swUpgExpPeriod != null" >
        #{swUpgExpPeriod,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="creationDatetime != null" >
        #{creationDatetime,jdbcType=DATE},
      </if>
      <if test="lastModBy != null" >
        #{lastModBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModDatetime != null" >
        #{lastModDatetime,jdbcType=DATE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTUpdSwwarrPeriodRef" >
    update ib_t_upd_swwarr_period_ref
    <set >
      <if test="swUpgExpPeriod != null" >
        SW_UPG_EXP_PERIOD = #{swUpgExpPeriod,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="creationDatetime != null" >
        CREATION_DATETIME = #{creationDatetime,jdbcType=DATE},
      </if>
      <if test="lastModBy != null" >
        LAST_MOD_BY = #{lastModBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModDatetime != null" >
        LAST_MOD_DATETIME = #{lastModDatetime,jdbcType=DATE},
      </if>
    </set>
    where APC_CODE = #{apcCode,jdbcType=VARCHAR}
      and COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdSwwarrPeriodRef" >
    update ib_t_upd_swwarr_period_ref
    set SW_UPG_EXP_PERIOD = #{swUpgExpPeriod,jdbcType=INTEGER},
      CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      CREATION_DATETIME = #{creationDatetime,jdbcType=DATE},
      LAST_MOD_BY = #{lastModBy,jdbcType=VARCHAR},
      LAST_MOD_DATETIME = #{lastModDatetime,jdbcType=DATE}
    where APC_CODE = #{apcCode,jdbcType=VARCHAR}
      and COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
  </update>
</mapper>