/*    */ package WEB-INF.classes.com.lenovo.iqs.simlock.bean;
/*    */ public class SimunlockRequest { private String newIMEI; private String MASCID;
/*    */   private String clientIP;
/*    */   private String clientReqType;
/*    */   private String rsd_log_id;
/*    */   private String prod_id;
/*    */   
/*  8 */   public void setNewIMEI(String newIMEI) { this.newIMEI = newIMEI; } private String type; private String keyname; private String data; private String userId; private String publicIP; private String rsdResponse; private boolean etokenInException; public void setMASCID(String MASCID) { this.MASCID = MASCID; } public void setClientIP(String clientIP) { this.clientIP = clientIP; } public void setClientReqType(String clientReqType) { this.clientReqType = clientReqType; } public void setRsd_log_id(String rsd_log_id) { this.rsd_log_id = rsd_log_id; } public void setProd_id(String prod_id) { this.prod_id = prod_id; } public void setType(String type) { this.type = type; } public void setKeyname(String keyname) { this.keyname = keyname; } public void setData(String data) { this.data = data; } public void setUserId(String userId) { this.userId = userId; } public void setPublicIP(String publicIP) { this.publicIP = publicIP; } public void setRsdResponse(String rsdResponse) { this.rsdResponse = rsdResponse; } public void setEtokenInException(boolean etokenInException) { this.etokenInException = etokenInException; } public void setError(String error) { this.error = error; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.simlock.bean.SimunlockRequest)) return false;  com.lenovo.iqs.simlock.bean.SimunlockRequest other = (com.lenovo.iqs.simlock.bean.SimunlockRequest)o; if (!other.canEqual(this)) return false;  Object this$newIMEI = getNewIMEI(), other$newIMEI = other.getNewIMEI(); if ((this$newIMEI == null) ? (other$newIMEI != null) : !this$newIMEI.equals(other$newIMEI)) return false;  Object this$MASCID = getMASCID(), other$MASCID = other.getMASCID(); if ((this$MASCID == null) ? (other$MASCID != null) : !this$MASCID.equals(other$MASCID)) return false;  Object this$clientIP = getClientIP(), other$clientIP = other.getClientIP(); if ((this$clientIP == null) ? (other$clientIP != null) : !this$clientIP.equals(other$clientIP)) return false;  Object this$clientReqType = getClientReqType(), other$clientReqType = other.getClientReqType(); if ((this$clientReqType == null) ? (other$clientReqType != null) : !this$clientReqType.equals(other$clientReqType)) return false;  Object this$rsd_log_id = getRsd_log_id(), other$rsd_log_id = other.getRsd_log_id(); if ((this$rsd_log_id == null) ? (other$rsd_log_id != null) : !this$rsd_log_id.equals(other$rsd_log_id)) return false;  Object this$prod_id = getProd_id(), other$prod_id = other.getProd_id(); if ((this$prod_id == null) ? (other$prod_id != null) : !this$prod_id.equals(other$prod_id)) return false;  Object this$type = getType(), other$type = other.getType(); if ((this$type == null) ? (other$type != null) : !this$type.equals(other$type)) return false;  Object this$keyname = getKeyname(), other$keyname = other.getKeyname(); if ((this$keyname == null) ? (other$keyname != null) : !this$keyname.equals(other$keyname)) return false;  Object this$data = getData(), other$data = other.getData(); if ((this$data == null) ? (other$data != null) : !this$data.equals(other$data)) return false;  Object this$userId = getUserId(), other$userId = other.getUserId(); if ((this$userId == null) ? (other$userId != null) : !this$userId.equals(other$userId)) return false;  Object this$publicIP = getPublicIP(), other$publicIP = other.getPublicIP(); if ((this$publicIP == null) ? (other$publicIP != null) : !this$publicIP.equals(other$publicIP)) return false;  Object this$rsdResponse = getRsdResponse(), other$rsdResponse = other.getRsdResponse(); if ((this$rsdResponse == null) ? (other$rsdResponse != null) : !this$rsdResponse.equals(other$rsdResponse)) return false;  if (isEtokenInException() != other.isEtokenInException()) return false;  Object this$error = getError(), other$error = other.getError(); return !((this$error == null) ? (other$error != null) : !this$error.equals(other$error)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.simlock.bean.SimunlockRequest; } public int hashCode() { int PRIME = 59; result = 1; Object $newIMEI = getNewIMEI(); result = result * 59 + (($newIMEI == null) ? 43 : $newIMEI.hashCode()); Object $MASCID = getMASCID(); result = result * 59 + (($MASCID == null) ? 43 : $MASCID.hashCode()); Object $clientIP = getClientIP(); result = result * 59 + (($clientIP == null) ? 43 : $clientIP.hashCode()); Object $clientReqType = getClientReqType(); result = result * 59 + (($clientReqType == null) ? 43 : $clientReqType.hashCode()); Object $rsd_log_id = getRsd_log_id(); result = result * 59 + (($rsd_log_id == null) ? 43 : $rsd_log_id.hashCode()); Object $prod_id = getProd_id(); result = result * 59 + (($prod_id == null) ? 43 : $prod_id.hashCode()); Object $type = getType(); result = result * 59 + (($type == null) ? 43 : $type.hashCode()); Object $keyname = getKeyname(); result = result * 59 + (($keyname == null) ? 43 : $keyname.hashCode()); Object $data = getData(); result = result * 59 + (($data == null) ? 43 : $data.hashCode()); Object $userId = getUserId(); result = result * 59 + (($userId == null) ? 43 : $userId.hashCode()); Object $publicIP = getPublicIP(); result = result * 59 + (($publicIP == null) ? 43 : $publicIP.hashCode()); Object $rsdResponse = getRsdResponse(); result = result * 59 + (($rsdResponse == null) ? 43 : $rsdResponse.hashCode()); result = result * 59 + (isEtokenInException() ? 79 : 97); Object $error = getError(); return result * 59 + (($error == null) ? 43 : $error.hashCode()); } public String toString() { return "SimunlockRequest(newIMEI=" + getNewIMEI() + ", MASCID=" + getMASCID() + ", clientIP=" + getClientIP() + ", clientReqType=" + getClientReqType() + ", rsd_log_id=" + getRsd_log_id() + ", prod_id=" + getProd_id() + ", type=" + getType() + ", keyname=" + getKeyname() + ", data=" + getData() + ", userId=" + getUserId() + ", publicIP=" + getPublicIP() + ", rsdResponse=" + getRsdResponse() + ", etokenInException=" + isEtokenInException() + ", error=" + getError() + ")"; }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getNewIMEI() {
/* 22 */     return this.newIMEI;
/* 23 */   } public String getMASCID() { return this.MASCID; }
/* 24 */   public String getClientIP() { return this.clientIP; }
/* 25 */   public String getClientReqType() { return this.clientReqType; }
/* 26 */   public String getRsd_log_id() { return this.rsd_log_id; }
/* 27 */   public String getProd_id() { return this.prod_id; }
/* 28 */   public String getType() { return this.type; }
/* 29 */   public String getKeyname() { return this.keyname; }
/* 30 */   public String getData() { return this.data; }
/* 31 */   public String getUserId() { return this.userId; }
/* 32 */   public String getPublicIP() { return this.publicIP; }
/* 33 */   public String getRsdResponse() { return this.rsdResponse; } public boolean isEtokenInException() {
/* 34 */     return this.etokenInException;
/* 35 */   } private String error = ""; public String getError() { return this.error; }
/*    */    }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\simlock\bean\SimunlockRequest.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */