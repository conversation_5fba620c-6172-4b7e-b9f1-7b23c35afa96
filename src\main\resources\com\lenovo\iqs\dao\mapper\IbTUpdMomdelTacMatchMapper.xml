<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdMomdelTacMatchMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdMomdelTacMatch" >
    <result column="MODEL" property="model" jdbcType="VARCHAR" />
    <result column="TAC_CODE" property="tacCode" jdbcType="VARCHAR" />
    <result column="APC_CODE" property="apcCode" jdbcType="VARCHAR" />
    <result column="MKT_NAME" property="mktName" jdbcType="VARCHAR" />
    <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR" />
    <result column="UPDATED_DATE" property="updatedDate" jdbcType="DATE" />
    <result column="REC_EXTR_DATE" property="recExtrDate" jdbcType="DATE" />
    <result column="REC_SRC" property="recSrc" jdbcType="VARCHAR" />
    <result column="LAST_UPDATE_DATE" property="lastUpdateDate" jdbcType="DATE" />
  </resultMap>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdMomdelTacMatch" >
    insert into ib_t_upd_momdel_tac_match (MODEL, TAC_CODE, APC_CODE, 
      MKT_NAME, UPDATED_BY, UPDATED_DATE, 
      REC_EXTR_DATE, REC_SRC, LAST_UPDATE_DATE
      )
    values (#{model,jdbcType=VARCHAR}, #{tacCode,jdbcType=VARCHAR}, #{apcCode,jdbcType=VARCHAR}, 
      #{mktName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=DATE}, 
      #{recExtrDate,jdbcType=DATE}, #{recSrc,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=DATE}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdMomdelTacMatch" >
    insert into ib_t_upd_momdel_tac_match
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="model != null" >
        MODEL,
      </if>
      <if test="tacCode != null" >
        TAC_CODE,
      </if>
      <if test="apcCode != null" >
        APC_CODE,
      </if>
      <if test="mktName != null" >
        MKT_NAME,
      </if>
      <if test="updatedBy != null" >
        UPDATED_BY,
      </if>
      <if test="updatedDate != null" >
        UPDATED_DATE,
      </if>
      <if test="recExtrDate != null" >
        REC_EXTR_DATE,
      </if>
      <if test="recSrc != null" >
        REC_SRC,
      </if>
      <if test="lastUpdateDate != null" >
        LAST_UPDATE_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="model != null" >
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="tacCode != null" >
        #{tacCode,jdbcType=VARCHAR},
      </if>
      <if test="apcCode != null" >
        #{apcCode,jdbcType=VARCHAR},
      </if>
      <if test="mktName != null" >
        #{mktName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null" >
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedDate != null" >
        #{updatedDate,jdbcType=DATE},
      </if>
      <if test="recExtrDate != null" >
        #{recExtrDate,jdbcType=DATE},
      </if>
      <if test="recSrc != null" >
        #{recSrc,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        #{lastUpdateDate,jdbcType=DATE},
      </if>
    </trim>
  </insert>
</mapper>