/*     */ package WEB-INF.classes.com.lenovo.iqs.sap;
/*     */ import com.lenovo.iqs.sap.UpdSnQueryParam;
/*     */ import com.sap.conn.jco.JCoDestination;
/*     */ import com.sap.conn.jco.JCoFunction;
/*     */ import com.sap.conn.jco.JCoParameterList;
/*     */ import com.sap.conn.jco.JCoTable;
/*     */ import java.lang.reflect.Field;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.List;
/*     */ import org.apache.cxf.common.util.StringUtils;
/*     */ import org.apache.log4j.Logger;
/*     */ 
/*     */ @Service
/*     */ public class SAPRfcs {
/*  16 */   private static final Logger log = Logger.getLogger(com.lenovo.iqs.sap.SAPRfcs.class);
/*     */   private static Properties props;
/*     */   @Autowired
/*     */   private SAPConnection connection;
/*     */   
/*     */   static {
/*     */     try {
/*  23 */       InputStream in = com.lenovo.iqs.sap.SAPRfcs.class.getClassLoader().getResourceAsStream("com/lenovo/iqs/sap/config/fieldMapping.properties");
/*  24 */       props = new Properties();
/*  25 */       props.load(in);
/*  26 */     } catch (Exception e) {
/*  27 */       log.error("IBASE RFC 加载映射文件失败", e);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public <T> List<T> callUpdSnReposFunction(String serialNo, String serialNoType, Class<T> retType, String rfcResultType) throws Exception {
/*  35 */     Date startTime = new Date();
/*     */     
/*  37 */     JCoFunction function = null;
/*  38 */     JCoDestination destination = this.connection.connect();
/*     */ 
/*     */     
/*  41 */     function = destination.getRepository().getFunction("ZIB_ZUPD_SN_REPOS_SEARCH");
/*  42 */     JCoParameterList inTableParam = function.getTableParameterList();
/*     */ 
/*     */     
/*  45 */     JCoTable inTable = inTableParam.getTable("CT_SEARCH_CRITERION");
/*     */     
/*  47 */     inTable.appendRow();
/*  48 */     inTable.setValue("TYPE", serialNoType);
/*  49 */     inTable.setValue("VALUE", serialNo);
/*  50 */     inTable.setValue("RETURN_FLAG", rfcResultType);
/*     */ 
/*     */     
/*  53 */     function.execute(destination);
/*     */ 
/*     */     
/*  56 */     JCoTable outTable = function.getTableParameterList().getTable("ET_ZUPD_SN_REPOS");
/*     */     
/*  58 */     Date endTime = new Date();
/*  59 */     log.info("Imei:callUpdSnReposFunction for input serial:" + serialNo + " cost time: " + (endTime.getTime() - startTime.getTime()) + "startTime:" + startTime + "endTime:" + endTime);
/*     */ 
/*     */     
/*  62 */     if (outTable.isEmpty()) {
/*  63 */       return null;
/*     */     }
/*     */     
/*  66 */     List<T> retList = new ArrayList<>();
/*     */     while (true) {
/*  68 */       T t = ConvertToTargetObj(outTable, retType);
/*  69 */       retList.add(t);
/*  70 */       if (outTable.isLastRow()) {
/*     */         break;
/*     */       }
/*  73 */       outTable.nextRow();
/*     */     } 
/*     */     
/*  76 */     return retList;
/*     */   }
/*     */   
/*     */   public <T> List<T> callUpdSnReposFunction(List<UpdSnQueryParam> queryParams, Class<T> retType) throws Exception {
/*  80 */     Date startTime = new Date();
/*  81 */     StringBuilder stringBuilder = new StringBuilder();
/*     */     
/*  83 */     JCoFunction function = null;
/*  84 */     JCoDestination destination = this.connection.connect();
/*     */ 
/*     */     
/*  87 */     function = destination.getRepository().getFunction("ZIB_ZUPD_SN_REPOS_SEARCH");
/*  88 */     JCoParameterList inTableParam = function.getTableParameterList();
/*     */ 
/*     */     
/*  91 */     JCoTable inTable = inTableParam.getTable("CT_SEARCH_CRITERION");
/*  92 */     for (UpdSnQueryParam param : queryParams) {
/*  93 */       String serialNo = param.getSerialNo();
/*  94 */       String serialNoType = param.getSerialNoType();
/*  95 */       String flag = param.getFlag();
/*  96 */       if (StringUtils.isEmpty(serialNo) || StringUtils.isEmpty(serialNoType)) {
/*     */         continue;
/*     */       }
/*  99 */       inTable.appendRow();
/* 100 */       inTable.setValue("TYPE", serialNoType);
/* 101 */       inTable.setValue("VALUE", serialNo);
/* 102 */       inTable.setValue("RETURN_FLAG", flag);
/* 103 */       stringBuilder.append(serialNo + ",");
/*     */     } 
/*     */     
/* 106 */     function.execute(destination);
/*     */     
/* 108 */     JCoTable outTable = function.getTableParameterList().getTable("ET_ZUPD_SN_REPOS");
/*     */     
/* 110 */     Date endTime = new Date();
/* 111 */     log.info("Call Ibase RFC:callUpdSnReposFunction batch for input serials:" + stringBuilder.toString() + "cost time: " + (endTime.getTime() - startTime.getTime()) + "startTime:" + startTime + "endTime:" + endTime);
/*     */ 
/*     */     
/* 114 */     if (outTable.isEmpty()) {
/* 115 */       return null;
/*     */     }
/*     */     
/* 118 */     List<T> retList = new ArrayList<>();
/*     */     while (true) {
/* 120 */       T t = ConvertToTargetObj(outTable, retType);
/* 121 */       retList.add(t);
/* 122 */       if (outTable.isLastRow()) {
/*     */         break;
/*     */       }
/* 125 */       outTable.nextRow();
/*     */     } 
/* 127 */     return retList;
/*     */   }
/*     */   
/*     */   public boolean isSerialExists(String serialNoType, String serialNo, String rfcResultType) throws Exception {
/* 131 */     Date startTime = new Date();
/*     */     
/* 133 */     JCoFunction function = null;
/* 134 */     JCoDestination destination = this.connection.connect();
/*     */ 
/*     */     
/* 137 */     function = destination.getRepository().getFunction("ZIB_ZUPD_SN_REPOS_SEARCH");
/* 138 */     JCoParameterList inTableParam = function.getTableParameterList();
/*     */ 
/*     */     
/* 141 */     JCoTable inTable = inTableParam.getTable("CT_SEARCH_CRITERION");
/*     */     
/* 143 */     inTable.appendRow();
/* 144 */     inTable.setValue("TYPE", serialNoType);
/* 145 */     inTable.setValue("VALUE", serialNo);
/* 146 */     inTable.setValue("RETURN_FLAG", rfcResultType);
/*     */ 
/*     */     
/* 149 */     function.execute(destination);
/*     */ 
/*     */     
/* 152 */     JCoTable outTable = function.getTableParameterList().getTable("ET_ZUPD_SN_REPOS");
/*     */     
/* 154 */     Date endTime = new Date();
/* 155 */     log.info("Call Ibase RFC:isSerialExists for input serial" + serialNo + "cost time: " + (endTime.getTime() - startTime.getTime()) + "startTime:" + startTime + "endTime:" + endTime);
/*     */ 
/*     */     
/* 158 */     if (outTable.isEmpty()) {
/* 159 */       return false;
/*     */     }
/* 161 */     return true;
/*     */   }
/*     */   
/*     */   public int updatePassword(String serialNo, String activeCode, String newScp, String sscp) throws Exception {
/* 165 */     Date startTime = new Date();
/*     */     
/* 167 */     JCoFunction function = null;
/* 168 */     JCoDestination destination = this.connection.connect();
/*     */ 
/*     */     
/* 171 */     function = destination.getRepository().getFunction("ZIB_RFC_UPDATE_UPD");
/* 172 */     JCoParameterList importParam = function.getImportParameterList();
/*     */ 
/*     */ 
/*     */     
/* 176 */     importParam.setValue("P_IMEI", serialNo);
/* 177 */     importParam.setValue("P_SSCP_LOCK_CODE", activeCode);
/* 178 */     importParam.setValue("P_ACTIVE_LOCK_CODE", newScp);
/* 179 */     importParam.setValue("P_NWSCP_LOCK_CODE", sscp);
/*     */ 
/*     */     
/* 182 */     function.execute(destination);
/*     */ 
/*     */     
/* 185 */     JCoParameterList exportParam = function.getExportParameterList();
/*     */     
/* 187 */     Date endTime = new Date();
/* 188 */     log.info("Call Ibase RFC:updatePassword for input serial" + serialNo + "cost time: " + (endTime.getTime() - startTime.getTime()) + "startTime:" + startTime + "endTime:" + endTime);
/*     */     
/* 190 */     return exportParam.getInt("P_ROW_COUNT");
/*     */   }
/*     */   
/*     */   public void swapImei(String serialNoType, String swapType, String sourceCode, String oldImei, String newImei, String masCid, Date repairDate) throws Exception {
/* 194 */     Date startTime = new Date();
/*     */     
/* 196 */     JCoFunction function = null;
/* 197 */     JCoDestination destination = this.connection.connect();
/*     */ 
/*     */     
/* 200 */     function = destination.getRepository().getFunction("ZMST_SWAP_REAL_UPDATE");
/* 201 */     JCoParameterList importParam = function.getTableParameterList();
/* 202 */     JCoTable inTable = importParam.getTable(0);
/*     */     
/* 204 */     inTable.appendRow();
/*     */     
/* 206 */     inTable.setValue("SERIALNOTYPE", serialNoType);
/* 207 */     inTable.setValue("SWAPTYPE", swapType);
/* 208 */     inTable.setValue("SOURCE_CODE", sourceCode);
/* 209 */     inTable.setValue("SERIALNOIN", oldImei);
/* 210 */     inTable.setValue("SERIALNOOUT", newImei);
/* 211 */     inTable.setValue("MASCID", masCid);
/* 212 */     inTable.setValue("REPAIRDATE", repairDate);
/*     */     
/* 214 */     Date endTime = new Date();
/* 215 */     log.info("Call Ibase RFC:swap for old IMEI:" + oldImei + " and new IMEI:" + newImei + " cost time: " + (endTime.getTime() - startTime.getTime()) + "startTime;" + startTime + "endTime:" + endTime);
/*     */ 
/*     */ 
/*     */     
/* 219 */     function.execute(destination);
/*     */   }
/*     */   
/*     */   private <T> T ConvertToTargetObj(JCoTable outTable, Class<T> retType) throws Exception {
/* 223 */     String className = retType.getName();
/* 224 */     className = className.substring(className.lastIndexOf(".") + 1);
/* 225 */     Field[] fields = retType.getDeclaredFields();
/* 226 */     T target = retType.newInstance();
/* 227 */     for (Field field : fields) {
/* 228 */       field.setAccessible(true);
/* 229 */       Class<?> beanFieldType = field.getType();
/* 230 */       String beanFieldName = field.getName();
/* 231 */       String dbFieldNames = props.getProperty(className + "-" + beanFieldName);
/* 232 */       if (!StringUtils.isEmpty(dbFieldNames)) {
/*     */ 
/*     */         
/* 235 */         String[] dbFieldNameArray = dbFieldNames.split(",");
/* 236 */         for (String dbFieldName : dbFieldNameArray) {
/* 237 */           String dbFieldVal = outTable.getString(dbFieldName);
/*     */           
/* 239 */           if (!StringUtils.isEmpty(dbFieldVal)) {
/* 240 */             if (beanFieldType == String.class) {
/* 241 */               field.set(target, dbFieldVal.trim()); break;
/* 242 */             }  if (beanFieldType == Date.class) {
/* 243 */               if (dbFieldVal.length() == 14) {
/* 244 */                 Date tempDate = (new SimpleDateFormat("yyyyMMddHHmmss")).parse(dbFieldVal.trim());
/* 245 */                 field.set(target, tempDate); break;
/*     */               } 
/* 247 */               field.set(target, null);
/*     */             } 
/*     */             break;
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/* 254 */     return target;
/*     */   }
/*     */   
/*     */   public String getIMEIByProcessorId(String processorID) throws Exception {
/* 258 */     log.info("PID here is " + processorID);
/* 259 */     JCoFunction function = null;
/* 260 */     JCoDestination destination = this.connection.connect();
/*     */     
/* 262 */     function = destination.getRepository().getFunction("ZIB_QUERY_BTLD_IMEI_BY_PID");
/* 263 */     JCoParameterList inParamList = function.getImportParameterList();
/*     */ 
/*     */     
/* 266 */     inParamList.setValue("I_PID", processorID);
/* 267 */     function.execute(destination);
/*     */     
/* 269 */     String eImei = function.getExportParameterList().getString("E_IMEI");
/* 270 */     if (eImei.trim().equalsIgnoreCase("E_IMEI") || eImei.trim().equalsIgnoreCase("")) {
/* 271 */       throw new Exception("Unable to get single IMEI from iBase");
/*     */     }
/* 273 */     log.info("imei got here is for PID " + processorID + " is " + eImei);
/* 274 */     return eImei;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> getIMEIListByProcessorId(String processorID) throws Exception {
/* 280 */     log.info("PID here is " + processorID);
/* 281 */     JCoFunction function = null;
/* 282 */     JCoDestination destination = this.connection.connect();
/*     */     
/* 284 */     function = destination.getRepository().getFunction("ZIB_QUERY_BTLD_IMEI_BY_PID");
/* 285 */     JCoParameterList inParamList = function.getImportParameterList();
/*     */ 
/*     */     
/* 288 */     inParamList.setValue("I_PID", processorID);
/* 289 */     function.execute(destination);
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 294 */     JCoTable outTable = function.getExportParameterList().getTable("E_IMEI");
/* 295 */     log.info("Result from iBase " + outTable.toString());
/* 296 */     log.info("Result from iBase " + outTable);
/* 297 */     List<String> eImei = new ArrayList<>();
/* 298 */     String tempIMEI = null;
/* 299 */     if (!outTable.isEmpty()) {
/*     */       while (true) {
/* 301 */         tempIMEI = outTable.getString("ZIMEI");
/* 302 */         eImei.add(tempIMEI);
/* 303 */         tempIMEI = null;
/* 304 */         if (outTable.isLastRow()) {
/*     */           break;
/*     */         }
/* 307 */         outTable.nextRow();
/*     */       } 
/*     */     }
/* 310 */     log.info("imei got here is for PID " + processorID + " is " + eImei);
/* 311 */     return eImei;
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\sap\SAPRfcs.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */