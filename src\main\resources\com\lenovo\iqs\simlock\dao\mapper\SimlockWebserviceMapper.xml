<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.rsu.dao.RsuWebserviceMapper" >
<!--	<select id="getAllowedTMOValues" resultType="java.lang.String">-->
<!--        SELECT `VALUE` FROM ib_t_upd_config WHERE SECTION = 'RSUSERVICE' AND `KEY` = 'MNO';-->
<!--    </select>-->

<!--    <select id="getCorrectModel" resultType="java.lang.String" parameterType="java.lang.String">-->
<!--        SELECT `VALUE` FROM ib_t_upd_config WHERE SECTION = 'RSUMODEL_MAP' AND `KEY` = #{deviceModel};-->
<!--    </select>-->

<!--    <select id="getSipForModel" resultType="java.lang.String" parameterType="java.lang.String">-->
<!--        SELECT `VALUE` FROM ib_t_upd_config WHERE SECTION = 'RSUSERVICE_SIP' AND `KEY` = #{deviceModel};-->
<!--    </select>-->

<!--    <select id="getSocForModel" resultType="java.lang.String" parameterType="java.lang.String">-->
<!--        SELECT `VALUE` FROM ib_t_upd_config WHERE SECTION = 'RSUSERVICE_SOC' AND `KEY` = #{deviceModel}-->
<!--    </select>-->

        <select id="getCertModelMapping" resultType="java.lang.String" parameterType="java.lang.String">
            SELECT `VALUE` FROM ib_t_upd_config WHERE SECTION = 'CERTMODEL_MAP_KEYS' AND `KEY` = #{deviceModel};
        </select>
</mapper>