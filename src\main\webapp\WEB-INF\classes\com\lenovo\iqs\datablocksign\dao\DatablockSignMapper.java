package WEB-INF.classes.com.lenovo.iqs.datablocksign.dao;

import java.util.Map;
import org.springframework.stereotype.Repository;

@Repository
public interface DatablockSignMapper {
  Map<String, String> queryWhiteMascs(String paramString);
  
  Integer countWihiteMascs(String paramString);
  
  Integer countWhiteImei(String paramString);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\dao\DatablockSignMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */