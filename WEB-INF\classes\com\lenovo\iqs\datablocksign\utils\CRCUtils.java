/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.utils;
/*    */ 
/*    */ import java.util.zip.CRC32;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CRCUtils
/*    */ {
/*    */   private static final int HEX_CRC_LENGTH = 8;
/*    */   
/*    */   public static int caculateCRC(byte[] buffer) {
/* 14 */     CRC32 crc32 = new CRC32();
/* 15 */     crc32.update(buffer);
/* 16 */     return (int)crc32.getValue();
/*    */   }
/*    */   public static String caculateHexCRC(byte[] buffer) {
/* 19 */     String hexCRC = Integer.toHexString(caculateCRC(buffer));
/* 20 */     StringBuilder stringBUilder = new StringBuilder(hexCRC);
/* 21 */     for (int i = hexCRC.length(); i < 8; i++) {
/* 22 */       stringBUilder.insert(0, "0");
/*    */     }
/* 24 */     return stringBUilder.toString();
/*    */   }
/*    */   
/*    */   public static void updateCRCCode(byte[] bytes) {
/* 28 */     byte[] bytesWOCRC = new byte[bytes.length - 4];
/* 29 */     System.arraycopy(bytes, 0, bytesWOCRC, 0, bytesWOCRC.length);
/* 30 */     long crc = caculateCRC(bytesWOCRC);
/*    */     
/* 32 */     int index = bytes.length - 4;
/*    */     
/* 34 */     bytes[index++] = (byte)(int)(crc >>> 24L & 0xFFL);
/*    */     
/* 36 */     bytes[index++] = (byte)(int)(crc >>> 16L & 0xFFL);
/* 37 */     bytes[index++] = (byte)(int)(crc >>> 8L & 0xFFL);
/* 38 */     bytes[index] = (byte)(int)(crc >>> 0L & 0xFFL);
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksig\\utils\CRCUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */