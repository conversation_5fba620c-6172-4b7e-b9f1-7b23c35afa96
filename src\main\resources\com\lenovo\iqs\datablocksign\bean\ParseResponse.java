/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.bean;
/*    */ @JsonIgnoreProperties(ignoreUnknown = true)
/*    */ public class ParseResponse {
/*    */   @JsonProperty("datablocks")
/*    */   List<DataBlock> datablocks;
/*    */   @JsonProperty("request_processor_uid")
/*    */   String request_processor_uid;
/*    */   
/*  9 */   public void setDatablocks(List<DataBlock> datablocks) { this.datablocks = datablocks; } @JsonProperty("etoken_ip") String etoken_ip; @JsonProperty("req_protocol_version") int req_protocol_version; @JsonProperty("no_of_data_blocks_in_request") int no_of_data_blocks_in_request; public void setRequest_processor_uid(String request_processor_uid) { this.request_processor_uid = request_processor_uid; } public void setEtoken_ip(String etoken_ip) { this.etoken_ip = etoken_ip; } public void setReq_protocol_version(int req_protocol_version) { this.req_protocol_version = req_protocol_version; } public void setNo_of_data_blocks_in_request(int no_of_data_blocks_in_request) { this.no_of_data_blocks_in_request = no_of_data_blocks_in_request; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.datablocksign.bean.ParseResponse)) return false;  com.lenovo.iqs.datablocksign.bean.ParseResponse other = (com.lenovo.iqs.datablocksign.bean.ParseResponse)o; if (!other.canEqual(this)) return false;  Object<DataBlock> this$datablocks = (Object<DataBlock>)getDatablocks(), other$datablocks = (Object<DataBlock>)other.getDatablocks(); if ((this$datablocks == null) ? (other$datablocks != null) : !this$datablocks.equals(other$datablocks)) return false;  Object this$request_processor_uid = getRequest_processor_uid(), other$request_processor_uid = other.getRequest_processor_uid(); if ((this$request_processor_uid == null) ? (other$request_processor_uid != null) : !this$request_processor_uid.equals(other$request_processor_uid)) return false;  Object this$etoken_ip = getEtoken_ip(), other$etoken_ip = other.getEtoken_ip(); return ((this$etoken_ip == null) ? (other$etoken_ip != null) : !this$etoken_ip.equals(other$etoken_ip)) ? false : ((getReq_protocol_version() != other.getReq_protocol_version()) ? false : (!(getNo_of_data_blocks_in_request() != other.getNo_of_data_blocks_in_request()))); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.datablocksign.bean.ParseResponse; } public int hashCode() { int PRIME = 59; result = 1; Object<DataBlock> $datablocks = (Object<DataBlock>)getDatablocks(); result = result * 59 + (($datablocks == null) ? 43 : $datablocks.hashCode()); Object $request_processor_uid = getRequest_processor_uid(); result = result * 59 + (($request_processor_uid == null) ? 43 : $request_processor_uid.hashCode()); Object $etoken_ip = getEtoken_ip(); result = result * 59 + (($etoken_ip == null) ? 43 : $etoken_ip.hashCode()); result = result * 59 + getReq_protocol_version(); return result * 59 + getNo_of_data_blocks_in_request(); } public String toString() { return "ParseResponse(datablocks=" + getDatablocks() + ", request_processor_uid=" + getRequest_processor_uid() + ", etoken_ip=" + getEtoken_ip() + ", req_protocol_version=" + getReq_protocol_version() + ", no_of_data_blocks_in_request=" + getNo_of_data_blocks_in_request() + ")"; }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public List<DataBlock> getDatablocks() {
/* 30 */     return this.datablocks;
/*    */   } public String getRequest_processor_uid() {
/* 32 */     return this.request_processor_uid;
/*    */   }
/*    */   
/*    */   public String getEtoken_ip() {
/* 36 */     return this.etoken_ip;
/*    */   } public int getReq_protocol_version() {
/* 38 */     return this.req_protocol_version;
/*    */   } public int getNo_of_data_blocks_in_request() {
/* 40 */     return this.no_of_data_blocks_in_request;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\bean\ParseResponse.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */