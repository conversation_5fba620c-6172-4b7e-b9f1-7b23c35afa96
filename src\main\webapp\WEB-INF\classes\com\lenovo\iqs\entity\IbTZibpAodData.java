/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTZibpAodData implements Serializable { private Integer id;
/*    */   private String machineType;
/*    */   private String serialNumber;
/*    */   private String aodType;
/*    */   
/*  7 */   public void setId(Integer id) { this.id = id; } private String modelNumber; private String aodDescription; private Date lastChangeTime; private String partitionChar; private static final long serialVersionUID = 1L; public void setMachineType(String machineType) { this.machineType = machineType; } public void setSerialNumber(String serialNumber) { this.serialNumber = serialNumber; } public void setAodType(String aodType) { this.aodType = aodType; } public void setModelNumber(String modelNumber) { this.modelNumber = modelNumber; } public void setAodDescription(String aodDescription) { this.aodDescription = aodDescription; } public void setLastChangeTime(Date lastChangeTime) { this.lastChangeTime = lastChangeTime; } public void setPartitionChar(String partitionChar) { this.partitionChar = partitionChar; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTZibpAodData)) return false;  com.lenovo.iqs.entity.IbTZibpAodData other = (com.lenovo.iqs.entity.IbTZibpAodData)o; if (!other.canEqual(this)) return false;  Object this$id = getId(), other$id = other.getId(); if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id)) return false;  Object this$machineType = getMachineType(), other$machineType = other.getMachineType(); if ((this$machineType == null) ? (other$machineType != null) : !this$machineType.equals(other$machineType)) return false;  Object this$serialNumber = getSerialNumber(), other$serialNumber = other.getSerialNumber(); if ((this$serialNumber == null) ? (other$serialNumber != null) : !this$serialNumber.equals(other$serialNumber)) return false;  Object this$aodType = getAodType(), other$aodType = other.getAodType(); if ((this$aodType == null) ? (other$aodType != null) : !this$aodType.equals(other$aodType)) return false;  Object this$modelNumber = getModelNumber(), other$modelNumber = other.getModelNumber(); if ((this$modelNumber == null) ? (other$modelNumber != null) : !this$modelNumber.equals(other$modelNumber)) return false;  Object this$aodDescription = getAodDescription(), other$aodDescription = other.getAodDescription(); if ((this$aodDescription == null) ? (other$aodDescription != null) : !this$aodDescription.equals(other$aodDescription)) return false;  Object this$lastChangeTime = getLastChangeTime(), other$lastChangeTime = other.getLastChangeTime(); if ((this$lastChangeTime == null) ? (other$lastChangeTime != null) : !this$lastChangeTime.equals(other$lastChangeTime)) return false;  Object this$partitionChar = getPartitionChar(), other$partitionChar = other.getPartitionChar(); return !((this$partitionChar == null) ? (other$partitionChar != null) : !this$partitionChar.equals(other$partitionChar)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTZibpAodData; } public int hashCode() { int PRIME = 59; result = 1; Object $id = getId(); result = result * 59 + (($id == null) ? 43 : $id.hashCode()); Object $machineType = getMachineType(); result = result * 59 + (($machineType == null) ? 43 : $machineType.hashCode()); Object $serialNumber = getSerialNumber(); result = result * 59 + (($serialNumber == null) ? 43 : $serialNumber.hashCode()); Object $aodType = getAodType(); result = result * 59 + (($aodType == null) ? 43 : $aodType.hashCode()); Object $modelNumber = getModelNumber(); result = result * 59 + (($modelNumber == null) ? 43 : $modelNumber.hashCode()); Object $aodDescription = getAodDescription(); result = result * 59 + (($aodDescription == null) ? 43 : $aodDescription.hashCode()); Object $lastChangeTime = getLastChangeTime(); result = result * 59 + (($lastChangeTime == null) ? 43 : $lastChangeTime.hashCode()); Object $partitionChar = getPartitionChar(); return result * 59 + (($partitionChar == null) ? 43 : $partitionChar.hashCode()); } public String toString() { return "IbTZibpAodData(id=" + getId() + ", machineType=" + getMachineType() + ", serialNumber=" + getSerialNumber() + ", aodType=" + getAodType() + ", modelNumber=" + getModelNumber() + ", aodDescription=" + getAodDescription() + ", lastChangeTime=" + getLastChangeTime() + ", partitionChar=" + getPartitionChar() + ")"; }
/*    */    public Integer getId() {
/*  9 */     return this.id;
/*    */   } public String getMachineType() {
/* 11 */     return this.machineType;
/*    */   } public String getSerialNumber() {
/* 13 */     return this.serialNumber;
/*    */   } public String getAodType() {
/* 15 */     return this.aodType;
/*    */   } public String getModelNumber() {
/* 17 */     return this.modelNumber;
/*    */   } public String getAodDescription() {
/* 19 */     return this.aodDescription;
/*    */   } public Date getLastChangeTime() {
/* 21 */     return this.lastChangeTime;
/*    */   } public String getPartitionChar() {
/* 23 */     return this.partitionChar;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTZibpAodData.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */