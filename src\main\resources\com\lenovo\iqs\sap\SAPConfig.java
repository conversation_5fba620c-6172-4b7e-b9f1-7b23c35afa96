package WEB-INF.classes.com.lenovo.iqs.sap;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class SAPConfig {
  @Value("${ibase.sap.appServerHost}")
  public String appServerHost;
  
  @Value("${ibase.sap.logonGroup}")
  public String logonGroup;
  
  @Value("${ibase.sap.messageServerHost}")
  public String messageServerHost;
  
  @Value("${ibase.sap.systemID}")
  public String systemID;
  
  @Value("${ibase.sap.systemNumber}")
  public String systemNumber;
  
  @Value("${ibase.sap.client}")
  public String client;
  
  @Value("${ibase.sap.user}")
  public String user;
  
  @Value("${ibase.sap.password}")
  public String password;
  
  @Value("${ibase.sap.language}")
  public String language;
  
  @Value("${ibase.sap.poolCapacity}")
  public String poolCapacity;
  
  @Value("${ibase.sap.peakLimit}")
  public String peakLimit;
  
  @Value("${ibase_url}")
  public String ibaseUrl;
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\sap\SAPConfig.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */