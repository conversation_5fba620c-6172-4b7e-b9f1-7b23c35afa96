<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTMaterialDescriptionMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTMaterialDescription" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="material_id" property="materialId" jdbcType="VARCHAR" />
    <result column="language" property="language" jdbcType="VARCHAR" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="last_change_time" property="lastChangeTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, material_id, language, description, last_change_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_material_description
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from ib_t_material_description
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTMaterialDescription" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_material_description (material_id, language, description, 
      last_change_time)
    values (#{materialId,jdbcType=VARCHAR}, #{language,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{lastChangeTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTMaterialDescription" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_material_description
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="materialId != null" >
        material_id,
      </if>
      <if test="language != null" >
        language,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="lastChangeTime != null" >
        last_change_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="materialId != null" >
        #{materialId,jdbcType=VARCHAR},
      </if>
      <if test="language != null" >
        #{language,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="lastChangeTime != null" >
        #{lastChangeTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTMaterialDescription" >
    update ib_t_material_description
    <set >
      <if test="materialId != null" >
        material_id = #{materialId,jdbcType=VARCHAR},
      </if>
      <if test="language != null" >
        language = #{language,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="lastChangeTime != null" >
        last_change_time = #{lastChangeTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTMaterialDescription" >
    update ib_t_material_description
    set material_id = #{materialId,jdbcType=VARCHAR},
      language = #{language,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      last_change_time = #{lastChangeTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>