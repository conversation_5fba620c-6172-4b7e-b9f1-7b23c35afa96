<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdWarrantyCancelCodeRefMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdWarrantyCancelCodeRef" >
    <id column="WARRANTY_CANCEL_CODE" property="warrantyCancelCode" jdbcType="VARCHAR" />
    <result column="START_DATE" property="startDate" jdbcType="DATE" />
    <result column="END_DATE" property="endDate" jdbcType="DATE" />
    <result column="WARRANTY_CANCEL_CODE_DESC" property="warrantyCancelCodeDesc" jdbcType="VARCHAR" />
    <result column="EFFECT_STD_WARRANTY" property="effectStdWarranty" jdbcType="VARCHAR" />
    <result column="EFFECT_EXT_WARRANTY" property="effectExtWarranty" jdbcType="VARCHAR" />
    <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR" />
    <result column="CREATION_DATETIME" property="creationDatetime" jdbcType="DATE" />
    <result column="LAST_MOD_USER" property="lastModUser" jdbcType="VARCHAR" />
    <result column="LAST_MOD_DATE" property="lastModDate" jdbcType="DATE" />
  </resultMap>
  <sql id="Base_Column_List" >
    WARRANTY_CANCEL_CODE, START_DATE, END_DATE, WARRANTY_CANCEL_CODE_DESC, EFFECT_STD_WARRANTY, 
    EFFECT_EXT_WARRANTY, CREATED_BY, CREATION_DATETIME, LAST_MOD_USER, LAST_MOD_DATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_upd_warranty_cancel_code_ref
    where WARRANTY_CANCEL_CODE = #{warrantyCancelCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from ib_t_upd_warranty_cancel_code_ref
    where WARRANTY_CANCEL_CODE = #{warrantyCancelCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdWarrantyCancelCodeRef" >
    insert into ib_t_upd_warranty_cancel_code_ref (WARRANTY_CANCEL_CODE, START_DATE, END_DATE, 
      WARRANTY_CANCEL_CODE_DESC, EFFECT_STD_WARRANTY, 
      EFFECT_EXT_WARRANTY, CREATED_BY, CREATION_DATETIME, 
      LAST_MOD_USER, LAST_MOD_DATE)
    values (#{warrantyCancelCode,jdbcType=VARCHAR}, #{startDate,jdbcType=DATE}, #{endDate,jdbcType=DATE}, 
      #{warrantyCancelCodeDesc,jdbcType=VARCHAR}, #{effectStdWarranty,jdbcType=VARCHAR}, 
      #{effectExtWarranty,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{creationDatetime,jdbcType=DATE}, 
      #{lastModUser,jdbcType=VARCHAR}, #{lastModDate,jdbcType=DATE})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdWarrantyCancelCodeRef" >
    insert into ib_t_upd_warranty_cancel_code_ref
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="warrantyCancelCode != null" >
        WARRANTY_CANCEL_CODE,
      </if>
      <if test="startDate != null" >
        START_DATE,
      </if>
      <if test="endDate != null" >
        END_DATE,
      </if>
      <if test="warrantyCancelCodeDesc != null" >
        WARRANTY_CANCEL_CODE_DESC,
      </if>
      <if test="effectStdWarranty != null" >
        EFFECT_STD_WARRANTY,
      </if>
      <if test="effectExtWarranty != null" >
        EFFECT_EXT_WARRANTY,
      </if>
      <if test="createdBy != null" >
        CREATED_BY,
      </if>
      <if test="creationDatetime != null" >
        CREATION_DATETIME,
      </if>
      <if test="lastModUser != null" >
        LAST_MOD_USER,
      </if>
      <if test="lastModDate != null" >
        LAST_MOD_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="warrantyCancelCode != null" >
        #{warrantyCancelCode,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null" >
        #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null" >
        #{endDate,jdbcType=DATE},
      </if>
      <if test="warrantyCancelCodeDesc != null" >
        #{warrantyCancelCodeDesc,jdbcType=VARCHAR},
      </if>
      <if test="effectStdWarranty != null" >
        #{effectStdWarranty,jdbcType=VARCHAR},
      </if>
      <if test="effectExtWarranty != null" >
        #{effectExtWarranty,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="creationDatetime != null" >
        #{creationDatetime,jdbcType=DATE},
      </if>
      <if test="lastModUser != null" >
        #{lastModUser,jdbcType=VARCHAR},
      </if>
      <if test="lastModDate != null" >
        #{lastModDate,jdbcType=DATE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTUpdWarrantyCancelCodeRef" >
    update ib_t_upd_warranty_cancel_code_ref
    <set >
      <if test="startDate != null" >
        START_DATE = #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null" >
        END_DATE = #{endDate,jdbcType=DATE},
      </if>
      <if test="warrantyCancelCodeDesc != null" >
        WARRANTY_CANCEL_CODE_DESC = #{warrantyCancelCodeDesc,jdbcType=VARCHAR},
      </if>
      <if test="effectStdWarranty != null" >
        EFFECT_STD_WARRANTY = #{effectStdWarranty,jdbcType=VARCHAR},
      </if>
      <if test="effectExtWarranty != null" >
        EFFECT_EXT_WARRANTY = #{effectExtWarranty,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="creationDatetime != null" >
        CREATION_DATETIME = #{creationDatetime,jdbcType=DATE},
      </if>
      <if test="lastModUser != null" >
        LAST_MOD_USER = #{lastModUser,jdbcType=VARCHAR},
      </if>
      <if test="lastModDate != null" >
        LAST_MOD_DATE = #{lastModDate,jdbcType=DATE},
      </if>
    </set>
    where WARRANTY_CANCEL_CODE = #{warrantyCancelCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdWarrantyCancelCodeRef" >
    update ib_t_upd_warranty_cancel_code_ref
    set START_DATE = #{startDate,jdbcType=DATE},
      END_DATE = #{endDate,jdbcType=DATE},
      WARRANTY_CANCEL_CODE_DESC = #{warrantyCancelCodeDesc,jdbcType=VARCHAR},
      EFFECT_STD_WARRANTY = #{effectStdWarranty,jdbcType=VARCHAR},
      EFFECT_EXT_WARRANTY = #{effectExtWarranty,jdbcType=VARCHAR},
      CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      CREATION_DATETIME = #{creationDatetime,jdbcType=DATE},
      LAST_MOD_USER = #{lastModUser,jdbcType=VARCHAR},
      LAST_MOD_DATE = #{lastModDate,jdbcType=DATE}
    where WARRANTY_CANCEL_CODE = #{warrantyCancelCode,jdbcType=VARCHAR}
  </update>
</mapper>