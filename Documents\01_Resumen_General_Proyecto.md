# IQS (Intelligent Query System) - Resumen General del Proyecto

## 📋 Información Básica
- **Nombre del Proyecto**: IQS (Intelligent Query System)
- **Versión**: 0.0.1-SNAPSHOT
- **Organización**: Lenovo/Motorola
- **Tipo**: Aplicación Web Java Enterprise (.war)
- **Fecha de Build**: 24 de Diciembre, 2023

## 🎯 Propósito del Sistema
IQS es un sistema empresarial diseñado para:

1. **Gestión de Dispositivos Móviles**: Manejo de información de dispositivos Motorola/Lenovo
2. **Procesamiento de Logs**: Análisis y parseo de logs de dispositivos móviles
3. **Gestión de SIMLOCK**: Control y desbloqueo de tarjetas SIM
4. **Integración SAP**: Conexión con sistemas SAP corporativos
5. **Servicios Web**: Exposición de APIs REST y SOAP para consultas
6. **Monitoreo y Análisis**: Seguimiento de rendimiento y métricas

## 🏗️ Arquitectura General

### **Frontend**
- Aplicación web Java basada en Spring MVC
- Interfaz web para administración y consultas

### **Backend**
- **Framework**: Spring Framework 4.2.1
- **Servicios Web**: Apache CXF (REST/SOAP)
- **Persistencia**: MyBatis + MySQL
- **Procesamiento**: Scripts Python para análisis de logs
- **Integración**: SAP JCo para conectividad SAP

### **Base de Datos**
- **Principal**: MySQL (AWS RDS)
- **Secundaria**: SQL Server (para PCG e iBase)
- **Pool de Conexiones**: Alibaba Druid

### **Infraestructura**
- **Servidor**: Apache Tomcat
- **Monitoreo**: JavaMelody + Druid
- **Logging**: Log4j2
- **Scheduler**: Quartz

## 🔧 Tecnologías Principales

### **Java/Spring Stack**
- Spring MVC 4.2.1
- Spring Batch 3.0.6
- MyBatis 3.3.0
- Apache CXF 3.1.3

### **Base de Datos**
- MySQL 8.0.33
- SQL Server (Microsoft JDBC)
- Alibaba Druid 1.0.14

### **Servicios Web**
- JAX-RS (REST)
- JAX-WS (SOAP)
- Jackson JSON

### **Integración**
- SAP JCo 3.x
- Apache HTTP Client
- OkHttp3

### **Monitoreo**
- JavaMelody 1.79.0
- Log4j2 2.17.1
- Druid Web Console

### **Procesamiento**
- Python 3.x (scripts de parseo)
- Cryptography (manejo de certificados)
- Quartz Scheduler

## 📁 Estructura del Proyecto

```
iqs-0.0.1-SNAPSHOT.war/
├── META-INF/                 # Metadatos Maven
├── WEB-INF/
│   ├── classes/              # Clases Java compiladas
│   │   ├── com/lenovo/iqs/   # Código fuente principal
│   │   ├── *.xml             # Configuraciones Spring
│   │   └── config.properties # Configuración de aplicación
│   ├── lib/                  # Librerías JAR
│   ├── web.xml              # Configuración web
│   └── *.py                 # Scripts Python de procesamiento
```

## 🌐 Módulos Principales

1. **DataBlock Sign**: Firma digital de bloques de datos
2. **RSU (Remote SIM Unlock)**: Desbloqueo remoto de SIM
3. **WeChat Integration**: Integración con WeChat
4. **Google RPK**: Gestión de claves públicas Google
5. **SAP Integration**: Conectividad con sistemas SAP
6. **Batch Processing**: Procesamiento por lotes

## 🔐 Seguridad
- Autenticación SAP
- Certificados digitales X.509
- Encriptación de datos sensibles
- Validación de entrada con Hibernate Validator

## 📊 Monitoreo
- **JavaMelody**: Métricas de aplicación
- **Druid**: Monitoreo de base de datos
- **Log4j2**: Sistema de logging avanzado

## 🔄 Integración Externa
- **SAP Systems**: Conectividad JCo
- **GPS Services**: Servicios de geolocalización Motorola
- **iBase**: Sistema de garantías Lenovo
- **WeChat API**: Integración con plataforma WeChat

## 📈 Características Técnicas
- **Multi-tenant**: Soporte para múltiples clientes
- **Escalable**: Pool de conexiones configurables
- **Resiliente**: Manejo de errores y reintentos
- **Auditable**: Logging completo de transacciones
- **Configurable**: Configuración externa por ambiente

## 🎯 Casos de Uso Principales
1. Consulta de información de dispositivos móviles
2. Desbloqueo de tarjetas SIM
3. Validación de garantías
4. Procesamiento de logs de dispositivos
5. Integración con sistemas corporativos SAP
6. Monitoreo de rendimiento del sistema
