import json
import argparse
from collections import defaultdict, namedtuple
import csv
from datetime import datetime
import os
import re
import sys
import json
import base64
import binascii
import struct
import cryptography
from cryptography import x509
from cryptography import utils
from cryptography.hazmat.backends import default_backend
import traceback
import warnings

class Object:
    def __init__(self) :
        self.error_code = -1
        self.error_message = "Parsing not complete yet"

    # def toJSON(self):
    #     return json.dumps(self, default=lambda o: o.__dict__, 
    #         sort_keys=True, indent=4)
    
    # def jsonDefault(self):
    #     return self.decode('utf-8')


def getDetailsFromRequestParam(message, bean) :
    dbreq = base64.b64decode(message)
    version = -1
    request_length = struct.unpack(">H", dbreq[0:2])[0]
    input_byte_array = bytearray(dbreq)

    if (input_byte_array[2] == 0x01):
        bean["version"] = 1
        start = 128
        parse_request_blob(dbreq, start, bean)
    elif (input_byte_array[2] == 0x02):
        bean["version"] = 2
        start = 160 
        parse_request_blob(dbreq, start, bean)
    else :
        bean["error_code"] = -1
        bean["error_message"] = "Unsupported Request Version"

# modified from Joel's script to support request compiles in version 1 and 2. This can also be overridden in Version 3. 
def parse_request_blob(dbreq, start, bean):
    pdata = defaultdict(dict)
    ab = dbreq[0:2]
    request_length = struct.unpack(">H", dbreq[0:2])[0]
    ##print (str(request_length))
    num_datablocks = struct.unpack(">H", dbreq[126:128])[0]
    ##print (str(num_datablocks))
    #db_start = 128
    db_start = start
    db_next = 0
    request_processor_uid = bytearray()
    err = 0
    pdata["Type"] = 0
    # We shouldn't ever see more than 3 datablocks in a request
    bean["no_of_data_blocks_in_request"] = num_datablocks
    #bean["datablocks = []
    #if num_datablocks <= 3:
    bean["datablocks"] = []
    for i in range(num_datablocks):
        datablock_in_requestparam = {}
        db_next = db_start + struct.unpack(">I", dbreq[db_start+4:db_start+8])[0]
        ##print (db_start)
        ##print (db_next)
        ##print ("DB length: " + binascii.b2a_hex(dbreq[db_start+4:db_start+8]).upper())
        #datablock_in_requestparam["length"] = binascii.b2a_hex(dbreq[db_start+4:db_start+8]).upper()
        
        #changed from Joels code to decode utf-8
        datablock_in_requestparam["length"] = binascii.hexlify(dbreq[db_start+4:db_start+8]).decode("utf-8")
        if struct.unpack(">H", dbreq[db_start:db_start+2])[0] == 0x000F:
            ###print "DB type IMEI"
            datablock_in_requestparam["dbtype"] = "IMEI - 0x000F"
            (db_pid, db_serial, db_cscid) = unpack_identity(dbreq[db_start:db_next])
            pdata["Type"] = 1
            #print(binascii.hexlify(db_cscid).decode("utf-8"))
            datablock_in_requestparam["csc_id"] = binascii.hexlify(db_cscid).decode("utf-8")
            ###print "PID: " + binascii.hexlify(db_pid)
            ###print "Serial: " + binascii.hexlify(db_serial)
        elif struct.unpack(">H", dbreq[db_start:db_start+2])[0] == 0x0033:
            ###print "DB type SIMLOCK"
            datablock_in_requestparam["dbtype"] = "SIMLOCK - 0x0033"
            (db_pid, db_serial) = unpack_simlock(dbreq[db_start:db_next])
            ##print "PID: " + binascii.hexlify(db_pid)
            ##print "Serial: " + binascii.hexlify(db_serial)
        elif struct.unpack(">H", dbreq[db_start:db_start+2])[0] == 0x0066:
            ###print "DB type Legacy SIMLOCK"
            datablock_in_requestparam["dbtype"] = "Legacy SIMLOCK - 0x0066"
            (db_pid, db_serial) = unpack_simlock(dbreq[db_start:db_next])
        elif struct.unpack(">H", dbreq[db_start:db_start+2])[0] == 0x00F0:
            ###print "DB type CID"
            datablock_in_requestparam["dbtype"] = "CID - 0x00F0"
            (db_pid, db_serial, db_cid) = unpack_cid(dbreq[db_start:db_next])
            datablock_in_requestparam["customer_identifier"] = binascii.hexlify(db_cid).decode("utf-8")
        else:
            # This is generally happening when malformed requests are passed in, it was noted
            # that some requests were missing requried fields and later rejected by the PKI server
            (db_pid, db_serial) = (bytearray("notavalidpid", 'utf-8'), bytearray("notavalidserial", 'utf-8'))
            bean["error_code"] = -3 
            bean["error_message"] = "Unknown DB type: " +  binascii.b2a_hex(dbreq[db_start:db_start+2]).decode("utf-8") + " DB Length: " + str(int(db_next - db_start)) + \
                    " Header Request Length: " + str(int(request_length)) + \
                    " Number DB: " + str(hex(num_datablocks)) + \
                    " Current DB Offset: " + str(int(db_start))
            # ##print "Unknown DB type: " + binascii.b2a_hex(dbreq[db_start:db_start+2]).upper() + \
            #         " DB Length: " + str(int(db_next - db_start)) + \
            #         " Header Request Length: " + str(int(request_length)) + \
            #         " Number DB: " + str(hex(num_datablocks)) + \
            #         " Current DB Offset: " + str(int(db_start))
            bean["error_message"] = bean["error_message"] + "------ERROR LOGS------" + binhexdump(dbreq)
            return
        datablock_in_requestparam["processor_uid"] = binascii.hexlify(db_pid).decode("utf-8")
        datablock_in_requestparam["serial_number"] = binascii.hexlify(db_serial).decode("utf-8")
        if request_processor_uid:
            if request_processor_uid != db_pid:
                datablock_in_requestparam["error_code"] = -2
                datablock_in_requestparam["error_message"] = "DB request contains datablocks with different processor uids!"
        else:
            request_processor_uid = db_pid
            bean["request_processor_uid"] = binascii.hexlify(db_pid).decode("utf-8")
        bean["datablocks"].append(datablock_in_requestparam)
        db_start = db_next
    extended_data_length = struct.unpack(">H", dbreq[db_start:db_start+2])[0]
    db_start += 2
    unpack_extended_data(dbreq[db_start:db_start + extended_data_length], extended_data_length, bean)
    #else:
     #   ##print "Unsupported number of datablocks in request: " + str(int(num_datablocks))
     #   err = 1

    pdata["PID"] = request_processor_uid
    pdata["Err"] = err
    return pdata


def unpack_identity(db):
    # check format version
    if struct.unpack(">H", db[2:4])[0] == 0x0000 or 0x0001:
        # unpack IMEI w/nibble swapping
        serial = bytearray(db[40:48])
        for i in range(0,8):
            serial[i] = ((serial[i] & 0xF0) >> 4) | ((serial[i] & 0x0F) << 4)
        # mask high nibble (legacy length)
        serial[0] = serial[0] & 0x0F
        # processor UID, serial
        return (bytearray(db[8:24]), serial, bytearray(db[64:66]))
    else:
        #print ("Unsupported IMEI format version")
        return (bytearray(), bytearray(), bytearray())

def unpack_simlock(db):
    if struct.unpack(">H", db[2:4])[0] == 0x0000:
        return(bytearray(db[8:24]), bytearray(0))
    else:
        ##print "Unsupported SIMLOCK DB version"
        return (bytearray(0), bytearray(0))

def unpack_cid(db):
    if struct.unpack(">H", db[2:4])[0] == 0x0000 or 0x0001: 
        return(bytearray(db[8:24]), bytearray(0), bytearray(db[42:44]))
    else:
        ##print "Unsupported CID DB version"
        return (bytearray(0), bytearray(0), bytearray(0))

def binhexdump(data):
    wblocks = len(data) // 32
    pblocks = len(data) % 32
    out = ""
    out += "len=" + str((len(data))) + " 32 byte blocks: " + str((wblocks)) + " partial blocks: " + str((pblocks))
    for i in range(0,wblocks):
        out += binascii.hexlify(data[i*32:(i*32)+32]).decode("utf-8")
    if pblocks:
        out += binascii.hexlify(data[i:i+pblocks]).decode("utf-8")
    return out 

def unpack_extended_data(edata, edata_len, bean):
    # check syntax byte
    #print (type(edata))
    #print(edata[0])
    #if struct.unpack("B", edata[0])[0] != 0x01:
    if edata[0] != 1:
        ##print "Extended data syntax byte incorrect: " + binascii.hexlify(edata[0])
        #TODONE: implement error capture 
        bean["error_code"] = -4
        bean["error_message"] = "Extended data syntax byte incorrect: " + binascii.hexlify(edata[0])

    #if struct.unpack("B", edata[1])[0] == 0x01:
    if edata[0] == 1:
        userid = edata[2:23]
        #print (userid.decode("utf-8"))
        bean['UserID'] = userid.decode("utf-8").rstrip('\x00')
        #timestamp = struct.unpack(">I", edata[23:27])[0]
        # signature - 128 bytes
        cert_length = struct.unpack(">H", edata[155:157])[0]

        try:
            # well, 1200 is lazy as it might be cut off
            cert = x509.load_der_x509_certificate(edata[157:157+edata_len], default_backend())
            #print(cert.__dict__)
            ip_name_attr = cert.subject.get_attributes_for_oid(x509.OID_COMMON_NAME)
            bean["etoken_ip"] = ip_name_attr[0].value.split(':')[0]
            ##print ip_name_attr
            # dump data as temporary test to see attributes
            # for attribute in cert:
            #    print(attribute)
        #except :
        except Exception as e:
            traceback.print_exc()
            # It's happening because the request message log is cut off @4000 bytes, so in
            # requests with multiple datablocks, the cert data at the end gets chopped off
            #print ("Cert decode exception:")
            ###print "PKI cert length: " + str(int(cert_length))
            ###print binascii.hexlify(edata[157:157+1200])
            #TODONE: implement error capture 
            bean["error_code"] = -5
            bean["error_message"] = "PKI cert length: " + str(int(cert_length)) + ", Exception is " + str(e)
            #pass 
def something (s) :
    bean = {}
    getDetailsFromRequestParam(s, bean)
    json_data = json.dumps(bean, indent=2)
    print(json.loads(json_data))
    return json.loads(json_data)

if __name__ == "__main__":

    #bean = {}
    #getDetailsFromRequestParam("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", bean)
    #json_data = json.dumps(bean, indent=2)
    ##print(json.loads(json_data))
	something(sys.argv[1])