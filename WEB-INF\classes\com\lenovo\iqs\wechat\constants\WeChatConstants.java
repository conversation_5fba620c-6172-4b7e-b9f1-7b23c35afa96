package WEB-INF.classes.com.lenovo.iqs.wechat.constants;

public class WeChatConstants {
  public static final String WE_CHAT_SERIAL_NO_INVALID = "5070";
  
  public static final String WE_CHAT_SERIAL_NO_INVALID_MSG = "Invalid serialNumber";
  
  public static final String WE_CHAT_SERIAL_NO_TYPE_INVALID = "5071";
  
  public static final String WE_CHAT_SERIAL_NO_TYPE_INVALID_MSG = "Invalid serialNumberType";
  
  public static final String WE_CHAT_DEVICE_MODEL_INVALID = "5072";
  
  public static final String WE_CHAT_DEVICE_MODEL_INVALID_MSG = "Invalid deviceModel";
  
  public static final String WE_CHAT_DEVICE_ID_INVALID = "5073";
  
  public static final String WE_CHAT_DEVICE_ID_INVALID_MSG = "Invalid deviceId";
  
  public static final String WE_CHAT_SECURITY_LEVEL_INVALID = "5074";
  
  public static final String WE_CHAT_SECURITY_LEVEL_INVALID_MSG = "Invalid security level";
  
  public static final String WE_CHAT_PUBLIC_KEY_INVALID = "5075";
  
  public static final String WE_CHAT_PUBLIC_KEY_INVALID_MSG = "Invalid public key";
  
  public static final String WE_CHAT_BRAND_NAME_INVALID = "5076";
  
  public static final String WE_CHAT_BRAND_NAME_INVALID_MSG = "Invalid brand name";
  
  public static final String WE_CHAT_INVALID_SN_TYPE = "5077";
  
  public static final String WE_CHAT_INVALID_SN_TYPE_MSG = "Serial number type should be either MEID or IMEI or MSN or UID";
  
  public static final String SUCCESS = "0000";
  
  public static final String WE_CHAT_SUCCES_MSG = "Success";
  
  public static final String WE_CHAT_GPS_CALL_FAILDED = "5078";
  
  public static final String WE_CHAT_GPS_CALL_FAILDED_MSG = "WeChat GPS Service Call Failed";
  
  public static final String RSU_INVALID_RSDUSER = "5079";
  
  public static final String RSU_INVALID_RSDUSER_MSG = "RSU Service Invalid RSD User";
  
  public static final String RSU_INVALID_MASCID = "5080";
  
  public static final String RSU_INVALID_MASCID_MSG = "RSU Service Invalid Masc ID";
  
  public static final String RSU_INVALID_SOCMODEL = "5081";
  
  public static final String RSU_INVALID_SOCMODEL_MSG = "RSU Service Invalid socModel";
  
  public static final String RSU_INVALID_SUID = "5082";
  
  public static final String RSU_INVALID_SUID_MSG = "RSU Service Invalid suid";
  
  public static final String GPS_SUBSIDY_BAD_PRODID = "5082";
  
  public static final String GPS_SUBSIDY_BAD_PRODID_MSG = "GPS Subsidy fail - Prodid null";
  
  public static final String GPS_SUBSIDY_BAD_CERTMODEL = "5082";
  
  public static final String GPS_SUBSIDY_BAD_CERTMODEL_MSG = "GPS dispatch keys fail - certmodel null";
  
  public static final String GPS_SUBSIDY_BAD_CERTTYPE = "5082";
  
  public static final String GPS_SUBSIDY_BAD_CERTTYPE_MSG = "GPS dispatch keys fail - certtype null";
  
  public static final String GPS_SUBSIDY_BAD_KEYNAME = "5083";
  
  public static final String GPS_SUBSIDY_BAD_KEYNAME_MSG = "GPS Subsidy fail - keyname null";
  
  public static final String GPS_SUBSIDY_BAD_TYPE = "5085";
  
  public static final String GPS_SUBSIDY_BAD_TYPE_MSG = "GPS Subsidy fail - type null";
  
  public static final String GPS_SUBSIDY_BAD_DATA = "5086";
  
  public static final String GPS_SUBSIDY_BAD_DATA_MSG = "GPS Subsidy fail - Data null";
  
  public static final String GPS_KS_BAD_CPUID = "5082";
  
  public static final String GPS_KS_BAD_CPUID_MSG = "GPS dispatch keys fail - CPUID null";
  
  public static final String GPS_KS_BAD_BUILDTYPE = "5083";
  
  public static final String GPS_KS_BAD_BUILDTYPE_MSG = "GPS Subsidy fail - BUILDTYPE null";
  
  public static final String GPS_KS_BAD_PNAME = "5085";
  
  public static final String GPS_KS_BAD_PNAME_MSG = "GPS Subsidy fail - PNAME null";
  
  public static final String GPS_SUBSIDY_BAD_RSDLOGID = "5087";
  
  public static final String GPS_SUBSIDY_BAD_RSDLOGID_MSG = "GPS Subsidy fail - RSD LOGID cannot be null";
  
  public static final String GPS_SUBSIDY_BAD_CLIENTIP = "5088";
  
  public static final String GPS_SUBSIDY_BAD_CLIENTIP_MSG = "GPS Subsidy fail - Client is null or not valid";
  
  public static final String GPS_SUBSIDY_BAD_CRTYPE = "5089";
  
  public static final String GPS_SUBSIDY_BAD_CRTYPE_MSG = "GPS Subsidy fail - ClientReqType is null or not valid";
  
  public static final String RSU_INVALID_RECIEPTDATA = "5083";
  
  public static final String RSU_INVALID_RECIEPTDATA_MSG = "RSU Service Invalid Reciept Data";
  
  public static final String RSU_INVALID_DEVICEMODEL = "5084";
  
  public static final String RSU_INVALID_DEVICEMODEL_MSG = "RSU Service Invalid Reciept Data";
  
  public static final String RSU_INVALID_MNO = "5085";
  
  public static final String RSU_INVALID_MNO_MSG = "RSU Service Invalid MNO";
  
  public static final String RSU_INVALID_DEVICEMODEL_SETUP = "5087";
  
  public static final String RSU_INVALID_DEVICEMODEL_SETUP_MSG = "DeviceModel is not set up in IBASE_CONFIG setup. Please contact support team to device model mapping to SOC and SIP";
  
  public static final String RSU_INVALID_TRACKID = "5086";
  
  public static final String RSU_INVALID_TRACKID_MSG = "RSU Service Invalid Track ID";
  
  public static final String RSU_UNCHECKD = "5099";
  
  public static final String RSU_UNCHECKD_MSG = "Unchecked Exception";
  
  public static final String RSU_GPS_CALL_FAILDED = "5100";
  
  public static final String NO_DATA_FOUND = "5100";
  
  public static final String NO_DATA_FOUND_MSG = "No Data Found in UPD and IBase for the given serials";
  
  public static final String GOOGLERPK_INVALID_RSDUSER = "5101";
  
  public static final String GOOGLERPK_INVALID_RSDUSER_MSG = "RSD User ID in the request is either empty or invalid.";
  
  public static final String GOOGLERPK_INVALID_MASCID = "5102";
  
  public static final String GOOGLERPK_INVALID_MASCID_MSG = "MASC ID in the request is either empty or invalid.";
  
  public static final String GOOGLERPK_INVALID_TRACKID = "5103";
  
  public static final String GOOGLERPK_INVALID_TRACKID_MSG = "Track ID in the request is either empty or invalid.";
  
  public static final String GOOGLERPK_INVALID_CSR = "5104";
  
  public static final String GOOGLERPK_INVALID_CSR_MSG = "Google CSR in the request is either empty or invalid.";
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\wechat\constants\WeChatConstants.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */