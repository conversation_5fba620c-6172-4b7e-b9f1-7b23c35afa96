/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTUpdSwwarrPeriodRef extends IbTUpdSwwarrPeriodRefKey implements Serializable {
/*    */   private Integer swUpgExpPeriod;
/*    */   private String createdBy;
/*    */   private Date creationDatetime;
/*    */   
/*  7 */   public void setSwUpgExpPeriod(Integer swUpgExpPeriod) { this.swUpgExpPeriod = swUpgExpPeriod; } private String lastModBy; private Date lastModDatetime; private static final long serialVersionUID = 1L; public void setCreatedBy(String createdBy) { this.createdBy = createdBy; } public void setCreationDatetime(Date creationDatetime) { this.creationDatetime = creationDatetime; } public void setLastModBy(String lastModBy) { this.lastModBy = lastModBy; } public void setLastModDatetime(Date lastModDatetime) { this.lastModDatetime = lastModDatetime; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdSwwarrPeriodRef)) return false;  com.lenovo.iqs.entity.IbTUpdSwwarrPeriodRef other = (com.lenovo.iqs.entity.IbTUpdSwwarrPeriodRef)o; if (!other.canEqual(this)) return false;  Object this$swUpgExpPeriod = getSwUpgExpPeriod(), other$swUpgExpPeriod = other.getSwUpgExpPeriod(); if ((this$swUpgExpPeriod == null) ? (other$swUpgExpPeriod != null) : !this$swUpgExpPeriod.equals(other$swUpgExpPeriod)) return false;  Object this$createdBy = getCreatedBy(), other$createdBy = other.getCreatedBy(); if ((this$createdBy == null) ? (other$createdBy != null) : !this$createdBy.equals(other$createdBy)) return false;  Object this$creationDatetime = getCreationDatetime(), other$creationDatetime = other.getCreationDatetime(); if ((this$creationDatetime == null) ? (other$creationDatetime != null) : !this$creationDatetime.equals(other$creationDatetime)) return false;  Object this$lastModBy = getLastModBy(), other$lastModBy = other.getLastModBy(); if ((this$lastModBy == null) ? (other$lastModBy != null) : !this$lastModBy.equals(other$lastModBy)) return false;  Object this$lastModDatetime = getLastModDatetime(), other$lastModDatetime = other.getLastModDatetime(); return !((this$lastModDatetime == null) ? (other$lastModDatetime != null) : !this$lastModDatetime.equals(other$lastModDatetime)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdSwwarrPeriodRef; } public int hashCode() { int PRIME = 59; result = 1; Object $swUpgExpPeriod = getSwUpgExpPeriod(); result = result * 59 + (($swUpgExpPeriod == null) ? 43 : $swUpgExpPeriod.hashCode()); Object $createdBy = getCreatedBy(); result = result * 59 + (($createdBy == null) ? 43 : $createdBy.hashCode()); Object $creationDatetime = getCreationDatetime(); result = result * 59 + (($creationDatetime == null) ? 43 : $creationDatetime.hashCode()); Object $lastModBy = getLastModBy(); result = result * 59 + (($lastModBy == null) ? 43 : $lastModBy.hashCode()); Object $lastModDatetime = getLastModDatetime(); return result * 59 + (($lastModDatetime == null) ? 43 : $lastModDatetime.hashCode()); } public String toString() { return "IbTUpdSwwarrPeriodRef(swUpgExpPeriod=" + getSwUpgExpPeriod() + ", createdBy=" + getCreatedBy() + ", creationDatetime=" + getCreationDatetime() + ", lastModBy=" + getLastModBy() + ", lastModDatetime=" + getLastModDatetime() + ")"; }
/*    */    public Integer getSwUpgExpPeriod() {
/*  9 */     return this.swUpgExpPeriod;
/*    */   } public String getCreatedBy() {
/* 11 */     return this.createdBy;
/*    */   } public Date getCreationDatetime() {
/* 13 */     return this.creationDatetime;
/*    */   } public String getLastModBy() {
/* 15 */     return this.lastModBy;
/*    */   } public Date getLastModDatetime() {
/* 17 */     return this.lastModDatetime;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdSwwarrPeriodRef.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */