/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTObjectBp implements Serializable { private Integer id;
/*    */   private String machineType;
/*    */   private String serialNumber;
/*    */   private String sequenceNumber;
/*    */   
/*  7 */   public void setId(Integer id) { this.id = id; } private String partnerFunction; private String bpId; private Date lastChangeTime; private static final long serialVersionUID = 1L; public void setMachineType(String machineType) { this.machineType = machineType; } public void setSerialNumber(String serialNumber) { this.serialNumber = serialNumber; } public void setSequenceNumber(String sequenceNumber) { this.sequenceNumber = sequenceNumber; } public void setPartnerFunction(String partnerFunction) { this.partnerFunction = partnerFunction; } public void setBpId(String bpId) { this.bpId = bpId; } public void setLastChangeTime(Date lastChangeTime) { this.lastChangeTime = lastChangeTime; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTObjectBp)) return false;  com.lenovo.iqs.entity.IbTObjectBp other = (com.lenovo.iqs.entity.IbTObjectBp)o; if (!other.canEqual(this)) return false;  Object this$id = getId(), other$id = other.getId(); if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id)) return false;  Object this$machineType = getMachineType(), other$machineType = other.getMachineType(); if ((this$machineType == null) ? (other$machineType != null) : !this$machineType.equals(other$machineType)) return false;  Object this$serialNumber = getSerialNumber(), other$serialNumber = other.getSerialNumber(); if ((this$serialNumber == null) ? (other$serialNumber != null) : !this$serialNumber.equals(other$serialNumber)) return false;  Object this$sequenceNumber = getSequenceNumber(), other$sequenceNumber = other.getSequenceNumber(); if ((this$sequenceNumber == null) ? (other$sequenceNumber != null) : !this$sequenceNumber.equals(other$sequenceNumber)) return false;  Object this$partnerFunction = getPartnerFunction(), other$partnerFunction = other.getPartnerFunction(); if ((this$partnerFunction == null) ? (other$partnerFunction != null) : !this$partnerFunction.equals(other$partnerFunction)) return false;  Object this$bpId = getBpId(), other$bpId = other.getBpId(); if ((this$bpId == null) ? (other$bpId != null) : !this$bpId.equals(other$bpId)) return false;  Object this$lastChangeTime = getLastChangeTime(), other$lastChangeTime = other.getLastChangeTime(); return !((this$lastChangeTime == null) ? (other$lastChangeTime != null) : !this$lastChangeTime.equals(other$lastChangeTime)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTObjectBp; } public int hashCode() { int PRIME = 59; result = 1; Object $id = getId(); result = result * 59 + (($id == null) ? 43 : $id.hashCode()); Object $machineType = getMachineType(); result = result * 59 + (($machineType == null) ? 43 : $machineType.hashCode()); Object $serialNumber = getSerialNumber(); result = result * 59 + (($serialNumber == null) ? 43 : $serialNumber.hashCode()); Object $sequenceNumber = getSequenceNumber(); result = result * 59 + (($sequenceNumber == null) ? 43 : $sequenceNumber.hashCode()); Object $partnerFunction = getPartnerFunction(); result = result * 59 + (($partnerFunction == null) ? 43 : $partnerFunction.hashCode()); Object $bpId = getBpId(); result = result * 59 + (($bpId == null) ? 43 : $bpId.hashCode()); Object $lastChangeTime = getLastChangeTime(); return result * 59 + (($lastChangeTime == null) ? 43 : $lastChangeTime.hashCode()); } public String toString() { return "IbTObjectBp(id=" + getId() + ", machineType=" + getMachineType() + ", serialNumber=" + getSerialNumber() + ", sequenceNumber=" + getSequenceNumber() + ", partnerFunction=" + getPartnerFunction() + ", bpId=" + getBpId() + ", lastChangeTime=" + getLastChangeTime() + ")"; }
/*    */    public Integer getId() {
/*  9 */     return this.id;
/*    */   } public String getMachineType() {
/* 11 */     return this.machineType;
/*    */   } public String getSerialNumber() {
/* 13 */     return this.serialNumber;
/*    */   } public String getSequenceNumber() {
/* 15 */     return this.sequenceNumber;
/*    */   } public String getPartnerFunction() {
/* 17 */     return this.partnerFunction;
/*    */   } public String getBpId() {
/* 19 */     return this.bpId;
/*    */   } public Date getLastChangeTime() {
/* 21 */     return this.lastChangeTime;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTObjectBp.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */