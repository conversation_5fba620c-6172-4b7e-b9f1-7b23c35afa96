# IQS - Monitoreo y Logging

## 📊 Arquitectura de Monitoreo

El sistema IQS implementa un sistema completo de monitoreo y logging utilizando:
- **JavaMelody**: Monitoreo de rendimiento de aplicación
- **Druid**: Monitoreo de base de datos y pool de conexiones
- **Log4j2**: Sistema de logging avanzado
- **JRobin**: Almacenamiento de métricas históricas

## 🔍 JavaMelody - Monitoreo de Aplicación

### **Configuración**
```xml
<!-- Configuración JavaMelody en applicationContext.xml -->
<bean id="monitoringAdvisor" class="net.bull.javamelody.MonitoringSpringAdvisor">
    <property name="pointcut">
        <bean class="net.bull.javamelody.MonitoredWithAnnotationPointcut"/>
    </property>
</bean>
```

### **Funcionalidades de JavaMelody**
- **URL de Acceso**: `/monitoring`
- **Métricas Disponibles**:
  - Tiempo de respuesta de HTTP requests
  - Throughput de la aplicación
  - Uso de memoria JVM
  - Garbage Collection statistics
  - Pool de threads
  - Conexiones de base de datos
  - Errores y excepciones

### **Gráficos y Reportes**
```java
// Anotación para monitoreo específico
@MonitoredWithSpring
@Service
public class DeviceService {
    
    @MonitoredWithSpring("device-processing")
    public void processDevice(Device device) {
        // Lógica de procesamiento monitoreada
    }
}
```

### **Configuración de Alertas**
- Tiempo de respuesta > 5 segundos
- Uso de memoria > 80%
- Tasa de errores > 5%
- Pool de conexiones agotado

## 🗄️ Druid - Monitoreo de Base de Datos

### **Configuración Web**
```xml
<!-- Servlet de monitoreo Druid -->
<servlet>
    <servlet-name>DruidStatView</servlet-name>
    <servlet-class>com.alibaba.druid.support.http.StatViewServlet</servlet-class>
</servlet>

<servlet-mapping>
    <servlet-name>DruidStatView</servlet-name>
    <url-pattern>/druid/*</url-pattern>
</servlet-mapping>
```

### **Filtro de Monitoreo**
```xml
<!-- Filtro para estadísticas web -->
<filter>
    <filter-name>DruidWebStatFilter</filter-name>
    <filter-class>com.alibaba.druid.support.http.WebStatFilter</filter-class>
    <init-param>
        <param-name>exclusions</param-name>
        <param-value>*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*</param-value>
    </init-param>
    <init-param>
        <param-name>profileEnable</param-name>
        <param-value>true</param-value>
    </init-param>
</filter>
```

### **Métricas de Druid**
- **URL de Acceso**: `/druid/`
- **Información Disponible**:
  - Estado del pool de conexiones
  - Queries SQL ejecutadas
  - Tiempo de ejecución de queries
  - Conexiones activas/inactivas
  - Estadísticas de transacciones
  - Análisis de queries lentas

### **Configuración de Pool Monitoring**
```xml
<bean name="dataSource" class="com.alibaba.druid.pool.DruidDataSource">
    <!-- Configuración básica -->
    <property name="url" value="${jdbc.url}" />
    <property name="username" value="${jdbc.username}" />
    <property name="password" value="${jdbc.password}" />
    
    <!-- Configuración de monitoreo -->
    <property name="filters" value="stat,wall,log4j2" />
    <property name="connectionProperties" value="druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000" />
    
    <!-- Configuración de alertas -->
    <property name="timeBetweenLogStatsMillis" value="300000" />
</bean>
```

## 📝 Log4j2 - Sistema de Logging

### **Configuración Principal (log4j2.xml)**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Appenders>
        <!-- Console Appender -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
        </Console>
        
        <!-- File Appender -->
        <RollingFile name="FileAppender" fileName="logs/iqs.log"
                     filePattern="logs/iqs-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout>
                <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</Pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
        
        <!-- JSON Appender para análisis -->
        <RollingFile name="JsonAppender" fileName="logs/iqs-json.log"
                     filePattern="logs/iqs-json-%d{yyyy-MM-dd}-%i.log.gz">
            <JsonLayout compact="true" eventEol="true"/>
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
        </RollingFile>
    </Appenders>
    
    <Loggers>
        <!-- Logger específico para IQS -->
        <Logger name="com.lenovo.iqs" level="INFO" additivity="false">
            <AppenderRef ref="FileAppender"/>
            <AppenderRef ref="JsonAppender"/>
        </Logger>
        
        <!-- Logger para SQL -->
        <Logger name="com.lenovo.iqs.dao" level="DEBUG" additivity="false">
            <AppenderRef ref="FileAppender"/>
        </Logger>
        
        <!-- Logger para servicios web -->
        <Logger name="org.apache.cxf" level="WARN" additivity="false">
            <AppenderRef ref="FileAppender"/>
        </Logger>
        
        <!-- Root Logger -->
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FileAppender"/>
        </Root>
    </Loggers>
</Configuration>
```

### **Configuración de Filtros Web**
```xml
<!-- Filtro Log4j2 para web -->
<filter>
    <filter-name>log4jServletFilter</filter-name>
    <filter-class>org.apache.logging.log4j.web.Log4jServletFilter</filter-class>
</filter>

<filter-mapping>
    <filter-name>log4jServletFilter</filter-name>
    <url-pattern>/*</url-pattern>
    <dispatcher>REQUEST</dispatcher>
    <dispatcher>FORWARD</dispatcher>
    <dispatcher>INCLUDE</dispatcher>
    <dispatcher>ERROR</dispatcher>
</filter-mapping>
```

### **Listener de Contexto**
```xml
<!-- Context Listener para Log4j2 -->
<listener>
    <listener-class>org.apache.logging.log4j.web.Log4jServletContextListener</listener-class>
</listener>
```

## 📊 Logging Estructurado

### **Ejemplo de Logging en Controladores**
```java
@RestController
@RequestMapping("/rsu")
public class RSUController {
    
    private static final Logger logger = LoggerFactory.getLogger(RSUController.class);
    
    @PostMapping("/unlock")
    public ResponseEntity<UnlockResponse> unlockSIM(@RequestBody UnlockRequest request) {
        String imei = request.getImei();
        
        // Log de inicio de operación
        logger.info("RSU unlock request started - IMEI: {}, Model: {}", 
                   imei, request.getModel());
        
        try {
            UnlockResponse response = rsuService.processUnlock(request);
            
            // Log de éxito
            logger.info("RSU unlock completed successfully - IMEI: {}, UnlockCode: {}, Duration: {}ms", 
                       imei, response.getUnlockCode(), getDuration());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            // Log de error
            logger.error("RSU unlock failed - IMEI: {}, Error: {}", imei, e.getMessage(), e);
            
            return ResponseEntity.status(500).body(createErrorResponse(e));
        }
    }
}
```

### **Logging de Transacciones**
```java
@Service
@Transactional
public class DeviceService {
    
    private static final Logger logger = LoggerFactory.getLogger(DeviceService.class);
    
    @MonitoredWithSpring
    public void saveDevice(Device device) {
        String transactionId = UUID.randomUUID().toString();
        
        logger.info("Transaction started - ID: {}, IMEI: {}", transactionId, device.getImei());
        
        try {
            deviceDAO.save(device);
            logger.info("Device saved successfully - TransactionID: {}, IMEI: {}", 
                       transactionId, device.getImei());
        } catch (Exception e) {
            logger.error("Failed to save device - TransactionID: {}, IMEI: {}, Error: {}", 
                        transactionId, device.getImei(), e.getMessage(), e);
            throw e;
        }
    }
}
```

## 📈 Métricas Personalizadas

### **Contador de Operaciones**
```java
@Component
public class MetricsCollector {
    
    private final AtomicLong unlockRequests = new AtomicLong(0);
    private final AtomicLong successfulUnlocks = new AtomicLong(0);
    private final AtomicLong failedUnlocks = new AtomicLong(0);
    
    public void incrementUnlockRequests() {
        unlockRequests.incrementAndGet();
        logger.info("Metrics - Total unlock requests: {}", unlockRequests.get());
    }
    
    public void incrementSuccessfulUnlocks() {
        successfulUnlocks.incrementAndGet();
        logger.info("Metrics - Successful unlocks: {}", successfulUnlocks.get());
    }
    
    public void incrementFailedUnlocks() {
        failedUnlocks.incrementAndGet();
        logger.info("Metrics - Failed unlocks: {}", failedUnlocks.get());
    }
    
    @Scheduled(fixedRate = 300000) // Cada 5 minutos
    public void logMetrics() {
        logger.info("Metrics Summary - Requests: {}, Success: {}, Failed: {}, Success Rate: {}%",
                   unlockRequests.get(), successfulUnlocks.get(), failedUnlocks.get(),
                   calculateSuccessRate());
    }
}
```

## 🚨 Alertas y Notificaciones

### **Configuración de Alertas**
```java
@Component
public class AlertManager {
    
    private static final Logger alertLogger = LoggerFactory.getLogger("ALERTS");
    
    @EventListener
    public void handleHighErrorRate(HighErrorRateEvent event) {
        alertLogger.error("ALERT: High error rate detected - Rate: {}%, Threshold: {}%", 
                         event.getErrorRate(), event.getThreshold());
        
        // Enviar notificación por email/SMS
        sendAlert("High Error Rate", event.getDetails());
    }
    
    @EventListener
    public void handleDatabaseConnectionIssue(DatabaseConnectionEvent event) {
        alertLogger.error("ALERT: Database connection issue - Pool: {}, Available: {}", 
                         event.getPoolName(), event.getAvailableConnections());
        
        sendAlert("Database Connection Issue", event.getDetails());
    }
}
```

### **Health Checks**
```java
@RestController
@RequestMapping("/health")
public class HealthController {
    
    @Autowired
    private DataSource dataSource;
    
    @GetMapping
    public ResponseEntity<HealthStatus> getHealth() {
        HealthStatus status = new HealthStatus();
        
        // Check database
        try {
            dataSource.getConnection().close();
            status.setDatabaseStatus("UP");
        } catch (Exception e) {
            status.setDatabaseStatus("DOWN");
            logger.error("Database health check failed", e);
        }
        
        // Check SAP connectivity
        status.setSapStatus(checkSAPConnectivity());
        
        // Check external services
        status.setExternalServicesStatus(checkExternalServices());
        
        return ResponseEntity.ok(status);
    }
}
```

## 📊 Dashboards y Reportes

### **Configuración de JRobin**
```xml
<!-- Configuración para almacenamiento de métricas históricas -->
<bean id="jrobinStorage" class="net.bull.javamelody.JRobinStorage">
    <property name="storageDirectory" value="/var/lib/iqs/metrics"/>
    <property name="resolutionSeconds" value="60"/>
</bean>
```

### **Métricas Clave a Monitorear**
1. **Rendimiento**:
   - Tiempo de respuesta promedio
   - Throughput (requests/segundo)
   - Percentiles de tiempo de respuesta (95%, 99%)

2. **Recursos**:
   - Uso de CPU
   - Uso de memoria
   - Conexiones de base de datos activas

3. **Errores**:
   - Tasa de errores HTTP
   - Excepciones de aplicación
   - Fallos de conexión a servicios externos

4. **Negocio**:
   - Número de desbloqueos exitosos
   - Tiempo promedio de procesamiento
   - Distribución por carrier/modelo

### **Configuración de Retención**
```properties
# Configuración de retención de logs
log4j2.appender.file.policies.time.interval=1
log4j2.appender.file.policies.time.modulate=true
log4j2.appender.file.strategy.max=30
log4j2.appender.file.strategy.fileIndex=min
```

## 🔧 Troubleshooting

### **Logs de Diagnóstico**
```bash
# Ubicación de logs
/var/log/iqs/
├── iqs.log              # Log principal
├── iqs-json.log         # Log estructurado JSON
├── error.log            # Solo errores
└── access.log           # Log de acceso web
```

### **Comandos Útiles**
```bash
# Monitorear logs en tiempo real
tail -f /var/log/iqs/iqs.log

# Buscar errores específicos
grep "ERROR" /var/log/iqs/iqs.log | tail -20

# Analizar patrones de error
awk '/ERROR/ {print $1, $2, $NF}' /var/log/iqs/iqs.log | sort | uniq -c
```
