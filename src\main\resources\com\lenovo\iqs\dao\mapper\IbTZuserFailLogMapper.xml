<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTZuserFailLogMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTZuserFailLog" >
    <id column="auto_id" property="autoId" jdbcType="INTEGER" />
    <result column="ip" property="ip" jdbcType="VARCHAR" />
    <result column="userid" property="userid" jdbcType="VARCHAR" />
    <result column="pw" property="pw" jdbcType="VARCHAR" />
    <result column="access_time" property="accessTime" jdbcType="TIMESTAMP" />
    <result column="logtype" property="logtype" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    auto_id, ip, userid, pw, access_time, logtype
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_zuser_fail_log
    where auto_id = #{autoId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from ib_t_zuser_fail_log
    where auto_id = #{autoId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTZuserFailLog" >
    <selectKey resultType="java.lang.Integer" keyProperty="autoId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_zuser_fail_log (ip, userid, pw, 
      access_time, logtype)
    values (#{ip,jdbcType=VARCHAR}, #{userid,jdbcType=VARCHAR}, #{pw,jdbcType=VARCHAR}, 
      #{accessTime,jdbcType=TIMESTAMP}, #{logtype,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTZuserFailLog" >
    <selectKey resultType="java.lang.Integer" keyProperty="autoId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_zuser_fail_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ip != null" >
        ip,
      </if>
      <if test="userid != null" >
        userid,
      </if>
      <if test="pw != null" >
        pw,
      </if>
      <if test="accessTime != null" >
        access_time,
      </if>
      <if test="logtype != null" >
        logtype,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ip != null" >
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="userid != null" >
        #{userid,jdbcType=VARCHAR},
      </if>
      <if test="pw != null" >
        #{pw,jdbcType=VARCHAR},
      </if>
      <if test="accessTime != null" >
        #{accessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="logtype != null" >
        #{logtype,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTZuserFailLog" >
    update ib_t_zuser_fail_log
    <set >
      <if test="ip != null" >
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="userid != null" >
        userid = #{userid,jdbcType=VARCHAR},
      </if>
      <if test="pw != null" >
        pw = #{pw,jdbcType=VARCHAR},
      </if>
      <if test="accessTime != null" >
        access_time = #{accessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="logtype != null" >
        logtype = #{logtype,jdbcType=VARCHAR},
      </if>
    </set>
    where auto_id = #{autoId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTZuserFailLog" >
    update ib_t_zuser_fail_log
    set ip = #{ip,jdbcType=VARCHAR},
      userid = #{userid,jdbcType=VARCHAR},
      pw = #{pw,jdbcType=VARCHAR},
      access_time = #{accessTime,jdbcType=TIMESTAMP},
      logtype = #{logtype,jdbcType=VARCHAR}
    where auto_id = #{autoId,jdbcType=INTEGER}
  </update>
</mapper>