/*     */ package WEB-INF.classes.com.lenovo.iqs.utils;
/*     */ 
/*     */ import com.alibaba.druid.util.StringUtils;
/*     */ import com.fasterxml.jackson.databind.ObjectMapper;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.OutputStream;
/*     */ import java.nio.charset.Charset;
/*     */ import javax.net.ssl.SSLContext;
/*     */ import org.apache.http.HttpEntity;
/*     */ import org.apache.http.HttpResponse;
/*     */ import org.apache.http.auth.AuthScope;
/*     */ import org.apache.http.auth.Credentials;
/*     */ import org.apache.http.auth.UsernamePasswordCredentials;
/*     */ import org.apache.http.client.CredentialsProvider;
/*     */ import org.apache.http.client.HttpClient;
/*     */ import org.apache.http.client.config.RequestConfig;
/*     */ import org.apache.http.client.methods.HttpPost;
/*     */ import org.apache.http.client.methods.HttpUriRequest;
/*     */ import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
/*     */ import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
/*     */ import org.apache.http.conn.ssl.SSLContextBuilder;
/*     */ import org.apache.http.conn.ssl.TrustStrategy;
/*     */ import org.apache.http.entity.StringEntity;
/*     */ import org.apache.http.impl.client.BasicCredentialsProvider;
/*     */ import org.apache.http.impl.client.HttpClients;
/*     */ import org.apache.http.util.EntityUtils;
/*     */ import org.apache.logging.log4j.LogManager;
/*     */ import org.apache.logging.log4j.Logger;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HttpClientUtils
/*     */ {
/*  38 */   private static final Logger log = LogManager.getLogger(com.lenovo.iqs.utils.HttpClientUtils.class);
/*     */   
/*  40 */   private static final Integer CONNECT_TIMEOUT = Integer.valueOf(10000);
/*  41 */   private static final Integer SOCKET_TIMEOUT = Integer.valueOf(10000);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static com.lenovo.iqs.utils.HttpClientUtils instance;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static HttpClient createSSLHttpClient(String authUser, String authPwd) throws Exception {
/*  53 */     SSLContext sslContext = (new SSLContextBuilder()).loadTrustMaterial(null, (TrustStrategy)new Object()).build();
/*  54 */     SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
/*     */     
/*  56 */     BasicCredentialsProvider basicCredentialsProvider = new BasicCredentialsProvider();
/*  57 */     if (!StringUtils.isEmpty(authUser) || !StringUtils.isEmpty(authPwd)) {
/*  58 */       basicCredentialsProvider.setCredentials(AuthScope.ANY, (Credentials)new UsernamePasswordCredentials(authUser, authPwd));
/*     */     }
/*     */     
/*  61 */     return (HttpClient)HttpClients.custom().setSSLSocketFactory((LayeredConnectionSocketFactory)sslsf).setDefaultCredentialsProvider((CredentialsProvider)basicCredentialsProvider).build();
/*     */   }
/*     */ 
/*     */   
/*     */   public static String postWebservice(String saopUrl, String params, String soapAction, String authUser, String authPwd) throws Exception {
/*  66 */     log.debug("Sending SOAP request");
/*  67 */     log.debug("SOAP URL - " + saopUrl);
/*  68 */     log.debug("SOAP Param - " + params);
/*  69 */     log.debug("SOAP Action - " + soapAction);
/*  70 */     log.debug("Auth User - " + authUser);
/*  71 */     log.debug("Auth Pwd - " + authPwd);
/*     */     
/*  73 */     HttpClient httpClient = createSSLHttpClient(authUser, authPwd);
/*  74 */     HttpPost httpPost = new HttpPost(saopUrl);
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  79 */     RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT.intValue()).setSocketTimeout(SOCKET_TIMEOUT.intValue()).build();
/*     */     
/*  81 */     httpPost.setConfig(requestConfig);
/*     */     
/*  83 */     httpPost.setHeader("Content-Type", "text/xml;charset=UTF-8");
/*  84 */     httpPost.setHeader("SOAPAction", soapAction);
/*     */     
/*  86 */     StringEntity entity = new StringEntity(params, Charset.forName("UTF-8"));
/*  87 */     httpPost.setEntity((HttpEntity)entity);
/*  88 */     log.debug("HPPT Post - " + EntityUtils.toString(httpPost.getEntity()));
/*  89 */     HttpResponse httpResponse = httpClient.execute((HttpUriRequest)httpPost);
/*  90 */     HttpEntity httpEntity = httpResponse.getEntity();
/*  91 */     String result = EntityUtils.toString(httpEntity);
/*  92 */     log.debug("HPPT Response - " + result);
/*  93 */     return result;
/*     */   }
/*     */   
/*     */   public static String postJSONWebservice(String url, Object req, String user, String pwd) throws Exception {
/*  97 */     ObjectMapper mapper = new ObjectMapper();
/*  98 */     OutputStream jos = new ByteArrayOutputStream();
/*  99 */     mapper.writeValue(jos, req);
/* 100 */     String params = jos.toString();
/* 101 */     log.info("params ---> " + params);
/* 102 */     HttpClient httpClient = createSSLHttpClient(user, pwd);
/* 103 */     HttpPost httpPost = new HttpPost(url);
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 108 */     RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT.intValue()).setSocketTimeout(SOCKET_TIMEOUT.intValue()).build();
/*     */     
/* 110 */     httpPost.setConfig(requestConfig);
/*     */     
/* 112 */     httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
/*     */     
/* 114 */     StringEntity entity = new StringEntity(params, Charset.forName("UTF-8"));
/* 115 */     httpPost.setEntity((HttpEntity)entity);
/* 116 */     System.out.println(httpPost.toString());
/* 117 */     HttpResponse httpResponse = httpClient.execute((HttpUriRequest)httpPost);
/* 118 */     int statuscode = httpResponse.getStatusLine().getStatusCode();
/* 119 */     if (statuscode != 200) {
/* 120 */       log.error("RSD Validation returned " + statuscode);
/* 121 */       throw new Exception("RSD Validation returned httpcode --> " + statuscode);
/*     */     } 
/* 123 */     HttpEntity httpEntity = httpResponse.getEntity();
/* 124 */     String result = EntityUtils.toString(httpEntity);
/* 125 */     return result;
/*     */   }
/*     */   
/*     */   public static void main(String[] args) {
/* 129 */     String params = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:upd=\"http://updtaccode.programmingservice.cfc.nextest.globaltest.motorolamobility.com/\">\n   <soapenv:Header/>\n   <soapenv:Body>\n      <upd:SendNewTac>\n         <!--Optional:-->\n         <site_name>sample</site_name>\n         <!--Optional:-->\n         <tac>sample</tac>\n         <!--Optional:-->\n         <min_value>sample</min_value>\n         <!--Optional:-->\n         <max_value>sample</max_value>\n         <!--Optional:-->\n         <product_internal_name>sample</product_internal_name>\n         <!--Optional:-->\n         <tac_country>sample</tac_country>\n         <!--Optional:-->\n         <range_country>sample</range_country>\n         <!--Optional:-->\n         <tac_memory>sample</tac_memory>\n         <!--Optional:-->\n         <range_memory>sample</range_memory>\n         <!--Optional:-->\n         <tac_color>sample</tac_color>\n         <!--Optional:-->\n         <range_color>sample</range_color>\n         <!--Optional:-->\n         <pmd_product_id>sample</pmd_product_id>\n         <!--Optional:-->\n         <tac_customer>sample</tac_customer>\n         <!--Optional:-->\n         <range_customer>sample</range_customer>\n         <!--Optional:-->\n         <range_customer_sku>sample</range_customer_sku>\n         <!--Optional:-->\n         <tac_comments>sample</tac_comments>\n         <!--Optional:-->\n         <range_comments>sample</range_comments>\n         <!--Optional:-->\n         <build_type>sample</build_type>\n         <!--Optional:-->\n         <market_model>sample</market_model>\n         <!--Optional:-->\n         <refill_request>sample</refill_request>\n         <!--Optional:-->\n         <prior_tac>sample</prior_tac>\n      </upd:SendNewTac>\n   </soapenv:Body>\n</soapenv:Envelope>";
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iq\\utils\HttpClientUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */