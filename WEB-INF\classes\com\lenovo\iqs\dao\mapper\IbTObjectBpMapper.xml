<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTObjectBpMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTObjectBp" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="machine_type" property="machineType" jdbcType="VARCHAR" />
    <result column="serial_number" property="serialNumber" jdbcType="VARCHAR" />
    <result column="sequence_number" property="sequenceNumber" jdbcType="VARCHAR" />
    <result column="partner_function" property="partnerFunction" jdbcType="VARCHAR" />
    <result column="bp_id" property="bpId" jdbcType="VARCHAR" />
    <result column="last_change_time" property="lastChangeTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, machine_type, serial_number, sequence_number, partner_function, bp_id, last_change_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_object_bp
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from ib_t_object_bp
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTObjectBp" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_object_bp (machine_type, serial_number, sequence_number, 
      partner_function, bp_id, last_change_time
      )
    values (#{machineType,jdbcType=VARCHAR}, #{serialNumber,jdbcType=VARCHAR}, #{sequenceNumber,jdbcType=VARCHAR}, 
      #{partnerFunction,jdbcType=VARCHAR}, #{bpId,jdbcType=VARCHAR}, #{lastChangeTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTObjectBp" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_object_bp
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="machineType != null" >
        machine_type,
      </if>
      <if test="serialNumber != null" >
        serial_number,
      </if>
      <if test="sequenceNumber != null" >
        sequence_number,
      </if>
      <if test="partnerFunction != null" >
        partner_function,
      </if>
      <if test="bpId != null" >
        bp_id,
      </if>
      <if test="lastChangeTime != null" >
        last_change_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="machineType != null" >
        #{machineType,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null" >
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="sequenceNumber != null" >
        #{sequenceNumber,jdbcType=VARCHAR},
      </if>
      <if test="partnerFunction != null" >
        #{partnerFunction,jdbcType=VARCHAR},
      </if>
      <if test="bpId != null" >
        #{bpId,jdbcType=VARCHAR},
      </if>
      <if test="lastChangeTime != null" >
        #{lastChangeTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTObjectBp" >
    update ib_t_object_bp
    <set >
      <if test="machineType != null" >
        machine_type = #{machineType,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null" >
        serial_number = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="sequenceNumber != null" >
        sequence_number = #{sequenceNumber,jdbcType=VARCHAR},
      </if>
      <if test="partnerFunction != null" >
        partner_function = #{partnerFunction,jdbcType=VARCHAR},
      </if>
      <if test="bpId != null" >
        bp_id = #{bpId,jdbcType=VARCHAR},
      </if>
      <if test="lastChangeTime != null" >
        last_change_time = #{lastChangeTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTObjectBp" >
    update ib_t_object_bp
    set machine_type = #{machineType,jdbcType=VARCHAR},
      serial_number = #{serialNumber,jdbcType=VARCHAR},
      sequence_number = #{sequenceNumber,jdbcType=VARCHAR},
      partner_function = #{partnerFunction,jdbcType=VARCHAR},
      bp_id = #{bpId,jdbcType=VARCHAR},
      last_change_time = #{lastChangeTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>