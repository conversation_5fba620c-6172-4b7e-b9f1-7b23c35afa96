<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenovo.iqs.dao.IbTJobStatusMapper">
	<resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTJobStatus">
		<id column="jobstatus_id" jdbcType="INTEGER" property="jobstatusId" />
		<result column="job_id" jdbcType="VARCHAR" property="jobId" />
		<result column="job_description" jdbcType="VARCHAR" property="jobDescription" />
		<result column="job_type" jdbcType="VARCHAR" property="jobType" />
		<result column="loaddata_status" jdbcType="VARCHAR" property="loaddataStatus" />
		<result column="processe_status" jdbcType="VARCHAR" property="processeStatus" />
		<result column="job_load_starttime" jdbcType="TIMESTAMP"
			property="jobLoadStarttime" />
		<result column="job_load_endtime" jdbcType="TIMESTAMP"
			property="jobLoadEndtime" />
		<result column="job_processe_starttime" jdbcType="TIMESTAMP"
			property="jobProcesseStarttime" />
		<result column="job_processe_endtime" jdbcType="TIMESTAMP"
			property="jobProcesseEndtime" />
		<result column="job_parameter" jdbcType="VARCHAR" property="jobParameter" />
		<result column="remark" jdbcType="VARCHAR" property="remark" />
		<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
		<result column="batch_number" jdbcType="VARCHAR" property="batchNumber" />
	</resultMap>
	<sql id="Base_Column_List">
		jobstatus_id, job_id, job_description, job_type,
		loaddata_status, processe_status,
		job_load_starttime, job_load_endtime,
		job_processe_starttime,
		job_processe_endtime,
		job_parameter, remark,
		create_time, batch_number
	</sql>
	<select id="selectByPrimaryKey" parameterType="java.lang.Integer"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from ib_t_job_status
		where jobstatus_id =
		#{jobstatusId,jdbcType=INTEGER}
	</select>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
		delete from
		ib_t_job_status
		where jobstatus_id = #{jobstatusId,jdbcType=INTEGER}
	</delete>
	<insert id="insert" parameterType="com.lenovo.iqs.entity.IbTJobStatus">
		<!-- <selectKey keyProperty="jobstatusId" order="AFTER" resultType="java.lang.Integer"> 
			SELECT LAST_INSERT_ID() </selectKey> -->
		insert into ib_t_job_status (job_id, job_description, job_type,
		loaddata_status, processe_status, job_load_starttime,
		job_load_endtime, job_processe_starttime,
		job_processe_endtime,
		job_parameter, remark,
		create_time, batch_number)
		values
		(#{jobId,jdbcType=VARCHAR}, #{jobDescription,jdbcType=VARCHAR},
		#{jobType,jdbcType=VARCHAR},
		#{loaddataStatus,jdbcType=VARCHAR},
		#{processeStatus,jdbcType=VARCHAR},
		#{jobLoadStarttime,jdbcType=TIMESTAMP},
		#{jobLoadEndtime,jdbcType=TIMESTAMP},
		#{jobProcesseStarttime,jdbcType=TIMESTAMP},
		#{jobProcesseEndtime,jdbcType=TIMESTAMP},
		#{jobParameter,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
		#{createTime,jdbcType=TIMESTAMP}, #{batchNumber,jdbcType=VARCHAR})
	</insert>
	<insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTJobStatus">
		<selectKey keyProperty="jobstatusId" order="AFTER"
			resultType="java.lang.Integer">
			SELECT LAST_INSERT_ID()
		</selectKey>
		insert into ib_t_job_status
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="jobId != null">
				job_id,
			</if>
			<if test="jobDescription != null">
				job_description,
			</if>
			<if test="jobType != null">
				job_type,
			</if>
			<if test="loaddataStatus != null">
				loaddata_status,
			</if>
			<if test="processeStatus != null">
				processe_status,
			</if>
			<if test="jobLoadStarttime != null">
				job_load_starttime,
			</if>
			<if test="jobLoadEndtime != null">
				job_load_endtime,
			</if>
			<if test="jobProcesseStarttime != null">
				job_processe_starttime,
			</if>
			<if test="jobProcesseEndtime != null">
				job_processe_endtime,
			</if>
			<if test="jobParameter != null">
				job_parameter,
			</if>
			<if test="remark != null">
				remark,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="batchNumber != null">
				batch_number,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="jobId != null">
				#{jobId,jdbcType=VARCHAR},
			</if>
			<if test="jobDescription != null">
				#{jobDescription,jdbcType=VARCHAR},
			</if>
			<if test="jobType != null">
				#{jobType,jdbcType=VARCHAR},
			</if>
			<if test="loaddataStatus != null">
				#{loaddataStatus,jdbcType=VARCHAR},
			</if>
			<if test="processeStatus != null">
				#{processeStatus,jdbcType=VARCHAR},
			</if>
			<if test="jobLoadStarttime != null">
				#{jobLoadStarttime,jdbcType=TIMESTAMP},
			</if>
			<if test="jobLoadEndtime != null">
				#{jobLoadEndtime,jdbcType=TIMESTAMP},
			</if>
			<if test="jobProcesseStarttime != null">
				#{jobProcesseStarttime,jdbcType=TIMESTAMP},
			</if>
			<if test="jobProcesseEndtime != null">
				#{jobProcesseEndtime,jdbcType=TIMESTAMP},
			</if>
			<if test="jobParameter != null">
				#{jobParameter,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				#{remark,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="batchNumber != null">
				#{batchNumber,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	<update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTJobStatus">
		update ib_t_job_status
		<set>
			<if test="jobId != null">
				job_id = #{jobId,jdbcType=VARCHAR},
			</if>
			<if test="jobDescription != null">
				job_description = #{jobDescription,jdbcType=VARCHAR},
			</if>
			<if test="jobType != null">
				job_type = #{jobType,jdbcType=VARCHAR},
			</if>
			<if test="loaddataStatus != null">
				loaddata_status = #{loaddataStatus,jdbcType=VARCHAR},
			</if>
			<if test="processeStatus != null">
				processe_status = #{processeStatus,jdbcType=VARCHAR},
			</if>
			<if test="jobLoadStarttime != null">
				job_load_starttime =
				#{jobLoadStarttime,jdbcType=TIMESTAMP},
			</if>
			<if test="jobLoadEndtime != null">
				job_load_endtime = #{jobLoadEndtime,jdbcType=TIMESTAMP},
			</if>
			<if test="jobProcesseStarttime != null">
				job_processe_starttime =
				#{jobProcesseStarttime,jdbcType=TIMESTAMP},
			</if>
			<if test="jobProcesseEndtime != null">
				job_processe_endtime =
				#{jobProcesseEndtime,jdbcType=TIMESTAMP},
			</if>
			<if test="jobParameter != null">
				job_parameter = #{jobParameter,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				remark = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="batchNumber != null">
				batch_number = #{batchNumber,jdbcType=VARCHAR},
			</if>
		</set>
		where jobstatus_id = #{jobstatusId,jdbcType=INTEGER}
	</update>
	<update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTJobStatus">
		update
		ib_t_job_status
		set
		job_description =
		#{jobDescription,jdbcType=VARCHAR},
		job_type =
		#{jobType,jdbcType=VARCHAR},
		loaddata_status =
		#{loaddataStatus,jdbcType=VARCHAR},
		processe_status =
		#{processeStatus,jdbcType=VARCHAR},
		job_load_starttime =
		#{jobLoadStarttime,jdbcType=TIMESTAMP},
		job_load_endtime =
		#{jobLoadEndtime,jdbcType=TIMESTAMP},
		job_processe_starttime =
		#{jobProcesseStarttime,jdbcType=TIMESTAMP},
		job_processe_endtime =
		#{jobProcesseEndtime,jdbcType=TIMESTAMP},
		job_parameter =
		#{jobParameter,jdbcType=VARCHAR},
		remark = #{remark,jdbcType=VARCHAR},
		create_time = #{createTime,jdbcType=TIMESTAMP},
		batch_number =
		#{batchNumber,jdbcType=VARCHAR}
		where jobstatus_id =
		#{jobstatusId,jdbcType=INTEGER}
	</update>


	<select id="selectByBatchNumber" parameterType="java.util.Map"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from ib_t_job_status
		where batch_number =
		#{batchNumber,jdbcType=VARCHAR} and
		job_id=#{JOB_ID,jdbcType=VARCHAR}
		and
		job_type=#{JOB_TYPE,jdbcType=VARCHAR}
	</select>
	<update id="updateToLoaddataLoading" parameterType="com.lenovo.iqs.entity.IbTJobStatus">
		update
		ib_t_job_status
		set loaddata_status =
		#{loaddataStatus,jdbcType=VARCHAR},
		job_load_starttime =
		#{jobLoadStarttime,jdbcType=TIMESTAMP}
		where job_id =
		#{jobId,jdbcType=VARCHAR} and batch_number =
		#{batchNumber,jdbcType=VARCHAR}
	</update>

	<update id="updateToLoaddataOver" parameterType="com.lenovo.iqs.entity.IbTJobStatus">
		update
		ib_t_job_status
		set loaddata_status =
		#{loaddataStatus,jdbcType=VARCHAR},
		job_load_endtime =
		#{jobLoadEndtime,jdbcType=TIMESTAMP}
		where job_id =
		#{jobId,jdbcType=VARCHAR} and batch_number =
		#{batchNumber,jdbcType=VARCHAR}
	</update>
	
	<update id="updateToLoaddataStatus" parameterType="com.lenovo.iqs.entity.IbTJobStatus">
		update
		ib_t_job_status
		set loaddata_status =
		#{loaddataStatus,jdbcType=VARCHAR},
		job_load_endtime =
		#{jobLoadEndtime,jdbcType=TIMESTAMP}
		where job_id =
		#{jobId,jdbcType=VARCHAR} and batch_number =
		#{batchNumber,jdbcType=VARCHAR}
	</update>
	
	<update id="updateToProcesseProcessing" parameterType="com.lenovo.iqs.entity.IbTJobStatus">
		update
		ib_t_job_status
		set processe_status =
		#{processeStatus,jdbcType=VARCHAR},
		job_processe_starttime =
		#{jobProcesseStarttime,jdbcType=TIMESTAMP}
		where job_id =
		#{jobId,jdbcType=VARCHAR} and batch_number =
		#{batchNumber,jdbcType=VARCHAR}
	</update>
	<update id="updateToProcesseOver" parameterType="com.lenovo.iqs.entity.IbTJobStatus">
		update
		ib_t_job_status
		set processe_status =
		#{processeStatus,jdbcType=VARCHAR},
		job_processe_endtime =
		#{jobProcesseEndtime,jdbcType=TIMESTAMP}
		where job_id =
		#{jobId,jdbcType=VARCHAR} and batch_number =
		#{batchNumber,jdbcType=VARCHAR}
	</update>
	<!-- chenye -->
	<select id="getMaxParameter" resultType="java.lang.String">
		/*balance*/
		select
		max(JOB_PARAMETER)
		from ib_t_job_status
		where JOB_ID
		='JOB_OUTBOUND_OVP_WTY' AND JOB_TYPE = 'OUTBOUND' and loaddata_status = 'OVER'
	</select>

	<select id="getAodMaxParameter" resultType="java.lang.String">
		/*balance*/
		select
		max(JOB_PARAMETER)
		from ib_t_job_status
		where JOB_ID
		='JOB_OUTBOUND_OVP_AOD' AND JOB_TYPE = 'OUTBOUND' and loaddata_status = 'OVER'
	</select>

	<update id="updateOVPToLoaddataOver" parameterType="com.lenovo.iqs.entity.IbTJobStatus">
		update
		ib_t_job_status
		set loaddata_status =
		#{loaddataStatus,jdbcType=VARCHAR},
		job_load_endtime =
		#{jobLoadEndtime,jdbcType=TIMESTAMP}
		where job_id =
		#{jobId,jdbcType=VARCHAR} and batch_number =
		#{batchNumber,jdbcType=VARCHAR} and job_type =
		#{jobType,jdbcType=VARCHAR}
	</update>

	<select id="selectByJobIdAndJobType" parameterType="string"
		resultType="java.lang.String">
		select
		max(batch_number)
		from ib_t_job_status
		where
		loaddata_status = 'OVER' and job_id=#{JobId,jdbcType=VARCHAR}
		and
		job_type='INBOUND'
	</select>
	
	
	<select id="selectLtBatchNumber" parameterType="java.util.Map"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from ib_t_job_status
		where
		loaddata_status = 'OVER' and job_id=#{JobId,jdbcType=VARCHAR}
		and
		job_type='INBOUND' and batch_number &lt; #{batchNumber,jdbcType=VARCHAR}
	</select>
	
	<select id="getMaxMotoParameter" resultType="java.lang.String">
		select
		max(JOB_PARAMETER)
		from ib_t_job_status
		where JOB_ID
		='JOB_OUTBOUND_MOTO_WARRANTY' AND JOB_TYPE = 'OUTBOUND' and loaddata_status = 'OVER'
	</select>
	<select id="getMaxPOPParameter" resultType="java.lang.String">
		select
		max(JOB_PARAMETER)
		from ib_t_job_status
		where JOB_ID
		='JOB_OUTBOUND_MOTO_POP' AND JOB_TYPE = 'OUTBOUND' and loaddata_status = 'OVER'
	</select>

</mapper>