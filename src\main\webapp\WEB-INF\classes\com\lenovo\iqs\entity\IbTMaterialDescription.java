/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTMaterialDescription implements Serializable {
/*    */   private Integer id;
/*    */   private String materialId;
/*    */   private String language;
/*    */   
/*  7 */   public void setId(Integer id) { this.id = id; } private String description; private Date lastChangeTime; private static final long serialVersionUID = 1L; public void setMaterialId(String materialId) { this.materialId = materialId; } public void setLanguage(String language) { this.language = language; } public void setDescription(String description) { this.description = description; } public void setLastChangeTime(Date lastChangeTime) { this.lastChangeTime = lastChangeTime; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTMaterialDescription)) return false;  com.lenovo.iqs.entity.IbTMaterialDescription other = (com.lenovo.iqs.entity.IbTMaterialDescription)o; if (!other.canEqual(this)) return false;  Object this$id = getId(), other$id = other.getId(); if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id)) return false;  Object this$materialId = getMaterialId(), other$materialId = other.getMaterialId(); if ((this$materialId == null) ? (other$materialId != null) : !this$materialId.equals(other$materialId)) return false;  Object this$language = getLanguage(), other$language = other.getLanguage(); if ((this$language == null) ? (other$language != null) : !this$language.equals(other$language)) return false;  Object this$description = getDescription(), other$description = other.getDescription(); if ((this$description == null) ? (other$description != null) : !this$description.equals(other$description)) return false;  Object this$lastChangeTime = getLastChangeTime(), other$lastChangeTime = other.getLastChangeTime(); return !((this$lastChangeTime == null) ? (other$lastChangeTime != null) : !this$lastChangeTime.equals(other$lastChangeTime)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTMaterialDescription; } public int hashCode() { int PRIME = 59; result = 1; Object $id = getId(); result = result * 59 + (($id == null) ? 43 : $id.hashCode()); Object $materialId = getMaterialId(); result = result * 59 + (($materialId == null) ? 43 : $materialId.hashCode()); Object $language = getLanguage(); result = result * 59 + (($language == null) ? 43 : $language.hashCode()); Object $description = getDescription(); result = result * 59 + (($description == null) ? 43 : $description.hashCode()); Object $lastChangeTime = getLastChangeTime(); return result * 59 + (($lastChangeTime == null) ? 43 : $lastChangeTime.hashCode()); } public String toString() { return "IbTMaterialDescription(id=" + getId() + ", materialId=" + getMaterialId() + ", language=" + getLanguage() + ", description=" + getDescription() + ", lastChangeTime=" + getLastChangeTime() + ")"; }
/*    */    public Integer getId() {
/*  9 */     return this.id;
/*    */   } public String getMaterialId() {
/* 11 */     return this.materialId;
/*    */   } public String getLanguage() {
/* 13 */     return this.language;
/*    */   } public String getDescription() {
/* 15 */     return this.description;
/*    */   } public Date getLastChangeTime() {
/* 17 */     return this.lastChangeTime;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTMaterialDescription.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */