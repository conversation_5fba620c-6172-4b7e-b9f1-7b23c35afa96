/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTImeiSnMb implements Serializable { private Integer id;
/*    */   private String imeiCode;
/*    */   private String serialNumber;
/*    */   private String partsType;
/*    */   
/*  7 */   public void setId(Integer id) { this.id = id; } private Date imeiSendDatetime; private String usedFlag; private Date lastChange; private static final long serialVersionUID = 1L; public void setImeiCode(String imeiCode) { this.imeiCode = imeiCode; } public void setSerialNumber(String serialNumber) { this.serialNumber = serialNumber; } public void setPartsType(String partsType) { this.partsType = partsType; } public void setImeiSendDatetime(Date imeiSendDatetime) { this.imeiSendDatetime = imeiSendDatetime; } public void setUsedFlag(String usedFlag) { this.usedFlag = usedFlag; } public void setLastChange(Date lastChange) { this.lastChange = lastChange; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTImeiSnMb)) return false;  com.lenovo.iqs.entity.IbTImeiSnMb other = (com.lenovo.iqs.entity.IbTImeiSnMb)o; if (!other.canEqual(this)) return false;  Object this$id = getId(), other$id = other.getId(); if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id)) return false;  Object this$imeiCode = getImeiCode(), other$imeiCode = other.getImeiCode(); if ((this$imeiCode == null) ? (other$imeiCode != null) : !this$imeiCode.equals(other$imeiCode)) return false;  Object this$serialNumber = getSerialNumber(), other$serialNumber = other.getSerialNumber(); if ((this$serialNumber == null) ? (other$serialNumber != null) : !this$serialNumber.equals(other$serialNumber)) return false;  Object this$partsType = getPartsType(), other$partsType = other.getPartsType(); if ((this$partsType == null) ? (other$partsType != null) : !this$partsType.equals(other$partsType)) return false;  Object this$imeiSendDatetime = getImeiSendDatetime(), other$imeiSendDatetime = other.getImeiSendDatetime(); if ((this$imeiSendDatetime == null) ? (other$imeiSendDatetime != null) : !this$imeiSendDatetime.equals(other$imeiSendDatetime)) return false;  Object this$usedFlag = getUsedFlag(), other$usedFlag = other.getUsedFlag(); if ((this$usedFlag == null) ? (other$usedFlag != null) : !this$usedFlag.equals(other$usedFlag)) return false;  Object this$lastChange = getLastChange(), other$lastChange = other.getLastChange(); return !((this$lastChange == null) ? (other$lastChange != null) : !this$lastChange.equals(other$lastChange)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTImeiSnMb; } public int hashCode() { int PRIME = 59; result = 1; Object $id = getId(); result = result * 59 + (($id == null) ? 43 : $id.hashCode()); Object $imeiCode = getImeiCode(); result = result * 59 + (($imeiCode == null) ? 43 : $imeiCode.hashCode()); Object $serialNumber = getSerialNumber(); result = result * 59 + (($serialNumber == null) ? 43 : $serialNumber.hashCode()); Object $partsType = getPartsType(); result = result * 59 + (($partsType == null) ? 43 : $partsType.hashCode()); Object $imeiSendDatetime = getImeiSendDatetime(); result = result * 59 + (($imeiSendDatetime == null) ? 43 : $imeiSendDatetime.hashCode()); Object $usedFlag = getUsedFlag(); result = result * 59 + (($usedFlag == null) ? 43 : $usedFlag.hashCode()); Object $lastChange = getLastChange(); return result * 59 + (($lastChange == null) ? 43 : $lastChange.hashCode()); } public String toString() { return "IbTImeiSnMb(id=" + getId() + ", imeiCode=" + getImeiCode() + ", serialNumber=" + getSerialNumber() + ", partsType=" + getPartsType() + ", imeiSendDatetime=" + getImeiSendDatetime() + ", usedFlag=" + getUsedFlag() + ", lastChange=" + getLastChange() + ")"; }
/*    */    public Integer getId() {
/*  9 */     return this.id;
/*    */   } public String getImeiCode() {
/* 11 */     return this.imeiCode;
/*    */   } public String getSerialNumber() {
/* 13 */     return this.serialNumber;
/*    */   } public String getPartsType() {
/* 15 */     return this.partsType;
/*    */   } public Date getImeiSendDatetime() {
/* 17 */     return this.imeiSendDatetime;
/*    */   } public String getUsedFlag() {
/* 19 */     return this.usedFlag;
/*    */   } public Date getLastChange() {
/* 21 */     return this.lastChange;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTImeiSnMb.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */