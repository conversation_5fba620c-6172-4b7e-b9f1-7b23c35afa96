package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTUpdR12Inbound;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTUpdR12InboundMapper {
  int deleteByPrimaryKey(Integer paramInteger);
  
  int insert(IbTUpdR12Inbound paramIbTUpdR12Inbound);
  
  IbTUpdR12Inbound selectByPrimaryKey(Integer paramInteger);
  
  int updateByPrimaryKey(IbTUpdR12Inbound paramIbTUpdR12Inbound);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTUpdR12InboundMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */