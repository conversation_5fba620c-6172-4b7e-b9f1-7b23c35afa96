package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.datablocksign.bean.Config;
import com.lenovo.iqs.entity.IbTUpdConfig;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTUpdConfigMapper {
  int deleteByPrimaryKey(IbTUpdConfig paramIbTUpdConfig);
  
  int insert(IbTUpdConfig paramIbTUpdConfig);
  
  int countBySectionAndKey(@Param("section") String paramString1, @Param("key") String paramString2);
  
  List<String> SelectBySectionAndKey(@Param("section") String paramString1, @Param("key") String paramString2);
  
  String selectOneRecord(@Param("section") String paramString1, @Param("key") String paramString2);
  
  int countForbiddenIMEI(@Param("oldImei") String paramString);
  
  int countExceptionIMEI(@Param("oldImei") String paramString);
  
  List<Config> getConfigRules();
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTUpdConfigMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */