/*    */ package WEB-INF.classes.com.lenovo.iqs.wechat.bean;
/*    */ 
/*    */ import com.lenovo.iqs.utils.XmlUtils;
/*    */ 
/*    */ public class WeChatResponse
/*    */ {
/*    */   private String responseCode;
/*    */   private String responseMsg;
/*    */   
/*    */   public void setResponseCode(String responseCode) {
/* 11 */     this.responseCode = responseCode; } public void setResponseMsg(String responseMsg) { this.responseMsg = responseMsg; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.wechat.bean.WeChatResponse)) return false;  com.lenovo.iqs.wechat.bean.WeChatResponse other = (com.lenovo.iqs.wechat.bean.WeChatResponse)o; if (!other.canEqual(this)) return false;  Object this$responseCode = getResponseCode(), other$responseCode = other.getResponseCode(); if ((this$responseCode == null) ? (other$responseCode != null) : !this$responseCode.equals(other$responseCode)) return false;  Object this$responseMsg = getResponseMsg(), other$responseMsg = other.getResponseMsg(); return !((this$responseMsg == null) ? (other$responseMsg != null) : !this$responseMsg.equals(other$responseMsg)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.wechat.bean.WeChatResponse; } public int hashCode() { int PRIME = 59; result = 1; Object $responseCode = getResponseCode(); result = result * 59 + (($responseCode == null) ? 43 : $responseCode.hashCode()); Object $responseMsg = getResponseMsg(); return result * 59 + (($responseMsg == null) ? 43 : $responseMsg.hashCode()); } public String toString() { return "WeChatResponse(responseCode=" + getResponseCode() + ", responseMsg=" + getResponseMsg() + ")"; }
/*    */   
/* 13 */   public String getResponseCode() { return this.responseCode; } public String getResponseMsg() {
/* 14 */     return this.responseMsg;
/*    */   }
/*    */   public static com.lenovo.iqs.wechat.bean.WeChatResponse build(String data) throws Exception {
/* 17 */     String errorCode = XmlUtils.getContentByTag(data, "errorCode");
/* 18 */     String errorMessage = XmlUtils.getContentByTag(data, "errorMessage");
/*    */     
/* 20 */     com.lenovo.iqs.wechat.bean.WeChatResponse response = new com.lenovo.iqs.wechat.bean.WeChatResponse();
/* 21 */     if ("0".equals(errorCode)) {
/* 22 */       response.setResponseCode("0000");
/* 23 */       response.setResponseMsg("Success");
/* 24 */     } else if ("9".equals(errorCode)) {
/* 25 */       response.setResponseCode("0000");
/* 26 */       response.setResponseMsg("Data not uploaded to GPS/Wechat however user data is stored at UPD. Upon request we shall relay it to GPS/wechat.");
/*    */     } else {
/* 28 */       response.setResponseCode("5078");
/* 29 */       response.setResponseMsg("WeChat GPS Service Call Failed");
/*    */     } 
/* 31 */     return response;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\wechat\bean\WeChatResponse.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */