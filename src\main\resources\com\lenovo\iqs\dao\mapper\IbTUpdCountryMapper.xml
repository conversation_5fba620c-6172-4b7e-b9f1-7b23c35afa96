<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdCountryMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdCountry" >
    <id column="COUNTRY_CODE" property="countryCode" jdbcType="VARCHAR" />
    <result column="DESCRIPTION" property="description" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    COUNTRY_CODE, DESCRIPTION
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_upd_country
    where COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from ib_t_upd_country
    where COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdCountry" >
    insert into ib_t_upd_country (COUNTRY_CODE, DESCRIPTION)
    values (#{countryCode,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdCountry" >
    insert into ib_t_upd_country
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="countryCode != null" >
        COUNTRY_CODE,
      </if>
      <if test="description != null" >
        DESCRIPTION,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="countryCode != null" >
        #{countryCode,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTUpdCountry" >
    update ib_t_upd_country
    <set >
      <if test="description != null" >
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
    </set>
    where COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdCountry" >
    update ib_t_upd_country
    set DESCRIPTION = #{description,jdbcType=VARCHAR}
    where COUNTRY_CODE = #{countryCode,jdbcType=VARCHAR}
  </update>
</mapper>