<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdConfigMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdConfig" >
    <id column="SECTION" property="section" jdbcType="VARCHAR" />
    <id column="key" property="key" jdbcType="VARCHAR" />
    <id column="value" property="value" jdbcType="VARCHAR" />
  </resultMap>

  <!-- RGB764 for rsd etoken validation -->

  <resultMap id="ConfigMap" type="com.lenovo.iqs.datablocksign.bean.Config" >
    <id column="keyfield" property="key" jdbcType="VARCHAR" />
    <id column="valuefield" property="value" jdbcType="VARCHAR" />
<!--    <id column="date_added" property="DateAdded" jdbcType="VARCHAR" />-->
  </resultMap>

  <!-- RGB764 for etoken validation -->
  <delete id="deleteByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdConfig" >
    delete from ib_t_upd_config
    where SECTION = #{section,jdbcType=VARCHAR}
      and key = #{key,jdbcType=VARCHAR}
      and value = #{value,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdConfig" >
    insert into ib_t_upd_config (SECTION, key, value
      )
    values (#{section,jdbcType=VARCHAR}, #{key,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdConfig" >
    insert into ib_t_upd_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="section != null" >
        SECTION,
      </if>
      <if test="key != null" >
        key,
      </if>
      <if test="value != null" >
        value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="section != null" >
        #{section,jdbcType=VARCHAR},
      </if>
      <if test="key != null" >
        #{key,jdbcType=VARCHAR},
      </if>
      <if test="value != null" >
        #{value,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="countBySectionAndKey" resultType="java.lang.Integer">
    select count(1) from ib_t_upd_config where section =  #{section} AND `KEY` = #{key};
  </select>

  <select id="selectOneRecord" resultType="java.lang.String">
    SELECT value FROM ib_t_upd_config WHERE 1 = 1
    <if test="section != null and section != ''">
      And SECTION = #{section}
    </if>
    <if test="key != null and key != ''">
      And `KEY` = #{key}
    </if>
    limit 1;
  </select>

  <select id="SelectBySectionAndKey" resultType="java.lang.String">
    SELECT value FROM ib_t_upd_config WHERE 1 = 1
    <if test="section != null and section != ''">
      and section = #{section}
    </if>
    <if test="key != null and key != ''">
      And `key` = #{key};
    </if>
  </select>

  <select id="countForbiddenIMEI" resultType="java.lang.Integer">
    select count(1) from ib_t_upd_config where section = 'DATABLOCK_SIGN' and `key` = 'FORBIDEN_IMEI' and `value` &lt;= #{oldImei} and value1 >= #{oldImei} and date_ended is null;
  </select>

  <select id="countExceptionIMEI" resultType="java.lang.Integer">
    select count(1) from ib_t_upd_config where section = 'DATABLOCK_SIGN' and `key` = 'EXCEPTION_IMEI' and `value` = #{oldImei};
  </select>

  <select id="getConfigRules" resultMap="ConfigMap">
    select `value` as keyfield, value1 as valuefield  from ib_t_upd_config where section = 'DATABLOCK_SIGN' and `key` = 'RSD_VALIDATION_FLAGS';
  </select>
</mapper>