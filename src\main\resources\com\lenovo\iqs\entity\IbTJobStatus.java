/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTJobStatus implements Serializable { private Integer jobstatusId; private String jobId; private String jobDescription; private String jobType;
/*    */   private String loaddataStatus;
/*    */   private String processeStatus;
/*    */   private Date jobLoadStarttime;
/*    */   
/*  7 */   public void setJobstatusId(Integer jobstatusId) { this.jobstatusId = jobstatusId; } private Date jobLoadEndtime; private Date jobProcesseStarttime; private Date jobProcesseEndtime; private String jobParameter; private String remark; private Date createTime; private String batchNumber; private static final long serialVersionUID = 1L; public void setJobId(String jobId) { this.jobId = jobId; } public void setJobDescription(String jobDescription) { this.jobDescription = jobDescription; } public void setJobType(String jobType) { this.jobType = jobType; } public void setLoaddataStatus(String loaddataStatus) { this.loaddataStatus = loaddataStatus; } public void setProcesseStatus(String processeStatus) { this.processeStatus = processeStatus; } public void setJobLoadStarttime(Date jobLoadStarttime) { this.jobLoadStarttime = jobLoadStarttime; } public void setJobLoadEndtime(Date jobLoadEndtime) { this.jobLoadEndtime = jobLoadEndtime; } public void setJobProcesseStarttime(Date jobProcesseStarttime) { this.jobProcesseStarttime = jobProcesseStarttime; } public void setJobProcesseEndtime(Date jobProcesseEndtime) { this.jobProcesseEndtime = jobProcesseEndtime; } public void setJobParameter(String jobParameter) { this.jobParameter = jobParameter; } public void setRemark(String remark) { this.remark = remark; } public void setCreateTime(Date createTime) { this.createTime = createTime; } public void setBatchNumber(String batchNumber) { this.batchNumber = batchNumber; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTJobStatus)) return false;  com.lenovo.iqs.entity.IbTJobStatus other = (com.lenovo.iqs.entity.IbTJobStatus)o; if (!other.canEqual(this)) return false;  Object this$jobstatusId = getJobstatusId(), other$jobstatusId = other.getJobstatusId(); if ((this$jobstatusId == null) ? (other$jobstatusId != null) : !this$jobstatusId.equals(other$jobstatusId)) return false;  Object this$jobId = getJobId(), other$jobId = other.getJobId(); if ((this$jobId == null) ? (other$jobId != null) : !this$jobId.equals(other$jobId)) return false;  Object this$jobDescription = getJobDescription(), other$jobDescription = other.getJobDescription(); if ((this$jobDescription == null) ? (other$jobDescription != null) : !this$jobDescription.equals(other$jobDescription)) return false;  Object this$jobType = getJobType(), other$jobType = other.getJobType(); if ((this$jobType == null) ? (other$jobType != null) : !this$jobType.equals(other$jobType)) return false;  Object this$loaddataStatus = getLoaddataStatus(), other$loaddataStatus = other.getLoaddataStatus(); if ((this$loaddataStatus == null) ? (other$loaddataStatus != null) : !this$loaddataStatus.equals(other$loaddataStatus)) return false;  Object this$processeStatus = getProcesseStatus(), other$processeStatus = other.getProcesseStatus(); if ((this$processeStatus == null) ? (other$processeStatus != null) : !this$processeStatus.equals(other$processeStatus)) return false;  Object this$jobLoadStarttime = getJobLoadStarttime(), other$jobLoadStarttime = other.getJobLoadStarttime(); if ((this$jobLoadStarttime == null) ? (other$jobLoadStarttime != null) : !this$jobLoadStarttime.equals(other$jobLoadStarttime)) return false;  Object this$jobLoadEndtime = getJobLoadEndtime(), other$jobLoadEndtime = other.getJobLoadEndtime(); if ((this$jobLoadEndtime == null) ? (other$jobLoadEndtime != null) : !this$jobLoadEndtime.equals(other$jobLoadEndtime)) return false;  Object this$jobProcesseStarttime = getJobProcesseStarttime(), other$jobProcesseStarttime = other.getJobProcesseStarttime(); if ((this$jobProcesseStarttime == null) ? (other$jobProcesseStarttime != null) : !this$jobProcesseStarttime.equals(other$jobProcesseStarttime)) return false;  Object this$jobProcesseEndtime = getJobProcesseEndtime(), other$jobProcesseEndtime = other.getJobProcesseEndtime(); if ((this$jobProcesseEndtime == null) ? (other$jobProcesseEndtime != null) : !this$jobProcesseEndtime.equals(other$jobProcesseEndtime)) return false;  Object this$jobParameter = getJobParameter(), other$jobParameter = other.getJobParameter(); if ((this$jobParameter == null) ? (other$jobParameter != null) : !this$jobParameter.equals(other$jobParameter)) return false;  Object this$remark = getRemark(), other$remark = other.getRemark(); if ((this$remark == null) ? (other$remark != null) : !this$remark.equals(other$remark)) return false;  Object this$createTime = getCreateTime(), other$createTime = other.getCreateTime(); if ((this$createTime == null) ? (other$createTime != null) : !this$createTime.equals(other$createTime)) return false;  Object this$batchNumber = getBatchNumber(), other$batchNumber = other.getBatchNumber(); return !((this$batchNumber == null) ? (other$batchNumber != null) : !this$batchNumber.equals(other$batchNumber)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTJobStatus; } public int hashCode() { int PRIME = 59; result = 1; Object $jobstatusId = getJobstatusId(); result = result * 59 + (($jobstatusId == null) ? 43 : $jobstatusId.hashCode()); Object $jobId = getJobId(); result = result * 59 + (($jobId == null) ? 43 : $jobId.hashCode()); Object $jobDescription = getJobDescription(); result = result * 59 + (($jobDescription == null) ? 43 : $jobDescription.hashCode()); Object $jobType = getJobType(); result = result * 59 + (($jobType == null) ? 43 : $jobType.hashCode()); Object $loaddataStatus = getLoaddataStatus(); result = result * 59 + (($loaddataStatus == null) ? 43 : $loaddataStatus.hashCode()); Object $processeStatus = getProcesseStatus(); result = result * 59 + (($processeStatus == null) ? 43 : $processeStatus.hashCode()); Object $jobLoadStarttime = getJobLoadStarttime(); result = result * 59 + (($jobLoadStarttime == null) ? 43 : $jobLoadStarttime.hashCode()); Object $jobLoadEndtime = getJobLoadEndtime(); result = result * 59 + (($jobLoadEndtime == null) ? 43 : $jobLoadEndtime.hashCode()); Object $jobProcesseStarttime = getJobProcesseStarttime(); result = result * 59 + (($jobProcesseStarttime == null) ? 43 : $jobProcesseStarttime.hashCode()); Object $jobProcesseEndtime = getJobProcesseEndtime(); result = result * 59 + (($jobProcesseEndtime == null) ? 43 : $jobProcesseEndtime.hashCode()); Object $jobParameter = getJobParameter(); result = result * 59 + (($jobParameter == null) ? 43 : $jobParameter.hashCode()); Object $remark = getRemark(); result = result * 59 + (($remark == null) ? 43 : $remark.hashCode()); Object $createTime = getCreateTime(); result = result * 59 + (($createTime == null) ? 43 : $createTime.hashCode()); Object $batchNumber = getBatchNumber(); return result * 59 + (($batchNumber == null) ? 43 : $batchNumber.hashCode()); } public String toString() { return "IbTJobStatus(jobstatusId=" + getJobstatusId() + ", jobId=" + getJobId() + ", jobDescription=" + getJobDescription() + ", jobType=" + getJobType() + ", loaddataStatus=" + getLoaddataStatus() + ", processeStatus=" + getProcesseStatus() + ", jobLoadStarttime=" + getJobLoadStarttime() + ", jobLoadEndtime=" + getJobLoadEndtime() + ", jobProcesseStarttime=" + getJobProcesseStarttime() + ", jobProcesseEndtime=" + getJobProcesseEndtime() + ", jobParameter=" + getJobParameter() + ", remark=" + getRemark() + ", createTime=" + getCreateTime() + ", batchNumber=" + getBatchNumber() + ")"; }
/*    */    public Integer getJobstatusId() {
/*  9 */     return this.jobstatusId;
/*    */   } public String getJobId() {
/* 11 */     return this.jobId;
/*    */   } public String getJobDescription() {
/* 13 */     return this.jobDescription;
/*    */   } public String getJobType() {
/* 15 */     return this.jobType;
/*    */   } public String getLoaddataStatus() {
/* 17 */     return this.loaddataStatus;
/*    */   } public String getProcesseStatus() {
/* 19 */     return this.processeStatus;
/*    */   } public Date getJobLoadStarttime() {
/* 21 */     return this.jobLoadStarttime;
/*    */   } public Date getJobLoadEndtime() {
/* 23 */     return this.jobLoadEndtime;
/*    */   } public Date getJobProcesseStarttime() {
/* 25 */     return this.jobProcesseStarttime;
/*    */   } public Date getJobProcesseEndtime() {
/* 27 */     return this.jobProcesseEndtime;
/*    */   } public String getJobParameter() {
/* 29 */     return this.jobParameter;
/*    */   } public String getRemark() {
/* 31 */     return this.remark;
/*    */   } public Date getCreateTime() {
/* 33 */     return this.createTime;
/*    */   } public String getBatchNumber() {
/* 35 */     return this.batchNumber;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTJobStatus.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */