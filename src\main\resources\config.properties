# This is PRODUCTION config file.

#mbg datasource config
jdbc.driverClassName=com.mysql.cj.jdbc.Driver
#jdbc.url=*************************************************************************************************************
#jdbc.username=admin
#jdbc.password=abcd-1233
#jdbc.url=**********************************************************************
#jdbc.url=***********************************************************************
#jdbc.url=************************************************************************************
jdbc.url=***********************************************************************************************
jdbc.username=iqs_user
jdbc.password=iqs_password

#Inbound, Batch, Quartz DB
config.driverClassName=com.mysql.cj.jdbc.Driver
#config.url=****************************************************************************************************************
#config.url=***********************************************************************
#config.url=************************************************************************************
config.url=***********************************************************************************************
#config.url=**********************************************************************
#config.username=root
#config.password=Love0uriqS
config.username=iqs_user
config.password=iqs_password

#PCG IQS DB
pcgjdbc.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
pcgjdbc.url=***********************************; DatabaseName=iBaseOlap
pcgjdbc.username=p_iqs_owner
pcgjdbc.password=Initial101

#spp datasource config
spp.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
spp.url=**********************************; DatabaseName=SOURCE_DB
spp.username=a_dc
spp.password=Initial0

#IBASE URL
ibase_url=http://csp.lenovo.com/ibapp/POIRequest.jsp

#IBASE WebService
ibase.webservice.username=RFCJIBCSP
ibase.webservice.password=initial

#IBASE resend
ibase.webservice.resendUrl=http://qas-cs.lenovo.com/sap/bc/srt/rfc/sap/zibp_batch_query_by_sn/301/lclaim1/lclaim1

#LCLAIM Batch Call iBase
ibase.webservice.lclaim-batch-quary=http://qas-cs.lenovo.com:8001/sap/bc/srt/rfc/sap/zibp_batch_query_result_by_sn/301/zclaim2/zclaim2

#Upd File Verifier
ibase.file.verify.input.address=***********
ibase.file.verify.input.port=21
ibase.file.verify.input.username=In_PKI2LSCRM_Q
ibase.file.verify.input.password=In_PKI2LSCRM_Q
ibase.file.verify.output.address=***********
ibase.file.verify.output.port=21
ibase.file.verify.output.username=In_PKI2LSCRM_Q
ibase.file.verify.output.password=In_PKI2LSCRM_Q
ibase.file.verify.filePath=PKI/Inbound,PKI/Verified;PKI/PKIMUA/Inbound,PKI/PKIMUA/Inbound/Verified


#upd get protocol
protocol_url=https://rsgw.motorola.com/imeitac/updweb/getProdTypeForSerial?inputStr=
protocol_user=RSD@UPD5
protocol_pwd=Rsd_1234

#ibase sap config CSQ
#ibase.sap.appServerHost=************
#ibase.sap.logonGroup=IBASE
#ibase.sap.messageServerHost=************
#ibase.sap.systemID=CSQ
#ibase.sap.systemNumber=01
#ibase.sap.client=301
#ibase.sap.user=SYSB-IQS
#ibase.sap.password=38(-2yEp
#ibase.sap.language=EN
#ibase.sap.poolCapacity=5
#ibase.sap.peakLimit=10

#ibase sap config CSP
ibase.sap.appServerHost=**********
ibase.sap.logonGroup=IBASE
ibase.sap.messageServerHost=**********
ibase.sap.systemID=CSP
ibase.sap.systemNumber=71
ibase.sap.client=301
ibase.sap.user=SYSB-MBGIQS
ibase.sap.password=1234qwer
ibase.sap.language=EN
ibase.sap.poolCapacity=10
ibase.sap.peakLimit=10

#Gps config
#GPS_URL=https://wsgw.mot.com/GPSTrustonicRSUService/RSUService
GPS_URL=https://wsgw.motorola.com:443/GPSTrustonicRSUService/RSUService
GPS_USER=rsdjira
GPS_PWD=Moto#2015


#pki config
pki_trust_cert=/opt/tomcat8/data/pki.keystore
pki_trust_pass=ZujuIduJyche7694
#pki_trust_pass=HasozuZaywe24845
#pki_identity_cert=/opt/tomcat8/data/ServiceCenterWebServices.pfx
pki_identity_cert=/opt/tomcat8/data/ServiceCenterWebServices.pfx
#pki_identity_pass=ZujuIduJyche7694
#pki_identity_pass=LyguAgyGoshy3319
pki_identity_pass=GoLuckiPhiDubu962

#pki nl config

nl_pki_trust_cert=/opt/tomcat8/data/pki.keystore
nl_pki_trust_pass=ZujuIduJyche7694
nl_pki_identity_cert=/opt/tomcat8/data/ServiceCenterWebServices.pfx
nl_pki_identity_pass=GoLuckiPhiDubu962
nl_pki_ip=**************
nl_pki_url=https://**************:2029/api/protocol/handler

RSD_VALIDATE_URL=https://ebiz-esb.cloud.motorola.net/SRPEtokenDBSValidationService
RSD_VALIDATE_USER=rsdgfs
RSD_VALIDATE_PWD=Moto@123
