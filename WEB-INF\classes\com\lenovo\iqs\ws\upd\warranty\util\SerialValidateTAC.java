/*     */ package WEB-INF.classes.com.lenovo.iqs.ws.upd.warranty.util;
/*     */ 
/*     */ import com.lenovo.iqs.ws.upd.warranty.util.HsnUtils;
/*     */ import com.lenovo.iqs.ws.upd.warranty.util.MeidUtils;
/*     */ import com.lenovo.iqs.ws.upd.warranty.util.SerialValidation;
/*     */ import org.apache.log4j.Logger;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SerialValidateTAC
/*     */ {
/*  23 */   private static Logger log = Logger.getLogger(com.lenovo.iqs.ws.upd.warranty.util.SerialValidateTAC.class);
/*  24 */   private static String IMEI = "IMEI";
/*  25 */   private static String MEID = "MEID";
/*  26 */   private static String ESN = "ESN";
/*  27 */   private static String MSN = "MSN";
/*  28 */   private static String RSN = "RSN";
/*  29 */   private static String ALL = "ALL";
/*  30 */   private static String LSA = "LSA";
/*  31 */   private static String HSN = "HSN";
/*  32 */   private static String UID = "UID";
/*  33 */   private static String TRACK_ID = "TRACK_ID";
/*     */ 
/*     */ 
/*     */   
/*     */   private static int hexValue(char ch) {
/*  38 */     switch (ch) {
/*     */       case '0':
/*  40 */         return 0;
/*     */       case '1':
/*  42 */         return 1;
/*     */       case '2':
/*  44 */         return 2;
/*     */       case '3':
/*  46 */         return 3;
/*     */       case '4':
/*  48 */         return 4;
/*     */       case '5':
/*  50 */         return 5;
/*     */       case '6':
/*  52 */         return 6;
/*     */       case '7':
/*  54 */         return 7;
/*     */       case '8':
/*  56 */         return 8;
/*     */       case '9':
/*  58 */         return 9;
/*     */       case 'A':
/*  60 */         return 10;
/*     */       case 'B':
/*  62 */         return 11;
/*     */       case 'C':
/*  64 */         return 12;
/*     */       case 'D':
/*  66 */         return 13;
/*     */       case 'E':
/*  68 */         return 14;
/*     */       case 'F':
/*  70 */         return 15;
/*     */     } 
/*  72 */     return -1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static int decValue(char ch) {
/*  79 */     switch (ch) {
/*     */       case '0':
/*  81 */         return 0;
/*     */       case '1':
/*  83 */         return 1;
/*     */       case '2':
/*  85 */         return 2;
/*     */       case '3':
/*  87 */         return 3;
/*     */       case '4':
/*  89 */         return 4;
/*     */       case '5':
/*  91 */         return 5;
/*     */       case '6':
/*  93 */         return 6;
/*     */       case '7':
/*  95 */         return 7;
/*     */       case '8':
/*  97 */         return 8;
/*     */       case '9':
/*  99 */         return 9;
/*     */     } 
/* 101 */     return -1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static int base29Value(char ch) {
/* 108 */     switch (ch) {
/*     */       case '0':
/* 110 */         return 0;
/*     */       case '1':
/* 112 */         return 1;
/*     */       case 'A':
/* 114 */         return 2;
/*     */       case 'E':
/* 116 */         return 3;
/*     */       case 'I':
/* 118 */         return 4;
/*     */       case 'O':
/* 120 */         return 5;
/*     */       case 'U':
/* 122 */         return 6;
/*     */     } 
/* 124 */     return -1;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static int alphaNumValue(char ch) {
/* 130 */     switch (ch) {
/*     */       case '0':
/* 132 */         return 0;
/*     */       case '1':
/* 134 */         return 1;
/*     */       case '2':
/* 136 */         return 2;
/*     */       case '3':
/* 138 */         return 3;
/*     */       case '4':
/* 140 */         return 4;
/*     */       case '5':
/* 142 */         return 5;
/*     */       case '6':
/* 144 */         return 6;
/*     */       case '7':
/* 146 */         return 7;
/*     */       case '8':
/* 148 */         return 8;
/*     */       case '9':
/* 150 */         return 9;
/*     */       case 'A':
/* 152 */         return 10;
/*     */       case 'B':
/* 154 */         return 11;
/*     */       case 'C':
/* 156 */         return 12;
/*     */       case 'D':
/* 158 */         return 13;
/*     */       case 'E':
/* 160 */         return 14;
/*     */       case 'F':
/* 162 */         return 15;
/*     */       case 'G':
/* 164 */         return 16;
/*     */       case 'H':
/* 166 */         return 17;
/*     */       case 'I':
/* 168 */         return 18;
/*     */       case 'J':
/* 170 */         return 19;
/*     */       case 'K':
/* 172 */         return 20;
/*     */       case 'L':
/* 174 */         return 21;
/*     */       case 'M':
/* 176 */         return 22;
/*     */       case 'N':
/* 178 */         return 23;
/*     */       case 'O':
/* 180 */         return 24;
/*     */       case 'P':
/* 182 */         return 25;
/*     */       case 'Q':
/* 184 */         return 26;
/*     */       case 'R':
/* 186 */         return 27;
/*     */       case 'S':
/* 188 */         return 28;
/*     */       case 'T':
/* 190 */         return 29;
/*     */       case 'U':
/* 192 */         return 30;
/*     */       case 'V':
/* 194 */         return 31;
/*     */       case 'W':
/* 196 */         return 32;
/*     */       case 'X':
/* 198 */         return 33;
/*     */       case 'Y':
/* 200 */         return 34;
/*     */       case 'Z':
/* 202 */         return 35;
/*     */     } 
/* 204 */     return -1;
/*     */   }
/*     */ 
/*     */   
/*     */   private static boolean isHex(String hex) {
/* 209 */     for (int i = 0; i < hex.length(); i++) {
/* 210 */       if (hexValue(hex.charAt(i)) == -1) {
/* 211 */         return false;
/*     */       }
/*     */     } 
/* 214 */     return true;
/*     */   }
/*     */   
/*     */   private static boolean isDec(String dec) {
/* 218 */     for (int i = 0; i < dec.length(); i++) {
/* 219 */       if (decValue(dec.charAt(i)) == -1) {
/* 220 */         return false;
/*     */       }
/*     */     } 
/* 223 */     return true;
/*     */   }
/*     */   
/*     */   private static boolean isAlphaNum(String sno) {
/* 227 */     for (int i = 0; i < sno.length(); i++) {
/* 228 */       if (alphaNumValue(sno.charAt(i)) == -1) {
/* 229 */         return false;
/*     */       }
/*     */     } 
/* 232 */     return true;
/*     */   }
/*     */   
/*     */   public static String getSerialNoType(String inSerialNumber) {
/* 236 */     String serialno_type = "";
/*     */     try {
/* 238 */       int serialNo_Length = inSerialNumber.trim().length();
/* 239 */       if (serialNo_Length == 8) {
/* 240 */         if (isSerialFormatValid("ESN", inSerialNumber)) {
/* 241 */           serialno_type = "ESN";
/* 242 */           log.info("In ESN - length check 8");
/*     */         } else {
/* 244 */           log.info("In HSN - length check 8");
/* 245 */           serialno_type = "HSN";
/*     */         } 
/* 247 */       } else if (serialNo_Length == 14 && isSerialFormatValid("MEID", inSerialNumber)) {
/* 248 */         serialno_type = "MEID";
/* 249 */       } else if (serialNo_Length == 15) {
/* 250 */         if (isSerialFormatValid("MEID", inSerialNumber)) {
/* 251 */           serialno_type = "MEID";
/* 252 */         } else if (isSerialFormatValid("IMEI", inSerialNumber)) {
/* 253 */           serialno_type = "IMEI";
/*     */         } 
/* 255 */       } else if (serialNo_Length == 10 && isSerialFormatValid("MSN", inSerialNumber)) {
/* 256 */         serialno_type = "MSN";
/* 257 */       } else if (serialNo_Length == 18 && isSerialFormatValid("IMEI", inSerialNumber)) {
/* 258 */         serialno_type = "MEID";
/* 259 */       } else if (serialNo_Length == 9 && isSerialFormatValid("LSA", inSerialNumber)) {
/* 260 */         log.info("Validating 9 digit LSA");
/* 261 */         serialno_type = "LSA";
/* 262 */       } else if (serialNo_Length == 11) {
/* 263 */         serialno_type = validateMSNHSN(inSerialNumber);
/* 264 */         log.info("In HSN");
/* 265 */       } else if (serialNo_Length == 32 && isSerialFormatValid("UID", inSerialNumber)) {
/* 266 */         log.info("Validating 32 digit UID");
/* 267 */         serialno_type = "UID";
/*     */       }
/* 269 */       else if (serialNo_Length == 24 && isSerialFormatValid("UID", inSerialNumber)) {
/* 270 */         log.info("Validating 24 digit UID");
/* 271 */         serialno_type = "UID";
/*     */       } 
/* 273 */     } catch (Exception e) {
/* 274 */       log.error("IQS-CK-ERROR[getSerialNoType]", e);
/*     */     } 
/* 276 */     return serialno_type;
/*     */   }
/*     */   
/*     */   public static boolean isSerialLengthValid(String serial_no_type, String serial_no) {
/* 280 */     int length = serial_no.length();
/*     */     
/* 282 */     if (serial_no_type.equals(IMEI) || serial_no_type.equals(MEID)) {
/* 283 */       if (length <= 8 || length == 0)
/* 284 */         return true; 
/* 285 */     } else if (serial_no_type.equalsIgnoreCase(ESN)) {
/* 286 */       if (length == 2 || length == 0 || length == 1)
/* 287 */         return true; 
/* 288 */     } else if (serial_no_type.equals(ALL) && 
/* 289 */       length == 0) {
/* 290 */       return true;
/*     */     } 
/* 292 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isSerialFormatValid(String serial_no_type, String serial_no) {
/*     */     try {
/* 299 */       if (serial_no_type.equals(IMEI)) {
/* 300 */         if (isDec(serial_no)) {
/* 301 */           return true;
/*     */         }
/* 303 */         return false;
/* 304 */       }  if (serial_no_type.equals(ESN)) {
/* 305 */         return isHex(serial_no);
/*     */       }
/* 307 */       if (serial_no_type.equals(UID)) {
/* 308 */         log.info("Validating the UID Serial number");
/* 309 */         if (serial_no.length() == 24 || serial_no.length() == 32) {
/* 310 */           return true;
/*     */         }
/* 312 */         return false;
/*     */       } 
/*     */ 
/*     */       
/* 316 */       if (serial_no_type.equals(MEID)) {
/* 317 */         if (serial_no.length() != 0) {
/* 318 */           if (MeidUtils.isDecMeid(serial_no) && serial_no.substring(0, 1).equals("9")) {
/* 319 */             if (isHex(serial_no)) {
/* 320 */               return true;
/*     */             }
/* 322 */             return false;
/*     */           } 
/*     */           
/* 325 */           int check0 = Integer.parseInt(serial_no.substring(0, 1), 16);
/* 326 */           int check1 = Integer.parseInt(serial_no.substring(1, 2), 16);
/* 327 */           if (check0 < 10 && check1 < 10) {
/* 328 */             return false;
/*     */           }
/* 330 */           if (isHex(serial_no)) {
/* 331 */             return true;
/*     */           }
/* 333 */           return false;
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 338 */         return false;
/* 339 */       }  if (serial_no_type.equals(MSN)) {
/* 340 */         for (int i = 0; i < serial_no.length(); i++) {
/* 341 */           if (alphaNumValue(serial_no.charAt(i)) == -1)
/* 342 */             return false; 
/*     */         } 
/* 344 */         return true;
/* 345 */       }  if (serial_no_type.equals(RSN)) {
/* 346 */         log.info("Validating the RSN Serial number");
/* 347 */         return isAlphaNum(serial_no);
/* 348 */       }  if (serial_no_type.equals(LSA)) {
/* 349 */         if (isHex(serial_no)) {
/* 350 */           if (validateChecksum(serial_no)) {
/* 351 */             return true;
/*     */           }
/* 353 */           return false;
/*     */         } 
/* 355 */         return false;
/*     */       } 
/* 357 */       return false;
/*     */     
/*     */     }
/* 360 */     catch (Exception e) {
/* 361 */       log.debug("Exception Occured while determining the serial number type" + e.toString());
/* 362 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static String validateMSNHSN(String serial_no) {
/* 368 */     String checkDigit = "";
/*     */     
/*     */     try {
/* 371 */       if (SerialValidation.isSerialFormatValid(HSN, serial_no)) {
/* 372 */         checkDigit = HsnUtils.getHSNCheckdigit(serial_no);
/* 373 */         if (checkDigit.equalsIgnoreCase(serial_no.substring(serial_no.length() - 1))) {
/* 374 */           return "HSN";
/*     */         }
/* 376 */         return "MSN";
/* 377 */       }  if (isSerialFormatValid(MSN, serial_no)) {
/* 378 */         return "MSN";
/*     */       }
/* 380 */       return "";
/*     */     }
/* 382 */     catch (Exception e) {
/* 383 */       log.debug("Exception Occured while determining the serial number type" + e.toString());
/* 384 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static boolean validateChecksum(String serialNumber) {
/*     */     try {
/* 391 */       String checkDigit = MeidUtils.getChecksum(serialNumber);
/* 392 */       log.info("Checksum calulcated for serial number " + serialNumber + "while determining the serial type =" + checkDigit);
/*     */       
/* 394 */       if (checkDigit.equals(serialNumber.substring(serialNumber.length() - 1))) {
/* 395 */         return true;
/*     */       }
/* 397 */       return false;
/*     */     }
/* 399 */     catch (Exception e) {
/* 400 */       log.debug("Exception Occured while determining the serial number type" + e.toString());
/* 401 */       return false;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\w\\upd\warrant\\util\SerialValidateTAC.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */