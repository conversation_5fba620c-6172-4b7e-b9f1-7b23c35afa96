<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdIdenAttributesMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdIdenAttributes" >
    <id column="IMEI_NUMBER" property="imeiNumber" jdbcType="VARCHAR" />
    <result column="SERIAL_NUMBER" property="serialNumber" jdbcType="VARCHAR" />
    <result column="ORDER_NUMBER" property="orderNumber" jdbcType="INTEGER" />
    <result column="LINE_NUMBER" property="lineNumber" jdbcType="INTEGER" />
    <result column="MODEL" property="model" jdbcType="VARCHAR" />
    <result column="SIM_NUMBER" property="simNumber" jdbcType="VARCHAR" />
    <result column="TANAPA" property="tanapa" jdbcType="VARCHAR" />
    <result column="TANAPA_SUFFIX" property="tanapaSuffix" jdbcType="VARCHAR" />
    <result column="FIRMWARE_VERSION" property="firmwareVersion" jdbcType="VARCHAR" />
    <result column="CDMA_ESN" property="cdmaEsn" jdbcType="VARCHAR" />
    <result column="CDMA_PESN" property="cdmaPesn" jdbcType="VARCHAR" />
    <result column="MSL" property="msl" jdbcType="VARCHAR" />
    <result column="OTKSL" property="otksl" jdbcType="VARCHAR" />
    <result column="AUTH_KEY" property="authKey" jdbcType="VARCHAR" />
    <result column="SERVICE_PASSCODE" property="servicePasscode" jdbcType="VARCHAR" />
    <result column="LOCK_4" property="lock4" jdbcType="VARCHAR" />
    <result column="LOCK_5" property="lock5" jdbcType="VARCHAR" />
    <result column="EDF_PART_NBR" property="edfPartNbr" jdbcType="VARCHAR" />
    <result column="APC" property="apc" jdbcType="VARCHAR" />
    <result column="BLUETOOTH_ADDRESS" property="bluetoothAddress" jdbcType="VARCHAR" />
    <result column="REGION_ID" property="regionId" jdbcType="VARCHAR" />
    <result column="SYSTEM_ID" property="systemId" jdbcType="VARCHAR" />
    <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR" />
    <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="MODIFIED_BY" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="MODIFIED_DATE" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    IMEI_NUMBER, SERIAL_NUMBER, ORDER_NUMBER, LINE_NUMBER, MODEL, SIM_NUMBER, TANAPA, 
    TANAPA_SUFFIX, FIRMWARE_VERSION, CDMA_ESN, CDMA_PESN, MSL, OTKSL, AUTH_KEY, SERVICE_PASSCODE, 
    LOCK_4, LOCK_5, EDF_PART_NBR, APC, BLUETOOTH_ADDRESS, REGION_ID, SYSTEM_ID, CREATED_BY, 
    CREATED_DATE, MODIFIED_BY, MODIFIED_DATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_upd_iden_attributes
    where IMEI_NUMBER = #{imeiNumber,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from ib_t_upd_iden_attributes
    where IMEI_NUMBER = #{imeiNumber,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdIdenAttributes" >
    insert into ib_t_upd_iden_attributes (IMEI_NUMBER, SERIAL_NUMBER, ORDER_NUMBER, 
      LINE_NUMBER, MODEL, SIM_NUMBER, 
      TANAPA, TANAPA_SUFFIX, FIRMWARE_VERSION, 
      CDMA_ESN, CDMA_PESN, MSL, 
      OTKSL, AUTH_KEY, SERVICE_PASSCODE, 
      LOCK_4, LOCK_5, EDF_PART_NBR, 
      APC, BLUETOOTH_ADDRESS, REGION_ID, 
      SYSTEM_ID, CREATED_BY, CREATED_DATE, 
      MODIFIED_BY, MODIFIED_DATE)
    values (#{imeiNumber,jdbcType=VARCHAR}, #{serialNumber,jdbcType=VARCHAR}, #{orderNumber,jdbcType=INTEGER}, 
      #{lineNumber,jdbcType=INTEGER}, #{model,jdbcType=VARCHAR}, #{simNumber,jdbcType=VARCHAR}, 
      #{tanapa,jdbcType=VARCHAR}, #{tanapaSuffix,jdbcType=VARCHAR}, #{firmwareVersion,jdbcType=VARCHAR}, 
      #{cdmaEsn,jdbcType=VARCHAR}, #{cdmaPesn,jdbcType=VARCHAR}, #{msl,jdbcType=VARCHAR}, 
      #{otksl,jdbcType=VARCHAR}, #{authKey,jdbcType=VARCHAR}, #{servicePasscode,jdbcType=VARCHAR}, 
      #{lock4,jdbcType=VARCHAR}, #{lock5,jdbcType=VARCHAR}, #{edfPartNbr,jdbcType=VARCHAR}, 
      #{apc,jdbcType=VARCHAR}, #{bluetoothAddress,jdbcType=VARCHAR}, #{regionId,jdbcType=VARCHAR}, 
      #{systemId,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdIdenAttributes" >
    insert into ib_t_upd_iden_attributes
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="imeiNumber != null" >
        IMEI_NUMBER,
      </if>
      <if test="serialNumber != null" >
        SERIAL_NUMBER,
      </if>
      <if test="orderNumber != null" >
        ORDER_NUMBER,
      </if>
      <if test="lineNumber != null" >
        LINE_NUMBER,
      </if>
      <if test="model != null" >
        MODEL,
      </if>
      <if test="simNumber != null" >
        SIM_NUMBER,
      </if>
      <if test="tanapa != null" >
        TANAPA,
      </if>
      <if test="tanapaSuffix != null" >
        TANAPA_SUFFIX,
      </if>
      <if test="firmwareVersion != null" >
        FIRMWARE_VERSION,
      </if>
      <if test="cdmaEsn != null" >
        CDMA_ESN,
      </if>
      <if test="cdmaPesn != null" >
        CDMA_PESN,
      </if>
      <if test="msl != null" >
        MSL,
      </if>
      <if test="otksl != null" >
        OTKSL,
      </if>
      <if test="authKey != null" >
        AUTH_KEY,
      </if>
      <if test="servicePasscode != null" >
        SERVICE_PASSCODE,
      </if>
      <if test="lock4 != null" >
        LOCK_4,
      </if>
      <if test="lock5 != null" >
        LOCK_5,
      </if>
      <if test="edfPartNbr != null" >
        EDF_PART_NBR,
      </if>
      <if test="apc != null" >
        APC,
      </if>
      <if test="bluetoothAddress != null" >
        BLUETOOTH_ADDRESS,
      </if>
      <if test="regionId != null" >
        REGION_ID,
      </if>
      <if test="systemId != null" >
        SYSTEM_ID,
      </if>
      <if test="createdBy != null" >
        CREATED_BY,
      </if>
      <if test="createdDate != null" >
        CREATED_DATE,
      </if>
      <if test="modifiedBy != null" >
        MODIFIED_BY,
      </if>
      <if test="modifiedDate != null" >
        MODIFIED_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="imeiNumber != null" >
        #{imeiNumber,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null" >
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="orderNumber != null" >
        #{orderNumber,jdbcType=INTEGER},
      </if>
      <if test="lineNumber != null" >
        #{lineNumber,jdbcType=INTEGER},
      </if>
      <if test="model != null" >
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="simNumber != null" >
        #{simNumber,jdbcType=VARCHAR},
      </if>
      <if test="tanapa != null" >
        #{tanapa,jdbcType=VARCHAR},
      </if>
      <if test="tanapaSuffix != null" >
        #{tanapaSuffix,jdbcType=VARCHAR},
      </if>
      <if test="firmwareVersion != null" >
        #{firmwareVersion,jdbcType=VARCHAR},
      </if>
      <if test="cdmaEsn != null" >
        #{cdmaEsn,jdbcType=VARCHAR},
      </if>
      <if test="cdmaPesn != null" >
        #{cdmaPesn,jdbcType=VARCHAR},
      </if>
      <if test="msl != null" >
        #{msl,jdbcType=VARCHAR},
      </if>
      <if test="otksl != null" >
        #{otksl,jdbcType=VARCHAR},
      </if>
      <if test="authKey != null" >
        #{authKey,jdbcType=VARCHAR},
      </if>
      <if test="servicePasscode != null" >
        #{servicePasscode,jdbcType=VARCHAR},
      </if>
      <if test="lock4 != null" >
        #{lock4,jdbcType=VARCHAR},
      </if>
      <if test="lock5 != null" >
        #{lock5,jdbcType=VARCHAR},
      </if>
      <if test="edfPartNbr != null" >
        #{edfPartNbr,jdbcType=VARCHAR},
      </if>
      <if test="apc != null" >
        #{apc,jdbcType=VARCHAR},
      </if>
      <if test="bluetoothAddress != null" >
        #{bluetoothAddress,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null" >
        #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null" >
        #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTUpdIdenAttributes" >
    update ib_t_upd_iden_attributes
    <set >
      <if test="serialNumber != null" >
        SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="orderNumber != null" >
        ORDER_NUMBER = #{orderNumber,jdbcType=INTEGER},
      </if>
      <if test="lineNumber != null" >
        LINE_NUMBER = #{lineNumber,jdbcType=INTEGER},
      </if>
      <if test="model != null" >
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="simNumber != null" >
        SIM_NUMBER = #{simNumber,jdbcType=VARCHAR},
      </if>
      <if test="tanapa != null" >
        TANAPA = #{tanapa,jdbcType=VARCHAR},
      </if>
      <if test="tanapaSuffix != null" >
        TANAPA_SUFFIX = #{tanapaSuffix,jdbcType=VARCHAR},
      </if>
      <if test="firmwareVersion != null" >
        FIRMWARE_VERSION = #{firmwareVersion,jdbcType=VARCHAR},
      </if>
      <if test="cdmaEsn != null" >
        CDMA_ESN = #{cdmaEsn,jdbcType=VARCHAR},
      </if>
      <if test="cdmaPesn != null" >
        CDMA_PESN = #{cdmaPesn,jdbcType=VARCHAR},
      </if>
      <if test="msl != null" >
        MSL = #{msl,jdbcType=VARCHAR},
      </if>
      <if test="otksl != null" >
        OTKSL = #{otksl,jdbcType=VARCHAR},
      </if>
      <if test="authKey != null" >
        AUTH_KEY = #{authKey,jdbcType=VARCHAR},
      </if>
      <if test="servicePasscode != null" >
        SERVICE_PASSCODE = #{servicePasscode,jdbcType=VARCHAR},
      </if>
      <if test="lock4 != null" >
        LOCK_4 = #{lock4,jdbcType=VARCHAR},
      </if>
      <if test="lock5 != null" >
        LOCK_5 = #{lock5,jdbcType=VARCHAR},
      </if>
      <if test="edfPartNbr != null" >
        EDF_PART_NBR = #{edfPartNbr,jdbcType=VARCHAR},
      </if>
      <if test="apc != null" >
        APC = #{apc,jdbcType=VARCHAR},
      </if>
      <if test="bluetoothAddress != null" >
        BLUETOOTH_ADDRESS = #{bluetoothAddress,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null" >
        REGION_ID = #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null" >
        SYSTEM_ID = #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        MODIFIED_BY = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        MODIFIED_DATE = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where IMEI_NUMBER = #{imeiNumber,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdIdenAttributes" >
    update ib_t_upd_iden_attributes
    set SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      ORDER_NUMBER = #{orderNumber,jdbcType=INTEGER},
      LINE_NUMBER = #{lineNumber,jdbcType=INTEGER},
      MODEL = #{model,jdbcType=VARCHAR},
      SIM_NUMBER = #{simNumber,jdbcType=VARCHAR},
      TANAPA = #{tanapa,jdbcType=VARCHAR},
      TANAPA_SUFFIX = #{tanapaSuffix,jdbcType=VARCHAR},
      FIRMWARE_VERSION = #{firmwareVersion,jdbcType=VARCHAR},
      CDMA_ESN = #{cdmaEsn,jdbcType=VARCHAR},
      CDMA_PESN = #{cdmaPesn,jdbcType=VARCHAR},
      MSL = #{msl,jdbcType=VARCHAR},
      OTKSL = #{otksl,jdbcType=VARCHAR},
      AUTH_KEY = #{authKey,jdbcType=VARCHAR},
      SERVICE_PASSCODE = #{servicePasscode,jdbcType=VARCHAR},
      LOCK_4 = #{lock4,jdbcType=VARCHAR},
      LOCK_5 = #{lock5,jdbcType=VARCHAR},
      EDF_PART_NBR = #{edfPartNbr,jdbcType=VARCHAR},
      APC = #{apc,jdbcType=VARCHAR},
      BLUETOOTH_ADDRESS = #{bluetoothAddress,jdbcType=VARCHAR},
      REGION_ID = #{regionId,jdbcType=VARCHAR},
      SYSTEM_ID = #{systemId,jdbcType=VARCHAR},
      CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
      MODIFIED_BY = #{modifiedBy,jdbcType=VARCHAR},
      MODIFIED_DATE = #{modifiedDate,jdbcType=TIMESTAMP}
    where IMEI_NUMBER = #{imeiNumber,jdbcType=VARCHAR}
  </update>
</mapper>