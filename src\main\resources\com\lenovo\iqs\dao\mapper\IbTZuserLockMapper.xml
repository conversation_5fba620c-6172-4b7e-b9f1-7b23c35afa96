<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTZuserLockMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTZuserLock" >
    <id column="auto_id" property="autoId" jdbcType="INTEGER" />
    <result column="ip" property="ip" jdbcType="VARCHAR" />
    <result column="userid" property="userid" jdbcType="VARCHAR" />
    <result column="lock_time" property="lockTime" jdbcType="TIMESTAMP" />
    <result column="unlock_time" property="unlockTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    auto_id, ip, userid, lock_time, unlock_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_zuser_lock
    where auto_id = #{autoId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from ib_t_zuser_lock
    where auto_id = #{autoId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTZuserLock" >
    <selectKey resultType="java.lang.Integer" keyProperty="autoId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_zuser_lock (ip, userid, lock_time, 
      unlock_time)
    values (#{ip,jdbcType=VARCHAR}, #{userid,jdbcType=VARCHAR}, #{lockTime,jdbcType=TIMESTAMP}, 
      #{unlockTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTZuserLock" >
    <selectKey resultType="java.lang.Integer" keyProperty="autoId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_zuser_lock
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ip != null" >
        ip,
      </if>
      <if test="userid != null" >
        userid,
      </if>
      <if test="lockTime != null" >
        lock_time,
      </if>
      <if test="unlockTime != null" >
        unlock_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ip != null" >
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="userid != null" >
        #{userid,jdbcType=VARCHAR},
      </if>
      <if test="lockTime != null" >
        #{lockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="unlockTime != null" >
        #{unlockTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTZuserLock" >
    update ib_t_zuser_lock
    <set >
      <if test="ip != null" >
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="userid != null" >
        userid = #{userid,jdbcType=VARCHAR},
      </if>
      <if test="lockTime != null" >
        lock_time = #{lockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="unlockTime != null" >
        unlock_time = #{unlockTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where auto_id = #{autoId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTZuserLock" >
    update ib_t_zuser_lock
    set ip = #{ip,jdbcType=VARCHAR},
      userid = #{userid,jdbcType=VARCHAR},
      lock_time = #{lockTime,jdbcType=TIMESTAMP},
      unlock_time = #{unlockTime,jdbcType=TIMESTAMP}
    where auto_id = #{autoId,jdbcType=INTEGER}
  </update>
</mapper>