/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTUpdParties implements Serializable { private Integer id;
/*    */   private String name;
/*    */   private Integer type;
/*    */   
/*  6 */   public void setId(Integer id) { this.id = id; } private Integer enable; private String countryCode; private static final long serialVersionUID = 1L; public void setName(String name) { this.name = name; } public void setType(Integer type) { this.type = type; } public void setEnable(Integer enable) { this.enable = enable; } public void setCountryCode(String countryCode) { this.countryCode = countryCode; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdParties)) return false;  com.lenovo.iqs.entity.IbTUpdParties other = (com.lenovo.iqs.entity.IbTUpdParties)o; if (!other.canEqual(this)) return false;  Object this$id = getId(), other$id = other.getId(); if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id)) return false;  Object this$name = getName(), other$name = other.getName(); if ((this$name == null) ? (other$name != null) : !this$name.equals(other$name)) return false;  Object this$type = getType(), other$type = other.getType(); if ((this$type == null) ? (other$type != null) : !this$type.equals(other$type)) return false;  Object this$enable = getEnable(), other$enable = other.getEnable(); if ((this$enable == null) ? (other$enable != null) : !this$enable.equals(other$enable)) return false;  Object this$countryCode = getCountryCode(), other$countryCode = other.getCountryCode(); return !((this$countryCode == null) ? (other$countryCode != null) : !this$countryCode.equals(other$countryCode)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdParties; } public int hashCode() { int PRIME = 59; result = 1; Object $id = getId(); result = result * 59 + (($id == null) ? 43 : $id.hashCode()); Object $name = getName(); result = result * 59 + (($name == null) ? 43 : $name.hashCode()); Object $type = getType(); result = result * 59 + (($type == null) ? 43 : $type.hashCode()); Object $enable = getEnable(); result = result * 59 + (($enable == null) ? 43 : $enable.hashCode()); Object $countryCode = getCountryCode(); return result * 59 + (($countryCode == null) ? 43 : $countryCode.hashCode()); } public String toString() { return "IbTUpdParties(id=" + getId() + ", name=" + getName() + ", type=" + getType() + ", enable=" + getEnable() + ", countryCode=" + getCountryCode() + ")"; }
/*    */    public Integer getId() {
/*  8 */     return this.id;
/*    */   } public String getName() {
/* 10 */     return this.name;
/*    */   } public Integer getType() {
/* 12 */     return this.type;
/*    */   } public Integer getEnable() {
/* 14 */     return this.enable;
/*    */   } public String getCountryCode() {
/* 16 */     return this.countryCode;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdParties.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */