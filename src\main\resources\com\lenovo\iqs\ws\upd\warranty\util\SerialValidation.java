/*     */ package WEB-INF.classes.com.lenovo.iqs.ws.upd.warranty.util;
/*     */ 
/*     */ import com.lenovo.iqs.ws.upd.warranty.util.MeidUtils;
/*     */ import org.apache.log4j.Logger;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SerialValidation
/*     */ {
/*  21 */   private static Logger log = Logger.getLogger(com.lenovo.iqs.ws.upd.warranty.util.SerialValidation.class);
/*  22 */   private static String IMEI = "IMEI";
/*  23 */   private static String MEID = "MEID";
/*  24 */   private static String ESN = "ESN";
/*  25 */   private static String MSN = "MSN";
/*  26 */   private static String UID = "UID";
/*  27 */   private static String RSN = "RSN";
/*  28 */   private static String TWOWAYRADIO = "2WAYRADIO";
/*  29 */   private static String LSA = "LSA";
/*  30 */   private static String HSN = "HSN";
/*  31 */   private static String IDEN = "IDEN";
/*  32 */   private static String TRACK_ID = "TRACK_ID";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static int hexValue(char ch) {
/*  38 */     switch (ch) {
/*     */       case '0':
/*  40 */         return 0;
/*     */       case '1':
/*  42 */         return 1;
/*     */       case '2':
/*  44 */         return 2;
/*     */       case '3':
/*  46 */         return 3;
/*     */       case '4':
/*  48 */         return 4;
/*     */       case '5':
/*  50 */         return 5;
/*     */       case '6':
/*  52 */         return 6;
/*     */       case '7':
/*  54 */         return 7;
/*     */       case '8':
/*  56 */         return 8;
/*     */       case '9':
/*  58 */         return 9;
/*     */       case 'A':
/*  60 */         return 10;
/*     */       case 'B':
/*  62 */         return 11;
/*     */       case 'C':
/*  64 */         return 12;
/*     */       case 'D':
/*  66 */         return 13;
/*     */       case 'E':
/*  68 */         return 14;
/*     */       case 'F':
/*  70 */         return 15;
/*     */     } 
/*  72 */     return -1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static int decValue(char ch) {
/*  80 */     switch (ch) {
/*     */       case '0':
/*  82 */         return 0;
/*     */       case '1':
/*  84 */         return 1;
/*     */       case '2':
/*  86 */         return 2;
/*     */       case '3':
/*  88 */         return 3;
/*     */       case '4':
/*  90 */         return 4;
/*     */       case '5':
/*  92 */         return 5;
/*     */       case '6':
/*  94 */         return 6;
/*     */       case '7':
/*  96 */         return 7;
/*     */       case '8':
/*  98 */         return 8;
/*     */       case '9':
/* 100 */         return 9;
/*     */     } 
/* 102 */     return -1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static int base29Value(char ch) {
/* 110 */     switch (ch) {
/*     */       case '0':
/* 112 */         return 0;
/*     */       case '1':
/* 114 */         return 1;
/*     */       case 'A':
/* 116 */         return 2;
/*     */       case 'E':
/* 118 */         return 3;
/*     */       case 'I':
/* 120 */         return 4;
/*     */       case 'O':
/* 122 */         return 5;
/*     */       case 'U':
/* 124 */         return 6;
/*     */     } 
/* 126 */     return -1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static int base28Value(char ch) {
/* 135 */     switch (ch) {
/*     */       case '2':
/* 137 */         return 0;
/*     */       case '3':
/* 139 */         return 1;
/*     */       case '4':
/* 141 */         return 2;
/*     */       case '5':
/* 143 */         return 3;
/*     */       case '6':
/* 145 */         return 4;
/*     */       case '7':
/* 147 */         return 5;
/*     */       case '8':
/* 149 */         return 6;
/*     */       case '9':
/* 151 */         return 7;
/*     */       case 'B':
/* 153 */         return 8;
/*     */       case 'C':
/* 155 */         return 9;
/*     */       case 'D':
/* 157 */         return 10;
/*     */       case 'F':
/* 159 */         return 11;
/*     */       case 'G':
/* 161 */         return 12;
/*     */       case 'H':
/* 163 */         return 13;
/*     */       case 'J':
/* 165 */         return 14;
/*     */       case 'K':
/* 167 */         return 15;
/*     */       case 'L':
/* 169 */         return 16;
/*     */       case 'M':
/* 171 */         return 17;
/*     */       case 'N':
/* 173 */         return 18;
/*     */       case 'P':
/* 175 */         return 19;
/*     */       case 'Q':
/* 177 */         return 20;
/*     */       case 'R':
/* 179 */         return 21;
/*     */       case 'S':
/* 181 */         return 22;
/*     */       case 'T':
/* 183 */         return 23;
/*     */       case 'V':
/* 185 */         return 24;
/*     */       case 'W':
/* 187 */         return 25;
/*     */       case 'X':
/* 189 */         return 26;
/*     */       case 'Z':
/* 191 */         return 27;
/*     */     } 
/* 193 */     return -1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static int alphaNumValue(char ch) {
/* 200 */     switch (ch) {
/*     */       case '0':
/* 202 */         return 0;
/*     */       case '1':
/* 204 */         return 1;
/*     */       case '2':
/* 206 */         return 2;
/*     */       case '3':
/* 208 */         return 3;
/*     */       case '4':
/* 210 */         return 4;
/*     */       case '5':
/* 212 */         return 5;
/*     */       case '6':
/* 214 */         return 6;
/*     */       case '7':
/* 216 */         return 7;
/*     */       case '8':
/* 218 */         return 8;
/*     */       case '9':
/* 220 */         return 9;
/*     */       case 'A':
/* 222 */         return 10;
/*     */       case 'B':
/* 224 */         return 11;
/*     */       case 'C':
/* 226 */         return 12;
/*     */       case 'D':
/* 228 */         return 13;
/*     */       case 'E':
/* 230 */         return 14;
/*     */       case 'F':
/* 232 */         return 15;
/*     */       case 'G':
/* 234 */         return 16;
/*     */       case 'H':
/* 236 */         return 17;
/*     */       case 'I':
/* 238 */         return 18;
/*     */       case 'J':
/* 240 */         return 19;
/*     */       case 'K':
/* 242 */         return 20;
/*     */       case 'L':
/* 244 */         return 21;
/*     */       case 'M':
/* 246 */         return 22;
/*     */       case 'N':
/* 248 */         return 23;
/*     */       case 'O':
/* 250 */         return 24;
/*     */       case 'P':
/* 252 */         return 25;
/*     */       case 'Q':
/* 254 */         return 26;
/*     */       case 'R':
/* 256 */         return 27;
/*     */       case 'S':
/* 258 */         return 28;
/*     */       case 'T':
/* 260 */         return 29;
/*     */       case 'U':
/* 262 */         return 30;
/*     */       case 'V':
/* 264 */         return 31;
/*     */       case 'W':
/* 266 */         return 32;
/*     */       case 'X':
/* 268 */         return 33;
/*     */       case 'Y':
/* 270 */         return 34;
/*     */       case 'Z':
/* 272 */         return 35;
/*     */     } 
/* 274 */     return -1;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static boolean isHex(String hex) {
/* 280 */     for (int i = 0; i < hex.length(); i++) {
/*     */       
/* 282 */       if (hexValue(hex.charAt(i)) == -1)
/*     */       {
/* 284 */         return false;
/*     */       }
/*     */     } 
/* 287 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   private static boolean isDec(String dec) {
/* 292 */     for (int i = 0; i < dec.length(); i++) {
/*     */       
/* 294 */       if (decValue(dec.charAt(i)) == -1)
/*     */       {
/* 296 */         return false;
/*     */       }
/*     */     } 
/* 299 */     return true;
/*     */   }
/*     */ 
/*     */   
/*     */   private static boolean isAlphaNum(String sno) {
/* 304 */     for (int i = 0; i < sno.length(); i++) {
/*     */       
/* 306 */       if (alphaNumValue(sno.charAt(i)) == -1)
/*     */       {
/* 308 */         return false;
/*     */       }
/*     */     } 
/* 311 */     return true;
/*     */   }
/*     */   
/*     */   public static boolean isSerialLengthValid(String serial_no_type, String serial_no) {
/* 315 */     int length = serial_no.length();
/*     */ 
/*     */     
/* 318 */     if (serial_no_type.equals(IMEI)) {
/*     */       
/* 320 */       if (length == 15) {
/* 321 */         return true;
/*     */       }
/*     */     }
/* 324 */     else if (serial_no_type.equals(MEID)) {
/*     */       
/* 326 */       if (length == 15 || length == 14 || length == 18) {
/* 327 */         return true;
/*     */       }
/* 329 */     } else if (serial_no_type.equalsIgnoreCase(ESN)) {
/*     */       
/* 331 */       if (length == 8) {
/* 332 */         return true;
/*     */       }
/* 334 */     } else if (serial_no_type.equals(MSN)) {
/* 335 */       if (length == 10 || length == 11 || serial_no.endsWith("SCR")) {
/* 336 */         return true;
/*     */       }
/*     */     }
/* 339 */     else if (serial_no_type.equals(TWOWAYRADIO)) {
/*     */       
/* 341 */       if (length == 10) {
/* 342 */         return true;
/*     */       }
/* 344 */     } else if (serial_no_type.equals(LSA)) {
/*     */       
/* 346 */       if (length == 9 || length == 11) {
/* 347 */         return true;
/*     */       }
/* 349 */     } else if (serial_no_type.equals(HSN)) {
/*     */       
/* 351 */       if (length == 11 || length == 8)
/* 352 */         return true; 
/*     */     } else {
/* 354 */       if (serial_no_type.equals(IDEN))
/*     */       {
/*     */         
/* 357 */         return true;
/*     */       }
/*     */       
/* 360 */       if (serial_no_type.equals(UID)) {
/* 361 */         if (length == 32 || length == 24 || length == 10) {
/* 362 */           return true;
/*     */         }
/* 364 */       } else if (serial_no_type.equals(TRACK_ID)) {
/* 365 */         return true;
/*     */       } 
/*     */     } 
/* 368 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isSerialFormatValid(String serial_no_type, String serial_no) {
/* 375 */     if (serial_no_type.equals(IMEI))
/* 376 */       return isDec(serial_no); 
/* 377 */     if (serial_no_type.equals(ESN))
/* 378 */       return isHex(serial_no); 
/* 379 */     if (serial_no_type.equals(MEID)) {
/*     */       
/*     */       try {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 402 */         String cvt_serial_no = MeidUtils.validateMEID(serial_no);
/* 403 */         return isHex(cvt_serial_no);
/* 404 */       } catch (Exception e) {
/* 405 */         return false;
/*     */       } 
/*     */     }
/*     */     
/* 409 */     if (serial_no_type.equals(MSN))
/*     */     {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 429 */       return true; } 
/* 430 */     if (serial_no_type.equals(UID))
/*     */     {
/* 432 */       return true;
/*     */     }
/* 434 */     if (serial_no_type.equals(HSN)) {
/*     */ 
/*     */       
/* 437 */       if (serial_no.length() != 8) {
/* 438 */         for (int i = 0; i < serial_no.length(); i++) {
/*     */           
/* 440 */           log.info("In HSN charValidation at:" + i + " : char - " + serial_no.charAt(i) + " and Boolean Value = " + base28Value(serial_no.charAt(i)));
/*     */           
/* 442 */           if (base28Value(serial_no.charAt(i)) == -1) {
/* 443 */             return false;
/*     */           }
/*     */         } 
/*     */       }
/*     */       
/* 448 */       return true;
/*     */     } 
/*     */ 
/*     */     
/* 452 */     if (serial_no_type.equals(TWOWAYRADIO)) {
/* 453 */       return isAlphaNum(serial_no);
/*     */     }
/* 455 */     if (serial_no_type.equals(RSN))
/* 456 */       return true; 
/* 457 */     if (serial_no_type.equals(IDEN)) {
/* 458 */       return true;
/*     */     }
/* 460 */     if (serial_no_type.equals(TRACK_ID)) {
/* 461 */       return true;
/*     */     }
/*     */     
/* 464 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   public static void main(String[] args) {
/* 469 */     String stype = args[0];
/* 470 */     String sno = args[1];
/* 471 */     System.out.println("Type         :" + stype);
/* 472 */     System.out.println("SNo          :" + sno);
/* 473 */     System.out.println("Length Result:" + isSerialLengthValid(stype, sno));
/* 474 */     System.out.println("Format Result:" + isSerialFormatValid(stype, sno));
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\w\\upd\warrant\\util\SerialValidation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */