/*     */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*     */ public class IbTUpdR12Outbound implements Serializable { private String serialNo; private String serialNumberType; private String factoryCode; private Date generationDate; private String apc; private String transceiverModel; private String customerModel; private String marketName; private String itemCode; private String warrantyCode; private Date shipDate; private String shipToCustomerNumber; private String shipToCustomerAddressId; private String shipToCustomerName; private String shipToCity; private String shipToCountry; private String soldToCustomerNumber; private String soldToCustomerName; private Date soldDate; private String trackId; private String taNumber; private String cartonId; private String poNumber; private String soNumber; private String palletId; private String primaryUnlockCode; private String primecoUnlockCode; private String verizonUnlockCode; private String secondaryUnlockCode; private String servicePasscode; private String lock4; private String lock5; private String aKeyRandom; private String aKeyZero; private String msn; private String bt; private String wlan; private String baseProcessorId; private String fasttId; private String locationType; private String packingList; private String fabDate; private String softwareVersion; private String organizationCode; private String flexOption; private String iccId; private String soLineNumber; private String directShipRegionCode; private String directShipSoNumber; private String directShipPoNumber; private String directShipCustomerNumber;
/*     */   private String directShipShipToAddressId;
/*     */   private String directShipCustomerCountry;
/*     */   private String directShipCustomerName;
/*     */   
/*   7 */   public void setSerialNo(String serialNo) { this.serialNo = serialNo; } private String directShipBillToId; private String shipmentNumber; private String wipDj; private String deleteFlag; private String ultimateDestinationCountry; private String cssn; private String min; private String billToId; private String currFlexVer; private String currFlashName; private String currPriVer; private String langPkgId; private String kjavaVer; private String bootloaderVer; private String hardwareVer; private String fotaEnabled; private String dualSerialNo; private String dualSnType; private String popInSysdate; private String popDate; private String popIdentifier; private String lastRepairDate; private String repairCount; private String wimaxMacAddr; private String hsn; private String dsnmuc1; private String dsnmuc2; private String dsnmuc3; private String dsnotuc; private String dsnservicepass; private String dsnlock4; private String dsnlock5; private String dsnAkeyRandom; private String dsnAkeyZero; private String dsnAkey2Type; private String dsnAkey2; private String wlan2; private String wlan3; private String wlan4; private String triSerialNo; private String triSerialNoType; private String tsnmuc1; private String tsnmuc2; private String tsnmuc3; private String tsnotuc; private String tsnservicepass; private String tsnlock4; private String tsnlock5; private String tsnAkeyRandom; private String tsnAkeyZero; private String tsnAkey2Type; private String tsnAkey2; private Date createTime; private String batchNumber; private static final long serialVersionUID = 1L; public void setSerialNumberType(String serialNumberType) { this.serialNumberType = serialNumberType; } public void setFactoryCode(String factoryCode) { this.factoryCode = factoryCode; } public void setGenerationDate(Date generationDate) { this.generationDate = generationDate; } public void setApc(String apc) { this.apc = apc; } public void setTransceiverModel(String transceiverModel) { this.transceiverModel = transceiverModel; } public void setCustomerModel(String customerModel) { this.customerModel = customerModel; } public void setMarketName(String marketName) { this.marketName = marketName; } public void setItemCode(String itemCode) { this.itemCode = itemCode; } public void setWarrantyCode(String warrantyCode) { this.warrantyCode = warrantyCode; } public void setShipDate(Date shipDate) { this.shipDate = shipDate; } public void setShipToCustomerNumber(String shipToCustomerNumber) { this.shipToCustomerNumber = shipToCustomerNumber; } public void setShipToCustomerAddressId(String shipToCustomerAddressId) { this.shipToCustomerAddressId = shipToCustomerAddressId; } public void setShipToCustomerName(String shipToCustomerName) { this.shipToCustomerName = shipToCustomerName; } public void setShipToCity(String shipToCity) { this.shipToCity = shipToCity; } public void setShipToCountry(String shipToCountry) { this.shipToCountry = shipToCountry; } public void setSoldToCustomerNumber(String soldToCustomerNumber) { this.soldToCustomerNumber = soldToCustomerNumber; } public void setSoldToCustomerName(String soldToCustomerName) { this.soldToCustomerName = soldToCustomerName; } public void setSoldDate(Date soldDate) { this.soldDate = soldDate; } public void setTrackId(String trackId) { this.trackId = trackId; } public void setTaNumber(String taNumber) { this.taNumber = taNumber; } public void setCartonId(String cartonId) { this.cartonId = cartonId; } public void setPoNumber(String poNumber) { this.poNumber = poNumber; } public void setSoNumber(String soNumber) { this.soNumber = soNumber; } public void setPalletId(String palletId) { this.palletId = palletId; } public void setPrimaryUnlockCode(String primaryUnlockCode) { this.primaryUnlockCode = primaryUnlockCode; } public void setPrimecoUnlockCode(String primecoUnlockCode) { this.primecoUnlockCode = primecoUnlockCode; } public void setVerizonUnlockCode(String verizonUnlockCode) { this.verizonUnlockCode = verizonUnlockCode; } public void setSecondaryUnlockCode(String secondaryUnlockCode) { this.secondaryUnlockCode = secondaryUnlockCode; } public void setServicePasscode(String servicePasscode) { this.servicePasscode = servicePasscode; } public void setLock4(String lock4) { this.lock4 = lock4; } public void setLock5(String lock5) { this.lock5 = lock5; } public void setAKeyRandom(String aKeyRandom) { this.aKeyRandom = aKeyRandom; } public void setAKeyZero(String aKeyZero) { this.aKeyZero = aKeyZero; } public void setMsn(String msn) { this.msn = msn; } public void setBt(String bt) { this.bt = bt; } public void setWlan(String wlan) { this.wlan = wlan; } public void setBaseProcessorId(String baseProcessorId) { this.baseProcessorId = baseProcessorId; } public void setFasttId(String fasttId) { this.fasttId = fasttId; } public void setLocationType(String locationType) { this.locationType = locationType; } public void setPackingList(String packingList) { this.packingList = packingList; } public void setFabDate(String fabDate) { this.fabDate = fabDate; } public void setSoftwareVersion(String softwareVersion) { this.softwareVersion = softwareVersion; } public void setOrganizationCode(String organizationCode) { this.organizationCode = organizationCode; } public void setFlexOption(String flexOption) { this.flexOption = flexOption; } public void setIccId(String iccId) { this.iccId = iccId; } public void setSoLineNumber(String soLineNumber) { this.soLineNumber = soLineNumber; } public void setDirectShipRegionCode(String directShipRegionCode) { this.directShipRegionCode = directShipRegionCode; } public void setDirectShipSoNumber(String directShipSoNumber) { this.directShipSoNumber = directShipSoNumber; } public void setDirectShipPoNumber(String directShipPoNumber) { this.directShipPoNumber = directShipPoNumber; } public void setDirectShipCustomerNumber(String directShipCustomerNumber) { this.directShipCustomerNumber = directShipCustomerNumber; } public void setDirectShipShipToAddressId(String directShipShipToAddressId) { this.directShipShipToAddressId = directShipShipToAddressId; } public void setDirectShipCustomerCountry(String directShipCustomerCountry) { this.directShipCustomerCountry = directShipCustomerCountry; } public void setDirectShipCustomerName(String directShipCustomerName) { this.directShipCustomerName = directShipCustomerName; } public void setDirectShipBillToId(String directShipBillToId) { this.directShipBillToId = directShipBillToId; } public void setShipmentNumber(String shipmentNumber) { this.shipmentNumber = shipmentNumber; } public void setWipDj(String wipDj) { this.wipDj = wipDj; } public void setDeleteFlag(String deleteFlag) { this.deleteFlag = deleteFlag; } public void setUltimateDestinationCountry(String ultimateDestinationCountry) { this.ultimateDestinationCountry = ultimateDestinationCountry; } public void setCssn(String cssn) { this.cssn = cssn; } public void setMin(String min) { this.min = min; } public void setBillToId(String billToId) { this.billToId = billToId; } public void setCurrFlexVer(String currFlexVer) { this.currFlexVer = currFlexVer; } public void setCurrFlashName(String currFlashName) { this.currFlashName = currFlashName; } public void setCurrPriVer(String currPriVer) { this.currPriVer = currPriVer; } public void setLangPkgId(String langPkgId) { this.langPkgId = langPkgId; } public void setKjavaVer(String kjavaVer) { this.kjavaVer = kjavaVer; } public void setBootloaderVer(String bootloaderVer) { this.bootloaderVer = bootloaderVer; } public void setHardwareVer(String hardwareVer) { this.hardwareVer = hardwareVer; } public void setFotaEnabled(String fotaEnabled) { this.fotaEnabled = fotaEnabled; } public void setDualSerialNo(String dualSerialNo) { this.dualSerialNo = dualSerialNo; } public void setDualSnType(String dualSnType) { this.dualSnType = dualSnType; } public void setPopInSysdate(String popInSysdate) { this.popInSysdate = popInSysdate; } public void setPopDate(String popDate) { this.popDate = popDate; } public void setPopIdentifier(String popIdentifier) { this.popIdentifier = popIdentifier; } public void setLastRepairDate(String lastRepairDate) { this.lastRepairDate = lastRepairDate; } public void setRepairCount(String repairCount) { this.repairCount = repairCount; } public void setWimaxMacAddr(String wimaxMacAddr) { this.wimaxMacAddr = wimaxMacAddr; } public void setHsn(String hsn) { this.hsn = hsn; } public void setDsnmuc1(String dsnmuc1) { this.dsnmuc1 = dsnmuc1; } public void setDsnmuc2(String dsnmuc2) { this.dsnmuc2 = dsnmuc2; } public void setDsnmuc3(String dsnmuc3) { this.dsnmuc3 = dsnmuc3; } public void setDsnotuc(String dsnotuc) { this.dsnotuc = dsnotuc; } public void setDsnservicepass(String dsnservicepass) { this.dsnservicepass = dsnservicepass; } public void setDsnlock4(String dsnlock4) { this.dsnlock4 = dsnlock4; } public void setDsnlock5(String dsnlock5) { this.dsnlock5 = dsnlock5; } public void setDsnAkeyRandom(String dsnAkeyRandom) { this.dsnAkeyRandom = dsnAkeyRandom; } public void setDsnAkeyZero(String dsnAkeyZero) { this.dsnAkeyZero = dsnAkeyZero; } public void setDsnAkey2Type(String dsnAkey2Type) { this.dsnAkey2Type = dsnAkey2Type; } public void setDsnAkey2(String dsnAkey2) { this.dsnAkey2 = dsnAkey2; } public void setWlan2(String wlan2) { this.wlan2 = wlan2; } public void setWlan3(String wlan3) { this.wlan3 = wlan3; } public void setWlan4(String wlan4) { this.wlan4 = wlan4; } public void setTriSerialNo(String triSerialNo) { this.triSerialNo = triSerialNo; } public void setTriSerialNoType(String triSerialNoType) { this.triSerialNoType = triSerialNoType; } public void setTsnmuc1(String tsnmuc1) { this.tsnmuc1 = tsnmuc1; } public void setTsnmuc2(String tsnmuc2) { this.tsnmuc2 = tsnmuc2; } public void setTsnmuc3(String tsnmuc3) { this.tsnmuc3 = tsnmuc3; } public void setTsnotuc(String tsnotuc) { this.tsnotuc = tsnotuc; } public void setTsnservicepass(String tsnservicepass) { this.tsnservicepass = tsnservicepass; } public void setTsnlock4(String tsnlock4) { this.tsnlock4 = tsnlock4; } public void setTsnlock5(String tsnlock5) { this.tsnlock5 = tsnlock5; } public void setTsnAkeyRandom(String tsnAkeyRandom) { this.tsnAkeyRandom = tsnAkeyRandom; } public void setTsnAkeyZero(String tsnAkeyZero) { this.tsnAkeyZero = tsnAkeyZero; } public void setTsnAkey2Type(String tsnAkey2Type) { this.tsnAkey2Type = tsnAkey2Type; } public void setTsnAkey2(String tsnAkey2) { this.tsnAkey2 = tsnAkey2; } public void setCreateTime(Date createTime) { this.createTime = createTime; } public void setBatchNumber(String batchNumber) { this.batchNumber = batchNumber; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdR12Outbound)) return false;  com.lenovo.iqs.entity.IbTUpdR12Outbound other = (com.lenovo.iqs.entity.IbTUpdR12Outbound)o; if (!other.canEqual(this)) return false;  Object this$serialNo = getSerialNo(), other$serialNo = other.getSerialNo(); if ((this$serialNo == null) ? (other$serialNo != null) : !this$serialNo.equals(other$serialNo)) return false;  Object this$serialNumberType = getSerialNumberType(), other$serialNumberType = other.getSerialNumberType(); if ((this$serialNumberType == null) ? (other$serialNumberType != null) : !this$serialNumberType.equals(other$serialNumberType)) return false;  Object this$factoryCode = getFactoryCode(), other$factoryCode = other.getFactoryCode(); if ((this$factoryCode == null) ? (other$factoryCode != null) : !this$factoryCode.equals(other$factoryCode)) return false;  Object this$generationDate = getGenerationDate(), other$generationDate = other.getGenerationDate(); if ((this$generationDate == null) ? (other$generationDate != null) : !this$generationDate.equals(other$generationDate)) return false;  Object this$apc = getApc(), other$apc = other.getApc(); if ((this$apc == null) ? (other$apc != null) : !this$apc.equals(other$apc)) return false;  Object this$transceiverModel = getTransceiverModel(), other$transceiverModel = other.getTransceiverModel(); if ((this$transceiverModel == null) ? (other$transceiverModel != null) : !this$transceiverModel.equals(other$transceiverModel)) return false;  Object this$customerModel = getCustomerModel(), other$customerModel = other.getCustomerModel(); if ((this$customerModel == null) ? (other$customerModel != null) : !this$customerModel.equals(other$customerModel)) return false;  Object this$marketName = getMarketName(), other$marketName = other.getMarketName(); if ((this$marketName == null) ? (other$marketName != null) : !this$marketName.equals(other$marketName)) return false;  Object this$itemCode = getItemCode(), other$itemCode = other.getItemCode(); if ((this$itemCode == null) ? (other$itemCode != null) : !this$itemCode.equals(other$itemCode)) return false;  Object this$warrantyCode = getWarrantyCode(), other$warrantyCode = other.getWarrantyCode(); if ((this$warrantyCode == null) ? (other$warrantyCode != null) : !this$warrantyCode.equals(other$warrantyCode)) return false;  Object this$shipDate = getShipDate(), other$shipDate = other.getShipDate(); if ((this$shipDate == null) ? (other$shipDate != null) : !this$shipDate.equals(other$shipDate)) return false;  Object this$shipToCustomerNumber = getShipToCustomerNumber(), other$shipToCustomerNumber = other.getShipToCustomerNumber(); if ((this$shipToCustomerNumber == null) ? (other$shipToCustomerNumber != null) : !this$shipToCustomerNumber.equals(other$shipToCustomerNumber)) return false;  Object this$shipToCustomerAddressId = getShipToCustomerAddressId(), other$shipToCustomerAddressId = other.getShipToCustomerAddressId(); if ((this$shipToCustomerAddressId == null) ? (other$shipToCustomerAddressId != null) : !this$shipToCustomerAddressId.equals(other$shipToCustomerAddressId)) return false;  Object this$shipToCustomerName = getShipToCustomerName(), other$shipToCustomerName = other.getShipToCustomerName(); if ((this$shipToCustomerName == null) ? (other$shipToCustomerName != null) : !this$shipToCustomerName.equals(other$shipToCustomerName)) return false;  Object this$shipToCity = getShipToCity(), other$shipToCity = other.getShipToCity(); if ((this$shipToCity == null) ? (other$shipToCity != null) : !this$shipToCity.equals(other$shipToCity)) return false;  Object this$shipToCountry = getShipToCountry(), other$shipToCountry = other.getShipToCountry(); if ((this$shipToCountry == null) ? (other$shipToCountry != null) : !this$shipToCountry.equals(other$shipToCountry)) return false;  Object this$soldToCustomerNumber = getSoldToCustomerNumber(), other$soldToCustomerNumber = other.getSoldToCustomerNumber(); if ((this$soldToCustomerNumber == null) ? (other$soldToCustomerNumber != null) : !this$soldToCustomerNumber.equals(other$soldToCustomerNumber)) return false;  Object this$soldToCustomerName = getSoldToCustomerName(), other$soldToCustomerName = other.getSoldToCustomerName(); if ((this$soldToCustomerName == null) ? (other$soldToCustomerName != null) : !this$soldToCustomerName.equals(other$soldToCustomerName)) return false;  Object this$soldDate = getSoldDate(), other$soldDate = other.getSoldDate(); if ((this$soldDate == null) ? (other$soldDate != null) : !this$soldDate.equals(other$soldDate)) return false;  Object this$trackId = getTrackId(), other$trackId = other.getTrackId(); if ((this$trackId == null) ? (other$trackId != null) : !this$trackId.equals(other$trackId)) return false;  Object this$taNumber = getTaNumber(), other$taNumber = other.getTaNumber(); if ((this$taNumber == null) ? (other$taNumber != null) : !this$taNumber.equals(other$taNumber)) return false;  Object this$cartonId = getCartonId(), other$cartonId = other.getCartonId(); if ((this$cartonId == null) ? (other$cartonId != null) : !this$cartonId.equals(other$cartonId)) return false;  Object this$poNumber = getPoNumber(), other$poNumber = other.getPoNumber(); if ((this$poNumber == null) ? (other$poNumber != null) : !this$poNumber.equals(other$poNumber)) return false;  Object this$soNumber = getSoNumber(), other$soNumber = other.getSoNumber(); if ((this$soNumber == null) ? (other$soNumber != null) : !this$soNumber.equals(other$soNumber)) return false;  Object this$palletId = getPalletId(), other$palletId = other.getPalletId(); if ((this$palletId == null) ? (other$palletId != null) : !this$palletId.equals(other$palletId)) return false;  Object this$primaryUnlockCode = getPrimaryUnlockCode(), other$primaryUnlockCode = other.getPrimaryUnlockCode(); if ((this$primaryUnlockCode == null) ? (other$primaryUnlockCode != null) : !this$primaryUnlockCode.equals(other$primaryUnlockCode)) return false;  Object this$primecoUnlockCode = getPrimecoUnlockCode(), other$primecoUnlockCode = other.getPrimecoUnlockCode(); if ((this$primecoUnlockCode == null) ? (other$primecoUnlockCode != null) : !this$primecoUnlockCode.equals(other$primecoUnlockCode)) return false;  Object this$verizonUnlockCode = getVerizonUnlockCode(), other$verizonUnlockCode = other.getVerizonUnlockCode(); if ((this$verizonUnlockCode == null) ? (other$verizonUnlockCode != null) : !this$verizonUnlockCode.equals(other$verizonUnlockCode)) return false;  Object this$secondaryUnlockCode = getSecondaryUnlockCode(), other$secondaryUnlockCode = other.getSecondaryUnlockCode(); if ((this$secondaryUnlockCode == null) ? (other$secondaryUnlockCode != null) : !this$secondaryUnlockCode.equals(other$secondaryUnlockCode)) return false;  Object this$servicePasscode = getServicePasscode(), other$servicePasscode = other.getServicePasscode(); if ((this$servicePasscode == null) ? (other$servicePasscode != null) : !this$servicePasscode.equals(other$servicePasscode)) return false;  Object this$lock4 = getLock4(), other$lock4 = other.getLock4(); if ((this$lock4 == null) ? (other$lock4 != null) : !this$lock4.equals(other$lock4)) return false;  Object this$lock5 = getLock5(), other$lock5 = other.getLock5(); if ((this$lock5 == null) ? (other$lock5 != null) : !this$lock5.equals(other$lock5)) return false;  Object this$aKeyRandom = getAKeyRandom(), other$aKeyRandom = other.getAKeyRandom(); if ((this$aKeyRandom == null) ? (other$aKeyRandom != null) : !this$aKeyRandom.equals(other$aKeyRandom)) return false;  Object this$aKeyZero = getAKeyZero(), other$aKeyZero = other.getAKeyZero(); if ((this$aKeyZero == null) ? (other$aKeyZero != null) : !this$aKeyZero.equals(other$aKeyZero)) return false;  Object this$msn = getMsn(), other$msn = other.getMsn(); if ((this$msn == null) ? (other$msn != null) : !this$msn.equals(other$msn)) return false;  Object this$bt = getBt(), other$bt = other.getBt(); if ((this$bt == null) ? (other$bt != null) : !this$bt.equals(other$bt)) return false;  Object this$wlan = getWlan(), other$wlan = other.getWlan(); if ((this$wlan == null) ? (other$wlan != null) : !this$wlan.equals(other$wlan)) return false;  Object this$baseProcessorId = getBaseProcessorId(), other$baseProcessorId = other.getBaseProcessorId(); if ((this$baseProcessorId == null) ? (other$baseProcessorId != null) : !this$baseProcessorId.equals(other$baseProcessorId)) return false;  Object this$fasttId = getFasttId(), other$fasttId = other.getFasttId(); if ((this$fasttId == null) ? (other$fasttId != null) : !this$fasttId.equals(other$fasttId)) return false;  Object this$locationType = getLocationType(), other$locationType = other.getLocationType(); if ((this$locationType == null) ? (other$locationType != null) : !this$locationType.equals(other$locationType)) return false;  Object this$packingList = getPackingList(), other$packingList = other.getPackingList(); if ((this$packingList == null) ? (other$packingList != null) : !this$packingList.equals(other$packingList)) return false;  Object this$fabDate = getFabDate(), other$fabDate = other.getFabDate(); if ((this$fabDate == null) ? (other$fabDate != null) : !this$fabDate.equals(other$fabDate)) return false;  Object this$softwareVersion = getSoftwareVersion(), other$softwareVersion = other.getSoftwareVersion(); if ((this$softwareVersion == null) ? (other$softwareVersion != null) : !this$softwareVersion.equals(other$softwareVersion)) return false;  Object this$organizationCode = getOrganizationCode(), other$organizationCode = other.getOrganizationCode(); if ((this$organizationCode == null) ? (other$organizationCode != null) : !this$organizationCode.equals(other$organizationCode)) return false;  Object this$flexOption = getFlexOption(), other$flexOption = other.getFlexOption(); if ((this$flexOption == null) ? (other$flexOption != null) : !this$flexOption.equals(other$flexOption)) return false;  Object this$iccId = getIccId(), other$iccId = other.getIccId(); if ((this$iccId == null) ? (other$iccId != null) : !this$iccId.equals(other$iccId)) return false;  Object this$soLineNumber = getSoLineNumber(), other$soLineNumber = other.getSoLineNumber(); if ((this$soLineNumber == null) ? (other$soLineNumber != null) : !this$soLineNumber.equals(other$soLineNumber)) return false;  Object this$directShipRegionCode = getDirectShipRegionCode(), other$directShipRegionCode = other.getDirectShipRegionCode(); if ((this$directShipRegionCode == null) ? (other$directShipRegionCode != null) : !this$directShipRegionCode.equals(other$directShipRegionCode)) return false;  Object this$directShipSoNumber = getDirectShipSoNumber(), other$directShipSoNumber = other.getDirectShipSoNumber(); if ((this$directShipSoNumber == null) ? (other$directShipSoNumber != null) : !this$directShipSoNumber.equals(other$directShipSoNumber)) return false;  Object this$directShipPoNumber = getDirectShipPoNumber(), other$directShipPoNumber = other.getDirectShipPoNumber(); if ((this$directShipPoNumber == null) ? (other$directShipPoNumber != null) : !this$directShipPoNumber.equals(other$directShipPoNumber)) return false;  Object this$directShipCustomerNumber = getDirectShipCustomerNumber(), other$directShipCustomerNumber = other.getDirectShipCustomerNumber(); if ((this$directShipCustomerNumber == null) ? (other$directShipCustomerNumber != null) : !this$directShipCustomerNumber.equals(other$directShipCustomerNumber)) return false;  Object this$directShipShipToAddressId = getDirectShipShipToAddressId(), other$directShipShipToAddressId = other.getDirectShipShipToAddressId(); if ((this$directShipShipToAddressId == null) ? (other$directShipShipToAddressId != null) : !this$directShipShipToAddressId.equals(other$directShipShipToAddressId)) return false;  Object this$directShipCustomerCountry = getDirectShipCustomerCountry(), other$directShipCustomerCountry = other.getDirectShipCustomerCountry(); if ((this$directShipCustomerCountry == null) ? (other$directShipCustomerCountry != null) : !this$directShipCustomerCountry.equals(other$directShipCustomerCountry)) return false;  Object this$directShipCustomerName = getDirectShipCustomerName(), other$directShipCustomerName = other.getDirectShipCustomerName(); if ((this$directShipCustomerName == null) ? (other$directShipCustomerName != null) : !this$directShipCustomerName.equals(other$directShipCustomerName)) return false;  Object this$directShipBillToId = getDirectShipBillToId(), other$directShipBillToId = other.getDirectShipBillToId(); if ((this$directShipBillToId == null) ? (other$directShipBillToId != null) : !this$directShipBillToId.equals(other$directShipBillToId)) return false;  Object this$shipmentNumber = getShipmentNumber(), other$shipmentNumber = other.getShipmentNumber(); if ((this$shipmentNumber == null) ? (other$shipmentNumber != null) : !this$shipmentNumber.equals(other$shipmentNumber)) return false;  Object this$wipDj = getWipDj(), other$wipDj = other.getWipDj(); if ((this$wipDj == null) ? (other$wipDj != null) : !this$wipDj.equals(other$wipDj)) return false;  Object this$deleteFlag = getDeleteFlag(), other$deleteFlag = other.getDeleteFlag(); if ((this$deleteFlag == null) ? (other$deleteFlag != null) : !this$deleteFlag.equals(other$deleteFlag)) return false;  Object this$ultimateDestinationCountry = getUltimateDestinationCountry(), other$ultimateDestinationCountry = other.getUltimateDestinationCountry(); if ((this$ultimateDestinationCountry == null) ? (other$ultimateDestinationCountry != null) : !this$ultimateDestinationCountry.equals(other$ultimateDestinationCountry)) return false;  Object this$cssn = getCssn(), other$cssn = other.getCssn(); if ((this$cssn == null) ? (other$cssn != null) : !this$cssn.equals(other$cssn)) return false;  Object this$min = getMin(), other$min = other.getMin(); if ((this$min == null) ? (other$min != null) : !this$min.equals(other$min)) return false;  Object this$billToId = getBillToId(), other$billToId = other.getBillToId(); if ((this$billToId == null) ? (other$billToId != null) : !this$billToId.equals(other$billToId)) return false;  Object this$currFlexVer = getCurrFlexVer(), other$currFlexVer = other.getCurrFlexVer(); if ((this$currFlexVer == null) ? (other$currFlexVer != null) : !this$currFlexVer.equals(other$currFlexVer)) return false;  Object this$currFlashName = getCurrFlashName(), other$currFlashName = other.getCurrFlashName(); if ((this$currFlashName == null) ? (other$currFlashName != null) : !this$currFlashName.equals(other$currFlashName)) return false;  Object this$currPriVer = getCurrPriVer(), other$currPriVer = other.getCurrPriVer(); if ((this$currPriVer == null) ? (other$currPriVer != null) : !this$currPriVer.equals(other$currPriVer)) return false;  Object this$langPkgId = getLangPkgId(), other$langPkgId = other.getLangPkgId(); if ((this$langPkgId == null) ? (other$langPkgId != null) : !this$langPkgId.equals(other$langPkgId)) return false;  Object this$kjavaVer = getKjavaVer(), other$kjavaVer = other.getKjavaVer(); if ((this$kjavaVer == null) ? (other$kjavaVer != null) : !this$kjavaVer.equals(other$kjavaVer)) return false;  Object this$bootloaderVer = getBootloaderVer(), other$bootloaderVer = other.getBootloaderVer(); if ((this$bootloaderVer == null) ? (other$bootloaderVer != null) : !this$bootloaderVer.equals(other$bootloaderVer)) return false;  Object this$hardwareVer = getHardwareVer(), other$hardwareVer = other.getHardwareVer(); if ((this$hardwareVer == null) ? (other$hardwareVer != null) : !this$hardwareVer.equals(other$hardwareVer)) return false;  Object this$fotaEnabled = getFotaEnabled(), other$fotaEnabled = other.getFotaEnabled(); if ((this$fotaEnabled == null) ? (other$fotaEnabled != null) : !this$fotaEnabled.equals(other$fotaEnabled)) return false;  Object this$dualSerialNo = getDualSerialNo(), other$dualSerialNo = other.getDualSerialNo(); if ((this$dualSerialNo == null) ? (other$dualSerialNo != null) : !this$dualSerialNo.equals(other$dualSerialNo)) return false;  Object this$dualSnType = getDualSnType(), other$dualSnType = other.getDualSnType(); if ((this$dualSnType == null) ? (other$dualSnType != null) : !this$dualSnType.equals(other$dualSnType)) return false;  Object this$popInSysdate = getPopInSysdate(), other$popInSysdate = other.getPopInSysdate(); if ((this$popInSysdate == null) ? (other$popInSysdate != null) : !this$popInSysdate.equals(other$popInSysdate)) return false;  Object this$popDate = getPopDate(), other$popDate = other.getPopDate(); if ((this$popDate == null) ? (other$popDate != null) : !this$popDate.equals(other$popDate)) return false;  Object this$popIdentifier = getPopIdentifier(), other$popIdentifier = other.getPopIdentifier(); if ((this$popIdentifier == null) ? (other$popIdentifier != null) : !this$popIdentifier.equals(other$popIdentifier)) return false;  Object this$lastRepairDate = getLastRepairDate(), other$lastRepairDate = other.getLastRepairDate(); if ((this$lastRepairDate == null) ? (other$lastRepairDate != null) : !this$lastRepairDate.equals(other$lastRepairDate)) return false;  Object this$repairCount = getRepairCount(), other$repairCount = other.getRepairCount(); if ((this$repairCount == null) ? (other$repairCount != null) : !this$repairCount.equals(other$repairCount)) return false;  Object this$wimaxMacAddr = getWimaxMacAddr(), other$wimaxMacAddr = other.getWimaxMacAddr(); if ((this$wimaxMacAddr == null) ? (other$wimaxMacAddr != null) : !this$wimaxMacAddr.equals(other$wimaxMacAddr)) return false;  Object this$hsn = getHsn(), other$hsn = other.getHsn(); if ((this$hsn == null) ? (other$hsn != null) : !this$hsn.equals(other$hsn)) return false;  Object this$dsnmuc1 = getDsnmuc1(), other$dsnmuc1 = other.getDsnmuc1(); if ((this$dsnmuc1 == null) ? (other$dsnmuc1 != null) : !this$dsnmuc1.equals(other$dsnmuc1)) return false;  Object this$dsnmuc2 = getDsnmuc2(), other$dsnmuc2 = other.getDsnmuc2(); if ((this$dsnmuc2 == null) ? (other$dsnmuc2 != null) : !this$dsnmuc2.equals(other$dsnmuc2)) return false;  Object this$dsnmuc3 = getDsnmuc3(), other$dsnmuc3 = other.getDsnmuc3(); if ((this$dsnmuc3 == null) ? (other$dsnmuc3 != null) : !this$dsnmuc3.equals(other$dsnmuc3)) return false;  Object this$dsnotuc = getDsnotuc(), other$dsnotuc = other.getDsnotuc(); if ((this$dsnotuc == null) ? (other$dsnotuc != null) : !this$dsnotuc.equals(other$dsnotuc)) return false;  Object this$dsnservicepass = getDsnservicepass(), other$dsnservicepass = other.getDsnservicepass(); if ((this$dsnservicepass == null) ? (other$dsnservicepass != null) : !this$dsnservicepass.equals(other$dsnservicepass)) return false;  Object this$dsnlock4 = getDsnlock4(), other$dsnlock4 = other.getDsnlock4(); if ((this$dsnlock4 == null) ? (other$dsnlock4 != null) : !this$dsnlock4.equals(other$dsnlock4)) return false;  Object this$dsnlock5 = getDsnlock5(), other$dsnlock5 = other.getDsnlock5(); if ((this$dsnlock5 == null) ? (other$dsnlock5 != null) : !this$dsnlock5.equals(other$dsnlock5)) return false;  Object this$dsnAkeyRandom = getDsnAkeyRandom(), other$dsnAkeyRandom = other.getDsnAkeyRandom(); if ((this$dsnAkeyRandom == null) ? (other$dsnAkeyRandom != null) : !this$dsnAkeyRandom.equals(other$dsnAkeyRandom)) return false;  Object this$dsnAkeyZero = getDsnAkeyZero(), other$dsnAkeyZero = other.getDsnAkeyZero(); if ((this$dsnAkeyZero == null) ? (other$dsnAkeyZero != null) : !this$dsnAkeyZero.equals(other$dsnAkeyZero)) return false;  Object this$dsnAkey2Type = getDsnAkey2Type(), other$dsnAkey2Type = other.getDsnAkey2Type(); if ((this$dsnAkey2Type == null) ? (other$dsnAkey2Type != null) : !this$dsnAkey2Type.equals(other$dsnAkey2Type)) return false;  Object this$dsnAkey2 = getDsnAkey2(), other$dsnAkey2 = other.getDsnAkey2(); if ((this$dsnAkey2 == null) ? (other$dsnAkey2 != null) : !this$dsnAkey2.equals(other$dsnAkey2)) return false;  Object this$wlan2 = getWlan2(), other$wlan2 = other.getWlan2(); if ((this$wlan2 == null) ? (other$wlan2 != null) : !this$wlan2.equals(other$wlan2)) return false;  Object this$wlan3 = getWlan3(), other$wlan3 = other.getWlan3(); if ((this$wlan3 == null) ? (other$wlan3 != null) : !this$wlan3.equals(other$wlan3)) return false;  Object this$wlan4 = getWlan4(), other$wlan4 = other.getWlan4(); if ((this$wlan4 == null) ? (other$wlan4 != null) : !this$wlan4.equals(other$wlan4)) return false;  Object this$triSerialNo = getTriSerialNo(), other$triSerialNo = other.getTriSerialNo(); if ((this$triSerialNo == null) ? (other$triSerialNo != null) : !this$triSerialNo.equals(other$triSerialNo)) return false;  Object this$triSerialNoType = getTriSerialNoType(), other$triSerialNoType = other.getTriSerialNoType(); if ((this$triSerialNoType == null) ? (other$triSerialNoType != null) : !this$triSerialNoType.equals(other$triSerialNoType)) return false;  Object this$tsnmuc1 = getTsnmuc1(), other$tsnmuc1 = other.getTsnmuc1(); if ((this$tsnmuc1 == null) ? (other$tsnmuc1 != null) : !this$tsnmuc1.equals(other$tsnmuc1)) return false;  Object this$tsnmuc2 = getTsnmuc2(), other$tsnmuc2 = other.getTsnmuc2(); if ((this$tsnmuc2 == null) ? (other$tsnmuc2 != null) : !this$tsnmuc2.equals(other$tsnmuc2)) return false;  Object this$tsnmuc3 = getTsnmuc3(), other$tsnmuc3 = other.getTsnmuc3(); if ((this$tsnmuc3 == null) ? (other$tsnmuc3 != null) : !this$tsnmuc3.equals(other$tsnmuc3)) return false;  Object this$tsnotuc = getTsnotuc(), other$tsnotuc = other.getTsnotuc(); if ((this$tsnotuc == null) ? (other$tsnotuc != null) : !this$tsnotuc.equals(other$tsnotuc)) return false;  Object this$tsnservicepass = getTsnservicepass(), other$tsnservicepass = other.getTsnservicepass(); if ((this$tsnservicepass == null) ? (other$tsnservicepass != null) : !this$tsnservicepass.equals(other$tsnservicepass)) return false;  Object this$tsnlock4 = getTsnlock4(), other$tsnlock4 = other.getTsnlock4(); if ((this$tsnlock4 == null) ? (other$tsnlock4 != null) : !this$tsnlock4.equals(other$tsnlock4)) return false;  Object this$tsnlock5 = getTsnlock5(), other$tsnlock5 = other.getTsnlock5(); if ((this$tsnlock5 == null) ? (other$tsnlock5 != null) : !this$tsnlock5.equals(other$tsnlock5)) return false;  Object this$tsnAkeyRandom = getTsnAkeyRandom(), other$tsnAkeyRandom = other.getTsnAkeyRandom(); if ((this$tsnAkeyRandom == null) ? (other$tsnAkeyRandom != null) : !this$tsnAkeyRandom.equals(other$tsnAkeyRandom)) return false;  Object this$tsnAkeyZero = getTsnAkeyZero(), other$tsnAkeyZero = other.getTsnAkeyZero(); if ((this$tsnAkeyZero == null) ? (other$tsnAkeyZero != null) : !this$tsnAkeyZero.equals(other$tsnAkeyZero)) return false;  Object this$tsnAkey2Type = getTsnAkey2Type(), other$tsnAkey2Type = other.getTsnAkey2Type(); if ((this$tsnAkey2Type == null) ? (other$tsnAkey2Type != null) : !this$tsnAkey2Type.equals(other$tsnAkey2Type)) return false;  Object this$tsnAkey2 = getTsnAkey2(), other$tsnAkey2 = other.getTsnAkey2(); if ((this$tsnAkey2 == null) ? (other$tsnAkey2 != null) : !this$tsnAkey2.equals(other$tsnAkey2)) return false;  Object this$createTime = getCreateTime(), other$createTime = other.getCreateTime(); if ((this$createTime == null) ? (other$createTime != null) : !this$createTime.equals(other$createTime)) return false;  Object this$batchNumber = getBatchNumber(), other$batchNumber = other.getBatchNumber(); return !((this$batchNumber == null) ? (other$batchNumber != null) : !this$batchNumber.equals(other$batchNumber)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdR12Outbound; } public int hashCode() { int PRIME = 59; result = 1; Object $serialNo = getSerialNo(); result = result * 59 + (($serialNo == null) ? 43 : $serialNo.hashCode()); Object $serialNumberType = getSerialNumberType(); result = result * 59 + (($serialNumberType == null) ? 43 : $serialNumberType.hashCode()); Object $factoryCode = getFactoryCode(); result = result * 59 + (($factoryCode == null) ? 43 : $factoryCode.hashCode()); Object $generationDate = getGenerationDate(); result = result * 59 + (($generationDate == null) ? 43 : $generationDate.hashCode()); Object $apc = getApc(); result = result * 59 + (($apc == null) ? 43 : $apc.hashCode()); Object $transceiverModel = getTransceiverModel(); result = result * 59 + (($transceiverModel == null) ? 43 : $transceiverModel.hashCode()); Object $customerModel = getCustomerModel(); result = result * 59 + (($customerModel == null) ? 43 : $customerModel.hashCode()); Object $marketName = getMarketName(); result = result * 59 + (($marketName == null) ? 43 : $marketName.hashCode()); Object $itemCode = getItemCode(); result = result * 59 + (($itemCode == null) ? 43 : $itemCode.hashCode()); Object $warrantyCode = getWarrantyCode(); result = result * 59 + (($warrantyCode == null) ? 43 : $warrantyCode.hashCode()); Object $shipDate = getShipDate(); result = result * 59 + (($shipDate == null) ? 43 : $shipDate.hashCode()); Object $shipToCustomerNumber = getShipToCustomerNumber(); result = result * 59 + (($shipToCustomerNumber == null) ? 43 : $shipToCustomerNumber.hashCode()); Object $shipToCustomerAddressId = getShipToCustomerAddressId(); result = result * 59 + (($shipToCustomerAddressId == null) ? 43 : $shipToCustomerAddressId.hashCode()); Object $shipToCustomerName = getShipToCustomerName(); result = result * 59 + (($shipToCustomerName == null) ? 43 : $shipToCustomerName.hashCode()); Object $shipToCity = getShipToCity(); result = result * 59 + (($shipToCity == null) ? 43 : $shipToCity.hashCode()); Object $shipToCountry = getShipToCountry(); result = result * 59 + (($shipToCountry == null) ? 43 : $shipToCountry.hashCode()); Object $soldToCustomerNumber = getSoldToCustomerNumber(); result = result * 59 + (($soldToCustomerNumber == null) ? 43 : $soldToCustomerNumber.hashCode()); Object $soldToCustomerName = getSoldToCustomerName(); result = result * 59 + (($soldToCustomerName == null) ? 43 : $soldToCustomerName.hashCode()); Object $soldDate = getSoldDate(); result = result * 59 + (($soldDate == null) ? 43 : $soldDate.hashCode()); Object $trackId = getTrackId(); result = result * 59 + (($trackId == null) ? 43 : $trackId.hashCode()); Object $taNumber = getTaNumber(); result = result * 59 + (($taNumber == null) ? 43 : $taNumber.hashCode()); Object $cartonId = getCartonId(); result = result * 59 + (($cartonId == null) ? 43 : $cartonId.hashCode()); Object $poNumber = getPoNumber(); result = result * 59 + (($poNumber == null) ? 43 : $poNumber.hashCode()); Object $soNumber = getSoNumber(); result = result * 59 + (($soNumber == null) ? 43 : $soNumber.hashCode()); Object $palletId = getPalletId(); result = result * 59 + (($palletId == null) ? 43 : $palletId.hashCode()); Object $primaryUnlockCode = getPrimaryUnlockCode(); result = result * 59 + (($primaryUnlockCode == null) ? 43 : $primaryUnlockCode.hashCode()); Object $primecoUnlockCode = getPrimecoUnlockCode(); result = result * 59 + (($primecoUnlockCode == null) ? 43 : $primecoUnlockCode.hashCode()); Object $verizonUnlockCode = getVerizonUnlockCode(); result = result * 59 + (($verizonUnlockCode == null) ? 43 : $verizonUnlockCode.hashCode()); Object $secondaryUnlockCode = getSecondaryUnlockCode(); result = result * 59 + (($secondaryUnlockCode == null) ? 43 : $secondaryUnlockCode.hashCode()); Object $servicePasscode = getServicePasscode(); result = result * 59 + (($servicePasscode == null) ? 43 : $servicePasscode.hashCode()); Object $lock4 = getLock4(); result = result * 59 + (($lock4 == null) ? 43 : $lock4.hashCode()); Object $lock5 = getLock5(); result = result * 59 + (($lock5 == null) ? 43 : $lock5.hashCode()); Object $aKeyRandom = getAKeyRandom(); result = result * 59 + (($aKeyRandom == null) ? 43 : $aKeyRandom.hashCode()); Object $aKeyZero = getAKeyZero(); result = result * 59 + (($aKeyZero == null) ? 43 : $aKeyZero.hashCode()); Object $msn = getMsn(); result = result * 59 + (($msn == null) ? 43 : $msn.hashCode()); Object $bt = getBt(); result = result * 59 + (($bt == null) ? 43 : $bt.hashCode()); Object $wlan = getWlan(); result = result * 59 + (($wlan == null) ? 43 : $wlan.hashCode()); Object $baseProcessorId = getBaseProcessorId(); result = result * 59 + (($baseProcessorId == null) ? 43 : $baseProcessorId.hashCode()); Object $fasttId = getFasttId(); result = result * 59 + (($fasttId == null) ? 43 : $fasttId.hashCode()); Object $locationType = getLocationType(); result = result * 59 + (($locationType == null) ? 43 : $locationType.hashCode()); Object $packingList = getPackingList(); result = result * 59 + (($packingList == null) ? 43 : $packingList.hashCode()); Object $fabDate = getFabDate(); result = result * 59 + (($fabDate == null) ? 43 : $fabDate.hashCode()); Object $softwareVersion = getSoftwareVersion(); result = result * 59 + (($softwareVersion == null) ? 43 : $softwareVersion.hashCode()); Object $organizationCode = getOrganizationCode(); result = result * 59 + (($organizationCode == null) ? 43 : $organizationCode.hashCode()); Object $flexOption = getFlexOption(); result = result * 59 + (($flexOption == null) ? 43 : $flexOption.hashCode()); Object $iccId = getIccId(); result = result * 59 + (($iccId == null) ? 43 : $iccId.hashCode()); Object $soLineNumber = getSoLineNumber(); result = result * 59 + (($soLineNumber == null) ? 43 : $soLineNumber.hashCode()); Object $directShipRegionCode = getDirectShipRegionCode(); result = result * 59 + (($directShipRegionCode == null) ? 43 : $directShipRegionCode.hashCode()); Object $directShipSoNumber = getDirectShipSoNumber(); result = result * 59 + (($directShipSoNumber == null) ? 43 : $directShipSoNumber.hashCode()); Object $directShipPoNumber = getDirectShipPoNumber(); result = result * 59 + (($directShipPoNumber == null) ? 43 : $directShipPoNumber.hashCode()); Object $directShipCustomerNumber = getDirectShipCustomerNumber(); result = result * 59 + (($directShipCustomerNumber == null) ? 43 : $directShipCustomerNumber.hashCode()); Object $directShipShipToAddressId = getDirectShipShipToAddressId(); result = result * 59 + (($directShipShipToAddressId == null) ? 43 : $directShipShipToAddressId.hashCode()); Object $directShipCustomerCountry = getDirectShipCustomerCountry(); result = result * 59 + (($directShipCustomerCountry == null) ? 43 : $directShipCustomerCountry.hashCode()); Object $directShipCustomerName = getDirectShipCustomerName(); result = result * 59 + (($directShipCustomerName == null) ? 43 : $directShipCustomerName.hashCode()); Object $directShipBillToId = getDirectShipBillToId(); result = result * 59 + (($directShipBillToId == null) ? 43 : $directShipBillToId.hashCode()); Object $shipmentNumber = getShipmentNumber(); result = result * 59 + (($shipmentNumber == null) ? 43 : $shipmentNumber.hashCode()); Object $wipDj = getWipDj(); result = result * 59 + (($wipDj == null) ? 43 : $wipDj.hashCode()); Object $deleteFlag = getDeleteFlag(); result = result * 59 + (($deleteFlag == null) ? 43 : $deleteFlag.hashCode()); Object $ultimateDestinationCountry = getUltimateDestinationCountry(); result = result * 59 + (($ultimateDestinationCountry == null) ? 43 : $ultimateDestinationCountry.hashCode()); Object $cssn = getCssn(); result = result * 59 + (($cssn == null) ? 43 : $cssn.hashCode()); Object $min = getMin(); result = result * 59 + (($min == null) ? 43 : $min.hashCode()); Object $billToId = getBillToId(); result = result * 59 + (($billToId == null) ? 43 : $billToId.hashCode()); Object $currFlexVer = getCurrFlexVer(); result = result * 59 + (($currFlexVer == null) ? 43 : $currFlexVer.hashCode()); Object $currFlashName = getCurrFlashName(); result = result * 59 + (($currFlashName == null) ? 43 : $currFlashName.hashCode()); Object $currPriVer = getCurrPriVer(); result = result * 59 + (($currPriVer == null) ? 43 : $currPriVer.hashCode()); Object $langPkgId = getLangPkgId(); result = result * 59 + (($langPkgId == null) ? 43 : $langPkgId.hashCode()); Object $kjavaVer = getKjavaVer(); result = result * 59 + (($kjavaVer == null) ? 43 : $kjavaVer.hashCode()); Object $bootloaderVer = getBootloaderVer(); result = result * 59 + (($bootloaderVer == null) ? 43 : $bootloaderVer.hashCode()); Object $hardwareVer = getHardwareVer(); result = result * 59 + (($hardwareVer == null) ? 43 : $hardwareVer.hashCode()); Object $fotaEnabled = getFotaEnabled(); result = result * 59 + (($fotaEnabled == null) ? 43 : $fotaEnabled.hashCode()); Object $dualSerialNo = getDualSerialNo(); result = result * 59 + (($dualSerialNo == null) ? 43 : $dualSerialNo.hashCode()); Object $dualSnType = getDualSnType(); result = result * 59 + (($dualSnType == null) ? 43 : $dualSnType.hashCode()); Object $popInSysdate = getPopInSysdate(); result = result * 59 + (($popInSysdate == null) ? 43 : $popInSysdate.hashCode()); Object $popDate = getPopDate(); result = result * 59 + (($popDate == null) ? 43 : $popDate.hashCode()); Object $popIdentifier = getPopIdentifier(); result = result * 59 + (($popIdentifier == null) ? 43 : $popIdentifier.hashCode()); Object $lastRepairDate = getLastRepairDate(); result = result * 59 + (($lastRepairDate == null) ? 43 : $lastRepairDate.hashCode()); Object $repairCount = getRepairCount(); result = result * 59 + (($repairCount == null) ? 43 : $repairCount.hashCode()); Object $wimaxMacAddr = getWimaxMacAddr(); result = result * 59 + (($wimaxMacAddr == null) ? 43 : $wimaxMacAddr.hashCode()); Object $hsn = getHsn(); result = result * 59 + (($hsn == null) ? 43 : $hsn.hashCode()); Object $dsnmuc1 = getDsnmuc1(); result = result * 59 + (($dsnmuc1 == null) ? 43 : $dsnmuc1.hashCode()); Object $dsnmuc2 = getDsnmuc2(); result = result * 59 + (($dsnmuc2 == null) ? 43 : $dsnmuc2.hashCode()); Object $dsnmuc3 = getDsnmuc3(); result = result * 59 + (($dsnmuc3 == null) ? 43 : $dsnmuc3.hashCode()); Object $dsnotuc = getDsnotuc(); result = result * 59 + (($dsnotuc == null) ? 43 : $dsnotuc.hashCode()); Object $dsnservicepass = getDsnservicepass(); result = result * 59 + (($dsnservicepass == null) ? 43 : $dsnservicepass.hashCode()); Object $dsnlock4 = getDsnlock4(); result = result * 59 + (($dsnlock4 == null) ? 43 : $dsnlock4.hashCode()); Object $dsnlock5 = getDsnlock5(); result = result * 59 + (($dsnlock5 == null) ? 43 : $dsnlock5.hashCode()); Object $dsnAkeyRandom = getDsnAkeyRandom(); result = result * 59 + (($dsnAkeyRandom == null) ? 43 : $dsnAkeyRandom.hashCode()); Object $dsnAkeyZero = getDsnAkeyZero(); result = result * 59 + (($dsnAkeyZero == null) ? 43 : $dsnAkeyZero.hashCode()); Object $dsnAkey2Type = getDsnAkey2Type(); result = result * 59 + (($dsnAkey2Type == null) ? 43 : $dsnAkey2Type.hashCode()); Object $dsnAkey2 = getDsnAkey2(); result = result * 59 + (($dsnAkey2 == null) ? 43 : $dsnAkey2.hashCode()); Object $wlan2 = getWlan2(); result = result * 59 + (($wlan2 == null) ? 43 : $wlan2.hashCode()); Object $wlan3 = getWlan3(); result = result * 59 + (($wlan3 == null) ? 43 : $wlan3.hashCode()); Object $wlan4 = getWlan4(); result = result * 59 + (($wlan4 == null) ? 43 : $wlan4.hashCode()); Object $triSerialNo = getTriSerialNo(); result = result * 59 + (($triSerialNo == null) ? 43 : $triSerialNo.hashCode()); Object $triSerialNoType = getTriSerialNoType(); result = result * 59 + (($triSerialNoType == null) ? 43 : $triSerialNoType.hashCode()); Object $tsnmuc1 = getTsnmuc1(); result = result * 59 + (($tsnmuc1 == null) ? 43 : $tsnmuc1.hashCode()); Object $tsnmuc2 = getTsnmuc2(); result = result * 59 + (($tsnmuc2 == null) ? 43 : $tsnmuc2.hashCode()); Object $tsnmuc3 = getTsnmuc3(); result = result * 59 + (($tsnmuc3 == null) ? 43 : $tsnmuc3.hashCode()); Object $tsnotuc = getTsnotuc(); result = result * 59 + (($tsnotuc == null) ? 43 : $tsnotuc.hashCode()); Object $tsnservicepass = getTsnservicepass(); result = result * 59 + (($tsnservicepass == null) ? 43 : $tsnservicepass.hashCode()); Object $tsnlock4 = getTsnlock4(); result = result * 59 + (($tsnlock4 == null) ? 43 : $tsnlock4.hashCode()); Object $tsnlock5 = getTsnlock5(); result = result * 59 + (($tsnlock5 == null) ? 43 : $tsnlock5.hashCode()); Object $tsnAkeyRandom = getTsnAkeyRandom(); result = result * 59 + (($tsnAkeyRandom == null) ? 43 : $tsnAkeyRandom.hashCode()); Object $tsnAkeyZero = getTsnAkeyZero(); result = result * 59 + (($tsnAkeyZero == null) ? 43 : $tsnAkeyZero.hashCode()); Object $tsnAkey2Type = getTsnAkey2Type(); result = result * 59 + (($tsnAkey2Type == null) ? 43 : $tsnAkey2Type.hashCode()); Object $tsnAkey2 = getTsnAkey2(); result = result * 59 + (($tsnAkey2 == null) ? 43 : $tsnAkey2.hashCode()); Object $createTime = getCreateTime(); result = result * 59 + (($createTime == null) ? 43 : $createTime.hashCode()); Object $batchNumber = getBatchNumber(); return result * 59 + (($batchNumber == null) ? 43 : $batchNumber.hashCode()); } public String toString() { return "IbTUpdR12Outbound(serialNo=" + getSerialNo() + ", serialNumberType=" + getSerialNumberType() + ", factoryCode=" + getFactoryCode() + ", generationDate=" + getGenerationDate() + ", apc=" + getApc() + ", transceiverModel=" + getTransceiverModel() + ", customerModel=" + getCustomerModel() + ", marketName=" + getMarketName() + ", itemCode=" + getItemCode() + ", warrantyCode=" + getWarrantyCode() + ", shipDate=" + getShipDate() + ", shipToCustomerNumber=" + getShipToCustomerNumber() + ", shipToCustomerAddressId=" + getShipToCustomerAddressId() + ", shipToCustomerName=" + getShipToCustomerName() + ", shipToCity=" + getShipToCity() + ", shipToCountry=" + getShipToCountry() + ", soldToCustomerNumber=" + getSoldToCustomerNumber() + ", soldToCustomerName=" + getSoldToCustomerName() + ", soldDate=" + getSoldDate() + ", trackId=" + getTrackId() + ", taNumber=" + getTaNumber() + ", cartonId=" + getCartonId() + ", poNumber=" + getPoNumber() + ", soNumber=" + getSoNumber() + ", palletId=" + getPalletId() + ", primaryUnlockCode=" + getPrimaryUnlockCode() + ", primecoUnlockCode=" + getPrimecoUnlockCode() + ", verizonUnlockCode=" + getVerizonUnlockCode() + ", secondaryUnlockCode=" + getSecondaryUnlockCode() + ", servicePasscode=" + getServicePasscode() + ", lock4=" + getLock4() + ", lock5=" + getLock5() + ", aKeyRandom=" + getAKeyRandom() + ", aKeyZero=" + getAKeyZero() + ", msn=" + getMsn() + ", bt=" + getBt() + ", wlan=" + getWlan() + ", baseProcessorId=" + getBaseProcessorId() + ", fasttId=" + getFasttId() + ", locationType=" + getLocationType() + ", packingList=" + getPackingList() + ", fabDate=" + getFabDate() + ", softwareVersion=" + getSoftwareVersion() + ", organizationCode=" + getOrganizationCode() + ", flexOption=" + getFlexOption() + ", iccId=" + getIccId() + ", soLineNumber=" + getSoLineNumber() + ", directShipRegionCode=" + getDirectShipRegionCode() + ", directShipSoNumber=" + getDirectShipSoNumber() + ", directShipPoNumber=" + getDirectShipPoNumber() + ", directShipCustomerNumber=" + getDirectShipCustomerNumber() + ", directShipShipToAddressId=" + getDirectShipShipToAddressId() + ", directShipCustomerCountry=" + getDirectShipCustomerCountry() + ", directShipCustomerName=" + getDirectShipCustomerName() + ", directShipBillToId=" + getDirectShipBillToId() + ", shipmentNumber=" + getShipmentNumber() + ", wipDj=" + getWipDj() + ", deleteFlag=" + getDeleteFlag() + ", ultimateDestinationCountry=" + getUltimateDestinationCountry() + ", cssn=" + getCssn() + ", min=" + getMin() + ", billToId=" + getBillToId() + ", currFlexVer=" + getCurrFlexVer() + ", currFlashName=" + getCurrFlashName() + ", currPriVer=" + getCurrPriVer() + ", langPkgId=" + getLangPkgId() + ", kjavaVer=" + getKjavaVer() + ", bootloaderVer=" + getBootloaderVer() + ", hardwareVer=" + getHardwareVer() + ", fotaEnabled=" + getFotaEnabled() + ", dualSerialNo=" + getDualSerialNo() + ", dualSnType=" + getDualSnType() + ", popInSysdate=" + getPopInSysdate() + ", popDate=" + getPopDate() + ", popIdentifier=" + getPopIdentifier() + ", lastRepairDate=" + getLastRepairDate() + ", repairCount=" + getRepairCount() + ", wimaxMacAddr=" + getWimaxMacAddr() + ", hsn=" + getHsn() + ", dsnmuc1=" + getDsnmuc1() + ", dsnmuc2=" + getDsnmuc2() + ", dsnmuc3=" + getDsnmuc3() + ", dsnotuc=" + getDsnotuc() + ", dsnservicepass=" + getDsnservicepass() + ", dsnlock4=" + getDsnlock4() + ", dsnlock5=" + getDsnlock5() + ", dsnAkeyRandom=" + getDsnAkeyRandom() + ", dsnAkeyZero=" + getDsnAkeyZero() + ", dsnAkey2Type=" + getDsnAkey2Type() + ", dsnAkey2=" + getDsnAkey2() + ", wlan2=" + getWlan2() + ", wlan3=" + getWlan3() + ", wlan4=" + getWlan4() + ", triSerialNo=" + getTriSerialNo() + ", triSerialNoType=" + getTriSerialNoType() + ", tsnmuc1=" + getTsnmuc1() + ", tsnmuc2=" + getTsnmuc2() + ", tsnmuc3=" + getTsnmuc3() + ", tsnotuc=" + getTsnotuc() + ", tsnservicepass=" + getTsnservicepass() + ", tsnlock4=" + getTsnlock4() + ", tsnlock5=" + getTsnlock5() + ", tsnAkeyRandom=" + getTsnAkeyRandom() + ", tsnAkeyZero=" + getTsnAkeyZero() + ", tsnAkey2Type=" + getTsnAkey2Type() + ", tsnAkey2=" + getTsnAkey2() + ", createTime=" + getCreateTime() + ", batchNumber=" + getBatchNumber() + ")"; }
/*     */    public String getSerialNo() {
/*   9 */     return this.serialNo;
/*     */   } public String getSerialNumberType() {
/*  11 */     return this.serialNumberType;
/*     */   } public String getFactoryCode() {
/*  13 */     return this.factoryCode;
/*     */   } public Date getGenerationDate() {
/*  15 */     return this.generationDate;
/*     */   } public String getApc() {
/*  17 */     return this.apc;
/*     */   } public String getTransceiverModel() {
/*  19 */     return this.transceiverModel;
/*     */   } public String getCustomerModel() {
/*  21 */     return this.customerModel;
/*     */   } public String getMarketName() {
/*  23 */     return this.marketName;
/*     */   } public String getItemCode() {
/*  25 */     return this.itemCode;
/*     */   } public String getWarrantyCode() {
/*  27 */     return this.warrantyCode;
/*     */   } public Date getShipDate() {
/*  29 */     return this.shipDate;
/*     */   } public String getShipToCustomerNumber() {
/*  31 */     return this.shipToCustomerNumber;
/*     */   } public String getShipToCustomerAddressId() {
/*  33 */     return this.shipToCustomerAddressId;
/*     */   } public String getShipToCustomerName() {
/*  35 */     return this.shipToCustomerName;
/*     */   } public String getShipToCity() {
/*  37 */     return this.shipToCity;
/*     */   } public String getShipToCountry() {
/*  39 */     return this.shipToCountry;
/*     */   } public String getSoldToCustomerNumber() {
/*  41 */     return this.soldToCustomerNumber;
/*     */   } public String getSoldToCustomerName() {
/*  43 */     return this.soldToCustomerName;
/*     */   } public Date getSoldDate() {
/*  45 */     return this.soldDate;
/*     */   } public String getTrackId() {
/*  47 */     return this.trackId;
/*     */   } public String getTaNumber() {
/*  49 */     return this.taNumber;
/*     */   } public String getCartonId() {
/*  51 */     return this.cartonId;
/*     */   } public String getPoNumber() {
/*  53 */     return this.poNumber;
/*     */   } public String getSoNumber() {
/*  55 */     return this.soNumber;
/*     */   } public String getPalletId() {
/*  57 */     return this.palletId;
/*     */   } public String getPrimaryUnlockCode() {
/*  59 */     return this.primaryUnlockCode;
/*     */   } public String getPrimecoUnlockCode() {
/*  61 */     return this.primecoUnlockCode;
/*     */   } public String getVerizonUnlockCode() {
/*  63 */     return this.verizonUnlockCode;
/*     */   } public String getSecondaryUnlockCode() {
/*  65 */     return this.secondaryUnlockCode;
/*     */   } public String getServicePasscode() {
/*  67 */     return this.servicePasscode;
/*     */   } public String getLock4() {
/*  69 */     return this.lock4;
/*     */   } public String getLock5() {
/*  71 */     return this.lock5;
/*     */   } public String getAKeyRandom() {
/*  73 */     return this.aKeyRandom;
/*     */   } public String getAKeyZero() {
/*  75 */     return this.aKeyZero;
/*     */   } public String getMsn() {
/*  77 */     return this.msn;
/*     */   } public String getBt() {
/*  79 */     return this.bt;
/*     */   } public String getWlan() {
/*  81 */     return this.wlan;
/*     */   } public String getBaseProcessorId() {
/*  83 */     return this.baseProcessorId;
/*     */   } public String getFasttId() {
/*  85 */     return this.fasttId;
/*     */   } public String getLocationType() {
/*  87 */     return this.locationType;
/*     */   } public String getPackingList() {
/*  89 */     return this.packingList;
/*     */   } public String getFabDate() {
/*  91 */     return this.fabDate;
/*     */   } public String getSoftwareVersion() {
/*  93 */     return this.softwareVersion;
/*     */   } public String getOrganizationCode() {
/*  95 */     return this.organizationCode;
/*     */   } public String getFlexOption() {
/*  97 */     return this.flexOption;
/*     */   } public String getIccId() {
/*  99 */     return this.iccId;
/*     */   } public String getSoLineNumber() {
/* 101 */     return this.soLineNumber;
/*     */   } public String getDirectShipRegionCode() {
/* 103 */     return this.directShipRegionCode;
/*     */   } public String getDirectShipSoNumber() {
/* 105 */     return this.directShipSoNumber;
/*     */   } public String getDirectShipPoNumber() {
/* 107 */     return this.directShipPoNumber;
/*     */   } public String getDirectShipCustomerNumber() {
/* 109 */     return this.directShipCustomerNumber;
/*     */   } public String getDirectShipShipToAddressId() {
/* 111 */     return this.directShipShipToAddressId;
/*     */   } public String getDirectShipCustomerCountry() {
/* 113 */     return this.directShipCustomerCountry;
/*     */   } public String getDirectShipCustomerName() {
/* 115 */     return this.directShipCustomerName;
/*     */   } public String getDirectShipBillToId() {
/* 117 */     return this.directShipBillToId;
/*     */   } public String getShipmentNumber() {
/* 119 */     return this.shipmentNumber;
/*     */   } public String getWipDj() {
/* 121 */     return this.wipDj;
/*     */   } public String getDeleteFlag() {
/* 123 */     return this.deleteFlag;
/*     */   } public String getUltimateDestinationCountry() {
/* 125 */     return this.ultimateDestinationCountry;
/*     */   } public String getCssn() {
/* 127 */     return this.cssn;
/*     */   } public String getMin() {
/* 129 */     return this.min;
/*     */   } public String getBillToId() {
/* 131 */     return this.billToId;
/*     */   } public String getCurrFlexVer() {
/* 133 */     return this.currFlexVer;
/*     */   } public String getCurrFlashName() {
/* 135 */     return this.currFlashName;
/*     */   } public String getCurrPriVer() {
/* 137 */     return this.currPriVer;
/*     */   } public String getLangPkgId() {
/* 139 */     return this.langPkgId;
/*     */   } public String getKjavaVer() {
/* 141 */     return this.kjavaVer;
/*     */   } public String getBootloaderVer() {
/* 143 */     return this.bootloaderVer;
/*     */   } public String getHardwareVer() {
/* 145 */     return this.hardwareVer;
/*     */   } public String getFotaEnabled() {
/* 147 */     return this.fotaEnabled;
/*     */   } public String getDualSerialNo() {
/* 149 */     return this.dualSerialNo;
/*     */   } public String getDualSnType() {
/* 151 */     return this.dualSnType;
/*     */   } public String getPopInSysdate() {
/* 153 */     return this.popInSysdate;
/*     */   } public String getPopDate() {
/* 155 */     return this.popDate;
/*     */   } public String getPopIdentifier() {
/* 157 */     return this.popIdentifier;
/*     */   } public String getLastRepairDate() {
/* 159 */     return this.lastRepairDate;
/*     */   } public String getRepairCount() {
/* 161 */     return this.repairCount;
/*     */   } public String getWimaxMacAddr() {
/* 163 */     return this.wimaxMacAddr;
/*     */   } public String getHsn() {
/* 165 */     return this.hsn;
/*     */   } public String getDsnmuc1() {
/* 167 */     return this.dsnmuc1;
/*     */   } public String getDsnmuc2() {
/* 169 */     return this.dsnmuc2;
/*     */   } public String getDsnmuc3() {
/* 171 */     return this.dsnmuc3;
/*     */   } public String getDsnotuc() {
/* 173 */     return this.dsnotuc;
/*     */   } public String getDsnservicepass() {
/* 175 */     return this.dsnservicepass;
/*     */   } public String getDsnlock4() {
/* 177 */     return this.dsnlock4;
/*     */   } public String getDsnlock5() {
/* 179 */     return this.dsnlock5;
/*     */   } public String getDsnAkeyRandom() {
/* 181 */     return this.dsnAkeyRandom;
/*     */   } public String getDsnAkeyZero() {
/* 183 */     return this.dsnAkeyZero;
/*     */   } public String getDsnAkey2Type() {
/* 185 */     return this.dsnAkey2Type;
/*     */   } public String getDsnAkey2() {
/* 187 */     return this.dsnAkey2;
/*     */   } public String getWlan2() {
/* 189 */     return this.wlan2;
/*     */   } public String getWlan3() {
/* 191 */     return this.wlan3;
/*     */   } public String getWlan4() {
/* 193 */     return this.wlan4;
/*     */   } public String getTriSerialNo() {
/* 195 */     return this.triSerialNo;
/*     */   } public String getTriSerialNoType() {
/* 197 */     return this.triSerialNoType;
/*     */   } public String getTsnmuc1() {
/* 199 */     return this.tsnmuc1;
/*     */   } public String getTsnmuc2() {
/* 201 */     return this.tsnmuc2;
/*     */   } public String getTsnmuc3() {
/* 203 */     return this.tsnmuc3;
/*     */   } public String getTsnotuc() {
/* 205 */     return this.tsnotuc;
/*     */   } public String getTsnservicepass() {
/* 207 */     return this.tsnservicepass;
/*     */   } public String getTsnlock4() {
/* 209 */     return this.tsnlock4;
/*     */   } public String getTsnlock5() {
/* 211 */     return this.tsnlock5;
/*     */   } public String getTsnAkeyRandom() {
/* 213 */     return this.tsnAkeyRandom;
/*     */   } public String getTsnAkeyZero() {
/* 215 */     return this.tsnAkeyZero;
/*     */   } public String getTsnAkey2Type() {
/* 217 */     return this.tsnAkey2Type;
/*     */   } public String getTsnAkey2() {
/* 219 */     return this.tsnAkey2;
/*     */   } public Date getCreateTime() {
/* 221 */     return this.createTime;
/*     */   } public String getBatchNumber() {
/* 223 */     return this.batchNumber;
/*     */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdR12Outbound.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */