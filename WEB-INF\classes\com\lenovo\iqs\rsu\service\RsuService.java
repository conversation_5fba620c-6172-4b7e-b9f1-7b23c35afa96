package WEB-INF.classes.com.lenovo.iqs.rsu.service;

import com.lenovo.iqs.rsu.bean.RsuRequest;
import com.lenovo.iqs.rsu.bean.RsuResponse;

public interface RsuService {
  RsuResponse processRsuRequest(RsuRequest paramRsuRequest) throws Exception;
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\rsu\service\RsuService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */