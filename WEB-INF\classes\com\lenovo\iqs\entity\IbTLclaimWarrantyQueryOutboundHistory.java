/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTLclaimWarrantyQueryOutboundHistory implements Serializable { private int claimId; private String status; private Date activationDate; private Date lastCheckInDate; private String machineType; private String mtmCode; private String mtmDescription; private String serialNo; private String imei1; private String imei2; private Date shipDate; private String chlPadCode; private String swVersion; private Date warrantyStartDate; private Date warrantyEndDate; private Date popDate; private String soldToCustId; private String shipToCountryCode; private String currWarrTypeCode;
/*    */   private String warrFlag;
/*    */   private String swapImei;
/*    */   private Date lastChangeTime;
/*    */   
/*  7 */   public void setClaimId(int claimId) { this.claimId = claimId; } private String batchNumber; private String factoryCode; private String apc; private String transModel; private String mktModel; private String servicePassword; private String ulma; private String renWarrCode; private String cancelCode; private String swapRefNo; private String swapCount; private String dsCustomerId; private String wlan; private String warrantyCountryCode; private String hsn; private String wlan2; private String wlan3; private String wlan4; private Date lastRepairDate; private String repairCount; private String brand; private static final long serialVersionUID = 1L; public void setStatus(String status) { this.status = status; } public void setActivationDate(Date activationDate) { this.activationDate = activationDate; } public void setLastCheckInDate(Date lastCheckInDate) { this.lastCheckInDate = lastCheckInDate; } public void setMachineType(String machineType) { this.machineType = machineType; } public void setMtmCode(String mtmCode) { this.mtmCode = mtmCode; } public void setMtmDescription(String mtmDescription) { this.mtmDescription = mtmDescription; } public void setSerialNo(String serialNo) { this.serialNo = serialNo; } public void setImei1(String imei1) { this.imei1 = imei1; } public void setImei2(String imei2) { this.imei2 = imei2; } public void setShipDate(Date shipDate) { this.shipDate = shipDate; } public void setChlPadCode(String chlPadCode) { this.chlPadCode = chlPadCode; } public void setSwVersion(String swVersion) { this.swVersion = swVersion; } public void setWarrantyStartDate(Date warrantyStartDate) { this.warrantyStartDate = warrantyStartDate; } public void setWarrantyEndDate(Date warrantyEndDate) { this.warrantyEndDate = warrantyEndDate; } public void setPopDate(Date popDate) { this.popDate = popDate; } public void setSoldToCustId(String soldToCustId) { this.soldToCustId = soldToCustId; } public void setShipToCountryCode(String shipToCountryCode) { this.shipToCountryCode = shipToCountryCode; } public void setCurrWarrTypeCode(String currWarrTypeCode) { this.currWarrTypeCode = currWarrTypeCode; } public void setWarrFlag(String warrFlag) { this.warrFlag = warrFlag; } public void setSwapImei(String swapImei) { this.swapImei = swapImei; } public void setLastChangeTime(Date lastChangeTime) { this.lastChangeTime = lastChangeTime; } public void setBatchNumber(String batchNumber) { this.batchNumber = batchNumber; } public void setFactoryCode(String factoryCode) { this.factoryCode = factoryCode; } public void setApc(String apc) { this.apc = apc; } public void setTransModel(String transModel) { this.transModel = transModel; } public void setMktModel(String mktModel) { this.mktModel = mktModel; } public void setServicePassword(String servicePassword) { this.servicePassword = servicePassword; } public void setUlma(String ulma) { this.ulma = ulma; } public void setRenWarrCode(String renWarrCode) { this.renWarrCode = renWarrCode; } public void setCancelCode(String cancelCode) { this.cancelCode = cancelCode; } public void setSwapRefNo(String swapRefNo) { this.swapRefNo = swapRefNo; } public void setSwapCount(String swapCount) { this.swapCount = swapCount; } public void setDsCustomerId(String dsCustomerId) { this.dsCustomerId = dsCustomerId; } public void setWlan(String wlan) { this.wlan = wlan; } public void setWarrantyCountryCode(String warrantyCountryCode) { this.warrantyCountryCode = warrantyCountryCode; } public void setHsn(String hsn) { this.hsn = hsn; } public void setWlan2(String wlan2) { this.wlan2 = wlan2; } public void setWlan3(String wlan3) { this.wlan3 = wlan3; } public void setWlan4(String wlan4) { this.wlan4 = wlan4; } public void setLastRepairDate(Date lastRepairDate) { this.lastRepairDate = lastRepairDate; } public void setRepairCount(String repairCount) { this.repairCount = repairCount; } public void setBrand(String brand) { this.brand = brand; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTLclaimWarrantyQueryOutboundHistory)) return false;  com.lenovo.iqs.entity.IbTLclaimWarrantyQueryOutboundHistory other = (com.lenovo.iqs.entity.IbTLclaimWarrantyQueryOutboundHistory)o; if (!other.canEqual(this)) return false;  if (getClaimId() != other.getClaimId()) return false;  Object this$status = getStatus(), other$status = other.getStatus(); if ((this$status == null) ? (other$status != null) : !this$status.equals(other$status)) return false;  Object this$activationDate = getActivationDate(), other$activationDate = other.getActivationDate(); if ((this$activationDate == null) ? (other$activationDate != null) : !this$activationDate.equals(other$activationDate)) return false;  Object this$lastCheckInDate = getLastCheckInDate(), other$lastCheckInDate = other.getLastCheckInDate(); if ((this$lastCheckInDate == null) ? (other$lastCheckInDate != null) : !this$lastCheckInDate.equals(other$lastCheckInDate)) return false;  Object this$machineType = getMachineType(), other$machineType = other.getMachineType(); if ((this$machineType == null) ? (other$machineType != null) : !this$machineType.equals(other$machineType)) return false;  Object this$mtmCode = getMtmCode(), other$mtmCode = other.getMtmCode(); if ((this$mtmCode == null) ? (other$mtmCode != null) : !this$mtmCode.equals(other$mtmCode)) return false;  Object this$mtmDescription = getMtmDescription(), other$mtmDescription = other.getMtmDescription(); if ((this$mtmDescription == null) ? (other$mtmDescription != null) : !this$mtmDescription.equals(other$mtmDescription)) return false;  Object this$serialNo = getSerialNo(), other$serialNo = other.getSerialNo(); if ((this$serialNo == null) ? (other$serialNo != null) : !this$serialNo.equals(other$serialNo)) return false;  Object this$imei1 = getImei1(), other$imei1 = other.getImei1(); if ((this$imei1 == null) ? (other$imei1 != null) : !this$imei1.equals(other$imei1)) return false;  Object this$imei2 = getImei2(), other$imei2 = other.getImei2(); if ((this$imei2 == null) ? (other$imei2 != null) : !this$imei2.equals(other$imei2)) return false;  Object this$shipDate = getShipDate(), other$shipDate = other.getShipDate(); if ((this$shipDate == null) ? (other$shipDate != null) : !this$shipDate.equals(other$shipDate)) return false;  Object this$chlPadCode = getChlPadCode(), other$chlPadCode = other.getChlPadCode(); if ((this$chlPadCode == null) ? (other$chlPadCode != null) : !this$chlPadCode.equals(other$chlPadCode)) return false;  Object this$swVersion = getSwVersion(), other$swVersion = other.getSwVersion(); if ((this$swVersion == null) ? (other$swVersion != null) : !this$swVersion.equals(other$swVersion)) return false;  Object this$warrantyStartDate = getWarrantyStartDate(), other$warrantyStartDate = other.getWarrantyStartDate(); if ((this$warrantyStartDate == null) ? (other$warrantyStartDate != null) : !this$warrantyStartDate.equals(other$warrantyStartDate)) return false;  Object this$warrantyEndDate = getWarrantyEndDate(), other$warrantyEndDate = other.getWarrantyEndDate(); if ((this$warrantyEndDate == null) ? (other$warrantyEndDate != null) : !this$warrantyEndDate.equals(other$warrantyEndDate)) return false;  Object this$popDate = getPopDate(), other$popDate = other.getPopDate(); if ((this$popDate == null) ? (other$popDate != null) : !this$popDate.equals(other$popDate)) return false;  Object this$soldToCustId = getSoldToCustId(), other$soldToCustId = other.getSoldToCustId(); if ((this$soldToCustId == null) ? (other$soldToCustId != null) : !this$soldToCustId.equals(other$soldToCustId)) return false;  Object this$shipToCountryCode = getShipToCountryCode(), other$shipToCountryCode = other.getShipToCountryCode(); if ((this$shipToCountryCode == null) ? (other$shipToCountryCode != null) : !this$shipToCountryCode.equals(other$shipToCountryCode)) return false;  Object this$currWarrTypeCode = getCurrWarrTypeCode(), other$currWarrTypeCode = other.getCurrWarrTypeCode(); if ((this$currWarrTypeCode == null) ? (other$currWarrTypeCode != null) : !this$currWarrTypeCode.equals(other$currWarrTypeCode)) return false;  Object this$warrFlag = getWarrFlag(), other$warrFlag = other.getWarrFlag(); if ((this$warrFlag == null) ? (other$warrFlag != null) : !this$warrFlag.equals(other$warrFlag)) return false;  Object this$swapImei = getSwapImei(), other$swapImei = other.getSwapImei(); if ((this$swapImei == null) ? (other$swapImei != null) : !this$swapImei.equals(other$swapImei)) return false;  Object this$lastChangeTime = getLastChangeTime(), other$lastChangeTime = other.getLastChangeTime(); if ((this$lastChangeTime == null) ? (other$lastChangeTime != null) : !this$lastChangeTime.equals(other$lastChangeTime)) return false;  Object this$batchNumber = getBatchNumber(), other$batchNumber = other.getBatchNumber(); if ((this$batchNumber == null) ? (other$batchNumber != null) : !this$batchNumber.equals(other$batchNumber)) return false;  Object this$factoryCode = getFactoryCode(), other$factoryCode = other.getFactoryCode(); if ((this$factoryCode == null) ? (other$factoryCode != null) : !this$factoryCode.equals(other$factoryCode)) return false;  Object this$apc = getApc(), other$apc = other.getApc(); if ((this$apc == null) ? (other$apc != null) : !this$apc.equals(other$apc)) return false;  Object this$transModel = getTransModel(), other$transModel = other.getTransModel(); if ((this$transModel == null) ? (other$transModel != null) : !this$transModel.equals(other$transModel)) return false;  Object this$mktModel = getMktModel(), other$mktModel = other.getMktModel(); if ((this$mktModel == null) ? (other$mktModel != null) : !this$mktModel.equals(other$mktModel)) return false;  Object this$servicePassword = getServicePassword(), other$servicePassword = other.getServicePassword(); if ((this$servicePassword == null) ? (other$servicePassword != null) : !this$servicePassword.equals(other$servicePassword)) return false;  Object this$ulma = getUlma(), other$ulma = other.getUlma(); if ((this$ulma == null) ? (other$ulma != null) : !this$ulma.equals(other$ulma)) return false;  Object this$renWarrCode = getRenWarrCode(), other$renWarrCode = other.getRenWarrCode(); if ((this$renWarrCode == null) ? (other$renWarrCode != null) : !this$renWarrCode.equals(other$renWarrCode)) return false;  Object this$cancelCode = getCancelCode(), other$cancelCode = other.getCancelCode(); if ((this$cancelCode == null) ? (other$cancelCode != null) : !this$cancelCode.equals(other$cancelCode)) return false;  Object this$swapRefNo = getSwapRefNo(), other$swapRefNo = other.getSwapRefNo(); if ((this$swapRefNo == null) ? (other$swapRefNo != null) : !this$swapRefNo.equals(other$swapRefNo)) return false;  Object this$swapCount = getSwapCount(), other$swapCount = other.getSwapCount(); if ((this$swapCount == null) ? (other$swapCount != null) : !this$swapCount.equals(other$swapCount)) return false;  Object this$dsCustomerId = getDsCustomerId(), other$dsCustomerId = other.getDsCustomerId(); if ((this$dsCustomerId == null) ? (other$dsCustomerId != null) : !this$dsCustomerId.equals(other$dsCustomerId)) return false;  Object this$wlan = getWlan(), other$wlan = other.getWlan(); if ((this$wlan == null) ? (other$wlan != null) : !this$wlan.equals(other$wlan)) return false;  Object this$warrantyCountryCode = getWarrantyCountryCode(), other$warrantyCountryCode = other.getWarrantyCountryCode(); if ((this$warrantyCountryCode == null) ? (other$warrantyCountryCode != null) : !this$warrantyCountryCode.equals(other$warrantyCountryCode)) return false;  Object this$hsn = getHsn(), other$hsn = other.getHsn(); if ((this$hsn == null) ? (other$hsn != null) : !this$hsn.equals(other$hsn)) return false;  Object this$wlan2 = getWlan2(), other$wlan2 = other.getWlan2(); if ((this$wlan2 == null) ? (other$wlan2 != null) : !this$wlan2.equals(other$wlan2)) return false;  Object this$wlan3 = getWlan3(), other$wlan3 = other.getWlan3(); if ((this$wlan3 == null) ? (other$wlan3 != null) : !this$wlan3.equals(other$wlan3)) return false;  Object this$wlan4 = getWlan4(), other$wlan4 = other.getWlan4(); if ((this$wlan4 == null) ? (other$wlan4 != null) : !this$wlan4.equals(other$wlan4)) return false;  Object this$lastRepairDate = getLastRepairDate(), other$lastRepairDate = other.getLastRepairDate(); if ((this$lastRepairDate == null) ? (other$lastRepairDate != null) : !this$lastRepairDate.equals(other$lastRepairDate)) return false;  Object this$repairCount = getRepairCount(), other$repairCount = other.getRepairCount(); if ((this$repairCount == null) ? (other$repairCount != null) : !this$repairCount.equals(other$repairCount)) return false;  Object this$brand = getBrand(), other$brand = other.getBrand(); return !((this$brand == null) ? (other$brand != null) : !this$brand.equals(other$brand)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTLclaimWarrantyQueryOutboundHistory; } public int hashCode() { int PRIME = 59; result = 1; result = result * 59 + getClaimId(); Object $status = getStatus(); result = result * 59 + (($status == null) ? 43 : $status.hashCode()); Object $activationDate = getActivationDate(); result = result * 59 + (($activationDate == null) ? 43 : $activationDate.hashCode()); Object $lastCheckInDate = getLastCheckInDate(); result = result * 59 + (($lastCheckInDate == null) ? 43 : $lastCheckInDate.hashCode()); Object $machineType = getMachineType(); result = result * 59 + (($machineType == null) ? 43 : $machineType.hashCode()); Object $mtmCode = getMtmCode(); result = result * 59 + (($mtmCode == null) ? 43 : $mtmCode.hashCode()); Object $mtmDescription = getMtmDescription(); result = result * 59 + (($mtmDescription == null) ? 43 : $mtmDescription.hashCode()); Object $serialNo = getSerialNo(); result = result * 59 + (($serialNo == null) ? 43 : $serialNo.hashCode()); Object $imei1 = getImei1(); result = result * 59 + (($imei1 == null) ? 43 : $imei1.hashCode()); Object $imei2 = getImei2(); result = result * 59 + (($imei2 == null) ? 43 : $imei2.hashCode()); Object $shipDate = getShipDate(); result = result * 59 + (($shipDate == null) ? 43 : $shipDate.hashCode()); Object $chlPadCode = getChlPadCode(); result = result * 59 + (($chlPadCode == null) ? 43 : $chlPadCode.hashCode()); Object $swVersion = getSwVersion(); result = result * 59 + (($swVersion == null) ? 43 : $swVersion.hashCode()); Object $warrantyStartDate = getWarrantyStartDate(); result = result * 59 + (($warrantyStartDate == null) ? 43 : $warrantyStartDate.hashCode()); Object $warrantyEndDate = getWarrantyEndDate(); result = result * 59 + (($warrantyEndDate == null) ? 43 : $warrantyEndDate.hashCode()); Object $popDate = getPopDate(); result = result * 59 + (($popDate == null) ? 43 : $popDate.hashCode()); Object $soldToCustId = getSoldToCustId(); result = result * 59 + (($soldToCustId == null) ? 43 : $soldToCustId.hashCode()); Object $shipToCountryCode = getShipToCountryCode(); result = result * 59 + (($shipToCountryCode == null) ? 43 : $shipToCountryCode.hashCode()); Object $currWarrTypeCode = getCurrWarrTypeCode(); result = result * 59 + (($currWarrTypeCode == null) ? 43 : $currWarrTypeCode.hashCode()); Object $warrFlag = getWarrFlag(); result = result * 59 + (($warrFlag == null) ? 43 : $warrFlag.hashCode()); Object $swapImei = getSwapImei(); result = result * 59 + (($swapImei == null) ? 43 : $swapImei.hashCode()); Object $lastChangeTime = getLastChangeTime(); result = result * 59 + (($lastChangeTime == null) ? 43 : $lastChangeTime.hashCode()); Object $batchNumber = getBatchNumber(); result = result * 59 + (($batchNumber == null) ? 43 : $batchNumber.hashCode()); Object $factoryCode = getFactoryCode(); result = result * 59 + (($factoryCode == null) ? 43 : $factoryCode.hashCode()); Object $apc = getApc(); result = result * 59 + (($apc == null) ? 43 : $apc.hashCode()); Object $transModel = getTransModel(); result = result * 59 + (($transModel == null) ? 43 : $transModel.hashCode()); Object $mktModel = getMktModel(); result = result * 59 + (($mktModel == null) ? 43 : $mktModel.hashCode()); Object $servicePassword = getServicePassword(); result = result * 59 + (($servicePassword == null) ? 43 : $servicePassword.hashCode()); Object $ulma = getUlma(); result = result * 59 + (($ulma == null) ? 43 : $ulma.hashCode()); Object $renWarrCode = getRenWarrCode(); result = result * 59 + (($renWarrCode == null) ? 43 : $renWarrCode.hashCode()); Object $cancelCode = getCancelCode(); result = result * 59 + (($cancelCode == null) ? 43 : $cancelCode.hashCode()); Object $swapRefNo = getSwapRefNo(); result = result * 59 + (($swapRefNo == null) ? 43 : $swapRefNo.hashCode()); Object $swapCount = getSwapCount(); result = result * 59 + (($swapCount == null) ? 43 : $swapCount.hashCode()); Object $dsCustomerId = getDsCustomerId(); result = result * 59 + (($dsCustomerId == null) ? 43 : $dsCustomerId.hashCode()); Object $wlan = getWlan(); result = result * 59 + (($wlan == null) ? 43 : $wlan.hashCode()); Object $warrantyCountryCode = getWarrantyCountryCode(); result = result * 59 + (($warrantyCountryCode == null) ? 43 : $warrantyCountryCode.hashCode()); Object $hsn = getHsn(); result = result * 59 + (($hsn == null) ? 43 : $hsn.hashCode()); Object $wlan2 = getWlan2(); result = result * 59 + (($wlan2 == null) ? 43 : $wlan2.hashCode()); Object $wlan3 = getWlan3(); result = result * 59 + (($wlan3 == null) ? 43 : $wlan3.hashCode()); Object $wlan4 = getWlan4(); result = result * 59 + (($wlan4 == null) ? 43 : $wlan4.hashCode()); Object $lastRepairDate = getLastRepairDate(); result = result * 59 + (($lastRepairDate == null) ? 43 : $lastRepairDate.hashCode()); Object $repairCount = getRepairCount(); result = result * 59 + (($repairCount == null) ? 43 : $repairCount.hashCode()); Object $brand = getBrand(); return result * 59 + (($brand == null) ? 43 : $brand.hashCode()); } public String toString() { return "IbTLclaimWarrantyQueryOutboundHistory(claimId=" + getClaimId() + ", status=" + getStatus() + ", activationDate=" + getActivationDate() + ", lastCheckInDate=" + getLastCheckInDate() + ", machineType=" + getMachineType() + ", mtmCode=" + getMtmCode() + ", mtmDescription=" + getMtmDescription() + ", serialNo=" + getSerialNo() + ", imei1=" + getImei1() + ", imei2=" + getImei2() + ", shipDate=" + getShipDate() + ", chlPadCode=" + getChlPadCode() + ", swVersion=" + getSwVersion() + ", warrantyStartDate=" + getWarrantyStartDate() + ", warrantyEndDate=" + getWarrantyEndDate() + ", popDate=" + getPopDate() + ", soldToCustId=" + getSoldToCustId() + ", shipToCountryCode=" + getShipToCountryCode() + ", currWarrTypeCode=" + getCurrWarrTypeCode() + ", warrFlag=" + getWarrFlag() + ", swapImei=" + getSwapImei() + ", lastChangeTime=" + getLastChangeTime() + ", batchNumber=" + getBatchNumber() + ", factoryCode=" + getFactoryCode() + ", apc=" + getApc() + ", transModel=" + getTransModel() + ", mktModel=" + getMktModel() + ", servicePassword=" + getServicePassword() + ", ulma=" + getUlma() + ", renWarrCode=" + getRenWarrCode() + ", cancelCode=" + getCancelCode() + ", swapRefNo=" + getSwapRefNo() + ", swapCount=" + getSwapCount() + ", dsCustomerId=" + getDsCustomerId() + ", wlan=" + getWlan() + ", warrantyCountryCode=" + getWarrantyCountryCode() + ", hsn=" + getHsn() + ", wlan2=" + getWlan2() + ", wlan3=" + getWlan3() + ", wlan4=" + getWlan4() + ", lastRepairDate=" + getLastRepairDate() + ", repairCount=" + getRepairCount() + ", brand=" + getBrand() + ")"; }
/*    */   
/*    */   public int getClaimId() {
/* 10 */     return this.claimId;
/*    */   } public String getStatus() {
/* 12 */     return this.status;
/*    */   } public Date getActivationDate() {
/* 14 */     return this.activationDate;
/*    */   } public Date getLastCheckInDate() {
/* 16 */     return this.lastCheckInDate;
/*    */   } public String getMachineType() {
/* 18 */     return this.machineType;
/*    */   } public String getMtmCode() {
/* 20 */     return this.mtmCode;
/*    */   } public String getMtmDescription() {
/* 22 */     return this.mtmDescription;
/*    */   } public String getSerialNo() {
/* 24 */     return this.serialNo;
/*    */   } public String getImei1() {
/* 26 */     return this.imei1;
/*    */   } public String getImei2() {
/* 28 */     return this.imei2;
/*    */   } public Date getShipDate() {
/* 30 */     return this.shipDate;
/*    */   } public String getChlPadCode() {
/* 32 */     return this.chlPadCode;
/*    */   } public String getSwVersion() {
/* 34 */     return this.swVersion;
/*    */   } public Date getWarrantyStartDate() {
/* 36 */     return this.warrantyStartDate;
/*    */   } public Date getWarrantyEndDate() {
/* 38 */     return this.warrantyEndDate;
/*    */   } public Date getPopDate() {
/* 40 */     return this.popDate;
/*    */   } public String getSoldToCustId() {
/* 42 */     return this.soldToCustId;
/*    */   } public String getShipToCountryCode() {
/* 44 */     return this.shipToCountryCode;
/*    */   } public String getCurrWarrTypeCode() {
/* 46 */     return this.currWarrTypeCode;
/*    */   } public String getWarrFlag() {
/* 48 */     return this.warrFlag;
/*    */   } public String getSwapImei() {
/* 50 */     return this.swapImei;
/*    */   } public Date getLastChangeTime() {
/* 52 */     return this.lastChangeTime;
/*    */   } public String getBatchNumber() {
/* 54 */     return this.batchNumber;
/*    */   } public String getFactoryCode() {
/* 56 */     return this.factoryCode;
/*    */   } public String getApc() {
/* 58 */     return this.apc;
/*    */   } public String getTransModel() {
/* 60 */     return this.transModel;
/*    */   } public String getMktModel() {
/* 62 */     return this.mktModel;
/*    */   } public String getServicePassword() {
/* 64 */     return this.servicePassword;
/*    */   } public String getUlma() {
/* 66 */     return this.ulma;
/*    */   } public String getRenWarrCode() {
/* 68 */     return this.renWarrCode;
/*    */   } public String getCancelCode() {
/* 70 */     return this.cancelCode;
/*    */   } public String getSwapRefNo() {
/* 72 */     return this.swapRefNo;
/*    */   } public String getSwapCount() {
/* 74 */     return this.swapCount;
/*    */   } public String getDsCustomerId() {
/* 76 */     return this.dsCustomerId;
/*    */   } public String getWlan() {
/* 78 */     return this.wlan;
/*    */   } public String getWarrantyCountryCode() {
/* 80 */     return this.warrantyCountryCode;
/*    */   } public String getHsn() {
/* 82 */     return this.hsn;
/*    */   } public String getWlan2() {
/* 84 */     return this.wlan2;
/*    */   } public String getWlan3() {
/* 86 */     return this.wlan3;
/*    */   } public String getWlan4() {
/* 88 */     return this.wlan4;
/*    */   } public Date getLastRepairDate() {
/* 90 */     return this.lastRepairDate;
/*    */   } public String getRepairCount() {
/* 92 */     return this.repairCount;
/*    */   } public String getBrand() {
/* 94 */     return this.brand;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTLclaimWarrantyQueryOutboundHistory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */