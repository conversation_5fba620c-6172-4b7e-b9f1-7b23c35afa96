/*     */ package WEB-INF.classes;
/*     */ 
/*     */ import java.io.FileInputStream;
/*     */ import java.io.InputStream;
/*     */ import java.io.OutputStream;
/*     */ import java.security.KeyStore;
/*     */ import java.sql.Timestamp;
/*     */ import java.util.Arrays;
/*     */ import java.util.Base64;
/*     */ import javax.net.ssl.HostnameVerifier;
/*     */ import javax.net.ssl.KeyManager;
/*     */ import javax.net.ssl.KeyManagerFactory;
/*     */ import javax.net.ssl.SSLContext;
/*     */ import javax.net.ssl.SSLSocket;
/*     */ import javax.net.ssl.SSLSocketFactory;
/*     */ import javax.net.ssl.TrustManager;
/*     */ import javax.net.ssl.TrustManagerFactory;
/*     */ import okhttp3.MediaType;
/*     */ import okhttp3.OkHttpClient;
/*     */ import okhttp3.Request;
/*     */ import okhttp3.RequestBody;
/*     */ import okhttp3.Response;
/*     */ 
/*     */ 
/*     */ public class PkiConnectionTester
/*     */ {
/*     */   public static int expSize;
/*     */   private String pkiTrustCert;
/*     */   private String pkiTrustPass;
/*     */   private String pkiIdentityCert;
/*  31 */   public static byte[] SIGNED_MESSAGE = "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".getBytes(); private String pkiIdentityPass; private String pkiIp; private String nlPkIp;
/*     */   private int port;
/*     */   private boolean nodeLockingTest;
/*  34 */   public static String REQUEST = "{\n  \"tnlDbsRequest\": {\n    \"tnlRequestMessage\": \"<NodeLockingReqMessage>\",\n    \"dbsRequestMessage\": \"<dbsRequestMessage>\"\n  }\n}";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  48 */   public static String sample_reponse = "{\n\"tnlDbsResponse\": { \n\"tnlResponseMessage\": \"something\", \n\"dbsResponseMessage\": \"something\" \n}}";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public PkiConnectionTester(String env) {
/*  55 */     if (env.equalsIgnoreCase("LOCAL")) {
/*  56 */       this.pkiTrustCert = "C:\\certs\\pki.keystore";
/*  57 */       this.pkiTrustPass = "ZujuIduJyche7694";
/*  58 */       this.pkiIdentityCert = "C:\\certs\\ServiceCenterWebServices.pfx";
/*  59 */       this.pkiIdentityPass = "GoLuckiPhiDubu962";
/*  60 */       this.pkiIp = "***********";
/*  61 */       this.nlPkIp = "https://**************:2029/api/protocol/handler";
/*  62 */       this.port = 2002;
/*     */     }
/*  64 */     else if (env.equals("TEST")) {
/*  65 */       this.pkiTrustCert = "/opt/tomcat8/data/pki.keystore";
/*  66 */       this.pkiTrustPass = "ZujuIduJyche7694";
/*  67 */       this.pkiIdentityCert = "/opt/tomcat8/data/ServiceCenterWebServices.pfx";
/*  68 */       this.pkiIdentityPass = "GoLuckiPhiDubu962";
/*  69 */       this.pkiIp = "***********";
/*  70 */       this.nlPkIp = "https://**************:2029/api/protocol/handler";
/*  71 */       this.port = 2002;
/*     */     }
/*  73 */     else if (env.equals("PROD")) {
/*  74 */       this.pkiTrustCert = "/opt/tomcat8/data/pki.keystore";
/*  75 */       this.pkiTrustPass = "ZujuIduJyche7694";
/*  76 */       this.pkiIdentityCert = "/opt/tomcat8/data/ServiceCenterWebServices.pfx";
/*  77 */       this.pkiIdentityPass = "GoLuckiPhiDubu962";
/*  78 */       this.nlPkIp = "https://**************:2029/api/protocol/handler";
/*  79 */       this.pkiIp = "***********";
/*  80 */       this.port = 2002;
/*     */     }
/*  82 */     else if (env.equals("TESTHOME")) {
/*  83 */       this.pkiTrustCert = "/home/<USER>/pki.keystore";
/*  84 */       this.pkiTrustPass = "ZujuIduJyche7694";
/*  85 */       this.pkiIdentityCert = "/home/<USER>/ServiceCenterWebServices.pfx";
/*  86 */       this.pkiIdentityPass = "GoLuckiPhiDubu962";
/*  87 */       this.pkiIp = "***********";
/*  88 */       this.nlPkIp = "https://**************:2029/api/protocol/handler";
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void connect(byte[] signedMesage) throws Exception {
/*  94 */     if (!this.nodeLockingTest) {
/*  95 */       System.out.println("Connecting to non node locking server at " + this.pkiIp + " on port " + this.port);
/*  96 */       forwardToPKI(signedMesage, this.port, this.pkiIp);
/*     */       return;
/*     */     } 
/*  99 */     System.out.println("Connecting to node locking server at " + this.nlPkIp);
/* 100 */     forwardToNLPKI();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void forwardToNLPKI() throws Exception {
/* 289 */     int port = 2029;
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 294 */     String tnlmessage = Base64.getEncoder().encodeToString(SIGNED_MESSAGE);
/* 295 */     String dbReq = Base64.getEncoder().encodeToString(SIGNED_MESSAGE);
/*     */ 
/*     */     
/* 298 */     String sign_message = REQUEST.replace("<NodeLockingReqMessage>", tnlmessage);
/* 299 */     sign_message = sign_message.replace("<dbsRequestMessage>", dbReq);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 335 */       OkHttpClient client = getSSLClientForNLPKI();
/* 336 */       MediaType mediaType = MediaType.parse("application/json");
/* 337 */       RequestBody body = RequestBody.create(mediaType, sign_message);
/* 338 */       System.out.println("nlpkiurl set here is " + this.nlPkIp);
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 343 */       Request request = (new Request.Builder()).url(this.nlPkIp).method("POST", body).addHeader("Content-Type", "application/json").build();
/* 344 */       Response response = client.newCall(request).execute();
/* 345 */       String result = "";
/* 346 */       result = response.body().string();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 354 */       System.out.println("after calling new PKI NL server");
/* 355 */       System.out.println(result);
/* 356 */       System.out.println("SUCCESS");
/*     */     
/*     */     }
/* 359 */     catch (Exception ex) {
/* 360 */       System.out.println(ex);
/* 361 */       throw ex;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private OkHttpClient getSSLClientForNLPKI() throws Exception {
/*     */     try {
/* 378 */       System.out.println("Inside getSSLClientForNLPKI");
/*     */       
/* 380 */       KeyStore trustKeyStore = KeyStore.getInstance("JKS");
/* 381 */       trustKeyStore.load(new FileInputStream(this.pkiTrustCert), this.pkiTrustPass.toCharArray());
/* 382 */       TrustManagerFactory tmf = TrustManagerFactory.getInstance("SunX509", "SunJSSE");
/* 383 */       tmf.init(trustKeyStore);
/* 384 */       TrustManager[] tm = tmf.getTrustManagers();
/*     */       
/* 386 */       KeyStore keyStore = KeyStore.getInstance("PKCS12");
/* 387 */       keyStore.load(new FileInputStream(this.pkiIdentityCert), this.pkiIdentityPass.toCharArray());
/* 388 */       KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509", "SunJSSE");
/* 389 */       kmf.init(keyStore, this.pkiIdentityPass.toCharArray());
/* 390 */       KeyManager[] km = kmf.getKeyManagers();
/*     */       
/* 392 */       SSLContext sslContext = SSLContext.getInstance("TLSv1.2", "SunJSSE");
/* 393 */       sslContext.init(km, tm, null);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 407 */       OkHttpClient client = (new OkHttpClient.Builder()).hostnameVerifier((HostnameVerifier)new Object(this)).sslSocketFactory(sslContext.getSocketFactory()).build();
/* 408 */       return client;
/* 409 */     } catch (Exception ex) {
/* 410 */       System.out.println(ex);
/* 411 */       throw ex;
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void nlconnect(byte[] signed) {}
/*     */ 
/*     */   
/* 419 */   private final String DEFAULT_PKI_SERVER = "***********";
/*     */ 
/*     */ 
/*     */   
/*     */   private SSLSocket connect(int port, String pkiIp) throws Exception {
/* 424 */     KeyStore trustKeyStore = KeyStore.getInstance("JKS");
/* 425 */     trustKeyStore.load(new FileInputStream(this.pkiTrustCert), this.pkiTrustPass.toCharArray());
/* 426 */     TrustManagerFactory tmf = TrustManagerFactory.getInstance("SunX509", "SunJSSE");
/* 427 */     tmf.init(trustKeyStore);
/* 428 */     TrustManager[] tm = tmf.getTrustManagers();
/*     */     
/* 430 */     KeyStore keyStore = KeyStore.getInstance("PKCS12");
/* 431 */     keyStore.load(new FileInputStream(this.pkiIdentityCert), this.pkiIdentityPass.toCharArray());
/* 432 */     KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509", "SunJSSE");
/* 433 */     kmf.init(keyStore, this.pkiIdentityPass.toCharArray());
/* 434 */     KeyManager[] km = kmf.getKeyManagers();
/*     */     
/* 436 */     SSLContext sslContext = SSLContext.getInstance("TLSv1.2", "SunJSSE");
/* 437 */     sslContext.init(km, tm, null);
/* 438 */     SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
/*     */ 
/*     */     
/* 441 */     SSLSocket sslSocket = (SSLSocket)sslSocketFactory.createSocket(pkiIp, port);
/* 442 */     sslSocket.setEnabledProtocols(new String[] { "TLSv1.2" });
/*     */     
/* 444 */     Timestamp ts = new Timestamp(System.currentTimeMillis());
/* 445 */     int preTime = ts.getNanos();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 452 */     Timestamp ts1 = new Timestamp(System.currentTimeMillis());
/* 453 */     int postTime = ts1.getNanos();
/* 454 */     int timediff = (postTime - preTime) / 1000000;
/* 455 */     System.out.println("PKI access success, and costTime:" + timediff);
/* 456 */     return sslSocket;
/*     */   }
/*     */   
/*     */   public byte[] forwardToPKI(byte[] signedMessage, int port, String pkiIp) throws Exception {
/* 460 */     byte[] actualPKIResponse = null;
/*     */     try {
/* 462 */       SSLSocket sslSocket = connect(port, pkiIp);
/* 463 */       System.out.println("Got the SSLSocket. TLS version = " + sslSocket.getSession().getProtocol());
/* 464 */       OutputStream outToServer = sslSocket.getOutputStream();
/* 465 */       outToServer.write(signedMessage);
/* 466 */       outToServer.flush();
/*     */       
/* 468 */       sslSocket.setReceiveBufferSize(65535);
/* 469 */       InputStream inStream = sslSocket.getInputStream();
/* 470 */       byte[] pkiResponse = null;
/* 471 */       byte[] buff1 = new byte[2];
/* 472 */       int numRead = 0;
/* 473 */       while (numRead < buff1.length && numRead != -1) {
/* 474 */         numRead += inStream.read(buff1, numRead, buff1.length - numRead);
/* 475 */         System.out.println("numRead is : " + numRead);
/*     */       } 
/* 477 */       System.out.println("PKI return data: " + Arrays.toString(buff1));
/* 478 */       expSize = computeExpSize(buff1);
/* 479 */       System.out.println("PKI expSize:" + expSize);
/* 480 */       pkiResponse = new byte[expSize];
/* 481 */       if (expSize != 0) {
/* 482 */         System.arraycopy(buff1, 0, pkiResponse, 0, 2);
/* 483 */         while (numRead != expSize) {
/* 484 */           numRead += inStream.read(pkiResponse, numRead, expSize - numRead);
/*     */         }
/*     */       } 
/* 487 */       System.out.println("PKI return data pkiResponse:" + Arrays.toString(pkiResponse));
/* 488 */       int totalBytes = pkiResponse.length;
/* 489 */       actualPKIResponse = new byte[totalBytes];
/* 490 */       for (int i = 0; i < totalBytes; i++) {
/* 491 */         actualPKIResponse[i] = pkiResponse[i];
/*     */       }
/*     */       
/* 494 */       outToServer.close();
/* 495 */       inStream.close();
/* 496 */       sslSocket.close();
/* 497 */       System.out.println("SUCCESS");
/* 498 */     } catch (Exception exp) {
/*     */       
/* 500 */       System.out.println(exp);
/*     */     } 
/*     */     
/* 503 */     return actualPKIResponse;
/*     */   }
/*     */   
/*     */   private int computeExpSize(byte[] buff1) {
/* 507 */     String hexFirst = Integer.toHexString(buff1[0] & 0xFF);
/* 508 */     hexFirst = (hexFirst.length() == 1) ? ("0" + hexFirst) : hexFirst;
/* 509 */     String hexSecond = Integer.toHexString(buff1[1] & 0xFF);
/* 510 */     hexSecond = (hexSecond.length() == 1) ? ("0" + hexSecond) : hexSecond;
/* 511 */     return Integer.parseInt(hexFirst + hexSecond, 16);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] args) {
/* 528 */     byte[] signedMessage = SIGNED_MESSAGE;
/* 529 */     if (args.length == 5)
/*     */     {
/*     */       
/* 532 */       signedMessage = args[4].getBytes();
/*     */     }
/* 534 */     String ipAddress = args[0].trim();
/* 535 */     String port = args[1].trim();
/* 536 */     String env = args[2];
/* 537 */     String mode = args[3];
/* 538 */     PkiConnectionTester tester = new PkiConnectionTester(env);
/* 539 */     if (!ipAddress.equalsIgnoreCase("")) {
/* 540 */       tester.pkiIp = ipAddress;
/*     */     }
/* 542 */     if (!port.equalsIgnoreCase("")) {
/* 543 */       tester.port = Integer.parseInt(port);
/*     */     }
/* 545 */     tester.nodeLockingTest = mode.equalsIgnoreCase("NODELOCKING");
/*     */     try {
/* 547 */       tester.connect(signedMessage);
/* 548 */     } catch (Exception e) {
/* 549 */       System.out.println(e.getMessage());
/*     */     } 
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\PkiConnectionTester.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */