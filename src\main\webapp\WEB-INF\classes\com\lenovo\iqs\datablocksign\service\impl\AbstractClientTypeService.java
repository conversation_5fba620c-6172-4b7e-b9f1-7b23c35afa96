/*     */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.service.impl;
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.fasterxml.jackson.databind.ObjectMapper;
/*     */ import com.google.gson.JsonArray;
/*     */ import com.google.gson.JsonObject;
/*     */ import com.google.gson.JsonParser;
/*     */ import com.lenovo.iqs.dao.IbTUpdConfigMapper;
/*     */ import com.lenovo.iqs.datablocksign.Constants.ClientTypeEnum;
/*     */ import com.lenovo.iqs.datablocksign.bean.ClientResponse;
/*     */ import com.lenovo.iqs.datablocksign.bean.Config;
/*     */ import com.lenovo.iqs.datablocksign.bean.DataBlock;
/*     */ import com.lenovo.iqs.datablocksign.bean.ParseResponse;
/*     */ import com.lenovo.iqs.datablocksign.bean.PkiNLHTTPSMessage;
/*     */ import com.lenovo.iqs.datablocksign.bean.PkiNodeLockingResponse;
/*     */ import com.lenovo.iqs.datablocksign.bean.RSDRules;
/*     */ import com.lenovo.iqs.datablocksign.bean.RSDValidationRequest;
/*     */ import com.lenovo.iqs.datablocksign.bean.RSDValidationResponse;
/*     */ import com.lenovo.iqs.datablocksign.bean.RequestBean;
/*     */ import com.lenovo.iqs.datablocksign.service.RsdValidationService;
/*     */ import com.lenovo.iqs.datablocksign.utils.HexUtil;
/*     */ import com.lenovo.iqs.utils.SpringHelper;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.IOException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.Base64;
/*     */ import java.util.Date;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import org.apache.cxf.common.util.StringUtils;
/*     */ import org.apache.cxf.message.Message;
/*     */ import org.apache.cxf.phase.PhaseInterceptorChain;
/*     */ import org.apache.logging.log4j.LogManager;
/*     */ import org.springframework.beans.factory.annotation.Autowired;
/*     */ 
/*     */ public abstract class AbstractClientTypeService implements ClientTypeService {
/*  39 */   private static final Logger log = LogManager.getLogger(com.lenovo.iqs.datablocksign.service.impl.AbstractClientTypeService.class);
/*     */   
/*     */   public static final String REQUEST_PROCESSOR_UID = "request_processor_uid";
/*     */   
/*     */   @Autowired
/*     */   private PkiConnectionService pkiConnectionService;
/*     */   
/*     */   @Autowired
/*     */   private IbTUpdConfigMapper ibTUpdConfigMapper;
/*     */   @Autowired
/*     */   private SAPRfcs sapRfcs;
/*     */   @Autowired
/*     */   private RsdValidationService rsdValidator;
/*     */   private static final String BACKUP_PKI_SERVER = "**************";
/*     */   private static final String DEFAUL_PKI_SERVER = "***********";
/*     */   
/*     */   protected ClientResponse validateReqParam(RequestBean requestBean) {
/*  56 */     String hexCRC = CRCUtils.caculateHexCRC(getBytesWOCRC(requestBean));
/*  57 */     ClientResponse response = null;
/*     */     
/*  59 */     log.info("rb here-->  " + requestBean.toString());
/*     */     
/*  61 */     String clientIP = requestBean.getIstrClientIP();
/*  62 */     String clientType = requestBean.getIstrClientReqType();
/*  63 */     String rsdLogId = requestBean.getIstrRsdLogId();
/*     */ 
/*     */     
/*  66 */     String blockRsdEmpty = this.ibTUpdConfigMapper.selectOneRecord("DATABLOCK_SIGN", "BLOCK_RSD_EMPTY");
/*     */     
/*  68 */     if (StringUtils.isEmpty(rsdLogId) && "Y".equalsIgnoreCase(blockRsdEmpty)) {
/*  69 */       response = new ClientResponse();
/*  70 */       String[] error = "7056,RSD_LOGID is empty and configured blocking requeset".split(",");
/*  71 */       response.setIstrStatusCode(error[0]);
/*  72 */       response.setIstrStatusData(error[1]);
/*  73 */       return response;
/*     */     } 
/*     */     
/*  76 */     if (ClientTypeEnum.isCheckForbidden(clientType)) {
/*     */       boolean isPrepaidAllowed;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*     */       try {
/*  91 */         dataBlockParseNew(requestBean, getDatablockVersion(requestBean));
/*     */       }
/*  93 */       catch (Exception e) {
/*  94 */         log.error("serial_no : " + requestBean.getIstrOldIMEI() + " pase requestparam error!");
/*  95 */         log.error(e);
/*  96 */         response = new ClientResponse();
/*  97 */         String[] error = "7053,Datablock parsing error".split(",");
/*  98 */         response.setIstrStatusCode(error[0]);
/*  99 */         response.setIstrStatusData(error[1]);
/* 100 */         return response;
/*     */       } 
/*     */       
/* 103 */       if (StringUtils.isEmpty(requestBean.getDataBlock()) || requestBean.getDataBlock().indexOf("error_code") > -1) {
/* 104 */         response = new ClientResponse();
/* 105 */         String[] error = "7056,datablock in request is invalid".split(",");
/* 106 */         response.setIstrStatusCode(error[0]);
/* 107 */         response.setIstrStatusData(error[1]);
/* 108 */         return response;
/*     */       } 
/*     */ 
/*     */       
/* 112 */       JSONObject jsonObject = JSON.parseObject(requestBean.getDataBlock());
/* 113 */       log.info(requestBean.getDataBlock());
/* 114 */       log.info("DEBUG");
/* 115 */       log.info(requestBean);
/* 116 */       String etokenIP = jsonObject.getString("etoken_ip");
/* 117 */       if (StringUtils.isEmpty(etokenIP) || !etokenIP.equals(requestBean.getIstrClientIP())) {
/* 118 */         String[] error = "7055,etoken missing or etoken in datablock does not match".split(",");
/* 119 */         response = new ClientResponse();
/* 120 */         response.setIstrStatusCode(error[0]);
/* 121 */         response.setIstrStatusData(error[1]);
/* 122 */         return response;
/*     */       } 
/*     */       
/* 125 */       String userID = jsonObject.getString("UserID");
/* 126 */       if (StringUtils.isEmpty(userID) || !userID.equalsIgnoreCase(requestBean.getUserId())) {
/* 127 */         String[] error = "7062,UserId missing or UserId in datablock does not match".split(",");
/* 128 */         response = new ClientResponse();
/* 129 */         response.setIstrStatusCode(error[0]);
/* 130 */         response.setIstrStatusData(error[1]);
/* 131 */         return response;
/*     */       } 
/*     */ 
/*     */       
/* 135 */       if (StringUtils.isEmpty(requestBean.getIstrMASCID()) || requestBean.getIstrMASCID().length() < 8) {
/* 136 */         String[] error = "7063, Mascid does not meet length requirement".split(",");
/* 137 */         response = new ClientResponse();
/* 138 */         response.setIstrStatusCode(error[0]);
/* 139 */         response.setIstrStatusData(error[1]);
/* 140 */         return response;
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 146 */       if ("3.4".equalsIgnoreCase(getDatablockVersion(requestBean))) {
/* 147 */         log.info(" dbs version 3.4 block executed");
/* 148 */         if (ClientTypeEnum.IMEI.clientType.equalsIgnoreCase(clientType) || ClientTypeEnum.CID.clientType.equalsIgnoreCase(clientType)) {
/* 149 */           String serialFromDatablock = getSerialNoFromDatablock(requestBean, clientType);
/* 150 */           if (StringUtils.isEmpty(serialFromDatablock)) {
/* 151 */             log.info("DatablockSign validate error: request serial is " + requestBean.getIstrOldIMEI() + "; serial from datablock is " + serialFromDatablock);
/* 152 */             response = new ClientResponse();
/* 153 */             String[] error = "7050, Serial from DataBlock is not match with request serial".split(",");
/* 154 */             response.setIstrStatusCode(error[0]);
/* 155 */             response.setIstrStatusData(error[1]);
/* 156 */             return response;
/*     */           } 
/*     */         } 
/*     */       } else {
/* 160 */         log.info(" dbs version 3.5 block executed");
/* 161 */         if (ClientTypeEnum.IMEI.clientType.equalsIgnoreCase(clientType) || ClientTypeEnum.CID.clientType.equalsIgnoreCase(clientType)) {
/* 162 */           log.info("parser result before the change --> " + requestBean.getDataBlock());
/*     */           
/* 164 */           String db_array = jsonObject.getString("datablocks");
/* 165 */           log.info(db_array + "--->  data blocks");
/* 166 */           JsonArray jArray = (new JsonParser()).parse(db_array).getAsJsonArray();
/* 167 */           List<String> IMEI_FROM_DB = new ArrayList<>();
/* 168 */           for (int i = 0; i < jArray.size(); i++) {
/* 169 */             JsonObject db = jArray.get(i).getAsJsonObject();
/* 170 */             IMEI_FROM_DB.add(db.get("serial_number").getAsString().substring(1, 15));
/*     */           } 
/*     */           
/* 173 */           log.info(IMEI_FROM_DB + "--->  IMEI_FROM_DB from blocks");
/*     */           
/* 175 */           String imei_from_Req = requestBean.getIstrOldIMEI().substring(0, 14);
/* 176 */           String new_imei_from_Req = requestBean.getIstrNewIMEI().substring(0, 14);
/* 177 */           log.info(imei_from_Req);
/* 178 */           log.info(new_imei_from_Req);
/* 179 */           boolean db_match = false;
/* 180 */           for (String imei_from_db : IMEI_FROM_DB) {
/* 181 */             if (!db_match) {
/* 182 */               db_match = (imei_from_db.matches(imei_from_Req) || imei_from_db.matches(new_imei_from_Req));
/*     */             }
/*     */           } 
/*     */ 
/*     */           
/* 187 */           log.info(db_match + "----> result of imei check");
/* 188 */           if (StringUtils.isEmpty(IMEI_FROM_DB) || !db_match) {
/* 189 */             log.info("new block by genius");
/* 190 */             log.info("DatablockSign validate error: request serial is " + requestBean.getIstrOldIMEI() + "; serial from datablock is " + IMEI_FROM_DB);
/* 191 */             response = new ClientResponse();
/* 192 */             String[] error = "7050, Serial from DataBlock is not match with request serial".split(",");
/* 193 */             response.setIstrStatusCode(error[0]);
/* 194 */             response.setIstrStatusData(error[1]);
/* 195 */             return response;
/*     */           } 
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 201 */       String imei = requestBean.getIstrOldIMEI();
/* 202 */       String processorId = jsonObject.getString("request_processor_uid");
/*     */       
/* 204 */       String mappingImei = "-1";
/* 205 */       boolean pidForbidden = true;
/*     */       
/*     */       try {
/* 208 */         mappingImei = this.sapRfcs.getIMEIByProcessorId(processorId);
/* 209 */         requestBean.setProcessorIdMappingImei(mappingImei);
/* 210 */         log.info("processsorId: " + processorId + "; MappingIMEI:" + mappingImei);
/* 211 */       } catch (Exception e) {
/* 212 */         log.info("getIMEIByProcessorId error for IMEI:" + imei + " pid: " + processorId);
/* 213 */         log.info("Trying to get IMEI list from the PID");
/*     */         
/*     */         try {
/* 216 */           List<String> eImei = new ArrayList<>();
/* 217 */           eImei = this.sapRfcs.getIMEIListByProcessorId(processorId);
/* 218 */           log.info("IMEI from PID : " + eImei);
/* 219 */           if (eImei.size() == 1) {
/* 220 */             mappingImei = eImei.get(0);
/* 221 */           } else if (eImei.size() > 0) {
/* 222 */             log.info("imei : " + imei);
/* 223 */             Iterator<String> iterator = eImei.iterator();
/* 224 */             String tempImei = "";
/* 225 */             while (iterator.hasNext()) {
/* 226 */               tempImei = iterator.next();
/* 227 */               log.info("tempImei : " + tempImei);
/* 228 */               if (tempImei.trim().equalsIgnoreCase(imei.trim())) {
/* 229 */                 mappingImei = tempImei;
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/* 234 */           if (mappingImei == "-1" && eImei.size() > 0) {
/* 235 */             Iterator<String> iterator = eImei.iterator();
/* 236 */             String tempImei = "";
/* 237 */             while (iterator.hasNext()) {
/* 238 */               tempImei = iterator.next();
/* 239 */               if (this.ibTUpdConfigMapper.countForbiddenIMEI(tempImei) <= 0 || this.ibTUpdConfigMapper
/* 240 */                 .countExceptionIMEI(tempImei) != 0) {
/* 241 */                 log.info(tempImei + " found as non-prepaid or in exception table.");
/* 242 */                 pidForbidden = false;
/* 243 */                 mappingImei = tempImei;
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/* 248 */           requestBean.setProcessorIdMappingImei(mappingImei);
/* 249 */           log.info("processsorId: " + processorId + "; MappingIMEI:" + mappingImei);
/* 250 */         } catch (Exception ex) {
/* 251 */           log.info("getIMEIByProcessorId error for IMEI:" + imei + " pid: " + processorId);
/* 252 */           log.error("getIMEIByProcessorId error for IMEI:" + imei + " pid: " + processorId, ex);
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 258 */       if (this.ibTUpdConfigMapper.countBySectionAndKey("DATABLOCK_SIGN", "SKIP_RSD_VALIDATION") == 0) {
/* 259 */         log.info("Switch not found. Using RSD Validation");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 265 */         String cid = "";
/* 266 */         if (requestBean.getIstrClientReqType().equalsIgnoreCase("0x02")) {
/*     */           try {
/* 268 */             cid = parserOutputReader(requestBean.getDataBlock());
/*     */           }
/* 270 */           catch (IOException e) {
/*     */             
/* 272 */             e.printStackTrace();
/* 273 */             log.error("serial_no : " + requestBean.getIstrOldIMEI() + " pase requestparam error!");
/* 274 */             log.error(e);
/* 275 */             response = new ClientResponse();
/* 276 */             String[] error = "7053,Datablock parsing error".split(",");
/* 277 */             response.setIstrStatusCode(error[0]);
/* 278 */             response.setIstrStatusData("NEW RSD Implementation parsing error");
/* 279 */             return response;
/*     */           } 
/*     */         }
/*     */ 
/*     */ 
/*     */         
/* 285 */         RSDValidationRequest request = buildValidationRequest(etokenIP, requestBean.getPublicIP(), requestBean.getIstrClientReqType(), requestBean.getUserId(), cid);
/* 286 */         log.info(request);
/*     */         
/*     */         try {
/* 289 */           RSDValidationResponse rsdresponse = this.rsdValidator.validateRequest(request);
/* 290 */           log.info("validation step completed");
/* 291 */           requestBean.setRsdResponse(rsdresponse.toString());
/* 292 */           List<Config> configs = this.ibTUpdConfigMapper.getConfigRules();
/* 293 */           RSDRules RSDValidation = new RSDRules(configs, rsdresponse);
/* 294 */           if (!RSDValidation.isEtokenAllowed()) {
/* 295 */             log.info("Error while validating request by RSD Etoken not allowed" + imei);
/* 296 */             response = new ClientResponse();
/* 297 */             String[] error = "7058, Etoken Not allowed as per RSD validation".split(",");
/* 298 */             response.setIstrStatusCode(error[0]);
/* 299 */             response.setIstrStatusData(error[1]);
/* 300 */             return response;
/*     */           } 
/* 302 */           if (!RSDValidation.isPublicIPAllowed()) {
/* 303 */             log.info("Error while validating request by RSD publicip not allowed" + imei);
/* 304 */             response = new ClientResponse();
/* 305 */             String[] error = "7059, publicip Not allowed as per RSD validation".split(",");
/* 306 */             response.setIstrStatusCode(error[0]);
/* 307 */             response.setIstrStatusData(error[1]);
/* 308 */             return response;
/*     */           } 
/* 310 */           if (!RSDValidation.isUserIdAllowed()) {
/* 311 */             log.info("Error while validating request by RSD userid not allowed" + imei);
/* 312 */             response = new ClientResponse();
/* 313 */             String[] error = "7060, userid Not allowed as per RSD validation".split(",");
/* 314 */             response.setIstrStatusCode(error[0]);
/* 315 */             response.setIstrStatusData(error[1]);
/* 316 */             return response;
/*     */           } 
/* 318 */           if (!RSDValidation.isReqTypeAllowed()) {
/* 319 */             log.info("Error while validating request by RSD reType not allowed for user/etoken" + imei);
/* 320 */             response = new ClientResponse();
/* 321 */             String[] error = "7061, reqType Not allowed as per RSD validation".split(",");
/* 322 */             response.setIstrStatusCode(error[0]);
/* 323 */             response.setIstrStatusData(error[1]);
/* 324 */             return response;
/*     */           } 
/* 326 */           isPrepaidAllowed = RSDValidation.isPrepaidAllowed();
/* 327 */         } catch (Exception e) {
/* 328 */           log.error(e.getMessage());
/* 329 */           log.info("Error while validating request by RSD" + imei);
/* 330 */           requestBean.setRsdResponse(e.getMessage());
/* 331 */           response = new ClientResponse();
/* 332 */           String[] error = "7057, RSD Validation service failed with exception".split(",");
/* 333 */           response.setIstrStatusCode(error[0]);
/* 334 */           response.setIstrStatusData(error[1]);
/* 335 */           return response;
/*     */         } 
/*     */       } else {
/* 338 */         log.info("Switch found. Using RSD Validation");
/* 339 */         isPrepaidAllowed = (this.ibTUpdConfigMapper.countBySectionAndKey("EtokenIP", etokenIP) != 0);
/* 340 */         log.info("isPrepaidallowed--> " + isPrepaidAllowed);
/*     */       } 
/*     */ 
/*     */       
/* 344 */       if (!isPrepaidAllowed) {
/* 345 */         if ("-1".equalsIgnoreCase(mappingImei)) {
/* 346 */           String blockMappingIMEI = this.ibTUpdConfigMapper.selectOneRecord("DATABLOCK_SIGN", "BLOCK_MAPPINGIMEI_EMPTY");
/* 347 */           if ("Y".equalsIgnoreCase(blockMappingIMEI)) {
/* 348 */             String[] error = "7054,Did not find PID Mapping IMEI".split(",");
/* 349 */             response = new ClientResponse();
/* 350 */             response.setIstrStatusCode(error[0]);
/* 351 */             response.setIstrStatusData(error[1]);
/* 352 */             return response;
/*     */           } 
/*     */         } else {
/* 355 */           imei = mappingImei;
/*     */         } 
/*     */         
/* 358 */         if (pidForbidden && this.ibTUpdConfigMapper
/* 359 */           .countForbiddenIMEI(imei) > 0 && this.ibTUpdConfigMapper.countExceptionIMEI(imei) == 0) {
/* 360 */           log.info("DatablockSign validate error: request serial is " + requestBean.getIstrOldIMEI() + "is forbidden!");
/* 361 */           requestBean.setError("IMEI: " + imei + " in exclude list");
/* 362 */           response = new ClientResponse();
/* 363 */           String[] error = "7051,Serial is Forbidden".split(",");
/* 364 */           response.setIstrStatusCode(error[0]);
/* 365 */           response.setIstrStatusData(error[1]);
/* 366 */           return response;
/*     */         } 
/*     */       } else {
/* 369 */         requestBean.setEtokenInException(true);
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 458 */     if (!hexCRC.equalsIgnoreCase(requestBean.getIstrCrc32())) {
/* 459 */       response = new ClientResponse();
/* 460 */       String[] error = "8023,CRC Check Failed".split(",");
/* 461 */       response.setIstrStatusCode(error[0]);
/* 462 */       response.setIstrStatusData(error[1]);
/* 463 */       return response;
/*     */     } 
/*     */     
/* 466 */     if (ClientTypeEnum.IMEI.clientType.equals(clientType) || ClientTypeEnum.SIMLock.clientType.equals(clientType) || ClientTypeEnum.WhiteListSerial.clientType
/* 467 */       .equals(clientType) || ClientTypeEnum.JanusOrIPRMKey.clientType.equals(clientType))
/*     */     {
/* 469 */       if (StringUtils.isEmpty(requestBean.getIstrOldIMEI()) || StringUtils.isEmpty(requestBean.getIstrNewIMEI()) || 
/* 470 */         StringUtils.isEmpty(requestBean.getIstrPassChgRequd())) {
/* 471 */         response = new ClientResponse();
/* 472 */         String[] error = "8045,oldIMEI or newIMEI or passChangereq is null".split(",");
/* 473 */         response.setIstrStatusCode(error[0]);
/* 474 */         response.setIstrStatusData(error[1]);
/* 475 */         return response;
/*     */       } 
/*     */     }
/* 478 */     String serialNoType = getSerialNoType(requestBean);
/* 479 */     if (ClientTypeEnum.WhiteListSerial.clientType.equals(clientType)) {
/* 480 */       IbTUpdConfigMapper mapper = (IbTUpdConfigMapper)SpringHelper.getBean(IbTUpdConfigMapper.class);
/* 481 */       int cnt = mapper.countBySectionAndKey("DBS_WHITE_LIST_SN", serialNoType);
/* 482 */       if (cnt == 0) {
/* 483 */         response = new ClientResponse();
/* 484 */         String[] error = "8002,Not a valid Serial Number Type".split(",");
/* 485 */         response.setIstrStatusCode(error[0]);
/* 486 */         response.setIstrStatusData(error[1]);
/* 487 */         return response;
/*     */       } 
/* 489 */     } else if (!"00".equals(serialNoType) && !"04".equals(serialNoType) && !"05".equals(serialNoType)) {
/* 490 */       response = new ClientResponse();
/* 491 */       String[] error = "8002,Not a valid Serial Number Type".split(",");
/* 492 */       response.setIstrStatusCode(error[0]);
/* 493 */       response.setIstrStatusData(error[1]);
/* 494 */       return response;
/*     */     } 
/*     */     
/* 497 */     if (!ClientTypeEnum.contains(clientType)) {
/* 498 */       response = new ClientResponse();
/* 499 */       String[] error = "8026,Invalid Data Block Signing Request Type".split(",");
/* 500 */       response.setIstrStatusCode(error[0]);
/* 501 */       response.setIstrStatusData(error[1]);
/* 502 */       return response;
/*     */     } 
/*     */     
/* 505 */     return null;
/*     */   }
/*     */   
/*     */   private void dataBlockParseNew(RequestBean requestBean, String dbs_version_no) throws Exception {
/* 509 */     String ret = "";
/* 510 */     Message message = PhaseInterceptorChain.getCurrentMessage();
/* 511 */     HttpServletRequest httpRequest = (HttpServletRequest)message.get("HTTP.REQUEST");
/*     */ 
/*     */     
/* 514 */     String path = "";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 519 */     log.info("dbs_version_no is -->" + dbs_version_no);
/* 520 */     path = httpRequest.getSession().getServletContext().getRealPath("/WEB-INF/ParseLog_v03.py");
/*     */ 
/*     */     
/* 523 */     String command = "python2.7 " + path + " " + Base64.getEncoder().encodeToString(requestBean.getIstrReqParam());
/* 524 */     Process p = Runtime.getRuntime().exec(command);
/* 525 */     BufferedReader in = new BufferedReader(new InputStreamReader(p.getInputStream()));
/*     */     String line;
/* 527 */     while ((line = in.readLine()) != null) {
/* 528 */       ret = ret + line;
/* 529 */       ret = ret + System.lineSeparator();
/*     */     } 
/* 531 */     log.info(ret);
/* 532 */     requestBean.setDataBlock(ret);
/*     */   }
/*     */   
/*     */   protected String getSerialNo(RequestBean requestBean) {
/* 536 */     int serialNoiSze = requestBean.getIstrReqParam()[5];
/* 537 */     byte[] byteSerialNoArray = new byte[serialNoiSze];
/* 538 */     System.arraycopy(requestBean.getIstrReqParam(), 6, byteSerialNoArray, 0, serialNoiSze);
/* 539 */     String hexValue = HexUtil.toTwoDigitHexString(byteSerialNoArray);
/* 540 */     String strIMEI = "";
/* 541 */     for (int i = 0; i < hexValue.length() - 2; i += 2) {
/* 542 */       strIMEI = strIMEI + hexValue.charAt(i) + hexValue.charAt(i + 3);
/*     */     }
/* 544 */     return HexUtil.CalcImeiCRC(strIMEI);
/*     */   }
/*     */   
/*     */   protected String getSerialNoFromDatablock(RequestBean requestBean, String clienType) {
/* 548 */     String hexReqParam = Hex.encodeHexString(requestBean.getIstrReqParam());
/* 549 */     String imeiInHex = "";
/* 550 */     if ("0x02".equalsIgnoreCase(clienType)) {
/* 551 */       imeiInHex = hexReqParam.substring(348, 364);
/* 552 */     } else if ("0x00".equalsIgnoreCase(clienType)) {
/* 553 */       imeiInHex = hexReqParam.substring(336, 352);
/*     */     } 
/* 555 */     String imei = "";
/* 556 */     for (int i = 0; i < imeiInHex.length(); i += 2) {
/* 557 */       char v1 = imeiInHex.charAt(i);
/* 558 */       char v2 = imeiInHex.charAt(i + 1);
/* 559 */       if (i != 0) {
/* 560 */         imei = imei + Character.toString(v2);
/*     */       }
/* 562 */       imei = imei + Character.toString(v1);
/*     */     } 
/*     */     
/* 565 */     String meid = "";
/* 566 */     for (int j = imeiInHex.length() - 4; j >= 0; j -= 4) {
/* 567 */       int end1 = Math.min(j + 2, imeiInHex.length() - 1);
/* 568 */       int end2 = Math.min(j + 4, imeiInHex.length() - 1);
/* 569 */       String s1 = imeiInHex.substring(j, end1);
/* 570 */       String s2 = imeiInHex.substring(j + 2, end2);
/* 571 */       meid = meid.concat(s2);
/* 572 */       meid = meid.concat(s1);
/*     */     } 
/*     */     
/* 575 */     String requestSerialNo = requestBean.getIstrOldIMEI().substring(0, 14);
/* 576 */     if (requestSerialNo.equalsIgnoreCase(imeiInHex.substring(0, 14)))
/* 577 */       return imeiInHex; 
/* 578 */     if (requestSerialNo.equalsIgnoreCase(imei.substring(0, 14)))
/* 579 */       return imei; 
/* 580 */     if (requestSerialNo.equalsIgnoreCase(meid.substring(1, 15))) {
/* 581 */       return meid;
/*     */     }
/* 583 */     return "";
/*     */   }
/*     */   
/*     */   public byte[] getBytesWOCRC(RequestBean requestBean) {
/* 587 */     String istrClientIP = requestBean.getIstrClientIP();
/* 588 */     String istrMASCID = requestBean.getIstrMASCID();
/* 589 */     String istrClientReqType = requestBean.getIstrClientReqType();
/* 590 */     String istrOldIMEI = requestBean.getIstrOldIMEI();
/* 591 */     String istrNewIMEI = requestBean.getIstrNewIMEI();
/* 592 */     String istrPassChgRequd = requestBean.getIstrPassChgRequd();
/* 593 */     byte[] istrReqParam = requestBean.getIstrReqParam();
/* 594 */     String istrRsdLogId = requestBean.getIstrRsdLogId();
/*     */     
/* 596 */     int rsdLogIdLength = StringUtils.isEmpty(istrRsdLogId) ? 0 : istrRsdLogId.length();
/*     */ 
/*     */     
/* 599 */     byte[] byteValues = new byte[istrClientIP.length() + istrMASCID.length() + istrClientReqType.length() + istrOldIMEI.length() + istrNewIMEI.length() + istrPassChgRequd.length() + istrReqParam.length + rsdLogIdLength];
/*     */     
/* 601 */     int destPos = 0;
/*     */     
/* 603 */     System.arraycopy(istrClientIP.getBytes(), 0, byteValues, destPos, (istrClientIP.getBytes()).length);
/* 604 */     destPos = (istrClientIP.getBytes()).length;
/*     */     
/* 606 */     System.arraycopy(istrMASCID.getBytes(), 0, byteValues, destPos, (istrMASCID.getBytes()).length);
/* 607 */     destPos += (istrMASCID.getBytes()).length;
/*     */     
/* 609 */     System.arraycopy(istrClientReqType.getBytes(), 0, byteValues, destPos, (istrClientReqType.getBytes()).length);
/* 610 */     destPos += (istrClientReqType.getBytes()).length;
/*     */     
/* 612 */     System.arraycopy(istrOldIMEI.getBytes(), 0, byteValues, destPos, (istrOldIMEI.getBytes()).length);
/* 613 */     destPos += (istrOldIMEI.getBytes()).length;
/*     */     
/* 615 */     System.arraycopy(istrNewIMEI.getBytes(), 0, byteValues, destPos, (istrNewIMEI.getBytes()).length);
/* 616 */     destPos += (istrNewIMEI.getBytes()).length;
/*     */     
/* 618 */     System.arraycopy(istrPassChgRequd.getBytes(), 0, byteValues, destPos, (istrPassChgRequd.getBytes()).length);
/* 619 */     destPos += (istrPassChgRequd.getBytes()).length;
/*     */     
/* 621 */     if (rsdLogIdLength != 0) {
/* 622 */       System.arraycopy(istrRsdLogId.getBytes(), 0, byteValues, destPos, (istrRsdLogId.getBytes()).length);
/* 623 */       destPos += (istrRsdLogId.getBytes()).length;
/*     */     } 
/*     */     
/* 626 */     System.arraycopy(istrReqParam, 0, byteValues, destPos, istrReqParam.length);
/* 627 */     destPos += istrReqParam.length;
/*     */ 
/*     */     
/* 630 */     return byteValues;
/*     */   }
/*     */   
/*     */   public String getSerialNoType(RequestBean requestBean) {
/* 634 */     String serialNoType = Integer.toHexString(requestBean.getIstrReqParam()[4]);
/* 635 */     return (serialNoType.length() == 1) ? ("0" + serialNoType) : serialNoType;
/*     */   }
/*     */   
/*     */   public int getDataBlockType(RequestBean requestBean) {
/* 639 */     byte[] istrReqParam = requestBean.getIstrReqParam();
/* 640 */     String istrClientReqType = requestBean.getIstrClientReqType();
/*     */     
/* 642 */     String protocolVersion = Integer.toHexString(istrReqParam[2]);
/* 643 */     protocolVersion = (protocolVersion.length() == 1) ? ("0" + protocolVersion) : protocolVersion;
/*     */     
/* 645 */     int dataBlockType = 0;
/* 646 */     if (ClientTypeEnum.JanusOrIPRMKey.clientType.equals(istrClientReqType)) {
/* 647 */       int numOfPkiDataTypes = Integer.parseInt("" + istrReqParam[5], 16);
/* 648 */       byte[] pkiTypeArray = null;
/*     */       
/* 650 */       if ("02".equals(protocolVersion)) {
/* 651 */         pkiTypeArray = new byte[32];
/* 652 */         System.arraycopy(istrReqParam, 6, pkiTypeArray, 0, pkiTypeArray.length);
/* 653 */         for (int k = 0, j = 1; k < pkiTypeArray.length && j <= numOfPkiDataTypes; k += 2, j++) {
/* 654 */           String pkiType = HexUtil.toTwoDigitHexString(pkiTypeArray[k]) + HexUtil.toTwoDigitHexString(pkiTypeArray[k + 1]);
/* 655 */           IbTUpdConfigMapper mapper = (IbTUpdConfigMapper)SpringHelper.getBean(IbTUpdConfigMapper.class);
/* 656 */           int cnt = mapper.countBySectionAndKey("DBS_PKI_TYPE", pkiType);
/* 657 */           if (cnt == 0) {
/* 658 */             dataBlockType = 7;
/* 659 */             return dataBlockType;
/*     */           } 
/*     */         } 
/*     */       } 
/* 663 */       if ("03".equals(protocolVersion)) {
/* 664 */         pkiTypeArray = new byte[64];
/* 665 */         System.arraycopy(istrReqParam, 6, pkiTypeArray, 0, pkiTypeArray.length);
/* 666 */         for (int k = 0, j = 1; k < pkiTypeArray.length && j <= numOfPkiDataTypes; k += 4, j++) {
/* 667 */           String pkiType = HexUtil.toTwoDigitHexString(pkiTypeArray[k + 2]) + HexUtil.toTwoDigitHexString(pkiTypeArray[k + 3]);
/* 668 */           IbTUpdConfigMapper mapper = (IbTUpdConfigMapper)SpringHelper.getBean(IbTUpdConfigMapper.class);
/* 669 */           int cnt = mapper.countBySectionAndKey("DBS_PKI_TYPE", pkiType);
/* 670 */           if (cnt == 0) {
/* 671 */             dataBlockType = 7;
/* 672 */             return dataBlockType;
/*     */           } 
/*     */         } 
/*     */       } 
/* 676 */       dataBlockType = 6;
/* 677 */       return dataBlockType;
/*     */     } 
/* 679 */     byte[] blockTypeByteArray = new byte[2];
/* 680 */     String blockType = "";
/* 681 */     if ("01".equals(protocolVersion)) {
/* 682 */       blockTypeByteArray = Arrays.copyOfRange(istrReqParam, 128, 130);
/* 683 */     } else if ("02".equals(protocolVersion) || "03".equals(protocolVersion)) {
/* 684 */       blockTypeByteArray = Arrays.copyOfRange(istrReqParam, 160, 162);
/*     */     } 
/* 686 */     for (int i = 0; i < blockTypeByteArray.length; i++) {
/* 687 */       int val = blockTypeByteArray[i] & 0xFF;
/* 688 */       String temp = Integer.toHexString(val);
/* 689 */       blockType = blockType + ((temp.length() == 1) ? ("0" + temp) : temp);
/*     */     } 
/* 691 */     if (blockType.equalsIgnoreCase("000F")) {
/* 692 */       dataBlockType = 1;
/* 693 */     } else if (blockType.equalsIgnoreCase("0033")) {
/* 694 */       dataBlockType = 2;
/* 695 */     } else if (blockType.equalsIgnoreCase("00F0")) {
/* 696 */       dataBlockType = 3;
/* 697 */     } else if (blockType.equalsIgnoreCase("00A5")) {
/* 698 */       dataBlockType = 4;
/* 699 */     } else if (blockType.equalsIgnoreCase("005A")) {
/* 700 */       dataBlockType = 5;
/* 701 */     } else if (blockType.equalsIgnoreCase("00C3") || blockType.equalsIgnoreCase("0096") || blockType.equalsIgnoreCase("0069")) {
/* 702 */       dataBlockType = 8;
/* 703 */     } else if (blockType.equalsIgnoreCase("030A")) {
/* 704 */       dataBlockType = 9;
/* 705 */     } else if (blockType.equalsIgnoreCase("0305")) {
/* 706 */       dataBlockType = 10;
/* 707 */     } else if (blockType.equalsIgnoreCase("0303")) {
/* 708 */       dataBlockType = 11;
/* 709 */     } else if (blockType.equalsIgnoreCase("0309")) {
/* 710 */       dataBlockType = 12;
/* 711 */     } else if (blockType.equalsIgnoreCase("030C")) {
/* 712 */       dataBlockType = 13;
/* 713 */     } else if (blockType.equalsIgnoreCase("0C03")) {
/*     */ 
/*     */       
/* 716 */       dataBlockType = 14;
/*     */     } 
/*     */     
/* 719 */     return dataBlockType;
/*     */   }
/*     */   
/*     */   protected ClientResponse callPKIAndProcessResult(RequestBean requestBean) throws Exception {
/* 723 */     ClientResponse clientResponse = new ClientResponse();
/* 724 */     String pkiIp = "***********";
/* 725 */     String datablockSignVersion = getDatablockVersion(requestBean);
/* 726 */     float dbsversion = getDatablockVersionInFloat(requestBean);
/*     */     
/* 728 */     List<String> pkiIps = this.ibTUpdConfigMapper.SelectBySectionAndKey("DATABLOCK_SIGN" + datablockSignVersion, "PKI_SERVER_IP");
/* 729 */     if (!CollectionUtils.isEmpty(pkiIps)) {
/* 730 */       pkiIp = pkiIps.get(0);
/*     */     }
/* 732 */     byte[] pkiResponse = new byte[2];
/* 733 */     byte[] tnlResponse = new byte[2];
/* 734 */     log.info("DatablockSign accsee PKI IP: " + pkiIp + " for serialno:" + requestBean.getIstrOldIMEI());
/* 735 */     if (dbsversion <= 3.5D) {
/* 736 */       pkiResponse = this.pkiConnectionService.forwardToPKI(requestBean, pkiIp);
/*     */     } else {
/* 738 */       PkiNLHTTPSMessage nlhttpsMessage = this.pkiConnectionService.forwardToNLPKI(requestBean, pkiIp);
/* 739 */       if (nlhttpsMessage.isRequestSuccessful()) {
/* 740 */         log.info("nlrequest is successfull");
/* 741 */         PkiNodeLockingResponse.TnlDbsResponse response = nlhttpsMessage.getResponse().getTnlDbsResponse();
/*     */ 
/*     */         
/* 744 */         pkiResponse = Base64.getDecoder().decode(response.getDbsResponseMessage());
/* 745 */         tnlResponse = Base64.getDecoder().decode(response.getTnlResponseMessage());
/*     */ 
/*     */       
/*     */       }
/*     */       else {
/*     */ 
/*     */         
/* 752 */         log.error("NL PKI SERVER error");
/* 753 */         String[] error = "8205,NL PKI server connection failure.".split(",");
/* 754 */         clientResponse.setIstrStatusCode(error[0]);
/* 755 */         clientResponse.setIstrStatusData(error[1]);
/* 756 */         return clientResponse;
/*     */       } 
/*     */     } 
/*     */     
/* 760 */     log.info("DatablockSign PKI Return result for serialno:" + requestBean.getIstrOldIMEI() + Arrays.toString(pkiResponse));
/*     */     
/* 762 */     if (pkiResponse == null || pkiResponse.length == 0) {
/* 763 */       String[] error = "8105,PKI connection failure.".split(",");
/* 764 */       clientResponse.setIstrStatusCode(error[0]);
/* 765 */       clientResponse.setIstrStatusData(error[1]);
/* 766 */       return clientResponse;
/*     */     } 
/* 768 */     String clientType = requestBean.getIstrClientReqType();
/* 769 */     byte[] byteStatusArray = new byte[2];
/* 770 */     System.arraycopy(pkiResponse, 4, byteStatusArray, 0, 2);
/* 771 */     String status = HexUtil.toTwoDigitHexString(byteStatusArray);
/* 772 */     log.info(requestBean.getIstrNewIMEI() + "----->" + clientType);
/* 773 */     log.debug("Status of request--> " + status);
/*     */     
/* 775 */     if (ClientTypeEnum.JanusOrIPRMKey.clientType.equals(clientType)) {
/* 776 */       if ("0000".equals(status)) {
/* 777 */         String[] error = "8049,Janus Key/IPRM Programming has been successfully completed".split(",");
/* 778 */         clientResponse.setIstrStatusCode(error[0]);
/* 779 */         clientResponse.setIstrStatusData(error[1]);
/* 780 */         clientResponse.setPkiResponse(pkiResponse);
/* 781 */       } else if ("0001".equals(status)) {
/* 782 */         String[] error = "8048,PKI Reply Failed".split(",");
/* 783 */         clientResponse.setIstrStatusCode(error[0]);
/* 784 */         clientResponse.setIstrStatusData(error[1] + ";" + "PKI_Data_Supply_Depleted");
/* 785 */       } else if ("0005".equals(status)) {
/* 786 */         String[] error = "8048,PKI Reply Failed".split(",");
/* 787 */         clientResponse.setIstrStatusCode(error[0]);
/* 788 */         clientResponse.setIstrStatusData(error[1] + ";" + "Unrecognized_PKI_Data_Type");
/* 789 */       } else if ("000B".equals(status)) {
/* 790 */         String[] error = "8048,PKI Reply Failed".split(",");
/* 791 */         clientResponse.setIstrStatusCode(error[0]);
/* 792 */         clientResponse.setIstrStatusData(error[1] + ";" + "Response_Oversized");
/* 793 */       } else if ("0009".equals(status) || "000A".equals(status)) {
/* 794 */         String[] error = "8048,PKI Reply Failed".split(",");
/* 795 */         clientResponse.setIstrStatusCode(error[0]);
/* 796 */         clientResponse.setIstrStatusData(error[1] + ";" + "RESERVED");
/*     */       }
/*     */     
/* 799 */     } else if ("0000".equals(status)) {
/* 800 */       log.info("status here is --->   " + status);
/* 801 */       String[] error = requestBean.isEtokenInException() ? "8022,Data Block Signing has been successfully completed due to exception".split(",") : "8022,Data Block Signing has been successfully completed".split(",");
/* 802 */       clientResponse.setIstrStatusCode(error[0]);
/* 803 */       clientResponse.setIstrStatusData(error[1]);
/* 804 */       clientResponse.setPkiResponse(pkiResponse);
/* 805 */     } else if ("0005".equals(status)) {
/* 806 */       String[] error = "8018,Data Block Signing completed with Error.".split(",");
/* 807 */       clientResponse.setIstrStatusCode(error[0]);
/* 808 */       clientResponse.setIstrStatusData(error[1] + ";" + "Unrecognized_PKI_Data_Type");
/* 809 */     } else if ("000B".equals(status)) {
/* 810 */       String[] error = "8018,Data Block Signing completed with Error.".split(",");
/* 811 */       clientResponse.setIstrStatusCode(error[0]);
/* 812 */       clientResponse.setIstrStatusData(error[1] + ";" + "Selected_Key_Not_Available");
/* 813 */     } else if ("0009".equals(status)) {
/* 814 */       String[] error = "8018,Data Block Signing completed with Error.".split(",");
/* 815 */       clientResponse.setIstrStatusCode(error[0]);
/* 816 */       clientResponse.setIstrStatusData(error[1] + ";" + "UPD_Connect_Error");
/* 817 */     } else if ("000A".equals(status)) {
/* 818 */       String[] error = "8018,Data Block Signing completed with Error.".split(",");
/* 819 */       clientResponse.setIstrStatusCode(error[0]);
/* 820 */       clientResponse.setIstrStatusData(error[1] + ";" + "SBK_Not_Available");
/* 821 */     } else if ("0002".equals(status)) {
/* 822 */       String[] error = "8018,Data Block Signing completed with Error.".split(",");
/* 823 */       clientResponse.setIstrStatusCode(error[0]);
/* 824 */       clientResponse.setIstrStatusData(error[1] + ";" + "CRC_Failed");
/* 825 */     } else if ("0003".equals(status)) {
/* 826 */       String[] error = "8018,Data Block Signing completed with Error.".split(",");
/* 827 */       clientResponse.setIstrStatusCode(error[0]);
/* 828 */       clientResponse.setIstrStatusData(error[1] + ";" + "Protocol_Version_Mismatch");
/* 829 */     } else if ("0004".equals(status)) {
/* 830 */       String[] error = "8018,Data Block Signing completed with Error.".split(",");
/* 831 */       clientResponse.setIstrStatusCode(error[0]);
/* 832 */       clientResponse.setIstrStatusData(error[1] + ";" + "Incorrect_Message_Length");
/* 833 */     } else if ("0006".equals(status)) {
/* 834 */       String[] error = "8018,Data Block Signing completed with Error.".split(",");
/* 835 */       clientResponse.setIstrStatusCode(error[0]);
/* 836 */       clientResponse.setIstrStatusData(error[1] + ";" + "Format_Error");
/* 837 */     } else if ("0007".equals(status)) {
/* 838 */       String[] error = "8018,Data Block Signing completed with Error.".split(",");
/* 839 */       clientResponse.setIstrStatusCode(error[0]);
/* 840 */       clientResponse.setIstrStatusData(error[1] + ";" + "Internal_Error");
/* 841 */     } else if ("0008".equals(status)) {
/* 842 */       String[] error = "8018,Data Block Signing completed with Error.".split(",");
/* 843 */       clientResponse.setIstrStatusCode(error[0]);
/* 844 */       clientResponse.setIstrStatusData(error[1] + ";" + "INVALID_MSG_ID");
/* 845 */     } else if ("000C".equals(status)) {
/* 846 */       String[] error = "8018,Data Block Signing completed with Error.".split(",");
/* 847 */       clientResponse.setIstrStatusCode(error[0]);
/*     */       
/* 849 */       clientResponse.setIstrStatusData(error[1] + ";" + "Not all Datablock types in the request is built for version 3");
/*     */     } else {
/* 851 */       log.error("status here is --->   " + status);
/* 852 */       String[] error = requestBean.isEtokenInException() ? "8022,Data Block Signing has been successfully completed due to exception".split(",") : "8022,Data Block Signing has been successfully completed".split(",");
/*     */ 
/*     */       
/* 855 */       clientResponse.setPkiResponse(pkiResponse);
/* 856 */       clientResponse.setIstrStatusCode(error[0]);
/* 857 */       clientResponse.setIstrStatusData(error[1]);
/*     */     } 
/*     */     
/* 860 */     if (dbsversion > 3.5D) {
/* 861 */       clientResponse.setIstrNodeLockingResponse(tnlResponse);
/*     */     }
/* 863 */     return clientResponse;
/*     */   }
/*     */   
/*     */   public String getDatablockVersion(RequestBean requestBean) {
/* 867 */     Message message = PhaseInterceptorChain.getCurrentMessage();
/* 868 */     HttpServletRequest httpRequest = (HttpServletRequest)message.get("HTTP.REQUEST");
/* 869 */     String curretRequestURI = httpRequest.getRequestURI();
/* 870 */     String datablockSignVersion = curretRequestURI.substring(curretRequestURI.lastIndexOf("_") + 1);
/* 871 */     log.info("DatablockSign curretRequestURI: " + curretRequestURI + " for serialno:" + requestBean.getIstrOldIMEI());
/* 872 */     return datablockSignVersion;
/*     */   }
/*     */   
/*     */   public float getDatablockVersionInFloat(RequestBean requestBean) {
/* 876 */     return Float.parseFloat(getDatablockVersion(requestBean));
/*     */   }
/*     */   
/*     */   public String generateTranscationId(String mascID) {
/* 880 */     String timePrefix = (new SimpleDateFormat("yyyyMMddHHmmssSS")).format(new Date());
/* 881 */     return timePrefix + "-" + mascID;
/*     */   }
/*     */   
/*     */   public RSDValidationRequest buildValidationRequest(String eip, String publicip, String reqtype, String username, String cid) {
/* 885 */     return new RSDValidationRequest(eip, publicip, username, reqtype, cid);
/*     */   }
/*     */ 
/*     */   
/*     */   private String parserOutputReader(String res) throws IOException {
/* 890 */     ObjectMapper mapper = new ObjectMapper();
/*     */     
/* 892 */     ParseResponse parseResponse = null;
/*     */     try {
/* 894 */       parseResponse = (ParseResponse)mapper.readValue(res, ParseResponse.class);
/* 895 */     } catch (IOException e) {
/* 896 */       e.printStackTrace();
/* 897 */       log.error("Error While parsing json response from RSD");
/* 898 */       log.error(e.getMessage());
/* 899 */       throw e;
/*     */     } 
/*     */ 
/*     */     
/* 903 */     return ((DataBlock)parseResponse.getDatablocks().get(0)).getCustomer_identifier();
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\service\impl\AbstractClientTypeService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */