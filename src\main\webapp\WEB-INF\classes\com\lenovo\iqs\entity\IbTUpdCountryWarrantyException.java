/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTUpdCountryWarrantyException implements Serializable { private Long autoId; private String isoCode; private String warrantyCode; private String carrierModelNo;
/*    */   private String shipToCustomer;
/*    */   private String transModelNo;
/*    */   private String apc;
/*    */   
/*  7 */   public void setAutoId(Long autoId) { this.autoId = autoId; } private Date startShipDate; private Date endShipDate; private Date lastModDate; private String lastModBy; private Date creationDatetime; private String createdBy; private static final long serialVersionUID = 1L; public void setIsoCode(String isoCode) { this.isoCode = isoCode; } public void setWarrantyCode(String warrantyCode) { this.warrantyCode = warrantyCode; } public void setCarrierModelNo(String carrierModelNo) { this.carrierModelNo = carrierModelNo; } public void setShipToCustomer(String shipToCustomer) { this.shipToCustomer = shipToCustomer; } public void setTransModelNo(String transModelNo) { this.transModelNo = transModelNo; } public void setApc(String apc) { this.apc = apc; } public void setStartShipDate(Date startShipDate) { this.startShipDate = startShipDate; } public void setEndShipDate(Date endShipDate) { this.endShipDate = endShipDate; } public void setLastModDate(Date lastModDate) { this.lastModDate = lastModDate; } public void setLastModBy(String lastModBy) { this.lastModBy = lastModBy; } public void setCreationDatetime(Date creationDatetime) { this.creationDatetime = creationDatetime; } public void setCreatedBy(String createdBy) { this.createdBy = createdBy; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdCountryWarrantyException)) return false;  com.lenovo.iqs.entity.IbTUpdCountryWarrantyException other = (com.lenovo.iqs.entity.IbTUpdCountryWarrantyException)o; if (!other.canEqual(this)) return false;  Object this$autoId = getAutoId(), other$autoId = other.getAutoId(); if ((this$autoId == null) ? (other$autoId != null) : !this$autoId.equals(other$autoId)) return false;  Object this$isoCode = getIsoCode(), other$isoCode = other.getIsoCode(); if ((this$isoCode == null) ? (other$isoCode != null) : !this$isoCode.equals(other$isoCode)) return false;  Object this$warrantyCode = getWarrantyCode(), other$warrantyCode = other.getWarrantyCode(); if ((this$warrantyCode == null) ? (other$warrantyCode != null) : !this$warrantyCode.equals(other$warrantyCode)) return false;  Object this$carrierModelNo = getCarrierModelNo(), other$carrierModelNo = other.getCarrierModelNo(); if ((this$carrierModelNo == null) ? (other$carrierModelNo != null) : !this$carrierModelNo.equals(other$carrierModelNo)) return false;  Object this$shipToCustomer = getShipToCustomer(), other$shipToCustomer = other.getShipToCustomer(); if ((this$shipToCustomer == null) ? (other$shipToCustomer != null) : !this$shipToCustomer.equals(other$shipToCustomer)) return false;  Object this$transModelNo = getTransModelNo(), other$transModelNo = other.getTransModelNo(); if ((this$transModelNo == null) ? (other$transModelNo != null) : !this$transModelNo.equals(other$transModelNo)) return false;  Object this$apc = getApc(), other$apc = other.getApc(); if ((this$apc == null) ? (other$apc != null) : !this$apc.equals(other$apc)) return false;  Object this$startShipDate = getStartShipDate(), other$startShipDate = other.getStartShipDate(); if ((this$startShipDate == null) ? (other$startShipDate != null) : !this$startShipDate.equals(other$startShipDate)) return false;  Object this$endShipDate = getEndShipDate(), other$endShipDate = other.getEndShipDate(); if ((this$endShipDate == null) ? (other$endShipDate != null) : !this$endShipDate.equals(other$endShipDate)) return false;  Object this$lastModDate = getLastModDate(), other$lastModDate = other.getLastModDate(); if ((this$lastModDate == null) ? (other$lastModDate != null) : !this$lastModDate.equals(other$lastModDate)) return false;  Object this$lastModBy = getLastModBy(), other$lastModBy = other.getLastModBy(); if ((this$lastModBy == null) ? (other$lastModBy != null) : !this$lastModBy.equals(other$lastModBy)) return false;  Object this$creationDatetime = getCreationDatetime(), other$creationDatetime = other.getCreationDatetime(); if ((this$creationDatetime == null) ? (other$creationDatetime != null) : !this$creationDatetime.equals(other$creationDatetime)) return false;  Object this$createdBy = getCreatedBy(), other$createdBy = other.getCreatedBy(); return !((this$createdBy == null) ? (other$createdBy != null) : !this$createdBy.equals(other$createdBy)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdCountryWarrantyException; } public int hashCode() { int PRIME = 59; result = 1; Object $autoId = getAutoId(); result = result * 59 + (($autoId == null) ? 43 : $autoId.hashCode()); Object $isoCode = getIsoCode(); result = result * 59 + (($isoCode == null) ? 43 : $isoCode.hashCode()); Object $warrantyCode = getWarrantyCode(); result = result * 59 + (($warrantyCode == null) ? 43 : $warrantyCode.hashCode()); Object $carrierModelNo = getCarrierModelNo(); result = result * 59 + (($carrierModelNo == null) ? 43 : $carrierModelNo.hashCode()); Object $shipToCustomer = getShipToCustomer(); result = result * 59 + (($shipToCustomer == null) ? 43 : $shipToCustomer.hashCode()); Object $transModelNo = getTransModelNo(); result = result * 59 + (($transModelNo == null) ? 43 : $transModelNo.hashCode()); Object $apc = getApc(); result = result * 59 + (($apc == null) ? 43 : $apc.hashCode()); Object $startShipDate = getStartShipDate(); result = result * 59 + (($startShipDate == null) ? 43 : $startShipDate.hashCode()); Object $endShipDate = getEndShipDate(); result = result * 59 + (($endShipDate == null) ? 43 : $endShipDate.hashCode()); Object $lastModDate = getLastModDate(); result = result * 59 + (($lastModDate == null) ? 43 : $lastModDate.hashCode()); Object $lastModBy = getLastModBy(); result = result * 59 + (($lastModBy == null) ? 43 : $lastModBy.hashCode()); Object $creationDatetime = getCreationDatetime(); result = result * 59 + (($creationDatetime == null) ? 43 : $creationDatetime.hashCode()); Object $createdBy = getCreatedBy(); return result * 59 + (($createdBy == null) ? 43 : $createdBy.hashCode()); } public String toString() { return "IbTUpdCountryWarrantyException(autoId=" + getAutoId() + ", isoCode=" + getIsoCode() + ", warrantyCode=" + getWarrantyCode() + ", carrierModelNo=" + getCarrierModelNo() + ", shipToCustomer=" + getShipToCustomer() + ", transModelNo=" + getTransModelNo() + ", apc=" + getApc() + ", startShipDate=" + getStartShipDate() + ", endShipDate=" + getEndShipDate() + ", lastModDate=" + getLastModDate() + ", lastModBy=" + getLastModBy() + ", creationDatetime=" + getCreationDatetime() + ", createdBy=" + getCreatedBy() + ")"; }
/*    */    public Long getAutoId() {
/*  9 */     return this.autoId;
/*    */   } public String getIsoCode() {
/* 11 */     return this.isoCode;
/*    */   } public String getWarrantyCode() {
/* 13 */     return this.warrantyCode;
/*    */   } public String getCarrierModelNo() {
/* 15 */     return this.carrierModelNo;
/*    */   } public String getShipToCustomer() {
/* 17 */     return this.shipToCustomer;
/*    */   } public String getTransModelNo() {
/* 19 */     return this.transModelNo;
/*    */   } public String getApc() {
/* 21 */     return this.apc;
/*    */   } public Date getStartShipDate() {
/* 23 */     return this.startShipDate;
/*    */   } public Date getEndShipDate() {
/* 25 */     return this.endShipDate;
/*    */   } public Date getLastModDate() {
/* 27 */     return this.lastModDate;
/*    */   } public String getLastModBy() {
/* 29 */     return this.lastModBy;
/*    */   } public Date getCreationDatetime() {
/* 31 */     return this.creationDatetime;
/*    */   } public String getCreatedBy() {
/* 33 */     return this.createdBy;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdCountryWarrantyException.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */