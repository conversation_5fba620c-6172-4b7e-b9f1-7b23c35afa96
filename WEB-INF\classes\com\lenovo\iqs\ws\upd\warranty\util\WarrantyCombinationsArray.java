/*     */ package WEB-INF.classes.com.lenovo.iqs.ws.upd.warranty.util;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.StringTokenizer;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WarrantyCombinationsArray
/*     */ {
/*  25 */   String staticRow = new String();
/*  26 */   static ArrayList<String> staticRowArrayList = new ArrayList<>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public WarrantyCombinationsArray() {
/*  33 */     this.staticRow = "C1|S1|T1|A1";
/*  34 */     staticRowArrayList.add(this.staticRow);
/*  35 */     this.staticRow = "C1|S1|T1|ALL";
/*  36 */     staticRowArrayList.add(this.staticRow);
/*  37 */     this.staticRow = "C1|S1|ALL|A1";
/*  38 */     staticRowArrayList.add(this.staticRow);
/*  39 */     this.staticRow = "C1|S1|ALL|ALL";
/*  40 */     staticRowArrayList.add(this.staticRow);
/*  41 */     this.staticRow = "C1|ALL|T1|A1";
/*  42 */     staticRowArrayList.add(this.staticRow);
/*  43 */     this.staticRow = "C1|ALL|T1|ALL";
/*  44 */     staticRowArrayList.add(this.staticRow);
/*  45 */     this.staticRow = "C1|ALL|ALL|A1";
/*  46 */     staticRowArrayList.add(this.staticRow);
/*  47 */     this.staticRow = "C1|ALL|ALL|ALL";
/*  48 */     staticRowArrayList.add(this.staticRow);
/*  49 */     this.staticRow = "ALL|S1|T1|A1";
/*  50 */     staticRowArrayList.add(this.staticRow);
/*  51 */     this.staticRow = "ALL|S1|T1|ALL";
/*  52 */     staticRowArrayList.add(this.staticRow);
/*  53 */     this.staticRow = "ALL|S1|ALL|A1";
/*  54 */     staticRowArrayList.add(this.staticRow);
/*  55 */     this.staticRow = "ALL|S1|ALL|ALL";
/*  56 */     staticRowArrayList.add(this.staticRow);
/*  57 */     this.staticRow = "ALL|ALL|T1|A1";
/*  58 */     staticRowArrayList.add(this.staticRow);
/*  59 */     this.staticRow = "ALL|ALL|T1|ALL";
/*  60 */     staticRowArrayList.add(this.staticRow);
/*  61 */     this.staticRow = "ALL|ALL|ALL|A1";
/*  62 */     staticRowArrayList.add(this.staticRow);
/*  63 */     this.staticRow = "ALL|ALL|ALL|ALL";
/*  64 */     staticRowArrayList.add(this.staticRow);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<String> warrCombinationsCall(String carrierModelNo, String shipToCustId, String transModelNo, String apcCode) {
/*     */     try {
/*  71 */       if (null == carrierModelNo || null == shipToCustId || null == transModelNo || null == apcCode || carrierModelNo
/*  72 */         .trim().equalsIgnoreCase("") || shipToCustId.trim().equalsIgnoreCase("") || transModelNo
/*  73 */         .trim().equalsIgnoreCase("") || apcCode.trim().equalsIgnoreCase("")) {
/*  74 */         ArrayList<String> errorAL = new ArrayList<>();
/*  75 */         String fail = "Failed";
/*  76 */         String error = "Check the input; It may have some null data ---> ";
/*  77 */         String error1 = "Please pass the data with values... If null ,pass it as 'ALL'";
/*  78 */         errorAL.add(fail);
/*  79 */         errorAL.add(error);
/*  80 */         errorAL.add(error1);
/*  81 */         return errorAL;
/*     */       } 
/*  83 */       String cMN = carrierModelNo.trim();
/*  84 */       String sTCI = shipToCustId.trim();
/*  85 */       String tMN = transModelNo.trim();
/*  86 */       String apcC = apcCode.trim();
/*     */       
/*  88 */       if (cMN.length() > 0 && !cMN.equalsIgnoreCase("ALL")) {
/*  89 */         cMN = "C1";
/*  90 */       } else if (cMN.equals("") || cMN.equals(" ") || cMN.equalsIgnoreCase("ALL")) {
/*  91 */         cMN = "ALL";
/*  92 */         carrierModelNo = carrierModelNo.toUpperCase();
/*     */       } 
/*     */       
/*  95 */       if (sTCI.length() > 0 && !sTCI.equalsIgnoreCase("ALL")) {
/*  96 */         sTCI = "S1";
/*  97 */       } else if (sTCI.equals("") || sTCI.equals(" ") || sTCI.equalsIgnoreCase("ALL")) {
/*  98 */         sTCI = "ALL";
/*  99 */         shipToCustId = shipToCustId.toUpperCase();
/*     */       } 
/*     */       
/* 102 */       if (tMN.length() > 0 && !tMN.equalsIgnoreCase("ALL")) {
/* 103 */         tMN = "T1";
/* 104 */       } else if (tMN.equals("") || tMN.equals(" ") || tMN.equalsIgnoreCase("ALL")) {
/* 105 */         tMN = "ALL";
/* 106 */         transModelNo = transModelNo.toUpperCase();
/*     */       } 
/*     */       
/* 109 */       if (apcC.length() > 0 && !apcC.equalsIgnoreCase("ALL")) {
/* 110 */         apcC = "A1";
/* 111 */       } else if (apcC.equals("") || apcC.equals(" ") || apcC.equalsIgnoreCase("ALL")) {
/* 112 */         apcC = "ALL";
/* 113 */         apcCode = apcCode.toUpperCase();
/*     */       } 
/*     */       
/* 116 */       String input = cMN + "|" + sTCI + "|" + tMN + "|" + apcC;
/*     */       
/* 118 */       return selectRows(input, staticRowArrayList, carrierModelNo, shipToCustId, transModelNo, apcCode);
/*     */     }
/* 120 */     catch (Exception e) {
/* 121 */       ArrayList<String> errorAL = new ArrayList<>();
/* 122 */       String fail = "Failed";
/* 123 */       String error = "Check the input; It may have some invalid data ---> " + e;
/* 124 */       errorAL.add(fail);
/* 125 */       errorAL.add(error);
/* 126 */       return errorAL;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static ArrayList<String> selectRows(String input, ArrayList<String> al, String cM, String sI, String tM, String apc) {
/* 133 */     ArrayList<String> resultAL = new ArrayList<>();
/*     */     
/* 135 */     int index = al.indexOf(input);
/* 136 */     if (index != -1) {
/* 137 */       String sucess = "Success";
/* 138 */       resultAL.add(sucess);
/* 139 */       for (int i = index; i < 16; i++) {
/* 140 */         String extractRow = al.get(i);
/*     */         
/* 142 */         StringTokenizer st = new StringTokenizer(extractRow, "|");
/* 143 */         String rS = "";
/* 144 */         while (st.hasMoreTokens()) {
/* 145 */           String cMM = "";
/* 146 */           String sII = "";
/* 147 */           String tMM = "";
/* 148 */           String apcC = "";
/* 149 */           String token = (String)st.nextElement();
/*     */           
/* 151 */           if (token.equalsIgnoreCase("C1")) {
/* 152 */             cMM = cM.trim();
/* 153 */             rS = cMM;
/* 154 */             rS = rS + "|";
/*     */             continue;
/*     */           } 
/* 157 */           if (token.equalsIgnoreCase("S1")) {
/* 158 */             sII = sI.trim();
/* 159 */             rS = rS + sII;
/* 160 */             rS = rS + "|";
/*     */             continue;
/*     */           } 
/* 163 */           if (token.equalsIgnoreCase("T1")) {
/* 164 */             tMM = tM.trim();
/* 165 */             rS = rS + tMM;
/* 166 */             rS = rS + "|";
/*     */             continue;
/*     */           } 
/* 169 */           if (token.equalsIgnoreCase("A1")) {
/* 170 */             apcC = apc.trim();
/* 171 */             rS = rS + apcC; continue;
/* 172 */           }  if (token.equalsIgnoreCase("ALL"))
/*     */           {
/* 174 */             rS = rS + "ALL|";
/*     */           }
/*     */         } 
/*     */ 
/*     */         
/* 179 */         if (rS.charAt(rS.length() - 1) == '|') {
/* 180 */           rS = rS.substring(0, rS.length() - 1);
/*     */         }
/*     */ 
/*     */         
/* 184 */         if (!resultAL.contains(rS)) {
/* 185 */           resultAL.add(rS.toString());
/*     */         }
/*     */       } 
/*     */       
/* 189 */       return resultAL;
/*     */     } 
/*     */     
/* 192 */     ArrayList<String> errorAL = new ArrayList<>();
/* 193 */     String fail = "Failed";
/* 194 */     String error = "No combination matched for this input";
/* 195 */     String error1 = "Check the input; It may have some invalid data ---> ";
/* 196 */     errorAL.add(fail);
/* 197 */     errorAL.add(error);
/* 198 */     errorAL.add(error1);
/* 199 */     return errorAL;
/*     */   }
/*     */   
/*     */   public static void main(String[] g) {}
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\w\\upd\warrant\\util\WarrantyCombinationsArray.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */