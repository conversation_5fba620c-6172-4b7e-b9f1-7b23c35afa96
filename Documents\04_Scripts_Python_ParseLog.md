# IQS - Scripts Python de Procesamiento de Logs

## 🐍 Descripción General

Los scripts Python en el proyecto IQS están diseñados para procesar y analizar logs de dispositivos móviles, especialmente relacionados con:
- **SIMLOCK**: Bloqueo/desbloqueo de tarjetas SIM
- **IMEI**: Identificación de dispositivos
- **Certificados digitales**: Validación y procesamiento
- **DataBlocks**: Bloques de datos de dispositivos

## 📁 Scripts Disponibles

### **1. ParseLog_v04.py** (Versión Principal)
**Propósito**: Script principal para procesamiento de logs de dispositivos

### **2. ParseLog_v03.py** 
**Propósito**: Versión anterior con funcionalidades base

### **3. ParseLog_v02.py**
**Propósito**: Versión de desarrollo intermedia

### **4. ParseLog.py**
**Propósito**: Versión inicial del parser

### **5. testparser.py**
**Propósito**: Script de pruebas para validar el parser

## 🔧 Análisis Detallado - ParseLog_v04.py

### **Librerías Utilizadas**
```python
import json
import argparse
from collections import defaultdict, namedtuple
import csv
from datetime import datetime
import os
import re
import sys
import base64
import binascii
import struct
import cryptography
from cryptography import x509
from cryptography.hazmat.backends import default_backend
import traceback
import warnings
import ast
```

### **Clase Principal - Object**
```python
class Object:
    def __init__(self):
        self.error_code = -1
        self.error_message = "Parsing not complete yet"
```

### **Constantes de Tipos de DataBlock**
```python
SIMLOCK_DBTYPE = "SIMLOCK - 0x0033"
IMEI_DBTYPE = "IMEI - 0x000F"
LEGACY_SIMLOCK = "Legacy SIMLOCK - 0x0066"
CID_DATABLOCK = "CID - 0x00F0"
SIMLOCKV2_DBTYPE = "UniversalSIMLOCKv2 - 0x0C03"
```

## 🔍 Funcionalidades Principales

### **1. Procesamiento de Parámetros de Request**
```python
def getDetailsFromRequestParam(message, bean):
    regex = re.compile("=")
    pad = regex.match(message)
    if not pad:
        padneeded = ((4 - len(message) % 4) % 4)
        message += "A" * (padneeded - 1)
        message += "="
```

**Función**: Procesa y valida parámetros de solicitudes, aplicando padding Base64 cuando es necesario.

### **2. Decodificación Base64**
- Manejo automático de padding Base64
- Validación de formato de datos
- Conversión a formato binario para análisis

### **3. Procesamiento de Certificados X.509**
```python
from cryptography import x509
from cryptography.hazmat.backends import default_backend
```

**Capacidades**:
- Lectura de certificados digitales
- Validación de firmas
- Extracción de información de certificados
- Verificación de cadenas de confianza

### **4. Análisis de DataBlocks**

#### **SIMLOCK DataBlocks (0x0033)**
- Extracción de códigos de desbloqueo
- Validación de estado de bloqueo
- Procesamiento de políticas de carrier

#### **IMEI DataBlocks (0x000F)**
- Validación de formato IMEI
- Extracción de información del dispositivo
- Verificación de checksums

#### **Legacy SIMLOCK (0x0066)**
- Soporte para formatos antiguos
- Migración de datos legacy
- Compatibilidad hacia atrás

#### **CID DataBlocks (0x00F0)**
- Procesamiento de Customer ID
- Validación de información de cliente
- Extracción de metadatos

#### **Universal SIMLOCKv2 (0x0C03)**
- Formato moderno de SIMLOCK
- Soporte para múltiples carriers
- Políticas avanzadas de desbloqueo

## 🔄 Flujo de Procesamiento

### **1. Entrada de Datos**
```
Log File → Base64 Decode → Binary Analysis → DataBlock Extraction
```

### **2. Validación**
```
Format Check → Checksum Validation → Certificate Verification
```

### **3. Procesamiento**
```
DataBlock Parsing → Information Extraction → Object Creation
```

### **4. Salida**
```
JSON Response → Error Handling → Logging
```

## 📊 Formatos de Datos

### **Entrada Típica (Base64)**
```
eyJkYXRhYmxvY2siOiAiU0lNTE9DSyIsICJpbWVpIjogIjEyMzQ1Njc4OTAxMjM0NSJ9
```

### **Salida JSON**
```json
{
    "error_code": 0,
    "error_message": "Success",
    "datablock_type": "SIMLOCK - 0x0033",
    "imei": "123456789012345",
    "unlock_code": "12345678",
    "carrier": "VERIZON",
    "status": "UNLOCKED",
    "timestamp": "2023-12-24T09:15:32Z"
}
```

## 🔐 Seguridad y Validación

### **Validación de Certificados**
```python
try:
    cert = x509.load_der_x509_certificate(cert_data, default_backend())
    # Validar firma, fechas, cadena de confianza
except Exception as e:
    # Manejo de errores de certificado
```

### **Validación de IMEI**
- Verificación de formato (15 dígitos)
- Cálculo de dígito de verificación Luhn
- Validación contra base de datos de dispositivos conocidos

### **Validación de DataBlocks**
- Verificación de checksums
- Validación de estructura binaria
- Comprobación de integridad de datos

## 🚨 Manejo de Errores

### **Códigos de Error Comunes**
```python
ERROR_CODES = {
    -1: "Parsing not complete yet",
    0: "Success",
    1: "Invalid Base64 format",
    2: "Invalid DataBlock structure",
    3: "Certificate validation failed",
    4: "IMEI format invalid",
    5: "Checksum mismatch"
}
```

### **Logging de Errores**
```python
try:
    # Procesamiento principal
except Exception as e:
    traceback.print_exc()
    bean.error_code = 500
    bean.error_message = str(e)
```

## 🔧 Configuración y Uso

### **Argumentos de Línea de Comandos**
```python
parser = argparse.ArgumentParser(description='Parse device logs')
parser.add_argument('--input', required=True, help='Input log file')
parser.add_argument('--output', help='Output JSON file')
parser.add_argument('--format', choices=['json', 'csv'], default='json')
parser.add_argument('--verbose', action='store_true')
```

### **Uso desde Java**
```java
ProcessBuilder pb = new ProcessBuilder(
    "python", "ParseLog_v04.py", 
    "--input", logFile,
    "--output", outputFile,
    "--format", "json"
);
Process process = pb.start();
```

## 📈 Optimizaciones

### **Procesamiento por Lotes**
- Soporte para múltiples archivos de log
- Procesamiento paralelo cuando es posible
- Optimización de memoria para archivos grandes

### **Caché de Certificados**
- Cache de certificados validados
- Reutilización de validaciones
- Reducción de overhead criptográfico

### **Validación Incremental**
- Procesamiento solo de cambios
- Checkpoints de progreso
- Recuperación de errores

## 🧪 Testing

### **testparser.py**
```python
# Script de pruebas unitarias
def test_base64_decode():
    # Pruebas de decodificación Base64
    
def test_imei_validation():
    # Pruebas de validación IMEI
    
def test_datablock_parsing():
    # Pruebas de parsing de DataBlocks
```

### **Casos de Prueba**
1. **Datos válidos**: Logs correctos con todos los campos
2. **Datos corruptos**: Logs con errores de formato
3. **Certificados inválidos**: Certificados expirados o malformados
4. **Edge cases**: Casos límite y datos extremos

## 🔄 Integración con Java

### **Llamada desde Controlador**
```java
@RestController
public class LogParserController {
    
    @PostMapping("/parse")
    public ResponseEntity<String> parseLog(@RequestBody String logData) {
        // Llamar script Python
        // Procesar resultado
        // Retornar JSON
    }
}
```

### **Manejo de Resultados**
- Captura de stdout/stderr
- Parsing de JSON de respuesta
- Manejo de timeouts
- Logging de errores
