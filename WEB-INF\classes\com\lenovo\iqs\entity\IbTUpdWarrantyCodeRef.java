/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTUpdWarrantyCodeRef implements Serializable { private String warrantyCode; private Date startDate; private Date endDate; private String warrantyCodeDesc; private String enhancementDesc;
/*    */   private String stdWarrantyPeriod;
/*    */   private String stdWarrantyPeriodType;
/*    */   private String extWarrantyPeriod;
/*    */   
/*  7 */   public void setWarrantyCode(String warrantyCode) { this.warrantyCode = warrantyCode; } private String extWarrantyPeriodType; private String createdBy; private Date creationDatetime; private Date lastModDate; private String lastModUser; private String popWarrantyPeriod; private String popWarrantyPeriodType; private static final long serialVersionUID = 1L; public void setStartDate(Date startDate) { this.startDate = startDate; } public void setEndDate(Date endDate) { this.endDate = endDate; } public void setWarrantyCodeDesc(String warrantyCodeDesc) { this.warrantyCodeDesc = warrantyCodeDesc; } public void setEnhancementDesc(String enhancementDesc) { this.enhancementDesc = enhancementDesc; } public void setStdWarrantyPeriod(String stdWarrantyPeriod) { this.stdWarrantyPeriod = stdWarrantyPeriod; } public void setStdWarrantyPeriodType(String stdWarrantyPeriodType) { this.stdWarrantyPeriodType = stdWarrantyPeriodType; } public void setExtWarrantyPeriod(String extWarrantyPeriod) { this.extWarrantyPeriod = extWarrantyPeriod; } public void setExtWarrantyPeriodType(String extWarrantyPeriodType) { this.extWarrantyPeriodType = extWarrantyPeriodType; } public void setCreatedBy(String createdBy) { this.createdBy = createdBy; } public void setCreationDatetime(Date creationDatetime) { this.creationDatetime = creationDatetime; } public void setLastModDate(Date lastModDate) { this.lastModDate = lastModDate; } public void setLastModUser(String lastModUser) { this.lastModUser = lastModUser; } public void setPopWarrantyPeriod(String popWarrantyPeriod) { this.popWarrantyPeriod = popWarrantyPeriod; } public void setPopWarrantyPeriodType(String popWarrantyPeriodType) { this.popWarrantyPeriodType = popWarrantyPeriodType; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdWarrantyCodeRef)) return false;  com.lenovo.iqs.entity.IbTUpdWarrantyCodeRef other = (com.lenovo.iqs.entity.IbTUpdWarrantyCodeRef)o; if (!other.canEqual(this)) return false;  Object this$warrantyCode = getWarrantyCode(), other$warrantyCode = other.getWarrantyCode(); if ((this$warrantyCode == null) ? (other$warrantyCode != null) : !this$warrantyCode.equals(other$warrantyCode)) return false;  Object this$startDate = getStartDate(), other$startDate = other.getStartDate(); if ((this$startDate == null) ? (other$startDate != null) : !this$startDate.equals(other$startDate)) return false;  Object this$endDate = getEndDate(), other$endDate = other.getEndDate(); if ((this$endDate == null) ? (other$endDate != null) : !this$endDate.equals(other$endDate)) return false;  Object this$warrantyCodeDesc = getWarrantyCodeDesc(), other$warrantyCodeDesc = other.getWarrantyCodeDesc(); if ((this$warrantyCodeDesc == null) ? (other$warrantyCodeDesc != null) : !this$warrantyCodeDesc.equals(other$warrantyCodeDesc)) return false;  Object this$enhancementDesc = getEnhancementDesc(), other$enhancementDesc = other.getEnhancementDesc(); if ((this$enhancementDesc == null) ? (other$enhancementDesc != null) : !this$enhancementDesc.equals(other$enhancementDesc)) return false;  Object this$stdWarrantyPeriod = getStdWarrantyPeriod(), other$stdWarrantyPeriod = other.getStdWarrantyPeriod(); if ((this$stdWarrantyPeriod == null) ? (other$stdWarrantyPeriod != null) : !this$stdWarrantyPeriod.equals(other$stdWarrantyPeriod)) return false;  Object this$stdWarrantyPeriodType = getStdWarrantyPeriodType(), other$stdWarrantyPeriodType = other.getStdWarrantyPeriodType(); if ((this$stdWarrantyPeriodType == null) ? (other$stdWarrantyPeriodType != null) : !this$stdWarrantyPeriodType.equals(other$stdWarrantyPeriodType)) return false;  Object this$extWarrantyPeriod = getExtWarrantyPeriod(), other$extWarrantyPeriod = other.getExtWarrantyPeriod(); if ((this$extWarrantyPeriod == null) ? (other$extWarrantyPeriod != null) : !this$extWarrantyPeriod.equals(other$extWarrantyPeriod)) return false;  Object this$extWarrantyPeriodType = getExtWarrantyPeriodType(), other$extWarrantyPeriodType = other.getExtWarrantyPeriodType(); if ((this$extWarrantyPeriodType == null) ? (other$extWarrantyPeriodType != null) : !this$extWarrantyPeriodType.equals(other$extWarrantyPeriodType)) return false;  Object this$createdBy = getCreatedBy(), other$createdBy = other.getCreatedBy(); if ((this$createdBy == null) ? (other$createdBy != null) : !this$createdBy.equals(other$createdBy)) return false;  Object this$creationDatetime = getCreationDatetime(), other$creationDatetime = other.getCreationDatetime(); if ((this$creationDatetime == null) ? (other$creationDatetime != null) : !this$creationDatetime.equals(other$creationDatetime)) return false;  Object this$lastModDate = getLastModDate(), other$lastModDate = other.getLastModDate(); if ((this$lastModDate == null) ? (other$lastModDate != null) : !this$lastModDate.equals(other$lastModDate)) return false;  Object this$lastModUser = getLastModUser(), other$lastModUser = other.getLastModUser(); if ((this$lastModUser == null) ? (other$lastModUser != null) : !this$lastModUser.equals(other$lastModUser)) return false;  Object this$popWarrantyPeriod = getPopWarrantyPeriod(), other$popWarrantyPeriod = other.getPopWarrantyPeriod(); if ((this$popWarrantyPeriod == null) ? (other$popWarrantyPeriod != null) : !this$popWarrantyPeriod.equals(other$popWarrantyPeriod)) return false;  Object this$popWarrantyPeriodType = getPopWarrantyPeriodType(), other$popWarrantyPeriodType = other.getPopWarrantyPeriodType(); return !((this$popWarrantyPeriodType == null) ? (other$popWarrantyPeriodType != null) : !this$popWarrantyPeriodType.equals(other$popWarrantyPeriodType)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdWarrantyCodeRef; } public int hashCode() { int PRIME = 59; result = 1; Object $warrantyCode = getWarrantyCode(); result = result * 59 + (($warrantyCode == null) ? 43 : $warrantyCode.hashCode()); Object $startDate = getStartDate(); result = result * 59 + (($startDate == null) ? 43 : $startDate.hashCode()); Object $endDate = getEndDate(); result = result * 59 + (($endDate == null) ? 43 : $endDate.hashCode()); Object $warrantyCodeDesc = getWarrantyCodeDesc(); result = result * 59 + (($warrantyCodeDesc == null) ? 43 : $warrantyCodeDesc.hashCode()); Object $enhancementDesc = getEnhancementDesc(); result = result * 59 + (($enhancementDesc == null) ? 43 : $enhancementDesc.hashCode()); Object $stdWarrantyPeriod = getStdWarrantyPeriod(); result = result * 59 + (($stdWarrantyPeriod == null) ? 43 : $stdWarrantyPeriod.hashCode()); Object $stdWarrantyPeriodType = getStdWarrantyPeriodType(); result = result * 59 + (($stdWarrantyPeriodType == null) ? 43 : $stdWarrantyPeriodType.hashCode()); Object $extWarrantyPeriod = getExtWarrantyPeriod(); result = result * 59 + (($extWarrantyPeriod == null) ? 43 : $extWarrantyPeriod.hashCode()); Object $extWarrantyPeriodType = getExtWarrantyPeriodType(); result = result * 59 + (($extWarrantyPeriodType == null) ? 43 : $extWarrantyPeriodType.hashCode()); Object $createdBy = getCreatedBy(); result = result * 59 + (($createdBy == null) ? 43 : $createdBy.hashCode()); Object $creationDatetime = getCreationDatetime(); result = result * 59 + (($creationDatetime == null) ? 43 : $creationDatetime.hashCode()); Object $lastModDate = getLastModDate(); result = result * 59 + (($lastModDate == null) ? 43 : $lastModDate.hashCode()); Object $lastModUser = getLastModUser(); result = result * 59 + (($lastModUser == null) ? 43 : $lastModUser.hashCode()); Object $popWarrantyPeriod = getPopWarrantyPeriod(); result = result * 59 + (($popWarrantyPeriod == null) ? 43 : $popWarrantyPeriod.hashCode()); Object $popWarrantyPeriodType = getPopWarrantyPeriodType(); return result * 59 + (($popWarrantyPeriodType == null) ? 43 : $popWarrantyPeriodType.hashCode()); } public String toString() { return "IbTUpdWarrantyCodeRef(warrantyCode=" + getWarrantyCode() + ", startDate=" + getStartDate() + ", endDate=" + getEndDate() + ", warrantyCodeDesc=" + getWarrantyCodeDesc() + ", enhancementDesc=" + getEnhancementDesc() + ", stdWarrantyPeriod=" + getStdWarrantyPeriod() + ", stdWarrantyPeriodType=" + getStdWarrantyPeriodType() + ", extWarrantyPeriod=" + getExtWarrantyPeriod() + ", extWarrantyPeriodType=" + getExtWarrantyPeriodType() + ", createdBy=" + getCreatedBy() + ", creationDatetime=" + getCreationDatetime() + ", lastModDate=" + getLastModDate() + ", lastModUser=" + getLastModUser() + ", popWarrantyPeriod=" + getPopWarrantyPeriod() + ", popWarrantyPeriodType=" + getPopWarrantyPeriodType() + ")"; }
/*    */    public String getWarrantyCode() {
/*  9 */     return this.warrantyCode;
/*    */   } public Date getStartDate() {
/* 11 */     return this.startDate;
/*    */   } public Date getEndDate() {
/* 13 */     return this.endDate;
/*    */   } public String getWarrantyCodeDesc() {
/* 15 */     return this.warrantyCodeDesc;
/*    */   } public String getEnhancementDesc() {
/* 17 */     return this.enhancementDesc;
/*    */   } public String getStdWarrantyPeriod() {
/* 19 */     return this.stdWarrantyPeriod;
/*    */   } public String getStdWarrantyPeriodType() {
/* 21 */     return this.stdWarrantyPeriodType;
/*    */   } public String getExtWarrantyPeriod() {
/* 23 */     return this.extWarrantyPeriod;
/*    */   } public String getExtWarrantyPeriodType() {
/* 25 */     return this.extWarrantyPeriodType;
/*    */   } public String getCreatedBy() {
/* 27 */     return this.createdBy;
/*    */   } public Date getCreationDatetime() {
/* 29 */     return this.creationDatetime;
/*    */   } public Date getLastModDate() {
/* 31 */     return this.lastModDate;
/*    */   } public String getLastModUser() {
/* 33 */     return this.lastModUser;
/*    */   } public String getPopWarrantyPeriod() {
/* 35 */     return this.popWarrantyPeriod;
/*    */   } public String getPopWarrantyPeriodType() {
/* 37 */     return this.popWarrantyPeriodType;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdWarrantyCodeRef.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */