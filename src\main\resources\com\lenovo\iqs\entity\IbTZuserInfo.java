/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.Date;
/*    */ 
/*    */ public class IbTZuserInfo implements Serializable {
/*    */   public void setUserId(String userId) {
/*  8 */     this.userId = userId; } public void setPassword(String password) { this.password = password; } public void setLastChangeTime(Date lastChangeTime) { this.lastChangeTime = lastChangeTime; } public void setLastChangeBy(String lastChangeBy) { this.lastChangeBy = lastChangeBy; } public void setIsvalid(String isvalid) { this.isvalid = isvalid; } public void setUserType(String userType) { this.userType = userType; } public void setSystem(String system) { this.system = system; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTZuserInfo)) return false;  com.lenovo.iqs.entity.IbTZuserInfo other = (com.lenovo.iqs.entity.IbTZuserInfo)o; if (!other.canEqual(this)) return false;  Object this$userId = getUserId(), other$userId = other.getUserId(); if ((this$userId == null) ? (other$userId != null) : !this$userId.equals(other$userId)) return false;  Object this$password = getPassword(), other$password = other.getPassword(); if ((this$password == null) ? (other$password != null) : !this$password.equals(other$password)) return false;  Object this$lastChangeTime = getLastChangeTime(), other$lastChangeTime = other.getLastChangeTime(); if ((this$lastChangeTime == null) ? (other$lastChangeTime != null) : !this$lastChangeTime.equals(other$lastChangeTime)) return false;  Object this$lastChangeBy = getLastChangeBy(), other$lastChangeBy = other.getLastChangeBy(); if ((this$lastChangeBy == null) ? (other$lastChangeBy != null) : !this$lastChangeBy.equals(other$lastChangeBy)) return false;  Object this$isvalid = getIsvalid(), other$isvalid = other.getIsvalid(); if ((this$isvalid == null) ? (other$isvalid != null) : !this$isvalid.equals(other$isvalid)) return false;  Object this$userType = getUserType(), other$userType = other.getUserType(); if ((this$userType == null) ? (other$userType != null) : !this$userType.equals(other$userType)) return false;  Object this$system = getSystem(), other$system = other.getSystem(); return !((this$system == null) ? (other$system != null) : !this$system.equals(other$system)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTZuserInfo; } public int hashCode() { int PRIME = 59; result = 1; Object $userId = getUserId(); result = result * 59 + (($userId == null) ? 43 : $userId.hashCode()); Object $password = getPassword(); result = result * 59 + (($password == null) ? 43 : $password.hashCode()); Object $lastChangeTime = getLastChangeTime(); result = result * 59 + (($lastChangeTime == null) ? 43 : $lastChangeTime.hashCode()); Object $lastChangeBy = getLastChangeBy(); result = result * 59 + (($lastChangeBy == null) ? 43 : $lastChangeBy.hashCode()); Object $isvalid = getIsvalid(); result = result * 59 + (($isvalid == null) ? 43 : $isvalid.hashCode()); Object $userType = getUserType(); result = result * 59 + (($userType == null) ? 43 : $userType.hashCode()); Object $system = getSystem(); return result * 59 + (($system == null) ? 43 : $system.hashCode()); } public String toString() { return "IbTZuserInfo(userId=" + getUserId() + ", password=" + getPassword() + ", lastChangeTime=" + getLastChangeTime() + ", lastChangeBy=" + getLastChangeBy() + ", isvalid=" + getIsvalid() + ", userType=" + getUserType() + ", system=" + getSystem() + ")"; }
/*    */   
/* 10 */   public static String USER_TYPE_MBG = "MBG";
/* 11 */   public static String USER_TYPE_PCG = "PCG";
/* 12 */   public static String USER_TYPE_ALL = "ALL";
/*    */   
/* 14 */   public static String SYSTEM_ESUPPORT = "ESUPPORT";
/*    */   
/* 16 */   public static String SYSTEM_TESTID = "TESTID";
/*    */   
/* 18 */   public static String SYSTEM_MDS = "SELFDEV";
/*    */   
/* 20 */   public static String SYSTEM_MAITROX = "MAITROX";
/*    */   
/* 22 */   public static String IBASE_USERID = "TESTID"; private String userId; private String password; private Date lastChangeTime;
/*    */   private String lastChangeBy;
/* 24 */   public static String IBASE_USERPW = "TESTPW"; private String isvalid; private String userType;
/*    */   public String getUserId() {
/* 26 */     return this.userId;
/*    */   } private String system; private static final long serialVersionUID = 1L; public String getPassword() {
/* 28 */     return this.password;
/*    */   } public Date getLastChangeTime() {
/* 30 */     return this.lastChangeTime;
/*    */   } public String getLastChangeBy() {
/* 32 */     return this.lastChangeBy;
/*    */   } public String getIsvalid() {
/* 34 */     return this.isvalid;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getUserType() {
/* 39 */     return this.userType;
/*    */   } public String getSystem() {
/* 41 */     return this.system;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTZuserInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */