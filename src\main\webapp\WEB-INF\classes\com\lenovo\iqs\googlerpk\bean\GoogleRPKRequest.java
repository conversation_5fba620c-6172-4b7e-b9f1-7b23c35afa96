/*    */ package WEB-INF.classes.com.lenovo.iqs.googlerpk.bean;
/*    */ public class GoogleRPKRequest { private String serialNo;
/*    */   private String rsdUser;
/*    */   private String mascId;
/*    */   private String track_id;
/*    */   private String googleCsr;
/*    */   
/*  8 */   public void setSerialNo(String serialNo) { this.serialNo = serialNo; } public void setRsdUser(String rsdUser) { this.rsdUser = rsdUser; } public void setMascId(String mascId) { this.mascId = mascId; } public void setTrack_id(String track_id) { this.track_id = track_id; } public void setGoogleCsr(String googleCsr) { this.googleCsr = googleCsr; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.googlerpk.bean.GoogleRPKRequest)) return false;  com.lenovo.iqs.googlerpk.bean.GoogleRPKRequest other = (com.lenovo.iqs.googlerpk.bean.GoogleRPKRequest)o; if (!other.canEqual(this)) return false;  Object this$serialNo = getSerialNo(), other$serialNo = other.getSerialNo(); if ((this$serialNo == null) ? (other$serialNo != null) : !this$serialNo.equals(other$serialNo)) return false;  Object this$rsdUser = getRsdUser(), other$rsdUser = other.getRsdUser(); if ((this$rsdUser == null) ? (other$rsdUser != null) : !this$rsdUser.equals(other$rsdUser)) return false;  Object this$mascId = getMascId(), other$mascId = other.getMascId(); if ((this$mascId == null) ? (other$mascId != null) : !this$mascId.equals(other$mascId)) return false;  Object this$track_id = getTrack_id(), other$track_id = other.getTrack_id(); if ((this$track_id == null) ? (other$track_id != null) : !this$track_id.equals(other$track_id)) return false;  Object this$googleCsr = getGoogleCsr(), other$googleCsr = other.getGoogleCsr(); return !((this$googleCsr == null) ? (other$googleCsr != null) : !this$googleCsr.equals(other$googleCsr)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.googlerpk.bean.GoogleRPKRequest; } public int hashCode() { int PRIME = 59; result = 1; Object $serialNo = getSerialNo(); result = result * 59 + (($serialNo == null) ? 43 : $serialNo.hashCode()); Object $rsdUser = getRsdUser(); result = result * 59 + (($rsdUser == null) ? 43 : $rsdUser.hashCode()); Object $mascId = getMascId(); result = result * 59 + (($mascId == null) ? 43 : $mascId.hashCode()); Object $track_id = getTrack_id(); result = result * 59 + (($track_id == null) ? 43 : $track_id.hashCode()); Object $googleCsr = getGoogleCsr(); return result * 59 + (($googleCsr == null) ? 43 : $googleCsr.hashCode()); } public String toString() { return "GoogleRPKRequest(serialNo=" + getSerialNo() + ", rsdUser=" + getRsdUser() + ", mascId=" + getMascId() + ", track_id=" + getTrack_id() + ", googleCsr=" + getGoogleCsr() + ")"; }
/*    */   
/* 10 */   public String getSerialNo() { return this.serialNo; }
/* 11 */   public String getRsdUser() { return this.rsdUser; }
/* 12 */   public String getMascId() { return this.mascId; }
/* 13 */   public String getTrack_id() { return this.track_id; } public String getGoogleCsr() {
/* 14 */     return this.googleCsr;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\googlerpk\bean\GoogleRPKRequest.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */