package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTMaterialMaster;
import java.util.List;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTMaterialMasterMapper {
  int deleteByPrimaryKey(Integer paramInteger);
  
  int insert(IbTMaterialMaster paramIbTMaterialMaster);
  
  IbTMaterialMaster selectByPrimaryKey(Integer paramInteger);
  
  IbTMaterialMaster selectByMaterialId(String paramString);
  
  int updateByPrimaryKey(IbTMaterialMaster paramIbTMaterialMaster);
  
  List<String> getFilterMOTO();
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTMaterialMasterMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */