/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTUpdCountryCodeMapping implements Serializable { private Integer autoId;
/*    */   private String countryCode;
/*    */   private String isoCode;
/*    */   private String description;
/*    */   
/*  7 */   public void setAutoId(Integer autoId) { this.autoId = autoId; } private Date lastModDate; private String lastModBy; private Date creationDatetime; private String createdBy; private static final long serialVersionUID = 1L; public void setCountryCode(String countryCode) { this.countryCode = countryCode; } public void setIsoCode(String isoCode) { this.isoCode = isoCode; } public void setDescription(String description) { this.description = description; } public void setLastModDate(Date lastModDate) { this.lastModDate = lastModDate; } public void setLastModBy(String lastModBy) { this.lastModBy = lastModBy; } public void setCreationDatetime(Date creationDatetime) { this.creationDatetime = creationDatetime; } public void setCreatedBy(String createdBy) { this.createdBy = createdBy; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdCountryCodeMapping)) return false;  com.lenovo.iqs.entity.IbTUpdCountryCodeMapping other = (com.lenovo.iqs.entity.IbTUpdCountryCodeMapping)o; if (!other.canEqual(this)) return false;  Object this$autoId = getAutoId(), other$autoId = other.getAutoId(); if ((this$autoId == null) ? (other$autoId != null) : !this$autoId.equals(other$autoId)) return false;  Object this$countryCode = getCountryCode(), other$countryCode = other.getCountryCode(); if ((this$countryCode == null) ? (other$countryCode != null) : !this$countryCode.equals(other$countryCode)) return false;  Object this$isoCode = getIsoCode(), other$isoCode = other.getIsoCode(); if ((this$isoCode == null) ? (other$isoCode != null) : !this$isoCode.equals(other$isoCode)) return false;  Object this$description = getDescription(), other$description = other.getDescription(); if ((this$description == null) ? (other$description != null) : !this$description.equals(other$description)) return false;  Object this$lastModDate = getLastModDate(), other$lastModDate = other.getLastModDate(); if ((this$lastModDate == null) ? (other$lastModDate != null) : !this$lastModDate.equals(other$lastModDate)) return false;  Object this$lastModBy = getLastModBy(), other$lastModBy = other.getLastModBy(); if ((this$lastModBy == null) ? (other$lastModBy != null) : !this$lastModBy.equals(other$lastModBy)) return false;  Object this$creationDatetime = getCreationDatetime(), other$creationDatetime = other.getCreationDatetime(); if ((this$creationDatetime == null) ? (other$creationDatetime != null) : !this$creationDatetime.equals(other$creationDatetime)) return false;  Object this$createdBy = getCreatedBy(), other$createdBy = other.getCreatedBy(); return !((this$createdBy == null) ? (other$createdBy != null) : !this$createdBy.equals(other$createdBy)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdCountryCodeMapping; } public int hashCode() { int PRIME = 59; result = 1; Object $autoId = getAutoId(); result = result * 59 + (($autoId == null) ? 43 : $autoId.hashCode()); Object $countryCode = getCountryCode(); result = result * 59 + (($countryCode == null) ? 43 : $countryCode.hashCode()); Object $isoCode = getIsoCode(); result = result * 59 + (($isoCode == null) ? 43 : $isoCode.hashCode()); Object $description = getDescription(); result = result * 59 + (($description == null) ? 43 : $description.hashCode()); Object $lastModDate = getLastModDate(); result = result * 59 + (($lastModDate == null) ? 43 : $lastModDate.hashCode()); Object $lastModBy = getLastModBy(); result = result * 59 + (($lastModBy == null) ? 43 : $lastModBy.hashCode()); Object $creationDatetime = getCreationDatetime(); result = result * 59 + (($creationDatetime == null) ? 43 : $creationDatetime.hashCode()); Object $createdBy = getCreatedBy(); return result * 59 + (($createdBy == null) ? 43 : $createdBy.hashCode()); } public String toString() { return "IbTUpdCountryCodeMapping(autoId=" + getAutoId() + ", countryCode=" + getCountryCode() + ", isoCode=" + getIsoCode() + ", description=" + getDescription() + ", lastModDate=" + getLastModDate() + ", lastModBy=" + getLastModBy() + ", creationDatetime=" + getCreationDatetime() + ", createdBy=" + getCreatedBy() + ")"; }
/*    */    public Integer getAutoId() {
/*  9 */     return this.autoId;
/*    */   } public String getCountryCode() {
/* 11 */     return this.countryCode;
/*    */   } public String getIsoCode() {
/* 13 */     return this.isoCode;
/*    */   } public String getDescription() {
/* 15 */     return this.description;
/*    */   } public Date getLastModDate() {
/* 17 */     return this.lastModDate;
/*    */   } public String getLastModBy() {
/* 19 */     return this.lastModBy;
/*    */   } public Date getCreationDatetime() {
/* 21 */     return this.creationDatetime;
/*    */   } public String getCreatedBy() {
/* 23 */     return this.createdBy;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdCountryCodeMapping.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */