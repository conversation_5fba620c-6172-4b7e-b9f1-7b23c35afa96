/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTUpdCountryWarrantyMapping implements Serializable { private Long autoId;
/*    */   private String isoCode;
/*    */   private String defWarrantyCode;
/*    */   private Date lastModDate;
/*    */   
/*  7 */   public void setAutoId(Long autoId) { this.autoId = autoId; } private String lastModBy; private Date creationDatetime; private String createdBy; private static final long serialVersionUID = 1L; public void setIsoCode(String isoCode) { this.isoCode = isoCode; } public void setDefWarrantyCode(String defWarrantyCode) { this.defWarrantyCode = defWarrantyCode; } public void setLastModDate(Date lastModDate) { this.lastModDate = lastModDate; } public void setLastModBy(String lastModBy) { this.lastModBy = lastModBy; } public void setCreationDatetime(Date creationDatetime) { this.creationDatetime = creationDatetime; } public void setCreatedBy(String createdBy) { this.createdBy = createdBy; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdCountryWarrantyMapping)) return false;  com.lenovo.iqs.entity.IbTUpdCountryWarrantyMapping other = (com.lenovo.iqs.entity.IbTUpdCountryWarrantyMapping)o; if (!other.canEqual(this)) return false;  Object this$autoId = getAutoId(), other$autoId = other.getAutoId(); if ((this$autoId == null) ? (other$autoId != null) : !this$autoId.equals(other$autoId)) return false;  Object this$isoCode = getIsoCode(), other$isoCode = other.getIsoCode(); if ((this$isoCode == null) ? (other$isoCode != null) : !this$isoCode.equals(other$isoCode)) return false;  Object this$defWarrantyCode = getDefWarrantyCode(), other$defWarrantyCode = other.getDefWarrantyCode(); if ((this$defWarrantyCode == null) ? (other$defWarrantyCode != null) : !this$defWarrantyCode.equals(other$defWarrantyCode)) return false;  Object this$lastModDate = getLastModDate(), other$lastModDate = other.getLastModDate(); if ((this$lastModDate == null) ? (other$lastModDate != null) : !this$lastModDate.equals(other$lastModDate)) return false;  Object this$lastModBy = getLastModBy(), other$lastModBy = other.getLastModBy(); if ((this$lastModBy == null) ? (other$lastModBy != null) : !this$lastModBy.equals(other$lastModBy)) return false;  Object this$creationDatetime = getCreationDatetime(), other$creationDatetime = other.getCreationDatetime(); if ((this$creationDatetime == null) ? (other$creationDatetime != null) : !this$creationDatetime.equals(other$creationDatetime)) return false;  Object this$createdBy = getCreatedBy(), other$createdBy = other.getCreatedBy(); return !((this$createdBy == null) ? (other$createdBy != null) : !this$createdBy.equals(other$createdBy)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdCountryWarrantyMapping; } public int hashCode() { int PRIME = 59; result = 1; Object $autoId = getAutoId(); result = result * 59 + (($autoId == null) ? 43 : $autoId.hashCode()); Object $isoCode = getIsoCode(); result = result * 59 + (($isoCode == null) ? 43 : $isoCode.hashCode()); Object $defWarrantyCode = getDefWarrantyCode(); result = result * 59 + (($defWarrantyCode == null) ? 43 : $defWarrantyCode.hashCode()); Object $lastModDate = getLastModDate(); result = result * 59 + (($lastModDate == null) ? 43 : $lastModDate.hashCode()); Object $lastModBy = getLastModBy(); result = result * 59 + (($lastModBy == null) ? 43 : $lastModBy.hashCode()); Object $creationDatetime = getCreationDatetime(); result = result * 59 + (($creationDatetime == null) ? 43 : $creationDatetime.hashCode()); Object $createdBy = getCreatedBy(); return result * 59 + (($createdBy == null) ? 43 : $createdBy.hashCode()); } public String toString() { return "IbTUpdCountryWarrantyMapping(autoId=" + getAutoId() + ", isoCode=" + getIsoCode() + ", defWarrantyCode=" + getDefWarrantyCode() + ", lastModDate=" + getLastModDate() + ", lastModBy=" + getLastModBy() + ", creationDatetime=" + getCreationDatetime() + ", createdBy=" + getCreatedBy() + ")"; }
/*    */    public Long getAutoId() {
/*  9 */     return this.autoId;
/*    */   } public String getIsoCode() {
/* 11 */     return this.isoCode;
/*    */   } public String getDefWarrantyCode() {
/* 13 */     return this.defWarrantyCode;
/*    */   } public Date getLastModDate() {
/* 15 */     return this.lastModDate;
/*    */   } public String getLastModBy() {
/* 17 */     return this.lastModBy;
/*    */   } public Date getCreationDatetime() {
/* 19 */     return this.creationDatetime;
/*    */   } public String getCreatedBy() {
/* 21 */     return this.createdBy;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdCountryWarrantyMapping.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */