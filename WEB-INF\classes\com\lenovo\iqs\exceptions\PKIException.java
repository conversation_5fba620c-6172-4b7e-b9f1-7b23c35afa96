/*    */ package WEB-INF.classes.com.lenovo.iqs.exceptions;
/*    */ 
/*    */ 
/*    */ public class PKIException
/*    */   extends RuntimeException
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   
/*    */   public PKIException() {}
/*    */   
/*    */   public PKIException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
/* 12 */     super(message, cause, enableSuppression, writableStackTrace);
/*    */   }
/*    */   
/*    */   public PKIException(String message, Throwable cause) {
/* 16 */     super(message, cause);
/*    */   }
/*    */   
/*    */   public PKIException(String message) {
/* 20 */     super(message);
/*    */   }
/*    */   
/*    */   public PKIException(Throwable cause) {
/* 24 */     super(cause);
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\exceptions\PKIException.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */