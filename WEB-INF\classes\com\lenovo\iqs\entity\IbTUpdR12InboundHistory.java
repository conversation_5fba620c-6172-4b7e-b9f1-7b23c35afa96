/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ 
/*    */ public class IbTUpdR12InboundHistory implements Serializable {
/*    */   private Integer autoId;
/*    */   private String serialNo;
/*    */   
/*  7 */   public void setAutoId(Integer autoId) { this.autoId = autoId; } private Date createTime; private String batchNumber; private static final long serialVersionUID = 1L; public void setSerialNo(String serialNo) { this.serialNo = serialNo; } public void setCreateTime(Date createTime) { this.createTime = createTime; } public void setBatchNumber(String batchNumber) { this.batchNumber = batchNumber; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdR12InboundHistory)) return false;  com.lenovo.iqs.entity.IbTUpdR12InboundHistory other = (com.lenovo.iqs.entity.IbTUpdR12InboundHistory)o; if (!other.canEqual(this)) return false;  Object this$autoId = getAutoId(), other$autoId = other.getAutoId(); if ((this$autoId == null) ? (other$autoId != null) : !this$autoId.equals(other$autoId)) return false;  Object this$serialNo = getSerialNo(), other$serialNo = other.getSerialNo(); if ((this$serialNo == null) ? (other$serialNo != null) : !this$serialNo.equals(other$serialNo)) return false;  Object this$createTime = getCreateTime(), other$createTime = other.getCreateTime(); if ((this$createTime == null) ? (other$createTime != null) : !this$createTime.equals(other$createTime)) return false;  Object this$batchNumber = getBatchNumber(), other$batchNumber = other.getBatchNumber(); return !((this$batchNumber == null) ? (other$batchNumber != null) : !this$batchNumber.equals(other$batchNumber)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdR12InboundHistory; } public int hashCode() { int PRIME = 59; result = 1; Object $autoId = getAutoId(); result = result * 59 + (($autoId == null) ? 43 : $autoId.hashCode()); Object $serialNo = getSerialNo(); result = result * 59 + (($serialNo == null) ? 43 : $serialNo.hashCode()); Object $createTime = getCreateTime(); result = result * 59 + (($createTime == null) ? 43 : $createTime.hashCode()); Object $batchNumber = getBatchNumber(); return result * 59 + (($batchNumber == null) ? 43 : $batchNumber.hashCode()); } public String toString() { return "IbTUpdR12InboundHistory(autoId=" + getAutoId() + ", serialNo=" + getSerialNo() + ", createTime=" + getCreateTime() + ", batchNumber=" + getBatchNumber() + ")"; }
/*    */    public Integer getAutoId() {
/*  9 */     return this.autoId;
/*    */   } public String getSerialNo() {
/* 11 */     return this.serialNo;
/*    */   } public Date getCreateTime() {
/* 13 */     return this.createTime;
/*    */   } public String getBatchNumber() {
/* 15 */     return this.batchNumber;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdR12InboundHistory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */