/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTBpMaster implements Serializable { private Integer id; private String bpId; private String companyCode; private String firstName; private String lastName; private String street; private String city;
/*    */   private String country;
/*    */   private String telephone;
/*    */   private String postalCode;
/*    */   
/*  7 */   public void setId(Integer id) { this.id = id; } private String email; private Date lastChangeTime; private String preserve1; private String preserve2; private String preserve3; private String houseNumber; private String street2; private String street3; private String region; private static final long serialVersionUID = 1L; public void setBpId(String bpId) { this.bpId = bpId; } public void setCompanyCode(String companyCode) { this.companyCode = companyCode; } public void setFirstName(String firstName) { this.firstName = firstName; } public void setLastName(String lastName) { this.lastName = lastName; } public void setStreet(String street) { this.street = street; } public void setCity(String city) { this.city = city; } public void setCountry(String country) { this.country = country; } public void setTelephone(String telephone) { this.telephone = telephone; } public void setPostalCode(String postalCode) { this.postalCode = postalCode; } public void setEmail(String email) { this.email = email; } public void setLastChangeTime(Date lastChangeTime) { this.lastChangeTime = lastChangeTime; } public void setPreserve1(String preserve1) { this.preserve1 = preserve1; } public void setPreserve2(String preserve2) { this.preserve2 = preserve2; } public void setPreserve3(String preserve3) { this.preserve3 = preserve3; } public void setHouseNumber(String houseNumber) { this.houseNumber = houseNumber; } public void setStreet2(String street2) { this.street2 = street2; } public void setStreet3(String street3) { this.street3 = street3; } public void setRegion(String region) { this.region = region; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTBpMaster)) return false;  com.lenovo.iqs.entity.IbTBpMaster other = (com.lenovo.iqs.entity.IbTBpMaster)o; if (!other.canEqual(this)) return false;  Object this$id = getId(), other$id = other.getId(); if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id)) return false;  Object this$bpId = getBpId(), other$bpId = other.getBpId(); if ((this$bpId == null) ? (other$bpId != null) : !this$bpId.equals(other$bpId)) return false;  Object this$companyCode = getCompanyCode(), other$companyCode = other.getCompanyCode(); if ((this$companyCode == null) ? (other$companyCode != null) : !this$companyCode.equals(other$companyCode)) return false;  Object this$firstName = getFirstName(), other$firstName = other.getFirstName(); if ((this$firstName == null) ? (other$firstName != null) : !this$firstName.equals(other$firstName)) return false;  Object this$lastName = getLastName(), other$lastName = other.getLastName(); if ((this$lastName == null) ? (other$lastName != null) : !this$lastName.equals(other$lastName)) return false;  Object this$street = getStreet(), other$street = other.getStreet(); if ((this$street == null) ? (other$street != null) : !this$street.equals(other$street)) return false;  Object this$city = getCity(), other$city = other.getCity(); if ((this$city == null) ? (other$city != null) : !this$city.equals(other$city)) return false;  Object this$country = getCountry(), other$country = other.getCountry(); if ((this$country == null) ? (other$country != null) : !this$country.equals(other$country)) return false;  Object this$telephone = getTelephone(), other$telephone = other.getTelephone(); if ((this$telephone == null) ? (other$telephone != null) : !this$telephone.equals(other$telephone)) return false;  Object this$postalCode = getPostalCode(), other$postalCode = other.getPostalCode(); if ((this$postalCode == null) ? (other$postalCode != null) : !this$postalCode.equals(other$postalCode)) return false;  Object this$email = getEmail(), other$email = other.getEmail(); if ((this$email == null) ? (other$email != null) : !this$email.equals(other$email)) return false;  Object this$lastChangeTime = getLastChangeTime(), other$lastChangeTime = other.getLastChangeTime(); if ((this$lastChangeTime == null) ? (other$lastChangeTime != null) : !this$lastChangeTime.equals(other$lastChangeTime)) return false;  Object this$preserve1 = getPreserve1(), other$preserve1 = other.getPreserve1(); if ((this$preserve1 == null) ? (other$preserve1 != null) : !this$preserve1.equals(other$preserve1)) return false;  Object this$preserve2 = getPreserve2(), other$preserve2 = other.getPreserve2(); if ((this$preserve2 == null) ? (other$preserve2 != null) : !this$preserve2.equals(other$preserve2)) return false;  Object this$preserve3 = getPreserve3(), other$preserve3 = other.getPreserve3(); if ((this$preserve3 == null) ? (other$preserve3 != null) : !this$preserve3.equals(other$preserve3)) return false;  Object this$houseNumber = getHouseNumber(), other$houseNumber = other.getHouseNumber(); if ((this$houseNumber == null) ? (other$houseNumber != null) : !this$houseNumber.equals(other$houseNumber)) return false;  Object this$street2 = getStreet2(), other$street2 = other.getStreet2(); if ((this$street2 == null) ? (other$street2 != null) : !this$street2.equals(other$street2)) return false;  Object this$street3 = getStreet3(), other$street3 = other.getStreet3(); if ((this$street3 == null) ? (other$street3 != null) : !this$street3.equals(other$street3)) return false;  Object this$region = getRegion(), other$region = other.getRegion(); return !((this$region == null) ? (other$region != null) : !this$region.equals(other$region)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTBpMaster; } public int hashCode() { int PRIME = 59; result = 1; Object $id = getId(); result = result * 59 + (($id == null) ? 43 : $id.hashCode()); Object $bpId = getBpId(); result = result * 59 + (($bpId == null) ? 43 : $bpId.hashCode()); Object $companyCode = getCompanyCode(); result = result * 59 + (($companyCode == null) ? 43 : $companyCode.hashCode()); Object $firstName = getFirstName(); result = result * 59 + (($firstName == null) ? 43 : $firstName.hashCode()); Object $lastName = getLastName(); result = result * 59 + (($lastName == null) ? 43 : $lastName.hashCode()); Object $street = getStreet(); result = result * 59 + (($street == null) ? 43 : $street.hashCode()); Object $city = getCity(); result = result * 59 + (($city == null) ? 43 : $city.hashCode()); Object $country = getCountry(); result = result * 59 + (($country == null) ? 43 : $country.hashCode()); Object $telephone = getTelephone(); result = result * 59 + (($telephone == null) ? 43 : $telephone.hashCode()); Object $postalCode = getPostalCode(); result = result * 59 + (($postalCode == null) ? 43 : $postalCode.hashCode()); Object $email = getEmail(); result = result * 59 + (($email == null) ? 43 : $email.hashCode()); Object $lastChangeTime = getLastChangeTime(); result = result * 59 + (($lastChangeTime == null) ? 43 : $lastChangeTime.hashCode()); Object $preserve1 = getPreserve1(); result = result * 59 + (($preserve1 == null) ? 43 : $preserve1.hashCode()); Object $preserve2 = getPreserve2(); result = result * 59 + (($preserve2 == null) ? 43 : $preserve2.hashCode()); Object $preserve3 = getPreserve3(); result = result * 59 + (($preserve3 == null) ? 43 : $preserve3.hashCode()); Object $houseNumber = getHouseNumber(); result = result * 59 + (($houseNumber == null) ? 43 : $houseNumber.hashCode()); Object $street2 = getStreet2(); result = result * 59 + (($street2 == null) ? 43 : $street2.hashCode()); Object $street3 = getStreet3(); result = result * 59 + (($street3 == null) ? 43 : $street3.hashCode()); Object $region = getRegion(); return result * 59 + (($region == null) ? 43 : $region.hashCode()); } public String toString() { return "IbTBpMaster(id=" + getId() + ", bpId=" + getBpId() + ", companyCode=" + getCompanyCode() + ", firstName=" + getFirstName() + ", lastName=" + getLastName() + ", street=" + getStreet() + ", city=" + getCity() + ", country=" + getCountry() + ", telephone=" + getTelephone() + ", postalCode=" + getPostalCode() + ", email=" + getEmail() + ", lastChangeTime=" + getLastChangeTime() + ", preserve1=" + getPreserve1() + ", preserve2=" + getPreserve2() + ", preserve3=" + getPreserve3() + ", houseNumber=" + getHouseNumber() + ", street2=" + getStreet2() + ", street3=" + getStreet3() + ", region=" + getRegion() + ")"; }
/*    */    public Integer getId() {
/*  9 */     return this.id;
/*    */   } public String getBpId() {
/* 11 */     return this.bpId;
/*    */   } public String getCompanyCode() {
/* 13 */     return this.companyCode;
/*    */   } public String getFirstName() {
/* 15 */     return this.firstName;
/*    */   } public String getLastName() {
/* 17 */     return this.lastName;
/*    */   } public String getStreet() {
/* 19 */     return this.street;
/*    */   } public String getCity() {
/* 21 */     return this.city;
/*    */   } public String getCountry() {
/* 23 */     return this.country;
/*    */   } public String getTelephone() {
/* 25 */     return this.telephone;
/*    */   } public String getPostalCode() {
/* 27 */     return this.postalCode;
/*    */   } public String getEmail() {
/* 29 */     return this.email;
/*    */   } public Date getLastChangeTime() {
/* 31 */     return this.lastChangeTime;
/*    */   } public String getPreserve1() {
/* 33 */     return this.preserve1;
/*    */   } public String getPreserve2() {
/* 35 */     return this.preserve2;
/*    */   } public String getPreserve3() {
/* 37 */     return this.preserve3;
/*    */   } public String getHouseNumber() {
/* 39 */     return this.houseNumber;
/*    */   } public String getStreet2() {
/* 41 */     return this.street2;
/*    */   } public String getStreet3() {
/* 43 */     return this.street3;
/*    */   } public String getRegion() {
/* 45 */     return this.region;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTBpMaster.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */