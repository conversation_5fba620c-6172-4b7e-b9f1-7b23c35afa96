package WEB-INF.classes.com.lenovo.iqs.datablocksign.Constants;

public class DataBlockSignErrorConstants {
  public static final String INVALID_ETOKEN_IP = "8020,Invalid etoken ip";
  
  public static final String CRC_VALID_ERROR = "8023,CRC Check Failed";
  
  public static final String INVALID_INPUT = "8045,oldIMEI or newIM<PERSON> or passC<PERSON>ereq is null";
  
  public static final String INVALID_SNO_TYPE = "8002,Not a valid Serial Number Type";
  
  public static final String INVALID_REQ_TYPE = "8026,Invalid Data Block Signing Request Type";
  
  public static final String SN_NOT_UPD = "8009,Serial Number not available in IBASE";
  
  public static final String UPDATE_IMEI_FAILURE = "8011,Updation of New IMEI is Failure";
  
  public static final String PKI_CONN_FAILURE = "8105,PKI connection failure.";
  
  public static final String PKI_NLCONN_FAILURE = "8205,NL PKI server connection failure.";
  
  public static final String PKI_SUCCESS = "8047,PKI Reply Success";
  
  public static final String PKI_FAILURE = "8048,PKI Reply Failed";
  
  public static final String PKI_DATA_SUPPLY_DEPLETED = "PKI_Data_Supply_Depleted";
  
  public static final String PKI_UNREG_DATA = "Unrecognized_PKI_Data_Type";
  
  public static final String PKI_OVER_SIZE = "Response_Oversized";
  
  public static final String JANUS_COMPLETED = "8049,Janus Key/IPRM Programming has been successfully completed";
  
  public static final String RESERVED = "RESERVED";
  
  public static final String DBS_COMPLETED = "8022,Data Block Signing has been successfully completed";
  
  public static final String DBS_COMPLETED_DUE_TO_Exception = "8022,Data Block Signing has been successfully completed due to exception";
  
  public static final String DBS_ERROR_REASON_PKI = "8018,Data Block Signing completed with Error.";
  
  public static final String Selected_Key_Not_Available = "Selected_Key_Not_Available";
  
  public static final String PKI_UPD_CONNECTION = "UPD_Connect_Error";
  
  public static final String PKI_SBK_NOT_AVAIL = "SBK_Not_Available";
  
  public static final String PKI_CRC_FAILED = "CRC_Failed";
  
  public static final String PKI_MISMATCH = "Protocol_Version_Mismatch";
  
  public static final String PKI_INCORRECT_LENGTH = "Incorrect_Message_Length";
  
  public static final String PKI_Format_Error = "Format_Error";
  
  public static final String PKI_Internal_Error = "Internal_Error";
  
  public static final String PKI_INVALID_ID = "INVALID_MSG_ID";
  
  public static final String PKI_UPD_COMM_ERR = "UPD_Comm_Error";
  
  public static final String NOT_ALL_DB_MATCHES_PROTOCOL_VERSION3 = "Not all Datablock types in the request is built for version 3";
  
  public static final String NOT_WHITE_IMEI = "8032,IMEI from PFE MASC is not White Listed IMEI";
  
  public static final String PWD_RET_FAILURE = "8012,Password Retrieval Failed";
  
  public static final String PROCESSRE_QUEST_FAIL = "8106,Exception while processing the request";
  
  public static final String PFE_LC_FAILURE = "8035,Unable to retrieve Lock Code";
  
  public static final String NOT_WHITE_SERIAL_NUMBER = "8036,Serial Number is not White Listed";
  
  public static final String WHITE_SERIAL_NUMBER = "8037,Serial Number is White Listed";
  
  public static final String INVALID_DATABLOCK_SERIAL = "7050, Serial from DataBlock is not match with request serial";
  
  public static final String FORBIDDEN_IMEI = "7051,Serial is Forbidden";
  
  public static final String PKI_IP_NOTFOUND = "7052,Can't find PKI server IP in Configuration table";
  
  public static final String DATABLOCK_PARSE_ERROR = "7053,Datablock parsing error";
  
  public static final String DATABLOCK_PID_MAPPING_ERROR = "7054,Did not find PID Mapping IMEI";
  
  public static final String DATABLOCK_INVALID = "7056,datablock in request is invalid";
  
  public static final String ETOKEN_MISSING_OR_NOT_MATCH = "7055,etoken missing or etoken in datablock does not match";
  
  public static final String RSD_LOGID_IS_EMPTY = "7056,RSD_LOGID is empty and configured blocking requeset";
  
  public static final String RSD_VALIDATION_ERROR = "7057, RSD Validation service failed with exception";
  
  public static final String RSD_ETOEKN_VALIDN_ERROR = "7058, Etoken Not allowed as per RSD validation";
  
  public static final String RSD_PUBIP_VALIDN_ERROR = "7059, publicip Not allowed as per RSD validation";
  
  public static final String RSD_USERID_VALIDN_ERROR = "7060, userid Not allowed as per RSD validation";
  
  public static final String RSD_REQTYPE_VALIDN_ERROR = "7061, reqType Not allowed as per RSD validation";
  
  public static final String USER_MISSING_OR_NOT_MATCH = "7062,UserId missing or UserId in datablock does not match";
  
  public static final String MASCID_MISSING = "7063, Mascid does not meet length requirement";
  
  public static final String GENERIC_ERROR = "9999,Request Failed";
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\Constants\DataBlockSignErrorConstants.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */