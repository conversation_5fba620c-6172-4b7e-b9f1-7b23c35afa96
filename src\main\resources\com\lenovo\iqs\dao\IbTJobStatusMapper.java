package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTJobStatus;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTJobStatusMapper {
  int deleteByPrimaryKey(Integer paramInteger);
  
  int insert(IbTJobStatus paramIbTJobStatus);
  
  IbTJobStatus selectByPrimaryKey(Integer paramInteger);
  
  int updateToLoaddataLoading(IbTJobStatus paramIbTJobStatus);
  
  int updateToLoaddataOver(IbTJobStatus paramIbTJobStatus);
  
  int updateToLoaddataStatus(IbTJobStatus paramIbTJobStatus);
  
  int updateToProcesseProcessing(IbTJobStatus paramIbTJobStatus);
  
  int updateToProcesseOver(IbTJobStatus paramIbTJobStatus);
  
  String getMaxParameter();
  
  String getAodMaxParameter();
  
  IbTJobStatus selectByBatchNumber(Map<String, String> paramMap);
  
  void updateByPrimaryKey(IbTJobStatus paramIbTJobStatus);
  
  String selectByJobIdAndJobType(String paramString);
  
  List<IbTJobStatus> selectLtBatchNumber(Map<String, String> paramMap);
  
  String getMaxMotoParameter();
  
  String getMaxPOPParameter();
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTJobStatusMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */