package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTUpdCartonMr;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTUpdCartonMrMapper {
  int deleteByPrimaryKey(String paramString);
  
  int insert(IbTUpdCartonMr paramIbTUpdCartonMr);
  
  IbTUpdCartonMr selectByPrimaryKey(String paramString);
  
  int updateByPrimaryKey(IbTUpdCartonMr paramIbTUpdCartonMr);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTUpdCartonMrMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */