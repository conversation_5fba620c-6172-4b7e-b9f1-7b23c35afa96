# IQS - Scripts de Automatización para Setup Local

## 🚀 Scripts de Configuración Automática

Estos scripts te ayudarán a configurar rápidamente el ambiente local de desarrollo para IQS.

## 📜 Script Principal de Setup (setup.sh)

### **Para Linux/macOS**
```bash
#!/bin/bash

echo "🚀 Iniciando configuración de IQS Local..."

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Función para logging
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar prerrequisitos
check_prerequisites() {
    log_info "Verificando prerrequisitos..."
    
    # Java 8
    if ! java -version 2>&1 | grep -q "1.8"; then
        log_error "Java 8 no encontrado. Por favor instalar Java 8 JDK"
        exit 1
    fi
    log_info "✓ Java 8 encontrado"
    
    # Maven
    if ! command -v mvn &> /dev/null; then
        log_error "Maven no encontrado. Por favor instalar Maven 3.6+"
        exit 1
    fi
    log_info "✓ Maven encontrado"
    
    # MySQL
    if ! command -v mysql &> /dev/null; then
        log_warn "MySQL no encontrado. Instalando..."
        install_mysql
    fi
    log_info "✓ MySQL disponible"
    
    # Python 3
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 no encontrado. Por favor instalar Python 3.8+"
        exit 1
    fi
    log_info "✓ Python 3 encontrado"
}

# Instalar MySQL
install_mysql() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        sudo apt update
        sudo apt install -y mysql-server mysql-client
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        brew install mysql
    fi
}

# Configurar base de datos
setup_database() {
    log_info "Configurando base de datos MySQL..."
    
    # Crear script SQL temporal
    cat > /tmp/iqs_setup.sql << EOF
CREATE DATABASE IF NOT EXISTS iqs_local CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'iqs_dev'@'localhost' IDENTIFIED BY 'iqs_dev_password';
GRANT ALL PRIVILEGES ON iqs_local.* TO 'iqs_dev'@'localhost';
FLUSH PRIVILEGES;

USE iqs_local;

-- Tablas Quartz
CREATE TABLE IF NOT EXISTS QRTZ_JOB_DETAILS (
    SCHED_NAME VARCHAR(120) NOT NULL,
    JOB_NAME VARCHAR(200) NOT NULL,
    JOB_GROUP VARCHAR(200) NOT NULL,
    DESCRIPTION VARCHAR(250) NULL,
    JOB_CLASS_NAME VARCHAR(250) NOT NULL,
    IS_DURABLE VARCHAR(1) NOT NULL,
    IS_NONCONCURRENT VARCHAR(1) NOT NULL,
    IS_UPDATE_DATA VARCHAR(1) NOT NULL,
    REQUESTS_RECOVERY VARCHAR(1) NOT NULL,
    JOB_DATA BLOB NULL,
    PRIMARY KEY (SCHED_NAME,JOB_NAME,JOB_GROUP)
);

-- Tablas de aplicación
CREATE TABLE IF NOT EXISTS devices (
    imei VARCHAR(15) PRIMARY KEY,
    model VARCHAR(50),
    carrier VARCHAR(50),
    simlock_status VARCHAR(20),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS unlock_transactions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    imei VARCHAR(15),
    unlock_code VARCHAR(20),
    status VARCHAR(20),
    request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completion_date TIMESTAMP NULL
);

-- Datos de prueba
INSERT IGNORE INTO devices (imei, model, carrier, simlock_status) VALUES
('123456789012345', 'MOTO_G', 'VERIZON', 'LOCKED'),
('123456789012346', 'MOTO_E', 'ATT', 'UNLOCKED'),
('123456789012347', 'MOTO_X', 'TMOBILE', 'LOCKED');
EOF

    # Ejecutar script SQL
    mysql -u root -p < /tmp/iqs_setup.sql
    
    if [ $? -eq 0 ]; then
        log_info "✓ Base de datos configurada correctamente"
    else
        log_error "Error configurando base de datos"
        exit 1
    fi
    
    # Limpiar archivo temporal
    rm /tmp/iqs_setup.sql
}

# Configurar Python
setup_python() {
    log_info "Configurando ambiente Python..."
    
    # Crear directorio para scripts Python
    mkdir -p python_scripts
    
    # Crear ambiente virtual
    python3 -m venv iqs_env
    source iqs_env/bin/activate
    
    # Instalar dependencias
    pip install cryptography requests python-dateutil
    
    log_info "✓ Ambiente Python configurado"
}

# Crear estructura del proyecto
create_project_structure() {
    log_info "Creando estructura del proyecto..."
    
    mkdir -p src/main/java/com/lenovo/iqs
    mkdir -p src/main/resources
    mkdir -p src/main/webapp/WEB-INF
    mkdir -p src/test/java
    mkdir -p logs
    
    log_info "✓ Estructura del proyecto creada"
}

# Crear archivos de configuración
create_config_files() {
    log_info "Creando archivos de configuración..."
    
    # config-local.properties
    cat > src/main/resources/config-local.properties << EOF
# Base de datos local MySQL
jdbc.driverClassName=com.mysql.cj.jdbc.Driver
jdbc.url=*********************************************************************
jdbc.username=iqs_dev
jdbc.password=iqs_dev_password

# Configuración para testing
config.driverClassName=com.mysql.cj.jdbc.Driver
config.url=*********************************************************************
config.username=iqs_dev
config.password=iqs_dev_password

# Servicios externos (Mock)
GPS_URL=http://localhost:8080/iqs/mock/gps
ibase_url=http://localhost:8080/iqs/mock/ibase

# Python scripts
python.scripts.path=./python_scripts
python.executable=python3
EOF

    # pom.xml básico
    cat > pom.xml << EOF
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.lenovo</groupId>
    <artifactId>iqs-local</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>war</packaging>
    
    <properties>
        <java.version>1.8</java.version>
        <spring.version>4.2.1.RELEASE</spring.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    
    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>\${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.0.14</version>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.tomcat.maven</groupId>
                <artifactId>tomcat7-maven-plugin</artifactId>
                <version>2.2</version>
                <configuration>
                    <port>8080</port>
                    <path>/iqs</path>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
EOF

    log_info "✓ Archivos de configuración creados"
}

# Función principal
main() {
    log_info "=== Setup IQS Local Development Environment ==="
    
    check_prerequisites
    setup_database
    setup_python
    create_project_structure
    create_config_files
    
    log_info "🎉 Setup completado exitosamente!"
    log_info ""
    log_info "Próximos pasos:"
    log_info "1. Copiar código fuente Java a src/main/java/"
    log_info "2. Copiar scripts Python a python_scripts/"
    log_info "3. Ejecutar: mvn clean compile"
    log_info "4. Ejecutar: mvn tomcat7:run"
    log_info "5. Acceder a: http://localhost:8080/iqs"
}

# Ejecutar función principal
main "$@"
```

## 🪟 Script para Windows (setup.bat)

```batch
@echo off
echo 🚀 Iniciando configuracion de IQS Local...

REM Verificar Java 8
java -version 2>&1 | findstr "1.8" >nul
if errorlevel 1 (
    echo [ERROR] Java 8 no encontrado. Por favor instalar Java 8 JDK
    pause
    exit /b 1
)
echo [INFO] ✓ Java 8 encontrado

REM Verificar Maven
mvn -version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Maven no encontrado. Por favor instalar Maven 3.6+
    pause
    exit /b 1
)
echo [INFO] ✓ Maven encontrado

REM Crear estructura del proyecto
echo [INFO] Creando estructura del proyecto...
mkdir src\main\java\com\lenovo\iqs 2>nul
mkdir src\main\resources 2>nul
mkdir src\main\webapp\WEB-INF 2>nul
mkdir src\test\java 2>nul
mkdir python_scripts 2>nul
mkdir logs 2>nul

REM Crear config-local.properties
echo [INFO] Creando archivos de configuracion...
(
echo # Base de datos local MySQL
echo jdbc.driverClassName=com.mysql.cj.jdbc.Driver
echo jdbc.url=**********************************************************************
echo jdbc.username=iqs_dev
echo jdbc.password=iqs_dev_password
echo.
echo # Servicios externos ^(Mock^)
echo GPS_URL=http://localhost:8080/iqs/mock/gps
echo ibase_url=http://localhost:8080/iqs/mock/ibase
echo.
echo # Python scripts
echo python.scripts.path=./python_scripts
echo python.executable=python
) > src\main\resources\config-local.properties

echo [INFO] 🎉 Setup completado!
echo [INFO] Proximos pasos:
echo [INFO] 1. Configurar MySQL manualmente
echo [INFO] 2. Copiar codigo fuente Java
echo [INFO] 3. Ejecutar: mvn clean compile
echo [INFO] 4. Ejecutar: mvn tomcat7:run
pause
```

## 🐳 Docker Setup (docker-setup.sh)

```bash
#!/bin/bash

echo "🐳 Configurando IQS con Docker..."

# Crear docker-compose.yml
cat > docker-compose.yml << EOF
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    container_name: iqs-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: iqs_local
      MYSQL_USER: iqs_dev
      MYSQL_PASSWORD: iqs_dev_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - iqs-network

  iqs-app:
    build: .
    container_name: iqs-app
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=iqs_local
      - DB_USER=iqs_dev
      - DB_PASSWORD=iqs_dev_password
    networks:
      - iqs-network
    volumes:
      - ./logs:/app/logs

volumes:
  mysql_data:

networks:
  iqs-network:
    driver: bridge
EOF

# Crear Dockerfile
cat > Dockerfile << EOF
FROM openjdk:8-jdk-alpine

# Instalar Python
RUN apk add --no-cache python3 py3-pip

# Crear directorio de aplicación
WORKDIR /app

# Copiar archivos de la aplicación
COPY target/iqs-local.war app.war
COPY python_scripts/ python_scripts/

# Instalar dependencias Python
RUN pip3 install cryptography requests python-dateutil

# Exponer puerto
EXPOSE 8080

# Comando de inicio
ENTRYPOINT ["java", "-jar", "app.war"]
EOF

# Crear script SQL de inicialización
cat > init.sql << EOF
USE iqs_local;

CREATE TABLE IF NOT EXISTS devices (
    imei VARCHAR(15) PRIMARY KEY,
    model VARCHAR(50),
    carrier VARCHAR(50),
    simlock_status VARCHAR(20),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT IGNORE INTO devices (imei, model, carrier, simlock_status) VALUES
('123456789012345', 'MOTO_G', 'VERIZON', 'LOCKED'),
('123456789012346', 'MOTO_E', 'ATT', 'UNLOCKED');
EOF

echo "✓ Archivos Docker creados"
echo "Para ejecutar:"
echo "1. mvn clean package"
echo "2. docker-compose up --build"
```

## 🧪 Script de Testing (test-apis.sh)

```bash
#!/bin/bash

BASE_URL="http://localhost:8080/iqs"

echo "🧪 Ejecutando tests de APIs..."

# Test Health Check
echo "Testing Health Check..."
curl -s "$BASE_URL/health" | jq '.' || echo "Health check failed"

# Test RSU Unlock
echo "Testing RSU Unlock..."
curl -X POST "$BASE_URL/webservice/rest/rsu/unlock" \
  -H "Content-Type: application/json" \
  -d '{"imei":"123456789012345","model":"MOTO_G","carrier":"VERIZON"}' \
  | jq '.' || echo "RSU unlock test failed"

# Test Device Query
echo "Testing Device Query..."
curl -s "$BASE_URL/webservice/rest/device/123456789012345" \
  | jq '.' || echo "Device query test failed"

echo "✓ Tests completados"
```

## 📋 Script de Limpieza (cleanup.sh)

```bash
#!/bin/bash

echo "🧹 Limpiando ambiente de desarrollo..."

# Parar servicios
docker-compose down 2>/dev/null
pkill -f tomcat 2>/dev/null

# Limpiar archivos generados
rm -rf target/
rm -rf logs/*.log
rm -rf iqs_env/

# Limpiar base de datos (opcional)
read -p "¿Limpiar base de datos? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    mysql -u root -p -e "DROP DATABASE IF EXISTS iqs_local;"
    echo "✓ Base de datos limpiada"
fi

echo "✓ Limpieza completada"
```

## 🚀 Uso de los Scripts

### **Configuración Inicial**
```bash
# Hacer ejecutables
chmod +x setup.sh docker-setup.sh test-apis.sh cleanup.sh

# Ejecutar setup
./setup.sh

# O con Docker
./docker-setup.sh
```

### **Testing**
```bash
# Después de que la aplicación esté corriendo
./test-apis.sh
```

### **Limpieza**
```bash
# Para limpiar el ambiente
./cleanup.sh
```
