package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTImeiSnMb;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTImeiSnMbMapper {
  int deleteByPrimaryKey(Integer paramInteger);
  
  int insert(IbTImeiSnMb paramIbTImeiSnMb);
  
  IbTImeiSnMb selectByPrimaryKey(Integer paramInteger);
  
  int updateByPrimaryKey(IbTImeiSnMb paramIbTImeiSnMb);
  
  List<IbTImeiSnMb> selectByImei(@Param("imeiCode") String paramString);
  
  List<IbTImeiSnMb> selectBySn(@Param("serialNumber") String paramString);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTImeiSnMbMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */