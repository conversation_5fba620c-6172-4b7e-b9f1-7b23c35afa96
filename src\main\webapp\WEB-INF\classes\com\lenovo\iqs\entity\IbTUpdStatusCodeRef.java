/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTUpdStatusCodeRef implements Serializable { private String statusCode; private Date startDate;
/*    */   private Date endDate;
/*    */   private String description;
/*    */   private String statusType;
/*    */   
/*  7 */   public void setStatusCode(String statusCode) { this.statusCode = statusCode; } private String cancelCode; private String createdBy; private Date creationDatetime; private String lastModUser; private Date lastModDate; private static final long serialVersionUID = 1L; public void setStartDate(Date startDate) { this.startDate = startDate; } public void setEndDate(Date endDate) { this.endDate = endDate; } public void setDescription(String description) { this.description = description; } public void setStatusType(String statusType) { this.statusType = statusType; } public void setCancelCode(String cancelCode) { this.cancelCode = cancelCode; } public void setCreatedBy(String createdBy) { this.createdBy = createdBy; } public void setCreationDatetime(Date creationDatetime) { this.creationDatetime = creationDatetime; } public void setLastModUser(String lastModUser) { this.lastModUser = lastModUser; } public void setLastModDate(Date lastModDate) { this.lastModDate = lastModDate; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdStatusCodeRef)) return false;  com.lenovo.iqs.entity.IbTUpdStatusCodeRef other = (com.lenovo.iqs.entity.IbTUpdStatusCodeRef)o; if (!other.canEqual(this)) return false;  Object this$statusCode = getStatusCode(), other$statusCode = other.getStatusCode(); if ((this$statusCode == null) ? (other$statusCode != null) : !this$statusCode.equals(other$statusCode)) return false;  Object this$startDate = getStartDate(), other$startDate = other.getStartDate(); if ((this$startDate == null) ? (other$startDate != null) : !this$startDate.equals(other$startDate)) return false;  Object this$endDate = getEndDate(), other$endDate = other.getEndDate(); if ((this$endDate == null) ? (other$endDate != null) : !this$endDate.equals(other$endDate)) return false;  Object this$description = getDescription(), other$description = other.getDescription(); if ((this$description == null) ? (other$description != null) : !this$description.equals(other$description)) return false;  Object this$statusType = getStatusType(), other$statusType = other.getStatusType(); if ((this$statusType == null) ? (other$statusType != null) : !this$statusType.equals(other$statusType)) return false;  Object this$cancelCode = getCancelCode(), other$cancelCode = other.getCancelCode(); if ((this$cancelCode == null) ? (other$cancelCode != null) : !this$cancelCode.equals(other$cancelCode)) return false;  Object this$createdBy = getCreatedBy(), other$createdBy = other.getCreatedBy(); if ((this$createdBy == null) ? (other$createdBy != null) : !this$createdBy.equals(other$createdBy)) return false;  Object this$creationDatetime = getCreationDatetime(), other$creationDatetime = other.getCreationDatetime(); if ((this$creationDatetime == null) ? (other$creationDatetime != null) : !this$creationDatetime.equals(other$creationDatetime)) return false;  Object this$lastModUser = getLastModUser(), other$lastModUser = other.getLastModUser(); if ((this$lastModUser == null) ? (other$lastModUser != null) : !this$lastModUser.equals(other$lastModUser)) return false;  Object this$lastModDate = getLastModDate(), other$lastModDate = other.getLastModDate(); return !((this$lastModDate == null) ? (other$lastModDate != null) : !this$lastModDate.equals(other$lastModDate)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdStatusCodeRef; } public int hashCode() { int PRIME = 59; result = 1; Object $statusCode = getStatusCode(); result = result * 59 + (($statusCode == null) ? 43 : $statusCode.hashCode()); Object $startDate = getStartDate(); result = result * 59 + (($startDate == null) ? 43 : $startDate.hashCode()); Object $endDate = getEndDate(); result = result * 59 + (($endDate == null) ? 43 : $endDate.hashCode()); Object $description = getDescription(); result = result * 59 + (($description == null) ? 43 : $description.hashCode()); Object $statusType = getStatusType(); result = result * 59 + (($statusType == null) ? 43 : $statusType.hashCode()); Object $cancelCode = getCancelCode(); result = result * 59 + (($cancelCode == null) ? 43 : $cancelCode.hashCode()); Object $createdBy = getCreatedBy(); result = result * 59 + (($createdBy == null) ? 43 : $createdBy.hashCode()); Object $creationDatetime = getCreationDatetime(); result = result * 59 + (($creationDatetime == null) ? 43 : $creationDatetime.hashCode()); Object $lastModUser = getLastModUser(); result = result * 59 + (($lastModUser == null) ? 43 : $lastModUser.hashCode()); Object $lastModDate = getLastModDate(); return result * 59 + (($lastModDate == null) ? 43 : $lastModDate.hashCode()); } public String toString() { return "IbTUpdStatusCodeRef(statusCode=" + getStatusCode() + ", startDate=" + getStartDate() + ", endDate=" + getEndDate() + ", description=" + getDescription() + ", statusType=" + getStatusType() + ", cancelCode=" + getCancelCode() + ", createdBy=" + getCreatedBy() + ", creationDatetime=" + getCreationDatetime() + ", lastModUser=" + getLastModUser() + ", lastModDate=" + getLastModDate() + ")"; }
/*    */    public String getStatusCode() {
/*  9 */     return this.statusCode;
/*    */   } public Date getStartDate() {
/* 11 */     return this.startDate;
/*    */   } public Date getEndDate() {
/* 13 */     return this.endDate;
/*    */   } public String getDescription() {
/* 15 */     return this.description;
/*    */   } public String getStatusType() {
/* 17 */     return this.statusType;
/*    */   } public String getCancelCode() {
/* 19 */     return this.cancelCode;
/*    */   } public String getCreatedBy() {
/* 21 */     return this.createdBy;
/*    */   } public Date getCreationDatetime() {
/* 23 */     return this.creationDatetime;
/*    */   } public String getLastModUser() {
/* 25 */     return this.lastModUser;
/*    */   } public Date getLastModDate() {
/* 27 */     return this.lastModDate;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdStatusCodeRef.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */