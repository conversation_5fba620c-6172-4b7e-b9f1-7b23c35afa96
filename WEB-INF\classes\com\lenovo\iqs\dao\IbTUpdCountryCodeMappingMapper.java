package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTUpdCountryCodeMapping;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTUpdCountryCodeMappingMapper {
  int deleteByPrimaryKey(Integer paramInteger);
  
  int insert(IbTUpdCountryCodeMapping paramIbTUpdCountryCodeMapping);
  
  IbTUpdCountryCodeMapping selectByPrimaryKey(Integer paramInteger);
  
  int updateByPrimaryKey(IbTUpdCountryCodeMapping paramIbTUpdCountryCodeMapping);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTUpdCountryCodeMappingMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */