package WEB-INF.classes.com.lenovo.iqs.datablocksign.service;

import com.lenovo.iqs.datablocksign.bean.PkiNLHTTPSMessage;
import com.lenovo.iqs.datablocksign.bean.RequestBean;

public interface PkiConnectionService {
  byte[] forwardToPKI(RequestBean paramRequestBean, String paramString) throws Exception;
  
  byte[] forwardToPKI(byte[] paramArrayOfbyte, int paramInt, String paramString) throws Exception;
  
  byte[] forwardDeviceUnlockToPKI(byte[] paramArrayOfbyte, int paramInt, float paramFloat) throws Exception;
  
  PkiNLHTTPSMessage forwardToNLPKI(RequestBean paramRequestBean, String paramString) throws Exception;
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\service\PkiConnectionService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */