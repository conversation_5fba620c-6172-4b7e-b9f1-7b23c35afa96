/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTObjectBuildShip implements Serializable { private Integer id; private String machineType; private String serialNumber; private String iobjectId; private String productId; private String modelNumber; private Date buildDate; private Date shipDate; private String soldToCountry; private String shipToCountry; private Date returnDate; private String channelId; private String oesOrderNumber;
/*    */   private String oesSource;
/*    */   private String deliveryNumber;
/*    */   private String deliveryItemNumber;
/*    */   
/*  7 */   public void setId(Integer id) { this.id = id; } private String status; private String legacyCustomerNumb; private Date popDate; private String cspnNumber; private String warrantyAttr; private Date lastChangeTime; private String partitionChar; private String legacyCustNumbSys; private String buildSource; private String shipSource; private String objectFamily; private String objectCategory; private String preserve1; private String preserve2; private String preserve3; private static final long serialVersionUID = 1L; public void setMachineType(String machineType) { this.machineType = machineType; } public void setSerialNumber(String serialNumber) { this.serialNumber = serialNumber; } public void setIobjectId(String iobjectId) { this.iobjectId = iobjectId; } public void setProductId(String productId) { this.productId = productId; } public void setModelNumber(String modelNumber) { this.modelNumber = modelNumber; } public void setBuildDate(Date buildDate) { this.buildDate = buildDate; } public void setShipDate(Date shipDate) { this.shipDate = shipDate; } public void setSoldToCountry(String soldToCountry) { this.soldToCountry = soldToCountry; } public void setShipToCountry(String shipToCountry) { this.shipToCountry = shipToCountry; } public void setReturnDate(Date returnDate) { this.returnDate = returnDate; } public void setChannelId(String channelId) { this.channelId = channelId; } public void setOesOrderNumber(String oesOrderNumber) { this.oesOrderNumber = oesOrderNumber; } public void setOesSource(String oesSource) { this.oesSource = oesSource; } public void setDeliveryNumber(String deliveryNumber) { this.deliveryNumber = deliveryNumber; } public void setDeliveryItemNumber(String deliveryItemNumber) { this.deliveryItemNumber = deliveryItemNumber; } public void setStatus(String status) { this.status = status; } public void setLegacyCustomerNumb(String legacyCustomerNumb) { this.legacyCustomerNumb = legacyCustomerNumb; } public void setPopDate(Date popDate) { this.popDate = popDate; } public void setCspnNumber(String cspnNumber) { this.cspnNumber = cspnNumber; } public void setWarrantyAttr(String warrantyAttr) { this.warrantyAttr = warrantyAttr; } public void setLastChangeTime(Date lastChangeTime) { this.lastChangeTime = lastChangeTime; } public void setPartitionChar(String partitionChar) { this.partitionChar = partitionChar; } public void setLegacyCustNumbSys(String legacyCustNumbSys) { this.legacyCustNumbSys = legacyCustNumbSys; } public void setBuildSource(String buildSource) { this.buildSource = buildSource; } public void setShipSource(String shipSource) { this.shipSource = shipSource; } public void setObjectFamily(String objectFamily) { this.objectFamily = objectFamily; } public void setObjectCategory(String objectCategory) { this.objectCategory = objectCategory; } public void setPreserve1(String preserve1) { this.preserve1 = preserve1; } public void setPreserve2(String preserve2) { this.preserve2 = preserve2; } public void setPreserve3(String preserve3) { this.preserve3 = preserve3; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTObjectBuildShip)) return false;  com.lenovo.iqs.entity.IbTObjectBuildShip other = (com.lenovo.iqs.entity.IbTObjectBuildShip)o; if (!other.canEqual(this)) return false;  Object this$id = getId(), other$id = other.getId(); if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id)) return false;  Object this$machineType = getMachineType(), other$machineType = other.getMachineType(); if ((this$machineType == null) ? (other$machineType != null) : !this$machineType.equals(other$machineType)) return false;  Object this$serialNumber = getSerialNumber(), other$serialNumber = other.getSerialNumber(); if ((this$serialNumber == null) ? (other$serialNumber != null) : !this$serialNumber.equals(other$serialNumber)) return false;  Object this$iobjectId = getIobjectId(), other$iobjectId = other.getIobjectId(); if ((this$iobjectId == null) ? (other$iobjectId != null) : !this$iobjectId.equals(other$iobjectId)) return false;  Object this$productId = getProductId(), other$productId = other.getProductId(); if ((this$productId == null) ? (other$productId != null) : !this$productId.equals(other$productId)) return false;  Object this$modelNumber = getModelNumber(), other$modelNumber = other.getModelNumber(); if ((this$modelNumber == null) ? (other$modelNumber != null) : !this$modelNumber.equals(other$modelNumber)) return false;  Object this$buildDate = getBuildDate(), other$buildDate = other.getBuildDate(); if ((this$buildDate == null) ? (other$buildDate != null) : !this$buildDate.equals(other$buildDate)) return false;  Object this$shipDate = getShipDate(), other$shipDate = other.getShipDate(); if ((this$shipDate == null) ? (other$shipDate != null) : !this$shipDate.equals(other$shipDate)) return false;  Object this$soldToCountry = getSoldToCountry(), other$soldToCountry = other.getSoldToCountry(); if ((this$soldToCountry == null) ? (other$soldToCountry != null) : !this$soldToCountry.equals(other$soldToCountry)) return false;  Object this$shipToCountry = getShipToCountry(), other$shipToCountry = other.getShipToCountry(); if ((this$shipToCountry == null) ? (other$shipToCountry != null) : !this$shipToCountry.equals(other$shipToCountry)) return false;  Object this$returnDate = getReturnDate(), other$returnDate = other.getReturnDate(); if ((this$returnDate == null) ? (other$returnDate != null) : !this$returnDate.equals(other$returnDate)) return false;  Object this$channelId = getChannelId(), other$channelId = other.getChannelId(); if ((this$channelId == null) ? (other$channelId != null) : !this$channelId.equals(other$channelId)) return false;  Object this$oesOrderNumber = getOesOrderNumber(), other$oesOrderNumber = other.getOesOrderNumber(); if ((this$oesOrderNumber == null) ? (other$oesOrderNumber != null) : !this$oesOrderNumber.equals(other$oesOrderNumber)) return false;  Object this$oesSource = getOesSource(), other$oesSource = other.getOesSource(); if ((this$oesSource == null) ? (other$oesSource != null) : !this$oesSource.equals(other$oesSource)) return false;  Object this$deliveryNumber = getDeliveryNumber(), other$deliveryNumber = other.getDeliveryNumber(); if ((this$deliveryNumber == null) ? (other$deliveryNumber != null) : !this$deliveryNumber.equals(other$deliveryNumber)) return false;  Object this$deliveryItemNumber = getDeliveryItemNumber(), other$deliveryItemNumber = other.getDeliveryItemNumber(); if ((this$deliveryItemNumber == null) ? (other$deliveryItemNumber != null) : !this$deliveryItemNumber.equals(other$deliveryItemNumber)) return false;  Object this$status = getStatus(), other$status = other.getStatus(); if ((this$status == null) ? (other$status != null) : !this$status.equals(other$status)) return false;  Object this$legacyCustomerNumb = getLegacyCustomerNumb(), other$legacyCustomerNumb = other.getLegacyCustomerNumb(); if ((this$legacyCustomerNumb == null) ? (other$legacyCustomerNumb != null) : !this$legacyCustomerNumb.equals(other$legacyCustomerNumb)) return false;  Object this$popDate = getPopDate(), other$popDate = other.getPopDate(); if ((this$popDate == null) ? (other$popDate != null) : !this$popDate.equals(other$popDate)) return false;  Object this$cspnNumber = getCspnNumber(), other$cspnNumber = other.getCspnNumber(); if ((this$cspnNumber == null) ? (other$cspnNumber != null) : !this$cspnNumber.equals(other$cspnNumber)) return false;  Object this$warrantyAttr = getWarrantyAttr(), other$warrantyAttr = other.getWarrantyAttr(); if ((this$warrantyAttr == null) ? (other$warrantyAttr != null) : !this$warrantyAttr.equals(other$warrantyAttr)) return false;  Object this$lastChangeTime = getLastChangeTime(), other$lastChangeTime = other.getLastChangeTime(); if ((this$lastChangeTime == null) ? (other$lastChangeTime != null) : !this$lastChangeTime.equals(other$lastChangeTime)) return false;  Object this$partitionChar = getPartitionChar(), other$partitionChar = other.getPartitionChar(); if ((this$partitionChar == null) ? (other$partitionChar != null) : !this$partitionChar.equals(other$partitionChar)) return false;  Object this$legacyCustNumbSys = getLegacyCustNumbSys(), other$legacyCustNumbSys = other.getLegacyCustNumbSys(); if ((this$legacyCustNumbSys == null) ? (other$legacyCustNumbSys != null) : !this$legacyCustNumbSys.equals(other$legacyCustNumbSys)) return false;  Object this$buildSource = getBuildSource(), other$buildSource = other.getBuildSource(); if ((this$buildSource == null) ? (other$buildSource != null) : !this$buildSource.equals(other$buildSource)) return false;  Object this$shipSource = getShipSource(), other$shipSource = other.getShipSource(); if ((this$shipSource == null) ? (other$shipSource != null) : !this$shipSource.equals(other$shipSource)) return false;  Object this$objectFamily = getObjectFamily(), other$objectFamily = other.getObjectFamily(); if ((this$objectFamily == null) ? (other$objectFamily != null) : !this$objectFamily.equals(other$objectFamily)) return false;  Object this$objectCategory = getObjectCategory(), other$objectCategory = other.getObjectCategory(); if ((this$objectCategory == null) ? (other$objectCategory != null) : !this$objectCategory.equals(other$objectCategory)) return false;  Object this$preserve1 = getPreserve1(), other$preserve1 = other.getPreserve1(); if ((this$preserve1 == null) ? (other$preserve1 != null) : !this$preserve1.equals(other$preserve1)) return false;  Object this$preserve2 = getPreserve2(), other$preserve2 = other.getPreserve2(); if ((this$preserve2 == null) ? (other$preserve2 != null) : !this$preserve2.equals(other$preserve2)) return false;  Object this$preserve3 = getPreserve3(), other$preserve3 = other.getPreserve3(); return !((this$preserve3 == null) ? (other$preserve3 != null) : !this$preserve3.equals(other$preserve3)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTObjectBuildShip; } public int hashCode() { int PRIME = 59; result = 1; Object $id = getId(); result = result * 59 + (($id == null) ? 43 : $id.hashCode()); Object $machineType = getMachineType(); result = result * 59 + (($machineType == null) ? 43 : $machineType.hashCode()); Object $serialNumber = getSerialNumber(); result = result * 59 + (($serialNumber == null) ? 43 : $serialNumber.hashCode()); Object $iobjectId = getIobjectId(); result = result * 59 + (($iobjectId == null) ? 43 : $iobjectId.hashCode()); Object $productId = getProductId(); result = result * 59 + (($productId == null) ? 43 : $productId.hashCode()); Object $modelNumber = getModelNumber(); result = result * 59 + (($modelNumber == null) ? 43 : $modelNumber.hashCode()); Object $buildDate = getBuildDate(); result = result * 59 + (($buildDate == null) ? 43 : $buildDate.hashCode()); Object $shipDate = getShipDate(); result = result * 59 + (($shipDate == null) ? 43 : $shipDate.hashCode()); Object $soldToCountry = getSoldToCountry(); result = result * 59 + (($soldToCountry == null) ? 43 : $soldToCountry.hashCode()); Object $shipToCountry = getShipToCountry(); result = result * 59 + (($shipToCountry == null) ? 43 : $shipToCountry.hashCode()); Object $returnDate = getReturnDate(); result = result * 59 + (($returnDate == null) ? 43 : $returnDate.hashCode()); Object $channelId = getChannelId(); result = result * 59 + (($channelId == null) ? 43 : $channelId.hashCode()); Object $oesOrderNumber = getOesOrderNumber(); result = result * 59 + (($oesOrderNumber == null) ? 43 : $oesOrderNumber.hashCode()); Object $oesSource = getOesSource(); result = result * 59 + (($oesSource == null) ? 43 : $oesSource.hashCode()); Object $deliveryNumber = getDeliveryNumber(); result = result * 59 + (($deliveryNumber == null) ? 43 : $deliveryNumber.hashCode()); Object $deliveryItemNumber = getDeliveryItemNumber(); result = result * 59 + (($deliveryItemNumber == null) ? 43 : $deliveryItemNumber.hashCode()); Object $status = getStatus(); result = result * 59 + (($status == null) ? 43 : $status.hashCode()); Object $legacyCustomerNumb = getLegacyCustomerNumb(); result = result * 59 + (($legacyCustomerNumb == null) ? 43 : $legacyCustomerNumb.hashCode()); Object $popDate = getPopDate(); result = result * 59 + (($popDate == null) ? 43 : $popDate.hashCode()); Object $cspnNumber = getCspnNumber(); result = result * 59 + (($cspnNumber == null) ? 43 : $cspnNumber.hashCode()); Object $warrantyAttr = getWarrantyAttr(); result = result * 59 + (($warrantyAttr == null) ? 43 : $warrantyAttr.hashCode()); Object $lastChangeTime = getLastChangeTime(); result = result * 59 + (($lastChangeTime == null) ? 43 : $lastChangeTime.hashCode()); Object $partitionChar = getPartitionChar(); result = result * 59 + (($partitionChar == null) ? 43 : $partitionChar.hashCode()); Object $legacyCustNumbSys = getLegacyCustNumbSys(); result = result * 59 + (($legacyCustNumbSys == null) ? 43 : $legacyCustNumbSys.hashCode()); Object $buildSource = getBuildSource(); result = result * 59 + (($buildSource == null) ? 43 : $buildSource.hashCode()); Object $shipSource = getShipSource(); result = result * 59 + (($shipSource == null) ? 43 : $shipSource.hashCode()); Object $objectFamily = getObjectFamily(); result = result * 59 + (($objectFamily == null) ? 43 : $objectFamily.hashCode()); Object $objectCategory = getObjectCategory(); result = result * 59 + (($objectCategory == null) ? 43 : $objectCategory.hashCode()); Object $preserve1 = getPreserve1(); result = result * 59 + (($preserve1 == null) ? 43 : $preserve1.hashCode()); Object $preserve2 = getPreserve2(); result = result * 59 + (($preserve2 == null) ? 43 : $preserve2.hashCode()); Object $preserve3 = getPreserve3(); return result * 59 + (($preserve3 == null) ? 43 : $preserve3.hashCode()); } public String toString() { return "IbTObjectBuildShip(id=" + getId() + ", machineType=" + getMachineType() + ", serialNumber=" + getSerialNumber() + ", iobjectId=" + getIobjectId() + ", productId=" + getProductId() + ", modelNumber=" + getModelNumber() + ", buildDate=" + getBuildDate() + ", shipDate=" + getShipDate() + ", soldToCountry=" + getSoldToCountry() + ", shipToCountry=" + getShipToCountry() + ", returnDate=" + getReturnDate() + ", channelId=" + getChannelId() + ", oesOrderNumber=" + getOesOrderNumber() + ", oesSource=" + getOesSource() + ", deliveryNumber=" + getDeliveryNumber() + ", deliveryItemNumber=" + getDeliveryItemNumber() + ", status=" + getStatus() + ", legacyCustomerNumb=" + getLegacyCustomerNumb() + ", popDate=" + getPopDate() + ", cspnNumber=" + getCspnNumber() + ", warrantyAttr=" + getWarrantyAttr() + ", lastChangeTime=" + getLastChangeTime() + ", partitionChar=" + getPartitionChar() + ", legacyCustNumbSys=" + getLegacyCustNumbSys() + ", buildSource=" + getBuildSource() + ", shipSource=" + getShipSource() + ", objectFamily=" + getObjectFamily() + ", objectCategory=" + getObjectCategory() + ", preserve1=" + getPreserve1() + ", preserve2=" + getPreserve2() + ", preserve3=" + getPreserve3() + ")"; }
/*    */    public Integer getId() {
/*  9 */     return this.id;
/*    */   } public String getMachineType() {
/* 11 */     return this.machineType;
/*    */   } public String getSerialNumber() {
/* 13 */     return this.serialNumber;
/*    */   } public String getIobjectId() {
/* 15 */     return this.iobjectId;
/*    */   } public String getProductId() {
/* 17 */     return this.productId;
/*    */   } public String getModelNumber() {
/* 19 */     return this.modelNumber;
/*    */   } public Date getBuildDate() {
/* 21 */     return this.buildDate;
/*    */   } public Date getShipDate() {
/* 23 */     return this.shipDate;
/*    */   } public String getSoldToCountry() {
/* 25 */     return this.soldToCountry;
/*    */   } public String getShipToCountry() {
/* 27 */     return this.shipToCountry;
/*    */   } public Date getReturnDate() {
/* 29 */     return this.returnDate;
/*    */   } public String getChannelId() {
/* 31 */     return this.channelId;
/*    */   } public String getOesOrderNumber() {
/* 33 */     return this.oesOrderNumber;
/*    */   } public String getOesSource() {
/* 35 */     return this.oesSource;
/*    */   } public String getDeliveryNumber() {
/* 37 */     return this.deliveryNumber;
/*    */   } public String getDeliveryItemNumber() {
/* 39 */     return this.deliveryItemNumber;
/*    */   } public String getStatus() {
/* 41 */     return this.status;
/*    */   } public String getLegacyCustomerNumb() {
/* 43 */     return this.legacyCustomerNumb;
/*    */   } public Date getPopDate() {
/* 45 */     return this.popDate;
/*    */   } public String getCspnNumber() {
/* 47 */     return this.cspnNumber;
/*    */   } public String getWarrantyAttr() {
/* 49 */     return this.warrantyAttr;
/*    */   } public Date getLastChangeTime() {
/* 51 */     return this.lastChangeTime;
/*    */   } public String getPartitionChar() {
/* 53 */     return this.partitionChar;
/*    */   } public String getLegacyCustNumbSys() {
/* 55 */     return this.legacyCustNumbSys;
/*    */   } public String getBuildSource() {
/* 57 */     return this.buildSource;
/*    */   } public String getShipSource() {
/* 59 */     return this.shipSource;
/*    */   } public String getObjectFamily() {
/* 61 */     return this.objectFamily;
/*    */   } public String getObjectCategory() {
/* 63 */     return this.objectCategory;
/*    */   } public String getPreserve1() {
/* 65 */     return this.preserve1;
/*    */   } public String getPreserve2() {
/* 67 */     return this.preserve2;
/*    */   } public String getPreserve3() {
/* 69 */     return this.preserve3;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTObjectBuildShip.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */