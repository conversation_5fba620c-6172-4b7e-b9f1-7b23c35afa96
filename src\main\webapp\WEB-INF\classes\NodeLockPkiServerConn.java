/*    */ package WEB-INF.classes;
/*    */ import java.net.URL;
/*    */ import java.security.KeyStore;
/*    */ import javax.net.ssl.HttpsURLConnection;
/*    */ import javax.net.ssl.KeyManagerFactory;
/*    */ import javax.net.ssl.SSLContext;
/*    */ import javax.net.ssl.TrustManagerFactory;
/*    */ 
/*    */ public class NodeLockPkiServerConn {
/*    */   public static void main(String[] args) {
/*    */     try {
/* 12 */       String pkiTrustCert = "/opt/tomcat8/data/pki.keystore";
/* 13 */       String pkiTrustPass = "ZujuIduJyche7694";
/* 14 */       String pkiIdentityCert = "/opt/tomcat8/data/ServiceCenterWebServices.pfx";
/* 15 */       String pkiIdentityPass = "GhibaLakaJuaTysho2676";
/*    */       
/* 17 */       String pkiIp = "**************";
/*    */       
/* 19 */       KeyStore trustKeyStore = KeyStore.getInstance("JKS");
/* 20 */       trustKeyStore.load(new FileInputStream(pkiTrustCert), pkiTrustPass.toCharArray());
/* 21 */       TrustManagerFactory tmf = TrustManagerFactory.getInstance("SunX509", "SunJSSE");
/* 22 */       tmf.init(trustKeyStore);
/* 23 */       TrustManager[] tm = tmf.getTrustManagers();
/*    */       
/* 25 */       KeyStore keyStore = KeyStore.getInstance("PKCS12");
/* 26 */       keyStore.load(new FileInputStream(pkiIdentityCert), pkiIdentityPass.toCharArray());
/* 27 */       KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509", "SunJSSE");
/* 28 */       kmf.init(keyStore, pkiIdentityPass.toCharArray());
/* 29 */       KeyManager[] km = kmf.getKeyManagers();
/*    */       
/* 31 */       SSLContext sslContext = SSLContext.getInstance("TLSv1", "SunJSSE");
/* 32 */       sslContext.init(km, tm, null);
/*    */       
/* 34 */       SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
/*    */       
/* 36 */       URL url = new URL("https://**************:2029/");
/* 37 */       HttpsURLConnection.setDefaultSSLSocketFactory(sslSocketFactory);
/*    */       
/* 39 */       HttpsURLConnection.setDefaultHostnameVerifier((HostnameVerifier)new Object());
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */       
/* 46 */       HttpsURLConnection con = (HttpsURLConnection)url.openConnection();
/*    */       
/* 48 */       con.setRequestMethod("POST");
/* 49 */       con.setRequestProperty("Content-Type", "application/json; utf-8");
/* 50 */       con.setRequestProperty("Accept", "application/json");
/* 51 */       con.setDoOutput(true);
/* 52 */       String jsonInputString = "{ \"tnlDbsRequest\": \"tnlRequestMessage\":\"something\",\"dbsRequestMessage\":\"something\" }";
/*    */       
/* 54 */       try (OutputStream os = con.getOutputStream()) {
/* 55 */         byte[] input = jsonInputString.getBytes("utf-8");
/* 56 */         os.write(input, 0, input.length);
/*    */       } 
/*    */       
/* 59 */       try (BufferedReader br = new BufferedReader(new InputStreamReader(con
/* 60 */               .getInputStream(), "utf-8"))) {
/* 61 */         StringBuilder response = new StringBuilder();
/* 62 */         String responseLine = null;
/* 63 */         while ((responseLine = br.readLine()) != null) {
/* 64 */           response.append(responseLine.trim());
/*    */         }
/* 66 */         System.out.println(response.toString());
/*    */       } 
/* 68 */     } catch (Exception e) {
/* 69 */       e.printStackTrace();
/*    */     } 
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\NodeLockPkiServerConn.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */