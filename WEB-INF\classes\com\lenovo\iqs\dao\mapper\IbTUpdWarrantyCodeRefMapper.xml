<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdWarrantyCodeRefMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdWarrantyCodeRef" >
    <id column="WARRANTY_CODE" property="warrantyCode" jdbcType="VARCHAR" />
    <result column="START_DATE" property="startDate" jdbcType="DATE" />
    <result column="END_DATE" property="endDate" jdbcType="DATE" />
    <result column="WARRANTY_CODE_DESC" property="warrantyCodeDesc" jdbcType="VARCHAR" />
    <result column="ENHANCEMENT_DESC" property="enhancementDesc" jdbcType="VARCHAR" />
    <result column="STD_WARRANTY_PERIOD" property="stdWarrantyPeriod" jdbcType="VARCHAR" />
    <result column="STD_WARRANTY_PERIOD_TYPE" property="stdWarrantyPeriodType" jdbcType="VARCHAR" />
    <result column="EXT_WARRANTY_PERIOD" property="extWarrantyPeriod" jdbcType="VARCHAR" />
    <result column="EXT_WARRANTY_PERIOD_TYPE" property="extWarrantyPeriodType" jdbcType="VARCHAR" />
    <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR" />
    <result column="CREATION_DATETIME" property="creationDatetime" jdbcType="DATE" />
    <result column="LAST_MOD_DATE" property="lastModDate" jdbcType="DATE" />
    <result column="LAST_MOD_USER" property="lastModUser" jdbcType="VARCHAR" />
    <result column="POP_WARRANTY_PERIOD" property="popWarrantyPeriod" jdbcType="VARCHAR" />
    <result column="POP_WARRANTY_PERIOD_TYPE" property="popWarrantyPeriodType" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    WARRANTY_CODE, START_DATE, END_DATE, WARRANTY_CODE_DESC, ENHANCEMENT_DESC, STD_WARRANTY_PERIOD, 
    STD_WARRANTY_PERIOD_TYPE, EXT_WARRANTY_PERIOD, EXT_WARRANTY_PERIOD_TYPE, CREATED_BY, 
    CREATION_DATETIME, LAST_MOD_DATE, LAST_MOD_USER, POP_WARRANTY_PERIOD, POP_WARRANTY_PERIOD_TYPE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_upd_warranty_code_ref
    where WARRANTY_CODE = #{warrantyCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from ib_t_upd_warranty_code_ref
    where WARRANTY_CODE = #{warrantyCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdWarrantyCodeRef" >
    insert into ib_t_upd_warranty_code_ref (WARRANTY_CODE, START_DATE, END_DATE, 
      WARRANTY_CODE_DESC, ENHANCEMENT_DESC, STD_WARRANTY_PERIOD, 
      STD_WARRANTY_PERIOD_TYPE, EXT_WARRANTY_PERIOD, 
      EXT_WARRANTY_PERIOD_TYPE, CREATED_BY, CREATION_DATETIME, 
      LAST_MOD_DATE, LAST_MOD_USER, POP_WARRANTY_PERIOD, 
      POP_WARRANTY_PERIOD_TYPE)
    values (#{warrantyCode,jdbcType=VARCHAR}, #{startDate,jdbcType=DATE}, #{endDate,jdbcType=DATE}, 
      #{warrantyCodeDesc,jdbcType=VARCHAR}, #{enhancementDesc,jdbcType=VARCHAR}, #{stdWarrantyPeriod,jdbcType=VARCHAR}, 
      #{stdWarrantyPeriodType,jdbcType=VARCHAR}, #{extWarrantyPeriod,jdbcType=VARCHAR}, 
      #{extWarrantyPeriodType,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{creationDatetime,jdbcType=DATE}, 
      #{lastModDate,jdbcType=DATE}, #{lastModUser,jdbcType=VARCHAR}, #{popWarrantyPeriod,jdbcType=VARCHAR}, 
      #{popWarrantyPeriodType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdWarrantyCodeRef" >
    insert into ib_t_upd_warranty_code_ref
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="warrantyCode != null" >
        WARRANTY_CODE,
      </if>
      <if test="startDate != null" >
        START_DATE,
      </if>
      <if test="endDate != null" >
        END_DATE,
      </if>
      <if test="warrantyCodeDesc != null" >
        WARRANTY_CODE_DESC,
      </if>
      <if test="enhancementDesc != null" >
        ENHANCEMENT_DESC,
      </if>
      <if test="stdWarrantyPeriod != null" >
        STD_WARRANTY_PERIOD,
      </if>
      <if test="stdWarrantyPeriodType != null" >
        STD_WARRANTY_PERIOD_TYPE,
      </if>
      <if test="extWarrantyPeriod != null" >
        EXT_WARRANTY_PERIOD,
      </if>
      <if test="extWarrantyPeriodType != null" >
        EXT_WARRANTY_PERIOD_TYPE,
      </if>
      <if test="createdBy != null" >
        CREATED_BY,
      </if>
      <if test="creationDatetime != null" >
        CREATION_DATETIME,
      </if>
      <if test="lastModDate != null" >
        LAST_MOD_DATE,
      </if>
      <if test="lastModUser != null" >
        LAST_MOD_USER,
      </if>
      <if test="popWarrantyPeriod != null" >
        POP_WARRANTY_PERIOD,
      </if>
      <if test="popWarrantyPeriodType != null" >
        POP_WARRANTY_PERIOD_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="warrantyCode != null" >
        #{warrantyCode,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null" >
        #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null" >
        #{endDate,jdbcType=DATE},
      </if>
      <if test="warrantyCodeDesc != null" >
        #{warrantyCodeDesc,jdbcType=VARCHAR},
      </if>
      <if test="enhancementDesc != null" >
        #{enhancementDesc,jdbcType=VARCHAR},
      </if>
      <if test="stdWarrantyPeriod != null" >
        #{stdWarrantyPeriod,jdbcType=VARCHAR},
      </if>
      <if test="stdWarrantyPeriodType != null" >
        #{stdWarrantyPeriodType,jdbcType=VARCHAR},
      </if>
      <if test="extWarrantyPeriod != null" >
        #{extWarrantyPeriod,jdbcType=VARCHAR},
      </if>
      <if test="extWarrantyPeriodType != null" >
        #{extWarrantyPeriodType,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="creationDatetime != null" >
        #{creationDatetime,jdbcType=DATE},
      </if>
      <if test="lastModDate != null" >
        #{lastModDate,jdbcType=DATE},
      </if>
      <if test="lastModUser != null" >
        #{lastModUser,jdbcType=VARCHAR},
      </if>
      <if test="popWarrantyPeriod != null" >
        #{popWarrantyPeriod,jdbcType=VARCHAR},
      </if>
      <if test="popWarrantyPeriodType != null" >
        #{popWarrantyPeriodType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTUpdWarrantyCodeRef" >
    update ib_t_upd_warranty_code_ref
    <set >
      <if test="startDate != null" >
        START_DATE = #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null" >
        END_DATE = #{endDate,jdbcType=DATE},
      </if>
      <if test="warrantyCodeDesc != null" >
        WARRANTY_CODE_DESC = #{warrantyCodeDesc,jdbcType=VARCHAR},
      </if>
      <if test="enhancementDesc != null" >
        ENHANCEMENT_DESC = #{enhancementDesc,jdbcType=VARCHAR},
      </if>
      <if test="stdWarrantyPeriod != null" >
        STD_WARRANTY_PERIOD = #{stdWarrantyPeriod,jdbcType=VARCHAR},
      </if>
      <if test="stdWarrantyPeriodType != null" >
        STD_WARRANTY_PERIOD_TYPE = #{stdWarrantyPeriodType,jdbcType=VARCHAR},
      </if>
      <if test="extWarrantyPeriod != null" >
        EXT_WARRANTY_PERIOD = #{extWarrantyPeriod,jdbcType=VARCHAR},
      </if>
      <if test="extWarrantyPeriodType != null" >
        EXT_WARRANTY_PERIOD_TYPE = #{extWarrantyPeriodType,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="creationDatetime != null" >
        CREATION_DATETIME = #{creationDatetime,jdbcType=DATE},
      </if>
      <if test="lastModDate != null" >
        LAST_MOD_DATE = #{lastModDate,jdbcType=DATE},
      </if>
      <if test="lastModUser != null" >
        LAST_MOD_USER = #{lastModUser,jdbcType=VARCHAR},
      </if>
      <if test="popWarrantyPeriod != null" >
        POP_WARRANTY_PERIOD = #{popWarrantyPeriod,jdbcType=VARCHAR},
      </if>
      <if test="popWarrantyPeriodType != null" >
        POP_WARRANTY_PERIOD_TYPE = #{popWarrantyPeriodType,jdbcType=VARCHAR},
      </if>
    </set>
    where WARRANTY_CODE = #{warrantyCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdWarrantyCodeRef" >
    update ib_t_upd_warranty_code_ref
    set START_DATE = #{startDate,jdbcType=DATE},
      END_DATE = #{endDate,jdbcType=DATE},
      WARRANTY_CODE_DESC = #{warrantyCodeDesc,jdbcType=VARCHAR},
      ENHANCEMENT_DESC = #{enhancementDesc,jdbcType=VARCHAR},
      STD_WARRANTY_PERIOD = #{stdWarrantyPeriod,jdbcType=VARCHAR},
      STD_WARRANTY_PERIOD_TYPE = #{stdWarrantyPeriodType,jdbcType=VARCHAR},
      EXT_WARRANTY_PERIOD = #{extWarrantyPeriod,jdbcType=VARCHAR},
      EXT_WARRANTY_PERIOD_TYPE = #{extWarrantyPeriodType,jdbcType=VARCHAR},
      CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      CREATION_DATETIME = #{creationDatetime,jdbcType=DATE},
      LAST_MOD_DATE = #{lastModDate,jdbcType=DATE},
      LAST_MOD_USER = #{lastModUser,jdbcType=VARCHAR},
      POP_WARRANTY_PERIOD = #{popWarrantyPeriod,jdbcType=VARCHAR},
      POP_WARRANTY_PERIOD_TYPE = #{popWarrantyPeriodType,jdbcType=VARCHAR}
    where WARRANTY_CODE = #{warrantyCode,jdbcType=VARCHAR}
  </update>
</mapper>