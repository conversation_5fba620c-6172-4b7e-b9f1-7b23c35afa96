# IQS - Integración con SAP

## 🏢 Descripción General

El sistema IQS se integra con sistemas SAP corporativos de Lenovo/Motorola utilizando **SAP Java Connector (JCo)** para:
- Consultas de información de productos
- Validación de garantías
- Sincronización de datos maestros
- Procesamiento de transacciones

## 🔧 Configuración SAP JCo

### **Librerías SAP**
```
WEB-INF/lib/
├── sapjco3.jar          # Librería Java SAP JCo
├── sapjco3.dll          # Librería nativa Windows
└── libsapjco3.so        # Librería nativa Linux
```

### **Configuración de Conexión**
```properties
# iBase SAP Config CSP
ibase.sap.appServerHost=**********
ibase.sap.logonGroup=IBASE
ibase.sap.messageServerHost=**********
ibase.sap.systemID=CSP
ibase.sap.systemNumber=71
ibase.sap.client=301
ibase.sap.user=SYSB-MBGIQS
ibase.sap.password=1234qwer
ibase.sap.language=EN
ibase.sap.poolCapacity=10
ibase.sap.peakLimit=10
```

## 🔗 Clase de Configuración SAP

### **SAPConfig.java**
```java
@Component
public class SAPConfig {
    @Value("${ibase.sap.appServerHost}")
    public String appServerHost;
    
    @Value("${ibase.sap.logonGroup}")
    public String logonGroup;
    
    @Value("${ibase.sap.messageServerHost}")
    public String messageServerHost;
    
    @Value("${ibase.sap.systemID}")
    public String systemID;
    
    @Value("${ibase.sap.systemNumber}")
    public String systemNumber;
    
    @Value("${ibase.sap.client}")
    public String client;
    
    @Value("${ibase.sap.user}")
    public String user;
    
    @Value("${ibase.sap.password}")
    public String password;
    
    @Value("${ibase.sap.language}")
    public String language;
    
    @Value("${ibase.sap.poolCapacity}")
    public String poolCapacity;
    
    @Value("${ibase.sap.peakLimit}")
    public String peakLimit;
    
    @Value("${ibase_url}")
    public String ibaseUrl;
}
```

## 🔄 Servicios SAP Disponibles

### **1. iBase Services**
**Propósito**: Consultas de información de productos y garantías

**URL Base**: `http://csp.lenovo.com/ibapp/POIRequest.jsp`

**Funcionalidades**:
- Consulta de información de productos por serial number
- Validación de garantías
- Información de configuración de hardware
- Historial de servicios

### **2. Batch Query Services**
**URL**: `http://qas-cs.lenovo.com/sap/bc/srt/rfc/sap/zibp_batch_query_by_sn/301/lclaim1/lclaim1`

**Funcionalidades**:
- Consultas por lotes de serial numbers
- Procesamiento masivo de información
- Sincronización de datos

### **3. WebService Authentication**
```properties
# iBase WebService Credentials
ibase.webservice.username=RFCJIBCSP
ibase.webservice.password=initial
```

## 🏗️ Arquitectura de Conexión

### **Pool de Conexiones SAP**
```java
public class SAPConnectionPool {
    private JCoDestination destination;
    private Properties connectProperties;
    
    public void initializeConnection() {
        connectProperties = new Properties();
        connectProperties.setProperty(DestinationDataProvider.JCO_ASHOST, appServerHost);
        connectProperties.setProperty(DestinationDataProvider.JCO_SYSNR, systemNumber);
        connectProperties.setProperty(DestinationDataProvider.JCO_CLIENT, client);
        connectProperties.setProperty(DestinationDataProvider.JCO_USER, user);
        connectProperties.setProperty(DestinationDataProvider.JCO_PASSWD, password);
        connectProperties.setProperty(DestinationDataProvider.JCO_LANG, language);
        connectProperties.setProperty(DestinationDataProvider.JCO_POOL_CAPACITY, poolCapacity);
        connectProperties.setProperty(DestinationDataProvider.JCO_PEAK_LIMIT, peakLimit);
    }
}
```

### **Gestión de Sesiones**
- **Pool Capacity**: 10 conexiones simultáneas
- **Peak Limit**: 10 conexiones máximas
- **Timeout**: Configurado por defecto SAP
- **Reconnection**: Automática en caso de fallo

## 📡 RFC (Remote Function Calls)

### **Estructura Típica de RFC**
```java
public class SAPRfcs {
    
    public JCoFunction getFunction(String functionName) throws JCoException {
        JCoDestination destination = JCoDestinationManager.getDestination("SAP_SYSTEM");
        JCoRepository repository = destination.getRepository();
        return repository.getFunction(functionName);
    }
    
    public void executeProductQuery(String serialNumber) throws JCoException {
        JCoFunction function = getFunction("ZIBP_PRODUCT_QUERY");
        
        // Parámetros de entrada
        JCoParameterList importParams = function.getImportParameterList();
        importParams.setValue("SERIAL_NUMBER", serialNumber);
        
        // Ejecutar función
        function.execute(destination);
        
        // Obtener resultados
        JCoParameterList exportParams = function.getExportParameterList();
        String result = exportParams.getString("PRODUCT_INFO");
    }
}
```

### **Funciones RFC Comunes**
1. **ZIBP_PRODUCT_QUERY**: Consulta de información de producto
2. **ZIBP_WARRANTY_CHECK**: Verificación de garantía
3. **ZIBP_BATCH_QUERY_BY_SN**: Consulta por lotes por serial number
4. **ZIBP_CONFIG_INFO**: Información de configuración

## 🔐 Autenticación y Seguridad

### **Credenciales de Sistema**
- **Usuario**: SYSB-MBGIQS (usuario de sistema)
- **Cliente SAP**: 301 (ambiente de producción)
- **Idioma**: EN (inglés)

### **Seguridad de Red**
- Conexión a través de red corporativa interna
- IPs específicas configuradas (**********)
- Autenticación por usuario/contraseña SAP

### **Manejo de Sesiones**
```java
public class SAPSessionManager {
    
    public void validateSession() throws JCoException {
        if (destination == null || !destination.isValid()) {
            reconnect();
        }
    }
    
    private void reconnect() throws JCoException {
        // Lógica de reconexión
        destination = JCoDestinationManager.getDestination("SAP_SYSTEM");
    }
}
```

## 📊 Procesamiento de Datos

### **Formato de Entrada**
```java
public class ProductQueryRequest {
    private String serialNumber;
    private String productType;
    private String queryType;
    
    // Getters y setters
}
```

### **Formato de Respuesta SAP**
```java
public class SAPResponse {
    private String returnCode;
    private String message;
    private ProductInfo productInfo;
    private WarrantyInfo warrantyInfo;
    
    public class ProductInfo {
        private String model;
        private String partNumber;
        private String manufacturingDate;
        private String configuration;
    }
    
    public class WarrantyInfo {
        private String warrantyStatus;
        private Date warrantyStartDate;
        private Date warrantyEndDate;
        private String coverageType;
    }
}
```

## 🔄 Integración con Servicios Web

### **Endpoint de Consulta**
```java
@RestController
@RequestMapping("/sap")
public class SAPIntegrationController {
    
    @Autowired
    private SAPRfcs sapRfcs;
    
    @PostMapping("/product-query")
    public ResponseEntity<SAPResponse> queryProduct(@RequestBody ProductQueryRequest request) {
        try {
            SAPResponse response = sapRfcs.queryProductInfo(request.getSerialNumber());
            return ResponseEntity.ok(response);
        } catch (JCoException e) {
            return ResponseEntity.status(500).body(createErrorResponse(e));
        }
    }
}
```

### **Procesamiento por Lotes**
```java
@Service
public class SAPBatchService {
    
    public List<SAPResponse> processBatch(List<String> serialNumbers) {
        List<SAPResponse> responses = new ArrayList<>();
        
        for (String serialNumber : serialNumbers) {
            try {
                SAPResponse response = queryProduct(serialNumber);
                responses.add(response);
            } catch (Exception e) {
                responses.add(createErrorResponse(serialNumber, e));
            }
        }
        
        return responses;
    }
}
```

## 📈 Monitoreo y Logging

### **Logging de Conexiones SAP**
```java
private static final Logger logger = LoggerFactory.getLogger(SAPRfcs.class);

public void logSAPCall(String functionName, String serialNumber) {
    logger.info("SAP RFC Call - Function: {}, Serial: {}, Timestamp: {}", 
                functionName, serialNumber, new Date());
}
```

### **Métricas de Rendimiento**
- Tiempo de respuesta de RFC calls
- Número de conexiones activas
- Tasa de errores de conexión
- Throughput de consultas

### **Alertas de Sistema**
- Conexiones fallidas
- Timeouts de RFC
- Pool de conexiones agotado
- Errores de autenticación

## 🚨 Manejo de Errores

### **Códigos de Error SAP Comunes**
```java
public enum SAPErrorCode {
    CONNECTION_FAILED("SAP_001", "Failed to connect to SAP system"),
    AUTHENTICATION_ERROR("SAP_002", "SAP authentication failed"),
    RFC_NOT_FOUND("SAP_003", "RFC function not found"),
    INVALID_PARAMETERS("SAP_004", "Invalid RFC parameters"),
    TIMEOUT("SAP_005", "SAP call timeout"),
    SYSTEM_UNAVAILABLE("SAP_006", "SAP system unavailable");
}
```

### **Estrategias de Recuperación**
1. **Retry Logic**: Reintentos automáticos con backoff exponencial
2. **Circuit Breaker**: Protección contra cascadas de fallos
3. **Fallback**: Datos cached o servicios alternativos
4. **Health Checks**: Verificación periódica de conectividad

## 🔧 Configuración de Ambiente

### **Desarrollo**
```properties
# SAP Development Environment
ibase.sap.appServerHost=dev-sap.lenovo.com
ibase.sap.systemID=DEV
ibase.sap.client=100
```

### **Testing**
```properties
# SAP Testing Environment  
ibase.sap.appServerHost=qas-cs.lenovo.com
ibase.sap.systemID=QAS
ibase.sap.client=200
```

### **Producción**
```properties
# SAP Production Environment
ibase.sap.appServerHost=**********
ibase.sap.systemID=CSP
ibase.sap.client=301
```
