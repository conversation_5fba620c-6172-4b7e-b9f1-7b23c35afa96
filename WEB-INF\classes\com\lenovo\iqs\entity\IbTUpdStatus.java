/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTUpdStatus implements Serializable {
/*    */   private Integer id;
/*    */   private String name;
/*    */   
/*  6 */   public void setId(Integer id) { this.id = id; } private String countryCode; private static final long serialVersionUID = 1L; public void setName(String name) { this.name = name; } public void setCountryCode(String countryCode) { this.countryCode = countryCode; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdStatus)) return false;  com.lenovo.iqs.entity.IbTUpdStatus other = (com.lenovo.iqs.entity.IbTUpdStatus)o; if (!other.canEqual(this)) return false;  Object this$id = getId(), other$id = other.getId(); if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id)) return false;  Object this$name = getName(), other$name = other.getName(); if ((this$name == null) ? (other$name != null) : !this$name.equals(other$name)) return false;  Object this$countryCode = getCountryCode(), other$countryCode = other.getCountryCode(); return !((this$countryCode == null) ? (other$countryCode != null) : !this$countryCode.equals(other$countryCode)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdStatus; } public int hashCode() { int PRIME = 59; result = 1; Object $id = getId(); result = result * 59 + (($id == null) ? 43 : $id.hashCode()); Object $name = getName(); result = result * 59 + (($name == null) ? 43 : $name.hashCode()); Object $countryCode = getCountryCode(); return result * 59 + (($countryCode == null) ? 43 : $countryCode.hashCode()); } public String toString() { return "IbTUpdStatus(id=" + getId() + ", name=" + getName() + ", countryCode=" + getCountryCode() + ")"; }
/*    */    public Integer getId() {
/*  8 */     return this.id;
/*    */   } public String getName() {
/* 10 */     return this.name;
/*    */   } public String getCountryCode() {
/* 12 */     return this.countryCode;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdStatus.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */