<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdCartonMrMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdCartonMr" >
    <id column="CARTON_ID" property="cartonId" jdbcType="VARCHAR" />
    <result column="WIP_DJ" property="wipDj" jdbcType="VARCHAR" />
    <result column="CARTON_QTY" property="cartonQty" jdbcType="INTEGER" />
    <result column="FLAG" property="flag" jdbcType="VARCHAR" />
    <result column="ATTRIBUTE1" property="attribute1" jdbcType="VARCHAR" />
    <result column="REC_EXTR_DATETIME" property="recExtrDatetime" jdbcType="DATE" />
    <result column="LAST_UPDATETIME_DATETIME" property="lastUpdatetimeDatetime" jdbcType="DATE" />
    <result column="REC_SRC" property="recSrc" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    CARTON_ID, WIP_DJ, CARTON_QTY, FLAG, ATTRIBUTE1, REC_EXTR_DATETIME, LAST_UPDATETIME_DATETIME, 
    REC_SRC
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_upd_carton_mr
    where CARTON_ID = #{cartonId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from ib_t_upd_carton_mr
    where CARTON_ID = #{cartonId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdCartonMr" >
    insert into ib_t_upd_carton_mr (CARTON_ID, WIP_DJ, CARTON_QTY, 
      FLAG, ATTRIBUTE1, REC_EXTR_DATETIME, 
      LAST_UPDATETIME_DATETIME, REC_SRC)
    values (#{cartonId,jdbcType=VARCHAR}, #{wipDj,jdbcType=VARCHAR}, #{cartonQty,jdbcType=INTEGER}, 
      #{flag,jdbcType=VARCHAR}, #{attribute1,jdbcType=VARCHAR}, #{recExtrDatetime,jdbcType=DATE}, 
      #{lastUpdatetimeDatetime,jdbcType=DATE}, #{recSrc,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdCartonMr" >
    insert into ib_t_upd_carton_mr
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="cartonId != null" >
        CARTON_ID,
      </if>
      <if test="wipDj != null" >
        WIP_DJ,
      </if>
      <if test="cartonQty != null" >
        CARTON_QTY,
      </if>
      <if test="flag != null" >
        FLAG,
      </if>
      <if test="attribute1 != null" >
        ATTRIBUTE1,
      </if>
      <if test="recExtrDatetime != null" >
        REC_EXTR_DATETIME,
      </if>
      <if test="lastUpdatetimeDatetime != null" >
        LAST_UPDATETIME_DATETIME,
      </if>
      <if test="recSrc != null" >
        REC_SRC,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="cartonId != null" >
        #{cartonId,jdbcType=VARCHAR},
      </if>
      <if test="wipDj != null" >
        #{wipDj,jdbcType=VARCHAR},
      </if>
      <if test="cartonQty != null" >
        #{cartonQty,jdbcType=INTEGER},
      </if>
      <if test="flag != null" >
        #{flag,jdbcType=VARCHAR},
      </if>
      <if test="attribute1 != null" >
        #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="recExtrDatetime != null" >
        #{recExtrDatetime,jdbcType=DATE},
      </if>
      <if test="lastUpdatetimeDatetime != null" >
        #{lastUpdatetimeDatetime,jdbcType=DATE},
      </if>
      <if test="recSrc != null" >
        #{recSrc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTUpdCartonMr" >
    update ib_t_upd_carton_mr
    <set >
      <if test="wipDj != null" >
        WIP_DJ = #{wipDj,jdbcType=VARCHAR},
      </if>
      <if test="cartonQty != null" >
        CARTON_QTY = #{cartonQty,jdbcType=INTEGER},
      </if>
      <if test="flag != null" >
        FLAG = #{flag,jdbcType=VARCHAR},
      </if>
      <if test="attribute1 != null" >
        ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="recExtrDatetime != null" >
        REC_EXTR_DATETIME = #{recExtrDatetime,jdbcType=DATE},
      </if>
      <if test="lastUpdatetimeDatetime != null" >
        LAST_UPDATETIME_DATETIME = #{lastUpdatetimeDatetime,jdbcType=DATE},
      </if>
      <if test="recSrc != null" >
        REC_SRC = #{recSrc,jdbcType=VARCHAR},
      </if>
    </set>
    where CARTON_ID = #{cartonId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdCartonMr" >
    update ib_t_upd_carton_mr
    set WIP_DJ = #{wipDj,jdbcType=VARCHAR},
      CARTON_QTY = #{cartonQty,jdbcType=INTEGER},
      FLAG = #{flag,jdbcType=VARCHAR},
      ATTRIBUTE1 = #{attribute1,jdbcType=VARCHAR},
      REC_EXTR_DATETIME = #{recExtrDatetime,jdbcType=DATE},
      LAST_UPDATETIME_DATETIME = #{lastUpdatetimeDatetime,jdbcType=DATE},
      REC_SRC = #{recSrc,jdbcType=VARCHAR}
    where CARTON_ID = #{cartonId,jdbcType=VARCHAR}
  </update>
</mapper>