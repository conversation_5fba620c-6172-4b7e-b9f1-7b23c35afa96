/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTUpdMomdelTacMatch implements Serializable { private String model; private String tacCode;
/*    */   private String apcCode;
/*    */   private String mktName;
/*    */   private String updatedBy;
/*    */   
/*  7 */   public void setModel(String model) { this.model = model; } private Date updatedDate; private Date recExtrDate; private String recSrc; private Date lastUpdateDate; private static final long serialVersionUID = 1L; public void setTacCode(String tacCode) { this.tacCode = tacCode; } public void setApcCode(String apcCode) { this.apcCode = apcCode; } public void setMktName(String mktName) { this.mktName = mktName; } public void setUpdatedBy(String updatedBy) { this.updatedBy = updatedBy; } public void setUpdatedDate(Date updatedDate) { this.updatedDate = updatedDate; } public void setRecExtrDate(Date recExtrDate) { this.recExtrDate = recExtrDate; } public void setRecSrc(String recSrc) { this.recSrc = recSrc; } public void setLastUpdateDate(Date lastUpdateDate) { this.lastUpdateDate = lastUpdateDate; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdMomdelTacMatch)) return false;  com.lenovo.iqs.entity.IbTUpdMomdelTacMatch other = (com.lenovo.iqs.entity.IbTUpdMomdelTacMatch)o; if (!other.canEqual(this)) return false;  Object this$model = getModel(), other$model = other.getModel(); if ((this$model == null) ? (other$model != null) : !this$model.equals(other$model)) return false;  Object this$tacCode = getTacCode(), other$tacCode = other.getTacCode(); if ((this$tacCode == null) ? (other$tacCode != null) : !this$tacCode.equals(other$tacCode)) return false;  Object this$apcCode = getApcCode(), other$apcCode = other.getApcCode(); if ((this$apcCode == null) ? (other$apcCode != null) : !this$apcCode.equals(other$apcCode)) return false;  Object this$mktName = getMktName(), other$mktName = other.getMktName(); if ((this$mktName == null) ? (other$mktName != null) : !this$mktName.equals(other$mktName)) return false;  Object this$updatedBy = getUpdatedBy(), other$updatedBy = other.getUpdatedBy(); if ((this$updatedBy == null) ? (other$updatedBy != null) : !this$updatedBy.equals(other$updatedBy)) return false;  Object this$updatedDate = getUpdatedDate(), other$updatedDate = other.getUpdatedDate(); if ((this$updatedDate == null) ? (other$updatedDate != null) : !this$updatedDate.equals(other$updatedDate)) return false;  Object this$recExtrDate = getRecExtrDate(), other$recExtrDate = other.getRecExtrDate(); if ((this$recExtrDate == null) ? (other$recExtrDate != null) : !this$recExtrDate.equals(other$recExtrDate)) return false;  Object this$recSrc = getRecSrc(), other$recSrc = other.getRecSrc(); if ((this$recSrc == null) ? (other$recSrc != null) : !this$recSrc.equals(other$recSrc)) return false;  Object this$lastUpdateDate = getLastUpdateDate(), other$lastUpdateDate = other.getLastUpdateDate(); return !((this$lastUpdateDate == null) ? (other$lastUpdateDate != null) : !this$lastUpdateDate.equals(other$lastUpdateDate)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdMomdelTacMatch; } public int hashCode() { int PRIME = 59; result = 1; Object $model = getModel(); result = result * 59 + (($model == null) ? 43 : $model.hashCode()); Object $tacCode = getTacCode(); result = result * 59 + (($tacCode == null) ? 43 : $tacCode.hashCode()); Object $apcCode = getApcCode(); result = result * 59 + (($apcCode == null) ? 43 : $apcCode.hashCode()); Object $mktName = getMktName(); result = result * 59 + (($mktName == null) ? 43 : $mktName.hashCode()); Object $updatedBy = getUpdatedBy(); result = result * 59 + (($updatedBy == null) ? 43 : $updatedBy.hashCode()); Object $updatedDate = getUpdatedDate(); result = result * 59 + (($updatedDate == null) ? 43 : $updatedDate.hashCode()); Object $recExtrDate = getRecExtrDate(); result = result * 59 + (($recExtrDate == null) ? 43 : $recExtrDate.hashCode()); Object $recSrc = getRecSrc(); result = result * 59 + (($recSrc == null) ? 43 : $recSrc.hashCode()); Object $lastUpdateDate = getLastUpdateDate(); return result * 59 + (($lastUpdateDate == null) ? 43 : $lastUpdateDate.hashCode()); } public String toString() { return "IbTUpdMomdelTacMatch(model=" + getModel() + ", tacCode=" + getTacCode() + ", apcCode=" + getApcCode() + ", mktName=" + getMktName() + ", updatedBy=" + getUpdatedBy() + ", updatedDate=" + getUpdatedDate() + ", recExtrDate=" + getRecExtrDate() + ", recSrc=" + getRecSrc() + ", lastUpdateDate=" + getLastUpdateDate() + ")"; }
/*    */    public String getModel() {
/*  9 */     return this.model;
/*    */   } public String getTacCode() {
/* 11 */     return this.tacCode;
/*    */   } public String getApcCode() {
/* 13 */     return this.apcCode;
/*    */   } public String getMktName() {
/* 15 */     return this.mktName;
/*    */   } public String getUpdatedBy() {
/* 17 */     return this.updatedBy;
/*    */   } public Date getUpdatedDate() {
/* 19 */     return this.updatedDate;
/*    */   } public Date getRecExtrDate() {
/* 21 */     return this.recExtrDate;
/*    */   } public String getRecSrc() {
/* 23 */     return this.recSrc;
/*    */   } public Date getLastUpdateDate() {
/* 25 */     return this.lastUpdateDate;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdMomdelTacMatch.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */