# IQS - Configuración de Base de Datos

## 🗄️ Arquitectura de Datos

El sistema IQS utiliza múltiples fuentes de datos para diferentes propósitos:

1. **MySQL Principal** - Datos de aplicación IQS
2. **MySQL Inbound** - Datos de procesamiento por lotes
3. **SQL Server PCG** - Datos de iBase OLAP
4. **SQL Server SPP** - Datos fuente adicionales

## 🔧 Configuración Principal (MySQL)

### **Conexión Principal IQS**
```properties
# Configuración MySQL Principal
jdbc.driverClassName=com.mysql.jdbc.Driver
jdbc.url=************************************************************************************
jdbc.username=root
jdbc.password=iqsprodnew
```

### **Pool de Conexiones Druid**
```xml
<bean name="dataSource" class="com.alibaba.druid.pool.DruidDataSource">
    <property name="driverClassName" value="${jdbc.driverClassName}" />
    <property name="url" value="${jdbc.url}" />
    <property name="username" value="${jdbc.username}" />
    <property name="password" value="${jdbc.password}" />
    
    <!-- Configuración del Pool -->
    <property name="maxActive" value="3000" />
    <property name="minIdle" value="10" />
    <property name="initialSize" value="10" />
    <property name="maxWait" value="60000" />
    
    <!-- Validación de Conexiones -->
    <property name="validationQuery" value="select 1" />
    <property name="testOnBorrow" value="true" />
    <property name="testOnReturn" value="false" />
    <property name="testWhileIdle" value="true" />
    
    <!-- Configuración de Monitoreo -->
    <property name="filters" value="stat" />
    <property name="removeAbandoned" value="false" />
    <property name="removeAbandonedTimeout" value="1800" />
</bean>
```

## 🔄 Configuración Inbound/Batch

### **Base de Datos de Procesamiento**
```properties
# Configuración para Inbound y Quartz
config.driverClassName=com.mysql.jdbc.Driver
config.url=************************************************************************************
config.username=root
config.password=iqsprodnew
```

### **Pool Druid para Inbound**
```xml
<bean name="configDataSource" class="com.alibaba.druid.pool.DruidDataSource">
    <property name="driverClassName" value="${config.driverClassName}" />
    <property name="url" value="${config.url}" />
    <property name="username" value="${config.username}" />
    <property name="password" value="${config.password}" />
    <property name="maxActive" value="1000" />
    <property name="minIdle" value="5" />
    <property name="validationQuery" value="select 1" />
    <property name="filters" value="stat" />
</bean>
```

## 🏢 Configuración SQL Server (PCG)

### **Conexión iBase OLAP**
```properties
# PCG IQS Database (SQL Server)
pcgjdbc.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
pcgjdbc.url=***********************************; DatabaseName=iBaseOlap
pcgjdbc.username=p_iqs_owner
pcgjdbc.password=Initial101
```

### **Pool Druid para PCG**
```xml
<bean name="PCGDataSource" class="com.alibaba.druid.pool.DruidDataSource">
    <property name="driverClassName" value="${pcgjdbc.driverClassName}" />
    <property name="url" value="${pcgjdbc.url}" />
    <property name="username" value="${pcgjdbc.username}" />
    <property name="password" value="${pcgjdbc.password}" />
    <property name="maxActive" value="3000" />
    <property name="minIdle" value="10" />
    <property name="validationQuery" value="select 1" />
    <property name="filters" value="stat" />
    <property name="testOnBorrow" value="true" />
</bean>
```

## 📊 Configuración SQL Server (SPP)

### **Conexión Source Database**
```properties
# SPP datasource config
spp.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
spp.url=**********************************; DatabaseName=SOURCE_DB
spp.username=a_dc
spp.password=Initial0
```

## 🔧 MyBatis Configuration

### **SqlSessionFactory**
```xml
<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
    <property name="configLocation" value="classpath:sqlMapConfig.xml" />
    <property name="dataSource" ref="dataSource" />
    <property name="mapperLocations" value="classpath*:com/lenovo/iqs/**/mapper/*Mapper.xml" />
</bean>
```

### **Configuración MyBatis (sqlMapConfig.xml)**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <settings>
        <setting name="cacheEnabled" value="true"/>
        <setting name="lazyLoadingEnabled" value="true"/>
        <setting name="multipleResultSetsEnabled" value="true"/>
        <setting name="useColumnLabel" value="true"/>
        <setting name="useGeneratedKeys" value="false"/>
        <setting name="autoMappingBehavior" value="PARTIAL"/>
        <setting name="defaultExecutorType" value="SIMPLE"/>
        <setting name="defaultStatementTimeout" value="25"/>
        <setting name="safeRowBoundsEnabled" value="false"/>
        <setting name="mapUnderscoreToCamelCase" value="false"/>
        <setting name="localCacheScope" value="SESSION"/>
        <setting name="jdbcTypeForNull" value="OTHER"/>
        <setting name="lazyLoadTriggerMethods" value="equals,clone,hashCode,toString"/>
    </settings>
</configuration>
```

## 🔄 Gestión de Transacciones

### **Transaction Manager**
```xml
<bean id="transactionManager" 
      class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
    <property name="dataSource" ref="dataSource" />
</bean>

<bean class="org.springframework.transaction.support.DefaultTransactionDefinition">
    <property name="propagationBehaviorName" value="PROPAGATION_REQUIRED" />
</bean>
```

### **Configuración de Transacciones**
```xml
<tx:annotation-driven transaction-manager="transactionManager" />
```

## 📋 Esquema de Base de Datos

### **Tablas Quartz (Scheduler)**
```sql
-- Tabla principal de trabajos
CREATE TABLE QRTZ_JOB_DETAILS (
    SCHED_NAME VARCHAR(120) NOT NULL,
    JOB_NAME VARCHAR(200) NOT NULL,
    JOB_GROUP VARCHAR(200) NOT NULL,
    DESCRIPTION VARCHAR(250) NULL,
    JOB_CLASS_NAME VARCHAR(250) NOT NULL,
    IS_DURABLE VARCHAR(1) NOT NULL,
    IS_NONCONCURRENT VARCHAR(1) NOT NULL,
    IS_UPDATE_DATA VARCHAR(1) NOT NULL,
    REQUESTS_RECOVERY VARCHAR(1) NOT NULL,
    JOB_DATA BLOB NULL,
    PRIMARY KEY (SCHED_NAME,JOB_NAME,JOB_GROUP)
);

-- Tabla de triggers
CREATE TABLE QRTZ_TRIGGERS (
    SCHED_NAME VARCHAR(120) NOT NULL,
    TRIGGER_NAME VARCHAR(200) NOT NULL,
    TRIGGER_GROUP VARCHAR(200) NOT NULL,
    JOB_NAME VARCHAR(200) NOT NULL,
    JOB_GROUP VARCHAR(200) NOT NULL,
    DESCRIPTION VARCHAR(250) NULL,
    NEXT_FIRE_TIME BIGINT(13) NULL,
    PREV_FIRE_TIME BIGINT(13) NULL,
    PRIORITY INTEGER NULL,
    TRIGGER_STATE VARCHAR(16) NOT NULL,
    TRIGGER_TYPE VARCHAR(8) NOT NULL,
    START_TIME BIGINT(13) NOT NULL,
    END_TIME BIGINT(13) NULL,
    CALENDAR_NAME VARCHAR(200) NULL,
    MISFIRE_INSTR SMALLINT(2) NULL,
    JOB_DATA BLOB NULL,
    PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP)
);
```

## 📊 Monitoreo de Base de Datos

### **Druid Web Console**
- **URL**: `/druid/*`
- **Funcionalidades**:
  - Monitor de pool de conexiones
  - Estadísticas de SQL
  - Análisis de rendimiento
  - Alertas de conexiones

### **Configuración de Monitoreo**
```xml
<servlet>
    <servlet-name>DruidStatView</servlet-name>
    <servlet-class>com.alibaba.druid.support.http.StatViewServlet</servlet-class>
</servlet>

<filter>
    <filter-name>DruidWebStatFilter</filter-name>
    <filter-class>com.alibaba.druid.support.http.WebStatFilter</filter-class>
    <init-param>
        <param-name>exclusions</param-name>
        <param-value>*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*</param-value>
    </init-param>
</filter>
```

## 🔐 Seguridad de Base de Datos

### **Configuración SSL**
- **MySQL**: `useSSL=false` (configurado para ambiente interno)
- **SQL Server**: Conexión segura por defecto

### **Gestión de Credenciales**
- Credenciales almacenadas en `config.properties`
- Separación por ambiente (dev/test/prod)
- Rotación periódica de contraseñas

### **Validación de Conexiones**
```xml
<property name="validationQuery" value="select 1" />
<property name="testOnBorrow" value="true" />
<property name="timeBetweenEvictionRunsMillis" value="60000" />
<property name="minEvictableIdleTimeMillis" value="300000" />
```

## 🚨 Troubleshooting

### **Problemas Comunes**
1. **Connection Pool Exhausted**: Aumentar `maxActive`
2. **Timeout Connections**: Ajustar `maxWait`
3. **Idle Connections**: Configurar `testWhileIdle`
4. **Memory Leaks**: Habilitar `removeAbandoned`

### **Logs de Diagnóstico**
```properties
# Habilitar logs SQL en MyBatis
log4j.logger.com.lenovo.iqs.dao=DEBUG
log4j.logger.java.sql=DEBUG
log4j.logger.java.sql.Statement=DEBUG
log4j.logger.java.sql.ResultSet=DEBUG
```
