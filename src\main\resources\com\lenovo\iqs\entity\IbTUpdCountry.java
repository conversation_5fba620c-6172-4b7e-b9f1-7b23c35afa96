/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ 
/*    */ public class IbTUpdCountry implements Serializable {
/*    */   private String countryCode;
/*    */   
/*  6 */   public void setCountryCode(String countryCode) { this.countryCode = countryCode; } private String description; private static final long serialVersionUID = 1L; public void setDescription(String description) { this.description = description; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdCountry)) return false;  com.lenovo.iqs.entity.IbTUpdCountry other = (com.lenovo.iqs.entity.IbTUpdCountry)o; if (!other.canEqual(this)) return false;  Object this$countryCode = getCountryCode(), other$countryCode = other.getCountryCode(); if ((this$countryCode == null) ? (other$countryCode != null) : !this$countryCode.equals(other$countryCode)) return false;  Object this$description = getDescription(), other$description = other.getDescription(); return !((this$description == null) ? (other$description != null) : !this$description.equals(other$description)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdCountry; } public int hashCode() { int PRIME = 59; result = 1; Object $countryCode = getCountryCode(); result = result * 59 + (($countryCode == null) ? 43 : $countryCode.hashCode()); Object $description = getDescription(); return result * 59 + (($description == null) ? 43 : $description.hashCode()); } public String toString() { return "IbTUpdCountry(countryCode=" + getCountryCode() + ", description=" + getDescription() + ")"; }
/*    */    public String getCountryCode() {
/*  8 */     return this.countryCode;
/*    */   } public String getDescription() {
/* 10 */     return this.description;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdCountry.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */