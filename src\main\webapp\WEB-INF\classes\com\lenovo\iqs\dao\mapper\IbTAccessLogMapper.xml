<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTAccessLogMapper" >

  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTAccessLog" >
    insert into ib_t_access_log (log_id, access_time, keyword, access_parameter, class_name, result)
    values (#{logId,jdbcType=INTEGER}, #{accessTime,jdbcType=TIMESTAMP}, #{keyword,jdbcType=VARCHAR}, 
      #{access_parameter,jdbcType=VARCHAR}, #{className,jdbcType=VARCHAR}, #{result, jdbcType=VARCHAR})
  </insert>
</mapper>