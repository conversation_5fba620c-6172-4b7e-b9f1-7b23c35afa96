/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ 
/*    */ public class IbTAccessLog implements Serializable {
/*    */   private String keyword;
/*    */   private String access_parameter;
/*    */   private String className;
/*    */   
/*  8 */   public void setKeyword(String keyword) { this.keyword = keyword; } private Integer logId; private Date accessTime; private String result; private static final long serialVersionUID = 1L; public void setAccess_parameter(String access_parameter) { this.access_parameter = access_parameter; } public void setClassName(String className) { this.className = className; } public void setLogId(Integer logId) { this.logId = logId; } public void setAccessTime(Date accessTime) { this.accessTime = accessTime; } public void setResult(String result) { this.result = result; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTAccessLog)) return false;  com.lenovo.iqs.entity.IbTAccessLog other = (com.lenovo.iqs.entity.IbTAccessLog)o; if (!other.canEqual(this)) return false;  Object this$keyword = getKeyword(), other$keyword = other.getKeyword(); if ((this$keyword == null) ? (other$keyword != null) : !this$keyword.equals(other$keyword)) return false;  Object this$access_parameter = getAccess_parameter(), other$access_parameter = other.getAccess_parameter(); if ((this$access_parameter == null) ? (other$access_parameter != null) : !this$access_parameter.equals(other$access_parameter)) return false;  Object this$className = getClassName(), other$className = other.getClassName(); if ((this$className == null) ? (other$className != null) : !this$className.equals(other$className)) return false;  Object this$logId = getLogId(), other$logId = other.getLogId(); if ((this$logId == null) ? (other$logId != null) : !this$logId.equals(other$logId)) return false;  Object this$accessTime = getAccessTime(), other$accessTime = other.getAccessTime(); if ((this$accessTime == null) ? (other$accessTime != null) : !this$accessTime.equals(other$accessTime)) return false;  Object this$result = getResult(), other$result = other.getResult(); return !((this$result == null) ? (other$result != null) : !this$result.equals(other$result)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTAccessLog; } public int hashCode() { int PRIME = 59; result = 1; Object $keyword = getKeyword(); result = result * 59 + (($keyword == null) ? 43 : $keyword.hashCode()); Object $access_parameter = getAccess_parameter(); result = result * 59 + (($access_parameter == null) ? 43 : $access_parameter.hashCode()); Object $className = getClassName(); result = result * 59 + (($className == null) ? 43 : $className.hashCode()); Object $logId = getLogId(); result = result * 59 + (($logId == null) ? 43 : $logId.hashCode()); Object $accessTime = getAccessTime(); result = result * 59 + (($accessTime == null) ? 43 : $accessTime.hashCode()); Object $result = getResult(); return result * 59 + (($result == null) ? 43 : $result.hashCode()); } public String toString() { return "IbTAccessLog(keyword=" + getKeyword() + ", access_parameter=" + getAccess_parameter() + ", className=" + getClassName() + ", logId=" + getLogId() + ", accessTime=" + getAccessTime() + ", result=" + getResult() + ")"; }
/*    */    public String getKeyword() {
/* 10 */     return this.keyword;
/*    */   } public String getAccess_parameter() {
/* 12 */     return this.access_parameter;
/*    */   } public String getClassName() {
/* 14 */     return this.className;
/*    */   } public Integer getLogId() {
/* 16 */     return this.logId;
/*    */   } public Date getAccessTime() {
/* 18 */     return this.accessTime;
/*    */   } public String getResult() {
/* 20 */     return this.result;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTAccessLog.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */