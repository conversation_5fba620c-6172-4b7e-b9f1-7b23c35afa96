/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTUpdRoamAddress implements Serializable { private String addressId; private String address1; private String address2; private String address3;
/*    */   private String address4;
/*    */   private String city;
/*    */   private String postalCode;
/*    */   
/*  7 */   public void setAddressId(String addressId) { this.addressId = addressId; } private String state; private String province; private String cnty; private String country; private Date lastUpdateDate; private String custNumber; private static final long serialVersionUID = 1L; public void setAddress1(String address1) { this.address1 = address1; } public void setAddress2(String address2) { this.address2 = address2; } public void setAddress3(String address3) { this.address3 = address3; } public void setAddress4(String address4) { this.address4 = address4; } public void setCity(String city) { this.city = city; } public void setPostalCode(String postalCode) { this.postalCode = postalCode; } public void setState(String state) { this.state = state; } public void setProvince(String province) { this.province = province; } public void setCnty(String cnty) { this.cnty = cnty; } public void setCountry(String country) { this.country = country; } public void setLastUpdateDate(Date lastUpdateDate) { this.lastUpdateDate = lastUpdateDate; } public void setCustNumber(String custNumber) { this.custNumber = custNumber; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdRoamAddress)) return false;  com.lenovo.iqs.entity.IbTUpdRoamAddress other = (com.lenovo.iqs.entity.IbTUpdRoamAddress)o; if (!other.canEqual(this)) return false;  Object this$addressId = getAddressId(), other$addressId = other.getAddressId(); if ((this$addressId == null) ? (other$addressId != null) : !this$addressId.equals(other$addressId)) return false;  Object this$address1 = getAddress1(), other$address1 = other.getAddress1(); if ((this$address1 == null) ? (other$address1 != null) : !this$address1.equals(other$address1)) return false;  Object this$address2 = getAddress2(), other$address2 = other.getAddress2(); if ((this$address2 == null) ? (other$address2 != null) : !this$address2.equals(other$address2)) return false;  Object this$address3 = getAddress3(), other$address3 = other.getAddress3(); if ((this$address3 == null) ? (other$address3 != null) : !this$address3.equals(other$address3)) return false;  Object this$address4 = getAddress4(), other$address4 = other.getAddress4(); if ((this$address4 == null) ? (other$address4 != null) : !this$address4.equals(other$address4)) return false;  Object this$city = getCity(), other$city = other.getCity(); if ((this$city == null) ? (other$city != null) : !this$city.equals(other$city)) return false;  Object this$postalCode = getPostalCode(), other$postalCode = other.getPostalCode(); if ((this$postalCode == null) ? (other$postalCode != null) : !this$postalCode.equals(other$postalCode)) return false;  Object this$state = getState(), other$state = other.getState(); if ((this$state == null) ? (other$state != null) : !this$state.equals(other$state)) return false;  Object this$province = getProvince(), other$province = other.getProvince(); if ((this$province == null) ? (other$province != null) : !this$province.equals(other$province)) return false;  Object this$cnty = getCnty(), other$cnty = other.getCnty(); if ((this$cnty == null) ? (other$cnty != null) : !this$cnty.equals(other$cnty)) return false;  Object this$country = getCountry(), other$country = other.getCountry(); if ((this$country == null) ? (other$country != null) : !this$country.equals(other$country)) return false;  Object this$lastUpdateDate = getLastUpdateDate(), other$lastUpdateDate = other.getLastUpdateDate(); if ((this$lastUpdateDate == null) ? (other$lastUpdateDate != null) : !this$lastUpdateDate.equals(other$lastUpdateDate)) return false;  Object this$custNumber = getCustNumber(), other$custNumber = other.getCustNumber(); return !((this$custNumber == null) ? (other$custNumber != null) : !this$custNumber.equals(other$custNumber)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdRoamAddress; } public int hashCode() { int PRIME = 59; result = 1; Object $addressId = getAddressId(); result = result * 59 + (($addressId == null) ? 43 : $addressId.hashCode()); Object $address1 = getAddress1(); result = result * 59 + (($address1 == null) ? 43 : $address1.hashCode()); Object $address2 = getAddress2(); result = result * 59 + (($address2 == null) ? 43 : $address2.hashCode()); Object $address3 = getAddress3(); result = result * 59 + (($address3 == null) ? 43 : $address3.hashCode()); Object $address4 = getAddress4(); result = result * 59 + (($address4 == null) ? 43 : $address4.hashCode()); Object $city = getCity(); result = result * 59 + (($city == null) ? 43 : $city.hashCode()); Object $postalCode = getPostalCode(); result = result * 59 + (($postalCode == null) ? 43 : $postalCode.hashCode()); Object $state = getState(); result = result * 59 + (($state == null) ? 43 : $state.hashCode()); Object $province = getProvince(); result = result * 59 + (($province == null) ? 43 : $province.hashCode()); Object $cnty = getCnty(); result = result * 59 + (($cnty == null) ? 43 : $cnty.hashCode()); Object $country = getCountry(); result = result * 59 + (($country == null) ? 43 : $country.hashCode()); Object $lastUpdateDate = getLastUpdateDate(); result = result * 59 + (($lastUpdateDate == null) ? 43 : $lastUpdateDate.hashCode()); Object $custNumber = getCustNumber(); return result * 59 + (($custNumber == null) ? 43 : $custNumber.hashCode()); } public String toString() { return "IbTUpdRoamAddress(addressId=" + getAddressId() + ", address1=" + getAddress1() + ", address2=" + getAddress2() + ", address3=" + getAddress3() + ", address4=" + getAddress4() + ", city=" + getCity() + ", postalCode=" + getPostalCode() + ", state=" + getState() + ", province=" + getProvince() + ", cnty=" + getCnty() + ", country=" + getCountry() + ", lastUpdateDate=" + getLastUpdateDate() + ", custNumber=" + getCustNumber() + ")"; }
/*    */    public String getAddressId() {
/*  9 */     return this.addressId;
/*    */   } public String getAddress1() {
/* 11 */     return this.address1;
/*    */   } public String getAddress2() {
/* 13 */     return this.address2;
/*    */   } public String getAddress3() {
/* 15 */     return this.address3;
/*    */   } public String getAddress4() {
/* 17 */     return this.address4;
/*    */   } public String getCity() {
/* 19 */     return this.city;
/*    */   } public String getPostalCode() {
/* 21 */     return this.postalCode;
/*    */   } public String getState() {
/* 23 */     return this.state;
/*    */   } public String getProvince() {
/* 25 */     return this.province;
/*    */   } public String getCnty() {
/* 27 */     return this.cnty;
/*    */   } public String getCountry() {
/* 29 */     return this.country;
/*    */   } public Date getLastUpdateDate() {
/* 31 */     return this.lastUpdateDate;
/*    */   } public String getCustNumber() {
/* 33 */     return this.custNumber;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdRoamAddress.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */