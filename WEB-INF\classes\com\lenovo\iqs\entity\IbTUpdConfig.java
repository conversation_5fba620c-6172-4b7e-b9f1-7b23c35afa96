/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ 
/*    */ public class IbTUpdConfig implements Serializable {
/*    */   private String section;
/*    */   private String key;
/*    */   
/*  7 */   public void setSection(String section) { this.section = section; } private String value; private static final long serialVersionUID = 1L; public void setKey(String key) { this.key = key; } public void setValue(String value) { this.value = value; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdConfig)) return false;  com.lenovo.iqs.entity.IbTUpdConfig other = (com.lenovo.iqs.entity.IbTUpdConfig)o; if (!other.canEqual(this)) return false;  Object this$section = getSection(), other$section = other.getSection(); if ((this$section == null) ? (other$section != null) : !this$section.equals(other$section)) return false;  Object this$key = getKey(), other$key = other.getKey(); if ((this$key == null) ? (other$key != null) : !this$key.equals(other$key)) return false;  Object this$value = getValue(), other$value = other.getValue(); return !((this$value == null) ? (other$value != null) : !this$value.equals(other$value)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdConfig; } public int hashCode() { int PRIME = 59; result = 1; Object $section = getSection(); result = result * 59 + (($section == null) ? 43 : $section.hashCode()); Object $key = getKey(); result = result * 59 + (($key == null) ? 43 : $key.hashCode()); Object $value = getValue(); return result * 59 + (($value == null) ? 43 : $value.hashCode()); } public String toString() { return "IbTUpdConfig(section=" + getSection() + ", key=" + getKey() + ", value=" + getValue() + ")"; }
/*    */    public String getSection() {
/*  9 */     return this.section;
/*    */   } public String getKey() {
/* 11 */     return this.key;
/*    */   } public String getValue() {
/* 13 */     return this.value;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdConfig.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */