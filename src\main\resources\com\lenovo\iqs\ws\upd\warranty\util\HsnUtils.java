/*     */ package WEB-INF.classes.com.lenovo.iqs.ws.upd.warranty.util;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.Set;
/*     */ 
/*     */ public class HsnUtils
/*     */ {
/*   9 */   static HashMap hm28toDecimal = new HashMap<>();
/*     */ 
/*     */   
/*     */   public static String getHSNCheckdigit(String hsn) {
/*  13 */     hm28toDecimal.put("2", "0");
/*  14 */     hm28toDecimal.put("3", "1");
/*  15 */     hm28toDecimal.put("4", "2");
/*  16 */     hm28toDecimal.put("5", "3");
/*  17 */     hm28toDecimal.put("6", "4");
/*  18 */     hm28toDecimal.put("7", "5");
/*  19 */     hm28toDecimal.put("8", "6");
/*  20 */     hm28toDecimal.put("9", "7");
/*  21 */     hm28toDecimal.put("B", "8");
/*  22 */     hm28toDecimal.put("C", "9");
/*  23 */     hm28toDecimal.put("D", "10");
/*  24 */     hm28toDecimal.put("F", "11");
/*  25 */     hm28toDecimal.put("G", "12");
/*  26 */     hm28toDecimal.put("H", "13");
/*  27 */     hm28toDecimal.put("J", "14");
/*  28 */     hm28toDecimal.put("K", "15");
/*  29 */     hm28toDecimal.put("L", "16");
/*  30 */     hm28toDecimal.put("M", "17");
/*  31 */     hm28toDecimal.put("N", "18");
/*  32 */     hm28toDecimal.put("P", "19");
/*  33 */     hm28toDecimal.put("Q", "20");
/*  34 */     hm28toDecimal.put("R", "21");
/*  35 */     hm28toDecimal.put("S", "22");
/*  36 */     hm28toDecimal.put("T", "23");
/*  37 */     hm28toDecimal.put("V", "24");
/*  38 */     hm28toDecimal.put("W", "25");
/*  39 */     hm28toDecimal.put("X", "26");
/*  40 */     hm28toDecimal.put("Z", "27");
/*  41 */     String chkDigit = getBase28CheckDigit(hsn);
/*     */ 
/*     */     
/*  44 */     return chkDigit;
/*     */   }
/*     */ 
/*     */   
/*     */   public static String getBase28CheckDigit(String base28Serial) {
/*  49 */     int total = 0;
/*  50 */     int valAtIdx = -1;
/*  51 */     int doubleVal = -1;
/*  52 */     int checkSumExpected = -1;
/*     */ 
/*     */     
/*  55 */     for (int i = 0; i < base28Serial.length() - 1; i++) {
/*     */ 
/*     */       
/*  58 */       valAtIdx = getDecimalValue(base28Serial.substring(i, i + 1));
/*  59 */       if (i % 2 != 0) {
/*     */         
/*  61 */         doubleVal = valAtIdx * 2;
/*  62 */         total += doubleVal / 28;
/*  63 */         total += doubleVal % 28;
/*     */       } else {
/*     */         
/*  66 */         total += valAtIdx;
/*     */       } 
/*     */     } 
/*  69 */     checkSumExpected = 28 - total % 28;
/*     */     
/*  71 */     if (checkSumExpected == 28) {
/*  72 */       checkSumExpected = 0;
/*     */     }
/*  74 */     return getbase28value(checkSumExpected);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getbase28value(int decimalValue) {
/*  81 */     Set ref = hm28toDecimal.keySet();
/*  82 */     Iterator i = ref.iterator();
/*  83 */     while (i.hasNext()) {
/*     */       
/*  85 */       Object o = i.next();
/*     */       
/*  87 */       if (hm28toDecimal.get(o).toString().equals((new Integer(decimalValue)).toString())) {
/*  88 */         return o.toString();
/*     */       }
/*     */     } 
/*  91 */     return "";
/*     */   }
/*     */   
/*     */   public static int getDecimalValue(String base28) {
/*  95 */     String o = hm28toDecimal.get(base28).toString();
/*     */ 
/*     */     
/*  98 */     Integer i = new Integer(o);
/*     */ 
/*     */     
/* 101 */     return i.intValue();
/*     */   }
/*     */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\w\\upd\warrant\\util\HsnUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */