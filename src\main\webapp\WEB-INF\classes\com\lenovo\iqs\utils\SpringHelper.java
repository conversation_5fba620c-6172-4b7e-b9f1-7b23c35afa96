/*    */ package WEB-INF.classes.com.lenovo.iqs.utils;
/*    */ 
/*    */ import org.springframework.beans.BeansException;
/*    */ import org.springframework.context.ApplicationContext;
/*    */ import org.springframework.context.ApplicationContextAware;
/*    */ import org.springframework.stereotype.Component;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Component
/*    */ public class SpringHelper
/*    */   implements ApplicationContextAware
/*    */ {
/*    */   private static ApplicationContext applicationContext;
/*    */   
/*    */   public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
/* 20 */     com.lenovo.iqs.utils.SpringHelper.applicationContext = applicationContext;
/*    */   }
/*    */   
/*    */   public static ApplicationContext getApplicationContext() {
/* 24 */     return applicationContext;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static <T> T getBean(Class<T> requiredType) {
/* 31 */     return (T)applicationContext.getBean(requiredType);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static <T> T getBean(String name, Class<T> requiredType) {
/* 38 */     return (T)applicationContext.getBean(name, requiredType);
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iq\\utils\SpringHelper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */