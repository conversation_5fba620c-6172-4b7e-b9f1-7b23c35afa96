/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.bean;
/*    */ public class DataBlock { @JsonProperty("db_version")
/*    */   String db_version; @JsonProperty("dbtype")
/*    */   String dbtype; @JsonProperty("customer_identifier")
/*    */   String customer_identifier;
/*  6 */   public void setDb_version(String db_version) { this.db_version = db_version; } @JsonProperty("length") String length; @JsonProperty("serial_number") String serial_number; @JsonProperty("processor_uid") String processor_uid; public void setDbtype(String dbtype) { this.dbtype = dbtype; } public void setCustomer_identifier(String customer_identifier) { this.customer_identifier = customer_identifier; } public void setLength(String length) { this.length = length; } public void setSerial_number(String serial_number) { this.serial_number = serial_number; } public void setProcessor_uid(String processor_uid) { this.processor_uid = processor_uid; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.datablocksign.bean.DataBlock)) return false;  com.lenovo.iqs.datablocksign.bean.DataBlock other = (com.lenovo.iqs.datablocksign.bean.DataBlock)o; if (!other.canEqual(this)) return false;  Object this$db_version = getDb_version(), other$db_version = other.getDb_version(); if ((this$db_version == null) ? (other$db_version != null) : !this$db_version.equals(other$db_version)) return false;  Object this$dbtype = getDbtype(), other$dbtype = other.getDbtype(); if ((this$dbtype == null) ? (other$dbtype != null) : !this$dbtype.equals(other$dbtype)) return false;  Object this$customer_identifier = getCustomer_identifier(), other$customer_identifier = other.getCustomer_identifier(); if ((this$customer_identifier == null) ? (other$customer_identifier != null) : !this$customer_identifier.equals(other$customer_identifier)) return false;  Object this$length = getLength(), other$length = other.getLength(); if ((this$length == null) ? (other$length != null) : !this$length.equals(other$length)) return false;  Object this$serial_number = getSerial_number(), other$serial_number = other.getSerial_number(); if ((this$serial_number == null) ? (other$serial_number != null) : !this$serial_number.equals(other$serial_number)) return false;  Object this$processor_uid = getProcessor_uid(), other$processor_uid = other.getProcessor_uid(); return !((this$processor_uid == null) ? (other$processor_uid != null) : !this$processor_uid.equals(other$processor_uid)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.datablocksign.bean.DataBlock; } public int hashCode() { int PRIME = 59; result = 1; Object $db_version = getDb_version(); result = result * 59 + (($db_version == null) ? 43 : $db_version.hashCode()); Object $dbtype = getDbtype(); result = result * 59 + (($dbtype == null) ? 43 : $dbtype.hashCode()); Object $customer_identifier = getCustomer_identifier(); result = result * 59 + (($customer_identifier == null) ? 43 : $customer_identifier.hashCode()); Object $length = getLength(); result = result * 59 + (($length == null) ? 43 : $length.hashCode()); Object $serial_number = getSerial_number(); result = result * 59 + (($serial_number == null) ? 43 : $serial_number.hashCode()); Object $processor_uid = getProcessor_uid(); return result * 59 + (($processor_uid == null) ? 43 : $processor_uid.hashCode()); } public String toString() { return "DataBlock(db_version=" + getDb_version() + ", dbtype=" + getDbtype() + ", customer_identifier=" + getCustomer_identifier() + ", length=" + getLength() + ", serial_number=" + getSerial_number() + ", processor_uid=" + getProcessor_uid() + ")"; }
/*    */   
/*    */   public String getDb_version() {
/*  9 */     return this.db_version;
/*    */   } public String getDbtype() {
/* 11 */     return this.dbtype;
/*    */   } public String getCustomer_identifier() {
/* 13 */     return this.customer_identifier;
/*    */   } public String getLength() {
/* 15 */     return this.length;
/*    */   } public String getSerial_number() {
/* 17 */     return this.serial_number;
/*    */   } public String getProcessor_uid() {
/* 19 */     return this.processor_uid;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\bean\DataBlock.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */