/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.service.impl;
/*    */ 
/*    */ import com.lenovo.iqs.datablocksign.bean.ClientResponse;
/*    */ import com.lenovo.iqs.datablocksign.bean.RequestBean;
/*    */ import com.lenovo.iqs.datablocksign.service.impl.AbstractClientTypeService;
/*    */ import org.springframework.stereotype.Service;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Service("ClientType-0x07")
/*    */ public class CidBootLoaderClientTypeServiceImpl
/*    */   extends AbstractClientTypeService
/*    */ {
/*    */   public ClientResponse process(RequestBean requestBean) throws Exception {
/* 17 */     String transcationId = generateTranscationId(requestBean.getIstrMASCID());
/* 18 */     ClientResponse clientResponse = validateReqParam(requestBean);
/* 19 */     if (clientResponse != null) {
/* 20 */       clientResponse.setIstrTransactionID(transcationId);
/* 21 */       return clientResponse;
/*    */     } 
/*    */     
/* 24 */     clientResponse = callPKIAndProcessResult(requestBean);
/* 25 */     clientResponse.setIstrTransactionID(transcationId);
/*    */     
/* 27 */     return clientResponse;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\service\impl\CidBootLoaderClientTypeServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */