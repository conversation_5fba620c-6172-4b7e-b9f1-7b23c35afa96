/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTZibpActivationInfo implements Serializable { private Integer id; private Integer client; private String serialNo1; private String serialNo2; private String trackId;
/*    */   private String msn;
/*    */   private String countryIsoCode;
/*    */   private String hsn;
/*    */   
/*  7 */   public void setId(Integer id) { this.id = id; } private Date activationDate; private String lastModBy; private Date lastModDatetime; private String createBy; private Date createDatetime; private String updateStatus; private String errorMessage; private String batchNumber; private static final long serialVersionUID = 1L; public void setClient(Integer client) { this.client = client; } public void setSerialNo1(String serialNo1) { this.serialNo1 = serialNo1; } public void setSerialNo2(String serialNo2) { this.serialNo2 = serialNo2; } public void setTrackId(String trackId) { this.trackId = trackId; } public void setMsn(String msn) { this.msn = msn; } public void setCountryIsoCode(String countryIsoCode) { this.countryIsoCode = countryIsoCode; } public void setHsn(String hsn) { this.hsn = hsn; } public void setActivationDate(Date activationDate) { this.activationDate = activationDate; } public void setLastModBy(String lastModBy) { this.lastModBy = lastModBy; } public void setLastModDatetime(Date lastModDatetime) { this.lastModDatetime = lastModDatetime; } public void setCreateBy(String createBy) { this.createBy = createBy; } public void setCreateDatetime(Date createDatetime) { this.createDatetime = createDatetime; } public void setUpdateStatus(String updateStatus) { this.updateStatus = updateStatus; } public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; } public void setBatchNumber(String batchNumber) { this.batchNumber = batchNumber; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTZibpActivationInfo)) return false;  com.lenovo.iqs.entity.IbTZibpActivationInfo other = (com.lenovo.iqs.entity.IbTZibpActivationInfo)o; if (!other.canEqual(this)) return false;  Object this$id = getId(), other$id = other.getId(); if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id)) return false;  Object this$client = getClient(), other$client = other.getClient(); if ((this$client == null) ? (other$client != null) : !this$client.equals(other$client)) return false;  Object this$serialNo1 = getSerialNo1(), other$serialNo1 = other.getSerialNo1(); if ((this$serialNo1 == null) ? (other$serialNo1 != null) : !this$serialNo1.equals(other$serialNo1)) return false;  Object this$serialNo2 = getSerialNo2(), other$serialNo2 = other.getSerialNo2(); if ((this$serialNo2 == null) ? (other$serialNo2 != null) : !this$serialNo2.equals(other$serialNo2)) return false;  Object this$trackId = getTrackId(), other$trackId = other.getTrackId(); if ((this$trackId == null) ? (other$trackId != null) : !this$trackId.equals(other$trackId)) return false;  Object this$msn = getMsn(), other$msn = other.getMsn(); if ((this$msn == null) ? (other$msn != null) : !this$msn.equals(other$msn)) return false;  Object this$countryIsoCode = getCountryIsoCode(), other$countryIsoCode = other.getCountryIsoCode(); if ((this$countryIsoCode == null) ? (other$countryIsoCode != null) : !this$countryIsoCode.equals(other$countryIsoCode)) return false;  Object this$hsn = getHsn(), other$hsn = other.getHsn(); if ((this$hsn == null) ? (other$hsn != null) : !this$hsn.equals(other$hsn)) return false;  Object this$activationDate = getActivationDate(), other$activationDate = other.getActivationDate(); if ((this$activationDate == null) ? (other$activationDate != null) : !this$activationDate.equals(other$activationDate)) return false;  Object this$lastModBy = getLastModBy(), other$lastModBy = other.getLastModBy(); if ((this$lastModBy == null) ? (other$lastModBy != null) : !this$lastModBy.equals(other$lastModBy)) return false;  Object this$lastModDatetime = getLastModDatetime(), other$lastModDatetime = other.getLastModDatetime(); if ((this$lastModDatetime == null) ? (other$lastModDatetime != null) : !this$lastModDatetime.equals(other$lastModDatetime)) return false;  Object this$createBy = getCreateBy(), other$createBy = other.getCreateBy(); if ((this$createBy == null) ? (other$createBy != null) : !this$createBy.equals(other$createBy)) return false;  Object this$createDatetime = getCreateDatetime(), other$createDatetime = other.getCreateDatetime(); if ((this$createDatetime == null) ? (other$createDatetime != null) : !this$createDatetime.equals(other$createDatetime)) return false;  Object this$updateStatus = getUpdateStatus(), other$updateStatus = other.getUpdateStatus(); if ((this$updateStatus == null) ? (other$updateStatus != null) : !this$updateStatus.equals(other$updateStatus)) return false;  Object this$errorMessage = getErrorMessage(), other$errorMessage = other.getErrorMessage(); if ((this$errorMessage == null) ? (other$errorMessage != null) : !this$errorMessage.equals(other$errorMessage)) return false;  Object this$batchNumber = getBatchNumber(), other$batchNumber = other.getBatchNumber(); return !((this$batchNumber == null) ? (other$batchNumber != null) : !this$batchNumber.equals(other$batchNumber)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTZibpActivationInfo; } public int hashCode() { int PRIME = 59; result = 1; Object $id = getId(); result = result * 59 + (($id == null) ? 43 : $id.hashCode()); Object $client = getClient(); result = result * 59 + (($client == null) ? 43 : $client.hashCode()); Object $serialNo1 = getSerialNo1(); result = result * 59 + (($serialNo1 == null) ? 43 : $serialNo1.hashCode()); Object $serialNo2 = getSerialNo2(); result = result * 59 + (($serialNo2 == null) ? 43 : $serialNo2.hashCode()); Object $trackId = getTrackId(); result = result * 59 + (($trackId == null) ? 43 : $trackId.hashCode()); Object $msn = getMsn(); result = result * 59 + (($msn == null) ? 43 : $msn.hashCode()); Object $countryIsoCode = getCountryIsoCode(); result = result * 59 + (($countryIsoCode == null) ? 43 : $countryIsoCode.hashCode()); Object $hsn = getHsn(); result = result * 59 + (($hsn == null) ? 43 : $hsn.hashCode()); Object $activationDate = getActivationDate(); result = result * 59 + (($activationDate == null) ? 43 : $activationDate.hashCode()); Object $lastModBy = getLastModBy(); result = result * 59 + (($lastModBy == null) ? 43 : $lastModBy.hashCode()); Object $lastModDatetime = getLastModDatetime(); result = result * 59 + (($lastModDatetime == null) ? 43 : $lastModDatetime.hashCode()); Object $createBy = getCreateBy(); result = result * 59 + (($createBy == null) ? 43 : $createBy.hashCode()); Object $createDatetime = getCreateDatetime(); result = result * 59 + (($createDatetime == null) ? 43 : $createDatetime.hashCode()); Object $updateStatus = getUpdateStatus(); result = result * 59 + (($updateStatus == null) ? 43 : $updateStatus.hashCode()); Object $errorMessage = getErrorMessage(); result = result * 59 + (($errorMessage == null) ? 43 : $errorMessage.hashCode()); Object $batchNumber = getBatchNumber(); return result * 59 + (($batchNumber == null) ? 43 : $batchNumber.hashCode()); } public String toString() { return "IbTZibpActivationInfo(id=" + getId() + ", client=" + getClient() + ", serialNo1=" + getSerialNo1() + ", serialNo2=" + getSerialNo2() + ", trackId=" + getTrackId() + ", msn=" + getMsn() + ", countryIsoCode=" + getCountryIsoCode() + ", hsn=" + getHsn() + ", activationDate=" + getActivationDate() + ", lastModBy=" + getLastModBy() + ", lastModDatetime=" + getLastModDatetime() + ", createBy=" + getCreateBy() + ", createDatetime=" + getCreateDatetime() + ", updateStatus=" + getUpdateStatus() + ", errorMessage=" + getErrorMessage() + ", batchNumber=" + getBatchNumber() + ")"; }
/*    */    public Integer getId() {
/*  9 */     return this.id;
/*    */   } public Integer getClient() {
/* 11 */     return this.client;
/*    */   } public String getSerialNo1() {
/* 13 */     return this.serialNo1;
/*    */   } public String getSerialNo2() {
/* 15 */     return this.serialNo2;
/*    */   } public String getTrackId() {
/* 17 */     return this.trackId;
/*    */   } public String getMsn() {
/* 19 */     return this.msn;
/*    */   } public String getCountryIsoCode() {
/* 21 */     return this.countryIsoCode;
/*    */   } public String getHsn() {
/* 23 */     return this.hsn;
/*    */   } public Date getActivationDate() {
/* 25 */     return this.activationDate;
/*    */   } public String getLastModBy() {
/* 27 */     return this.lastModBy;
/*    */   } public Date getLastModDatetime() {
/* 29 */     return this.lastModDatetime;
/*    */   } public String getCreateBy() {
/* 31 */     return this.createBy;
/*    */   } public Date getCreateDatetime() {
/* 33 */     return this.createDatetime;
/*    */   } public String getUpdateStatus() {
/* 35 */     return this.updateStatus;
/*    */   } public String getErrorMessage() {
/* 37 */     return this.errorMessage;
/*    */   } public String getBatchNumber() {
/* 39 */     return this.batchNumber;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTZibpActivationInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */