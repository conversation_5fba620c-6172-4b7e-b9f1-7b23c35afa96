# IQS - Servicios Web y APIs

## 📡 Arquitectura de Servicios Web

El sistema IQS expone servicios web usando **Apache CXF** con soporte para:
- **REST APIs** (JAX-RS)
- **SOAP Web Services** (JAX-WS)

## 🔧 Configuración Base

### **Servlet CXF**
```xml
<servlet>
    <servlet-name>CXFServlet</servlet-name>
    <servlet-class>org.apache.cxf.transport.servlet.CXFServlet</servlet-class>
    <load-on-startup>2</load-on-startup>
</servlet>

<servlet-mapping>
    <servlet-name>CXFServlet</servlet-name>
    <url-pattern>/webservice/*</url-pattern>
</servlet-mapping>
```

### **Configuración Spring CXF**
- **Archivo**: `spring-webservice.xml`
- **Base URL REST**: `/webservice/rest`
- **Providers**: <PERSON> JSON, JAXB XML

## 🌐 Servicios REST Disponibles

### **1. RSU Controller** (`/webservice/rest/rsu`)
**Propósito**: Remote SIM Unlock - Desbloqueo remoto de tarjetas SIM

**Endpoints principales**:
- `POST /unlock` - Desbloquear SIM
- `GET /status/{imei}` - Consultar estado de desbloqueo
- `POST /validate` - Validar dispositivo

### **2. WeChat Controller** (`/webservice/rest/wechat`)
**Propósito**: Integración con plataforma WeChat

**Endpoints principales**:
- `POST /message` - Procesar mensajes WeChat
- `GET /verify` - Verificación de webhook
- `POST /auth` - Autenticación WeChat

### **3. SIM Unlock Controller** (`/webservice/rest/simunlock`)
**Propósito**: Gestión general de desbloqueo de SIM

**Endpoints principales**:
- `POST /request` - Solicitar desbloqueo
- `GET /history/{imei}` - Historial de desbloqueos
- `POST /batch` - Desbloqueo por lotes

### **4. Google RPK Controller** (`/webservice/rest/googlerpk`)
**Propósito**: Gestión de claves públicas de Google

**Endpoints principales**:
- `POST /register` - Registrar clave pública
- `GET /validate/{key}` - Validar clave
- `DELETE /revoke/{key}` - Revocar clave

## 🔐 Autenticación y Seguridad

### **Métodos de Autenticación**
1. **SAP Authentication**: Para servicios internos
2. **API Keys**: Para servicios externos
3. **Certificate-based**: Para servicios críticos

### **Configuración de Seguridad**
```xml
<!-- Filtro de codificación UTF-8 -->
<filter>
    <filter-name>encodingFilter</filter-name>
    <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
    <init-param>
        <param-name>encoding</param-name>
        <param-value>UTF-8</param-value>
    </init-param>
</filter>
```

## 📊 Formatos de Datos

### **JSON (Predeterminado)**
```json
{
    "status": "success",
    "data": {
        "imei": "123456789012345",
        "unlockCode": "12345678",
        "timestamp": "2023-12-24T09:15:32Z"
    },
    "message": "SIM unlocked successfully"
}
```

### **XML (Alternativo)**
```xml
<response>
    <status>success</status>
    <data>
        <imei>123456789012345</imei>
        <unlockCode>12345678</unlockCode>
        <timestamp>2023-12-24T09:15:32Z</timestamp>
    </data>
    <message>SIM unlocked successfully</message>
</response>
```

## 🔄 Integración Externa

### **GPS Services (Motorola)**
- **URL**: `https://wsgw.motorola.com:443/GPSTrustonicRSUService/RSUService`
- **Autenticación**: Usuario/Contraseña
- **Propósito**: Servicios de geolocalización y RSU

### **SAP Integration**
- **Protocolo**: RFC (Remote Function Call)
- **Connector**: SAP JCo 3.x
- **Sistemas**: CSP (Client 301)

### **iBase Services (Lenovo)**
- **URL**: `http://csp.lenovo.com/ibapp/POIRequest.jsp`
- **Propósito**: Consultas de garantía y información de productos

## 📈 Monitoreo de Servicios

### **JavaMelody**
- **URL**: `/monitoring`
- **Métricas**: Tiempo de respuesta, throughput, errores
- **Gráficos**: Rendimiento histórico

### **Druid Console**
- **URL**: `/druid`
- **Función**: Monitoreo de conexiones de base de datos
- **Métricas**: Pool de conexiones, queries SQL

## 🛠️ Configuración de Providers

### **Jackson JSON Provider**
```xml
<jaxrs:providers>
    <bean class="org.codehaus.jackson.jaxrs.JacksonJsonProvider" />
    <bean class="org.apache.cxf.jaxrs.provider.JAXBElementProvider" />
</jaxrs:providers>
```

### **Configuración de Timeouts**
```xml
<http-conf:conduit name="*.http-conduit">
    <http-conf:client ConnectionTimeout="30000" ReceiveTimeout="60000"/>
</http-conf:conduit>
```

## 🔍 Logging de Servicios

### **Log4j2 Configuration**
- **Nivel**: INFO para producción, DEBUG para desarrollo
- **Formato**: JSON estructurado
- **Destinos**: Archivos rotativos, consola

### **Ejemplo de Log**
```json
{
    "timestamp": "2023-12-24T09:15:32.123Z",
    "level": "INFO",
    "logger": "com.lenovo.iqs.rsu.controller.RSUController",
    "message": "SIM unlock request processed",
    "imei": "123456789012345",
    "status": "success",
    "duration": 1250
}
```

## 🚨 Manejo de Errores

### **Códigos de Error Estándar**
- **200**: Éxito
- **400**: Solicitud inválida
- **401**: No autorizado
- **404**: Recurso no encontrado
- **500**: Error interno del servidor
- **503**: Servicio no disponible

### **Formato de Error**
```json
{
    "status": "error",
    "errorCode": "SIM_001",
    "message": "Invalid IMEI format",
    "timestamp": "2023-12-24T09:15:32Z",
    "details": {
        "field": "imei",
        "value": "invalid_imei",
        "expected": "15-digit numeric string"
    }
}
```

## 📋 Testing de APIs

### **Herramientas Recomendadas**
- **Postman**: Para testing manual
- **curl**: Para scripts automatizados
- **JUnit**: Para testing unitario
- **REST Assured**: Para testing de integración

### **Ejemplo de Prueba con curl**
```bash
curl -X POST \
  http://localhost:8080/iqs/webservice/rest/rsu/unlock \
  -H 'Content-Type: application/json' \
  -d '{
    "imei": "123456789012345",
    "model": "MOTO_G",
    "carrier": "VERIZON"
  }'
```
