/*    */ package WEB-INF.classes.com.lenovo.iqs.rsu.bean;
/*    */ public class RsuRequest { private String serialNo;
/*    */   private String rsdUser;
/*    */   private String mascId;
/*    */   private String soCModel;
/*    */   private String suid;
/*    */   
/*  8 */   public void setSerialNo(String serialNo) { this.serialNo = serialNo; } private String receiptData; private String sip; private String deviceModel; private String mn_operator; private String track_id; public void setRsdUser(String rsdUser) { this.rsdUser = rsdUser; } public void setMascId(String mascId) { this.mascId = mascId; } public void setSoCModel(String soCModel) { this.soCModel = soCModel; } public void setSuid(String suid) { this.suid = suid; } public void setReceiptData(String receiptData) { this.receiptData = receiptData; } public void setSip(String sip) { this.sip = sip; } public void setDeviceModel(String deviceModel) { this.deviceModel = deviceModel; } public void setMn_operator(String mn_operator) { this.mn_operator = mn_operator; } public void setTrack_id(String track_id) { this.track_id = track_id; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.rsu.bean.RsuRequest)) return false;  com.lenovo.iqs.rsu.bean.RsuRequest other = (com.lenovo.iqs.rsu.bean.RsuRequest)o; if (!other.canEqual(this)) return false;  Object this$serialNo = getSerialNo(), other$serialNo = other.getSerialNo(); if ((this$serialNo == null) ? (other$serialNo != null) : !this$serialNo.equals(other$serialNo)) return false;  Object this$rsdUser = getRsdUser(), other$rsdUser = other.getRsdUser(); if ((this$rsdUser == null) ? (other$rsdUser != null) : !this$rsdUser.equals(other$rsdUser)) return false;  Object this$mascId = getMascId(), other$mascId = other.getMascId(); if ((this$mascId == null) ? (other$mascId != null) : !this$mascId.equals(other$mascId)) return false;  Object this$soCModel = getSoCModel(), other$soCModel = other.getSoCModel(); if ((this$soCModel == null) ? (other$soCModel != null) : !this$soCModel.equals(other$soCModel)) return false;  Object this$suid = getSuid(), other$suid = other.getSuid(); if ((this$suid == null) ? (other$suid != null) : !this$suid.equals(other$suid)) return false;  Object this$receiptData = getReceiptData(), other$receiptData = other.getReceiptData(); if ((this$receiptData == null) ? (other$receiptData != null) : !this$receiptData.equals(other$receiptData)) return false;  Object this$sip = getSip(), other$sip = other.getSip(); if ((this$sip == null) ? (other$sip != null) : !this$sip.equals(other$sip)) return false;  Object this$deviceModel = getDeviceModel(), other$deviceModel = other.getDeviceModel(); if ((this$deviceModel == null) ? (other$deviceModel != null) : !this$deviceModel.equals(other$deviceModel)) return false;  Object this$mn_operator = getMn_operator(), other$mn_operator = other.getMn_operator(); if ((this$mn_operator == null) ? (other$mn_operator != null) : !this$mn_operator.equals(other$mn_operator)) return false;  Object this$track_id = getTrack_id(), other$track_id = other.getTrack_id(); return !((this$track_id == null) ? (other$track_id != null) : !this$track_id.equals(other$track_id)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.rsu.bean.RsuRequest; } public int hashCode() { int PRIME = 59; result = 1; Object $serialNo = getSerialNo(); result = result * 59 + (($serialNo == null) ? 43 : $serialNo.hashCode()); Object $rsdUser = getRsdUser(); result = result * 59 + (($rsdUser == null) ? 43 : $rsdUser.hashCode()); Object $mascId = getMascId(); result = result * 59 + (($mascId == null) ? 43 : $mascId.hashCode()); Object $soCModel = getSoCModel(); result = result * 59 + (($soCModel == null) ? 43 : $soCModel.hashCode()); Object $suid = getSuid(); result = result * 59 + (($suid == null) ? 43 : $suid.hashCode()); Object $receiptData = getReceiptData(); result = result * 59 + (($receiptData == null) ? 43 : $receiptData.hashCode()); Object $sip = getSip(); result = result * 59 + (($sip == null) ? 43 : $sip.hashCode()); Object $deviceModel = getDeviceModel(); result = result * 59 + (($deviceModel == null) ? 43 : $deviceModel.hashCode()); Object $mn_operator = getMn_operator(); result = result * 59 + (($mn_operator == null) ? 43 : $mn_operator.hashCode()); Object $track_id = getTrack_id(); return result * 59 + (($track_id == null) ? 43 : $track_id.hashCode()); } public String toString() { return "RsuRequest(serialNo=" + getSerialNo() + ", rsdUser=" + getRsdUser() + ", mascId=" + getMascId() + ", soCModel=" + getSoCModel() + ", suid=" + getSuid() + ", receiptData=" + getReceiptData() + ", sip=" + getSip() + ", deviceModel=" + getDeviceModel() + ", mn_operator=" + getMn_operator() + ", track_id=" + getTrack_id() + ")"; }
/*    */   
/* 10 */   public String getSerialNo() { return this.serialNo; }
/* 11 */   public String getRsdUser() { return this.rsdUser; }
/* 12 */   public String getMascId() { return this.mascId; }
/* 13 */   public String getSoCModel() { return this.soCModel; }
/* 14 */   public String getSuid() { return this.suid; }
/* 15 */   public String getReceiptData() { return this.receiptData; }
/* 16 */   public String getSip() { return this.sip; }
/* 17 */   public String getDeviceModel() { return this.deviceModel; }
/* 18 */   public String getMn_operator() { return this.mn_operator; } public String getTrack_id() {
/* 19 */     return this.track_id;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\rsu\bean\RsuRequest.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */