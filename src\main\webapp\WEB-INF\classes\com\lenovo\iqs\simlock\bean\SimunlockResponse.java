/*    */ package WEB-INF.classes.com.lenovo.iqs.simlock.bean;
/*    */ 
/*    */ import com.lenovo.iqs.utils.XmlUtils;
/*    */ 
/*    */ public class SimunlockResponse {
/*    */   private String responseCode;
/*    */   private String responseMsg;
/*    */   private String msg;
/*    */   
/* 10 */   public void setResponseCode(String responseCode) { this.responseCode = responseCode; } public void setResponseMsg(String responseMsg) { this.responseMsg = responseMsg; } public void setMsg(String msg) { this.msg = msg; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.simlock.bean.SimunlockResponse)) return false;  com.lenovo.iqs.simlock.bean.SimunlockResponse other = (com.lenovo.iqs.simlock.bean.SimunlockResponse)o; if (!other.canEqual(this)) return false;  Object this$responseCode = getResponseCode(), other$responseCode = other.getResponseCode(); if ((this$responseCode == null) ? (other$responseCode != null) : !this$responseCode.equals(other$responseCode)) return false;  Object this$responseMsg = getResponseMsg(), other$responseMsg = other.getResponseMsg(); if ((this$responseMsg == null) ? (other$responseMsg != null) : !this$responseMsg.equals(other$responseMsg)) return false;  Object this$msg = getMsg(), other$msg = other.getMsg(); return !((this$msg == null) ? (other$msg != null) : !this$msg.equals(other$msg)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.simlock.bean.SimunlockResponse; } public int hashCode() { int PRIME = 59; result = 1; Object $responseCode = getResponseCode(); result = result * 59 + (($responseCode == null) ? 43 : $responseCode.hashCode()); Object $responseMsg = getResponseMsg(); result = result * 59 + (($responseMsg == null) ? 43 : $responseMsg.hashCode()); Object $msg = getMsg(); return result * 59 + (($msg == null) ? 43 : $msg.hashCode()); } public String toString() { return "SimunlockResponse(responseCode=" + getResponseCode() + ", responseMsg=" + getResponseMsg() + ", msg=" + getMsg() + ")"; }
/*    */   
/* 12 */   public String getResponseCode() { return this.responseCode; }
/* 13 */   public String getResponseMsg() { return this.responseMsg; } public String getMsg() {
/* 14 */     return this.msg;
/*    */   }
/*    */   public static com.lenovo.iqs.simlock.bean.SimunlockResponse build(String data) throws Exception {
/* 17 */     com.lenovo.iqs.simlock.bean.SimunlockResponse rsuResponse = new com.lenovo.iqs.simlock.bean.SimunlockResponse();
/* 18 */     String errorCode = XmlUtils.getContentByTag(data, "errorCode");
/* 19 */     String errorMessage = XmlUtils.getContentByTag(data, "errorMessage");
/* 20 */     String certBlob = XmlUtils.getContentByTag(data, "msg");
/* 21 */     if ("0".equalsIgnoreCase(errorCode)) {
/* 22 */       errorMessage = "Success";
/*    */     }
/*    */     
/* 25 */     rsuResponse.setResponseCode(errorCode);
/* 26 */     rsuResponse.setResponseMsg(StringUtils.isEmpty(errorMessage) ? "" : errorMessage);
/* 27 */     rsuResponse.setMsg(StringUtils.isEmpty(certBlob) ? "" : certBlob);
/*    */     
/* 29 */     return rsuResponse;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\simlock\bean\SimunlockResponse.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */