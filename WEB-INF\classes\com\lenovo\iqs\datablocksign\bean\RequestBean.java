/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.bean;
/*    */ 
/*    */ import java.util.Arrays;
/*    */ import javax.xml.bind.annotation.XmlAccessType;
/*    */ import javax.xml.bind.annotation.XmlAccessorType;
/*    */ import javax.xml.bind.annotation.XmlElement;
/*    */ import javax.xml.bind.annotation.XmlType;
/*    */ 
/*    */ @XmlAccessorType(XmlAccessType.NONE)
/*    */ @XmlType(name = "RequestBean", propOrder = {"istrMASCID", "istrClientIP", "istrClientReqType", "istrCrc32", "istrNewIMEI", "istrOldIMEI", "istrPassChgRequd", "istrReqParam", "istrNodeLockParam", "istrRsdLogId"}, namespace = "java:com.lenovo.iqs.datablocksign.bean")
/*    */ public class RequestBean {
/*    */   public void setUserId(String userId) {
/* 13 */     this.userId = userId; } public void setPublicIP(String publicIP) { this.publicIP = publicIP; } public void setProcessorIdMappingImei(String processorIdMappingImei) { this.processorIdMappingImei = processorIdMappingImei; } public void setIstrClientIP(String istrClientIP) { this.istrClientIP = istrClientIP; } public void setIstrMASCID(String istrMASCID) { this.istrMASCID = istrMASCID; } public void setIstrClientReqType(String istrClientReqType) { this.istrClientReqType = istrClientReqType; } public void setIstrOldIMEI(String istrOldIMEI) { this.istrOldIMEI = istrOldIMEI; } public void setIstrNewIMEI(String istrNewIMEI) { this.istrNewIMEI = istrNewIMEI; } public void setIstrPassChgRequd(String istrPassChgRequd) { this.istrPassChgRequd = istrPassChgRequd; } public void setIstrReqParam(byte[] istrReqParam) { this.istrReqParam = istrReqParam; } public void setIstrNodeLockParam(byte[] istrNodeLockParam) { this.istrNodeLockParam = istrNodeLockParam; } public void setIstrCrc32(String istrCrc32) { this.istrCrc32 = istrCrc32; } public void setIstrRsdLogId(String istrRsdLogId) { this.istrRsdLogId = istrRsdLogId; } public void setDataBlock(String dataBlock) { this.dataBlock = dataBlock; } public void setError(String error) { this.error = error; } public void setEtokenInException(boolean etokenInException) { this.etokenInException = etokenInException; } public void setRsdResponse(String rsdResponse) { this.rsdResponse = rsdResponse; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.datablocksign.bean.RequestBean)) return false;  com.lenovo.iqs.datablocksign.bean.RequestBean other = (com.lenovo.iqs.datablocksign.bean.RequestBean)o; if (!other.canEqual(this)) return false;  Object this$userId = getUserId(), other$userId = other.getUserId(); if ((this$userId == null) ? (other$userId != null) : !this$userId.equals(other$userId)) return false;  Object this$publicIP = getPublicIP(), other$publicIP = other.getPublicIP(); if ((this$publicIP == null) ? (other$publicIP != null) : !this$publicIP.equals(other$publicIP)) return false;  Object this$processorIdMappingImei = getProcessorIdMappingImei(), other$processorIdMappingImei = other.getProcessorIdMappingImei(); if ((this$processorIdMappingImei == null) ? (other$processorIdMappingImei != null) : !this$processorIdMappingImei.equals(other$processorIdMappingImei)) return false;  Object this$istrClientIP = getIstrClientIP(), other$istrClientIP = other.getIstrClientIP(); if ((this$istrClientIP == null) ? (other$istrClientIP != null) : !this$istrClientIP.equals(other$istrClientIP)) return false;  Object this$istrMASCID = getIstrMASCID(), other$istrMASCID = other.getIstrMASCID(); if ((this$istrMASCID == null) ? (other$istrMASCID != null) : !this$istrMASCID.equals(other$istrMASCID)) return false;  Object this$istrClientReqType = getIstrClientReqType(), other$istrClientReqType = other.getIstrClientReqType(); if ((this$istrClientReqType == null) ? (other$istrClientReqType != null) : !this$istrClientReqType.equals(other$istrClientReqType)) return false;  Object this$istrOldIMEI = getIstrOldIMEI(), other$istrOldIMEI = other.getIstrOldIMEI(); if ((this$istrOldIMEI == null) ? (other$istrOldIMEI != null) : !this$istrOldIMEI.equals(other$istrOldIMEI)) return false;  Object this$istrNewIMEI = getIstrNewIMEI(), other$istrNewIMEI = other.getIstrNewIMEI(); if ((this$istrNewIMEI == null) ? (other$istrNewIMEI != null) : !this$istrNewIMEI.equals(other$istrNewIMEI)) return false;  Object this$istrPassChgRequd = getIstrPassChgRequd(), other$istrPassChgRequd = other.getIstrPassChgRequd(); if ((this$istrPassChgRequd == null) ? (other$istrPassChgRequd != null) : !this$istrPassChgRequd.equals(other$istrPassChgRequd)) return false;  if (!Arrays.equals(getIstrReqParam(), other.getIstrReqParam())) return false;  if (!Arrays.equals(getIstrNodeLockParam(), other.getIstrNodeLockParam())) return false;  Object this$istrCrc32 = getIstrCrc32(), other$istrCrc32 = other.getIstrCrc32(); if ((this$istrCrc32 == null) ? (other$istrCrc32 != null) : !this$istrCrc32.equals(other$istrCrc32)) return false;  Object this$istrRsdLogId = getIstrRsdLogId(), other$istrRsdLogId = other.getIstrRsdLogId(); if ((this$istrRsdLogId == null) ? (other$istrRsdLogId != null) : !this$istrRsdLogId.equals(other$istrRsdLogId)) return false;  Object this$dataBlock = getDataBlock(), other$dataBlock = other.getDataBlock(); if ((this$dataBlock == null) ? (other$dataBlock != null) : !this$dataBlock.equals(other$dataBlock)) return false;  Object this$error = getError(), other$error = other.getError(); if ((this$error == null) ? (other$error != null) : !this$error.equals(other$error)) return false;  if (isEtokenInException() != other.isEtokenInException()) return false;  Object this$rsdResponse = getRsdResponse(), other$rsdResponse = other.getRsdResponse(); return !((this$rsdResponse == null) ? (other$rsdResponse != null) : !this$rsdResponse.equals(other$rsdResponse)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.datablocksign.bean.RequestBean; } public int hashCode() { int PRIME = 59; result = 1; Object $userId = getUserId(); result = result * 59 + (($userId == null) ? 43 : $userId.hashCode()); Object $publicIP = getPublicIP(); result = result * 59 + (($publicIP == null) ? 43 : $publicIP.hashCode()); Object $processorIdMappingImei = getProcessorIdMappingImei(); result = result * 59 + (($processorIdMappingImei == null) ? 43 : $processorIdMappingImei.hashCode()); Object $istrClientIP = getIstrClientIP(); result = result * 59 + (($istrClientIP == null) ? 43 : $istrClientIP.hashCode()); Object $istrMASCID = getIstrMASCID(); result = result * 59 + (($istrMASCID == null) ? 43 : $istrMASCID.hashCode()); Object $istrClientReqType = getIstrClientReqType(); result = result * 59 + (($istrClientReqType == null) ? 43 : $istrClientReqType.hashCode()); Object $istrOldIMEI = getIstrOldIMEI(); result = result * 59 + (($istrOldIMEI == null) ? 43 : $istrOldIMEI.hashCode()); Object $istrNewIMEI = getIstrNewIMEI(); result = result * 59 + (($istrNewIMEI == null) ? 43 : $istrNewIMEI.hashCode()); Object $istrPassChgRequd = getIstrPassChgRequd(); result = result * 59 + (($istrPassChgRequd == null) ? 43 : $istrPassChgRequd.hashCode()); result = result * 59 + Arrays.hashCode(getIstrReqParam()); result = result * 59 + Arrays.hashCode(getIstrNodeLockParam()); Object $istrCrc32 = getIstrCrc32(); result = result * 59 + (($istrCrc32 == null) ? 43 : $istrCrc32.hashCode()); Object $istrRsdLogId = getIstrRsdLogId(); result = result * 59 + (($istrRsdLogId == null) ? 43 : $istrRsdLogId.hashCode()); Object $dataBlock = getDataBlock(); result = result * 59 + (($dataBlock == null) ? 43 : $dataBlock.hashCode()); Object $error = getError(); result = result * 59 + (($error == null) ? 43 : $error.hashCode()); result = result * 59 + (isEtokenInException() ? 79 : 97); Object $rsdResponse = getRsdResponse(); return result * 59 + (($rsdResponse == null) ? 43 : $rsdResponse.hashCode()); } public String toString() { return "RequestBean(userId=" + getUserId() + ", publicIP=" + getPublicIP() + ", processorIdMappingImei=" + getProcessorIdMappingImei() + ", istrClientIP=" + getIstrClientIP() + ", istrMASCID=" + getIstrMASCID() + ", istrClientReqType=" + getIstrClientReqType() + ", istrOldIMEI=" + getIstrOldIMEI() + ", istrNewIMEI=" + getIstrNewIMEI() + ", istrPassChgRequd=" + getIstrPassChgRequd() + ", istrReqParam=" + Arrays.toString(getIstrReqParam()) + ", istrNodeLockParam=" + Arrays.toString(getIstrNodeLockParam()) + ", istrCrc32=" + getIstrCrc32() + ", istrRsdLogId=" + getIstrRsdLogId() + ", dataBlock=" + getDataBlock() + ", error=" + getError() + ", etokenInException=" + isEtokenInException() + ", rsdResponse=" + getRsdResponse() + ")"; }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/* 18 */   private String userId = ""; public String getUserId() { return this.userId; } private String processorIdMappingImei; @XmlElement(name = "clientIP", nillable = true, required = true, namespace = "java:com.lenovo.iqs.datablocksign.bean") private String istrClientIP; @XmlElement(name = "MASCID", nillable = true, required = true, namespace = "java:com.lenovo.iqs.datablocksign.bean") private String istrMASCID; @XmlElement(name = "clientReqType", nillable = true, required = true, namespace = "java:com.lenovo.iqs.datablocksign.bean") private String istrClientReqType; @XmlElement(name = "oldIMEI", nillable = true, required = true, namespace = "java:com.lenovo.iqs.datablocksign.bean")
/*    */   private String istrOldIMEI; @XmlElement(name = "newIMEI", nillable = true, required = true, namespace = "java:com.lenovo.iqs.datablocksign.bean")
/* 20 */   private String istrNewIMEI; private String publicIP = ""; @XmlElement(name = "passChgRequd", nillable = true, required = true, namespace = "java:com.lenovo.iqs.datablocksign.bean") private String istrPassChgRequd; @XmlElement(name = "reqParam", nillable = true, required = true, namespace = "java:com.lenovo.iqs.datablocksign.bean") private byte[] istrReqParam; @XmlElement(name = "nodeLockParam", nillable = true, required = true, namespace = "java:com.lenovo.iqs.datablocksign.bean") private byte[] istrNodeLockParam; @XmlElement(name = "crc32", nillable = true, required = true, namespace = "java:com.lenovo.iqs.datablocksign.bean") private String istrCrc32; @XmlElement(name = "rsd_log_id", nillable = true, namespace = "java:com.lenovo.iqs.datablocksign.bean") private String istrRsdLogId; private String dataBlock; public String getPublicIP() { return this.publicIP; }
/*    */    public String getProcessorIdMappingImei() {
/* 22 */     return this.processorIdMappingImei;
/*    */   }
/*    */   public String getIstrClientIP() {
/* 25 */     return this.istrClientIP;
/*    */   }
/*    */   public String getIstrMASCID() {
/* 28 */     return this.istrMASCID;
/*    */   }
/*    */   public String getIstrClientReqType() {
/* 31 */     return this.istrClientReqType;
/*    */   }
/*    */   public String getIstrOldIMEI() {
/* 34 */     return this.istrOldIMEI;
/*    */   }
/*    */   public String getIstrNewIMEI() {
/* 37 */     return this.istrNewIMEI;
/*    */   }
/*    */   public String getIstrPassChgRequd() {
/* 40 */     return this.istrPassChgRequd;
/*    */   }
/*    */   public byte[] getIstrReqParam() {
/* 43 */     return this.istrReqParam;
/*    */   }
/*    */   public byte[] getIstrNodeLockParam() {
/* 46 */     return this.istrNodeLockParam;
/*    */   }
/*    */   public String getIstrCrc32() {
/* 49 */     return this.istrCrc32;
/*    */   }
/*    */   public String getIstrRsdLogId() {
/* 52 */     return this.istrRsdLogId;
/*    */   } public String getDataBlock() {
/* 54 */     return this.dataBlock;
/*    */   }
/* 56 */   private String error = ""; private boolean etokenInException; private String rsdResponse; public String getError() { return this.error; }
/*    */    public boolean isEtokenInException() {
/* 58 */     return this.etokenInException;
/*    */   } public String getRsdResponse() {
/* 60 */     return this.rsdResponse;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\bean\RequestBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */