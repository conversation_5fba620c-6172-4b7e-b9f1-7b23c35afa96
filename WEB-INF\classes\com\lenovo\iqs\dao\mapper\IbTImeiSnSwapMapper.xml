<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTImeiSnSwapMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTImeiSnSwap" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="ori_sn" property="oriSn" jdbcType="VARCHAR" />
    <result column="old_sn" property="oldSn" jdbcType="VARCHAR" />
    <result column="old_imei1" property="oldImei1" jdbcType="VARCHAR" />
    <result column="old_imei2" property="oldImei2" jdbcType="VARCHAR" />
    <result column="new_imei1" property="newImei1" jdbcType="VARCHAR" />
    <result column="new_imei2" property="newImei2" jdbcType="VARCHAR" />
    <result column="new_sn" property="newSn" jdbcType="VARCHAR" />
    <result column="up_user" property="upUser" jdbcType="VARCHAR" />
    <result column="last_change" property="lastChange" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, ori_sn, old_sn, old_imei1, old_imei2, new_imei1, new_imei2, new_sn, up_user, 
    last_change
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_imei_sn_swap
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from ib_t_imei_sn_swap
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTImeiSnSwap" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_imei_sn_swap (ori_sn, old_sn, old_imei1, 
      old_imei2, new_imei1, new_imei2, 
      new_sn, up_user, last_change
      )
    values (#{oriSn,jdbcType=VARCHAR}, #{oldSn,jdbcType=VARCHAR}, #{oldImei1,jdbcType=VARCHAR}, 
      #{oldImei2,jdbcType=VARCHAR}, #{newImei1,jdbcType=VARCHAR}, #{newImei2,jdbcType=VARCHAR}, 
      #{newSn,jdbcType=VARCHAR}, #{upUser,jdbcType=VARCHAR}, #{lastChange,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTImeiSnSwap" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_imei_sn_swap
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="oriSn != null" >
        ori_sn,
      </if>
      <if test="oldSn != null" >
        old_sn,
      </if>
      <if test="oldImei1 != null" >
        old_imei1,
      </if>
      <if test="oldImei2 != null" >
        old_imei2,
      </if>
      <if test="newImei1 != null" >
        new_imei1,
      </if>
      <if test="newImei2 != null" >
        new_imei2,
      </if>
      <if test="newSn != null" >
        new_sn,
      </if>
      <if test="upUser != null" >
        up_user,
      </if>
      <if test="lastChange != null" >
        last_change,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="oriSn != null" >
        #{oriSn,jdbcType=VARCHAR},
      </if>
      <if test="oldSn != null" >
        #{oldSn,jdbcType=VARCHAR},
      </if>
      <if test="oldImei1 != null" >
        #{oldImei1,jdbcType=VARCHAR},
      </if>
      <if test="oldImei2 != null" >
        #{oldImei2,jdbcType=VARCHAR},
      </if>
      <if test="newImei1 != null" >
        #{newImei1,jdbcType=VARCHAR},
      </if>
      <if test="newImei2 != null" >
        #{newImei2,jdbcType=VARCHAR},
      </if>
      <if test="newSn != null" >
        #{newSn,jdbcType=VARCHAR},
      </if>
      <if test="upUser != null" >
        #{upUser,jdbcType=VARCHAR},
      </if>
      <if test="lastChange != null" >
        #{lastChange,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTImeiSnSwap" >
    update ib_t_imei_sn_swap
    <set >
      <if test="oriSn != null" >
        ori_sn = #{oriSn,jdbcType=VARCHAR},
      </if>
      <if test="oldSn != null" >
        old_sn = #{oldSn,jdbcType=VARCHAR},
      </if>
      <if test="oldImei1 != null" >
        old_imei1 = #{oldImei1,jdbcType=VARCHAR},
      </if>
      <if test="oldImei2 != null" >
        old_imei2 = #{oldImei2,jdbcType=VARCHAR},
      </if>
      <if test="newImei1 != null" >
        new_imei1 = #{newImei1,jdbcType=VARCHAR},
      </if>
      <if test="newImei2 != null" >
        new_imei2 = #{newImei2,jdbcType=VARCHAR},
      </if>
      <if test="newSn != null" >
        new_sn = #{newSn,jdbcType=VARCHAR},
      </if>
      <if test="upUser != null" >
        up_user = #{upUser,jdbcType=VARCHAR},
      </if>
      <if test="lastChange != null" >
        last_change = #{lastChange,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTImeiSnSwap" >
    update ib_t_imei_sn_swap
    set ori_sn = #{oriSn,jdbcType=VARCHAR},
      old_sn = #{oldSn,jdbcType=VARCHAR},
      old_imei1 = #{oldImei1,jdbcType=VARCHAR},
      old_imei2 = #{oldImei2,jdbcType=VARCHAR},
      new_imei1 = #{newImei1,jdbcType=VARCHAR},
      new_imei2 = #{newImei2,jdbcType=VARCHAR},
      new_sn = #{newSn,jdbcType=VARCHAR},
      up_user = #{upUser,jdbcType=VARCHAR},
      last_change = #{lastChange,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>