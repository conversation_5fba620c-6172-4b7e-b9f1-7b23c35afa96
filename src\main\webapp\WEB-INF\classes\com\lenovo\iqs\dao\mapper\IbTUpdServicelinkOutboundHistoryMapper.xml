<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdServicelinkOutboundHistoryMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdServicelinkOutboundHistory" >
    <result column="warranty_claim_no" property="warrantyClaimNo" jdbcType="VARCHAR" />
    <result column="serial_no" property="serialNo" jdbcType="VARCHAR" />
    <result column="source_serial_no" property="sourceSerialNo" jdbcType="VARCHAR" />
    <result column="stored_serial_no" property="storedSerialNo" jdbcType="VARCHAR" />
    <result column="status_code" property="statusCode" jdbcType="VARCHAR" />
    <result column="factory_code" property="factoryCode" jdbcType="VARCHAR" />
    <result column="apc_code" property="apcCode" jdbcType="VARCHAR" />
    <result column="transceiver_model_no" property="transceiverModelNo" jdbcType="VARCHAR" />
    <result column="customer_model_no" property="customerModelNo" jdbcType="VARCHAR" />
    <result column="ship_date" property="shipDate" jdbcType="TIMESTAMP" />
    <result column="sold_to_cust_id" property="soldToCustId" jdbcType="VARCHAR" />
    <result column="sold_to_cust_name" property="soldToCustName" jdbcType="VARCHAR" />
    <result column="ship_to_cust_id" property="shipToCustId" jdbcType="VARCHAR" />
    <result column="ship_to_cust_addr_id" property="shipToCustAddrId" jdbcType="VARCHAR" />
    <result column="ship_to_cust_name" property="shipToCustName" jdbcType="VARCHAR" />
    <result column="ship_to_country_code" property="shipToCountryCode" jdbcType="VARCHAR" />
    <result column="original_warr_type_code" property="originalWarrTypeCode" jdbcType="VARCHAR" />
    <result column="original_std_warr_exp_date" property="originalStdWarrExpDate" jdbcType="TIMESTAMP" />
    <result column="original_ext_warr_exp_date" property="originalExtWarrExpDate" jdbcType="TIMESTAMP" />
    <result column="renewal_warr_type_code" property="renewalWarrTypeCode" jdbcType="VARCHAR" />
    <result column="renewal_std_warr_expiry_date" property="renewalStdWarrExpiryDate" jdbcType="TIMESTAMP" />
    <result column="renewal_ext_warr_expiry_date" property="renewalExtWarrExpiryDate" jdbcType="TIMESTAMP" />
    <result column="warr_cancel_code" property="warrCancelCode" jdbcType="VARCHAR" />
    <result column="warr_can_code_effective_date" property="warrCanCodeEffectiveDate" jdbcType="TIMESTAMP" />
    <result column="curr_warr_type_code" property="currWarrTypeCode" jdbcType="VARCHAR" />
    <result column="curr_std_warr_expiry_date" property="currStdWarrExpiryDate" jdbcType="TIMESTAMP" />
    <result column="curr_extended_warr_expiry_date" property="currExtendedWarrExpiryDate" jdbcType="TIMESTAMP" />
    <result column="warr_flag" property="warrFlag" jdbcType="VARCHAR" />
    <result column="dual_serial_no" property="dualSerialNo" jdbcType="VARCHAR" />
    <result column="dual_serial_no_type" property="dualSerialNoType" jdbcType="VARCHAR" />
    <result column="tri_serial_no" property="triSerialNo" jdbcType="VARCHAR" />
    <result column="tri_serial_no_type" property="triSerialNoType" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="batch_number" property="batchNumber" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdServicelinkOutboundHistory" >
    insert into ib_t_upd_servicelink_outbound_history (warranty_claim_no, serial_no, source_serial_no, 
      stored_serial_no, status_code, factory_code, 
      apc_code, transceiver_model_no, customer_model_no, 
      ship_date, sold_to_cust_id, sold_to_cust_name, 
      ship_to_cust_id, ship_to_cust_addr_id, ship_to_cust_name, 
      ship_to_country_code, original_warr_type_code, 
      original_std_warr_exp_date, original_ext_warr_exp_date, 
      renewal_warr_type_code, renewal_std_warr_expiry_date, 
      renewal_ext_warr_expiry_date, warr_cancel_code, 
      warr_can_code_effective_date, curr_warr_type_code, 
      curr_std_warr_expiry_date, curr_extended_warr_expiry_date, 
      warr_flag, dual_serial_no, dual_serial_no_type, 
      tri_serial_no, tri_serial_no_type, create_time, 
      batch_number)
    values (#{warrantyClaimNo,jdbcType=VARCHAR}, #{serialNo,jdbcType=VARCHAR}, #{sourceSerialNo,jdbcType=VARCHAR}, 
      #{storedSerialNo,jdbcType=VARCHAR}, #{statusCode,jdbcType=VARCHAR}, #{factoryCode,jdbcType=VARCHAR}, 
      #{apcCode,jdbcType=VARCHAR}, #{transceiverModelNo,jdbcType=VARCHAR}, #{customerModelNo,jdbcType=VARCHAR}, 
      #{shipDate,jdbcType=TIMESTAMP}, #{soldToCustId,jdbcType=VARCHAR}, #{soldToCustName,jdbcType=VARCHAR}, 
      #{shipToCustId,jdbcType=VARCHAR}, #{shipToCustAddrId,jdbcType=VARCHAR}, #{shipToCustName,jdbcType=VARCHAR}, 
      #{shipToCountryCode,jdbcType=VARCHAR}, #{originalWarrTypeCode,jdbcType=VARCHAR}, 
      #{originalStdWarrExpDate,jdbcType=TIMESTAMP}, #{originalExtWarrExpDate,jdbcType=TIMESTAMP}, 
      #{renewalWarrTypeCode,jdbcType=VARCHAR}, #{renewalStdWarrExpiryDate,jdbcType=TIMESTAMP}, 
      #{renewalExtWarrExpiryDate,jdbcType=TIMESTAMP}, #{warrCancelCode,jdbcType=VARCHAR}, 
      #{warrCanCodeEffectiveDate,jdbcType=TIMESTAMP}, #{currWarrTypeCode,jdbcType=VARCHAR}, 
      #{currStdWarrExpiryDate,jdbcType=TIMESTAMP}, #{currExtendedWarrExpiryDate,jdbcType=TIMESTAMP}, 
      #{warrFlag,jdbcType=VARCHAR}, #{dualSerialNo,jdbcType=VARCHAR}, #{dualSerialNoType,jdbcType=VARCHAR}, 
      #{triSerialNo,jdbcType=VARCHAR}, #{triSerialNoType,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{batchNumber,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdServicelinkOutboundHistory" >
    insert into ib_t_upd_servicelink_outbound_history
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="warrantyClaimNo != null" >
        warranty_claim_no,
      </if>
      <if test="serialNo != null" >
        serial_no,
      </if>
      <if test="sourceSerialNo != null" >
        source_serial_no,
      </if>
      <if test="storedSerialNo != null" >
        stored_serial_no,
      </if>
      <if test="statusCode != null" >
        status_code,
      </if>
      <if test="factoryCode != null" >
        factory_code,
      </if>
      <if test="apcCode != null" >
        apc_code,
      </if>
      <if test="transceiverModelNo != null" >
        transceiver_model_no,
      </if>
      <if test="customerModelNo != null" >
        customer_model_no,
      </if>
      <if test="shipDate != null" >
        ship_date,
      </if>
      <if test="soldToCustId != null" >
        sold_to_cust_id,
      </if>
      <if test="soldToCustName != null" >
        sold_to_cust_name,
      </if>
      <if test="shipToCustId != null" >
        ship_to_cust_id,
      </if>
      <if test="shipToCustAddrId != null" >
        ship_to_cust_addr_id,
      </if>
      <if test="shipToCustName != null" >
        ship_to_cust_name,
      </if>
      <if test="shipToCountryCode != null" >
        ship_to_country_code,
      </if>
      <if test="originalWarrTypeCode != null" >
        original_warr_type_code,
      </if>
      <if test="originalStdWarrExpDate != null" >
        original_std_warr_exp_date,
      </if>
      <if test="originalExtWarrExpDate != null" >
        original_ext_warr_exp_date,
      </if>
      <if test="renewalWarrTypeCode != null" >
        renewal_warr_type_code,
      </if>
      <if test="renewalStdWarrExpiryDate != null" >
        renewal_std_warr_expiry_date,
      </if>
      <if test="renewalExtWarrExpiryDate != null" >
        renewal_ext_warr_expiry_date,
      </if>
      <if test="warrCancelCode != null" >
        warr_cancel_code,
      </if>
      <if test="warrCanCodeEffectiveDate != null" >
        warr_can_code_effective_date,
      </if>
      <if test="currWarrTypeCode != null" >
        curr_warr_type_code,
      </if>
      <if test="currStdWarrExpiryDate != null" >
        curr_std_warr_expiry_date,
      </if>
      <if test="currExtendedWarrExpiryDate != null" >
        curr_extended_warr_expiry_date,
      </if>
      <if test="warrFlag != null" >
        warr_flag,
      </if>
      <if test="dualSerialNo != null" >
        dual_serial_no,
      </if>
      <if test="dualSerialNoType != null" >
        dual_serial_no_type,
      </if>
      <if test="triSerialNo != null" >
        tri_serial_no,
      </if>
      <if test="triSerialNoType != null" >
        tri_serial_no_type,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="batchNumber != null" >
        batch_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="warrantyClaimNo != null" >
        #{warrantyClaimNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null" >
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="sourceSerialNo != null" >
        #{sourceSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="storedSerialNo != null" >
        #{storedSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="statusCode != null" >
        #{statusCode,jdbcType=VARCHAR},
      </if>
      <if test="factoryCode != null" >
        #{factoryCode,jdbcType=VARCHAR},
      </if>
      <if test="apcCode != null" >
        #{apcCode,jdbcType=VARCHAR},
      </if>
      <if test="transceiverModelNo != null" >
        #{transceiverModelNo,jdbcType=VARCHAR},
      </if>
      <if test="customerModelNo != null" >
        #{customerModelNo,jdbcType=VARCHAR},
      </if>
      <if test="shipDate != null" >
        #{shipDate,jdbcType=TIMESTAMP},
      </if>
      <if test="soldToCustId != null" >
        #{soldToCustId,jdbcType=VARCHAR},
      </if>
      <if test="soldToCustName != null" >
        #{soldToCustName,jdbcType=VARCHAR},
      </if>
      <if test="shipToCustId != null" >
        #{shipToCustId,jdbcType=VARCHAR},
      </if>
      <if test="shipToCustAddrId != null" >
        #{shipToCustAddrId,jdbcType=VARCHAR},
      </if>
      <if test="shipToCustName != null" >
        #{shipToCustName,jdbcType=VARCHAR},
      </if>
      <if test="shipToCountryCode != null" >
        #{shipToCountryCode,jdbcType=VARCHAR},
      </if>
      <if test="originalWarrTypeCode != null" >
        #{originalWarrTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="originalStdWarrExpDate != null" >
        #{originalStdWarrExpDate,jdbcType=TIMESTAMP},
      </if>
      <if test="originalExtWarrExpDate != null" >
        #{originalExtWarrExpDate,jdbcType=TIMESTAMP},
      </if>
      <if test="renewalWarrTypeCode != null" >
        #{renewalWarrTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="renewalStdWarrExpiryDate != null" >
        #{renewalStdWarrExpiryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="renewalExtWarrExpiryDate != null" >
        #{renewalExtWarrExpiryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="warrCancelCode != null" >
        #{warrCancelCode,jdbcType=VARCHAR},
      </if>
      <if test="warrCanCodeEffectiveDate != null" >
        #{warrCanCodeEffectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="currWarrTypeCode != null" >
        #{currWarrTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="currStdWarrExpiryDate != null" >
        #{currStdWarrExpiryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="currExtendedWarrExpiryDate != null" >
        #{currExtendedWarrExpiryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="warrFlag != null" >
        #{warrFlag,jdbcType=VARCHAR},
      </if>
      <if test="dualSerialNo != null" >
        #{dualSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="dualSerialNoType != null" >
        #{dualSerialNoType,jdbcType=VARCHAR},
      </if>
      <if test="triSerialNo != null" >
        #{triSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="triSerialNoType != null" >
        #{triSerialNoType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNumber != null" >
        #{batchNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>