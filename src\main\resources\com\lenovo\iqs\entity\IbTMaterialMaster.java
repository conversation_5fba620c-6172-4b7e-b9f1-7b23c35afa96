/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTMaterialMaster implements Serializable { private Integer id; private String materialId;
/*    */   private String machineType;
/*    */   private String materialType;
/*    */   private String hierarchy;
/*    */   
/*  7 */   public void setId(Integer id) { this.id = id; } private Date lastChangeTime; private String preserve1; private String preserve2; private String preserve3; private static final long serialVersionUID = 1L; public void setMaterialId(String materialId) { this.materialId = materialId; } public void setMachineType(String machineType) { this.machineType = machineType; } public void setMaterialType(String materialType) { this.materialType = materialType; } public void setHierarchy(String hierarchy) { this.hierarchy = hierarchy; } public void setLastChangeTime(Date lastChangeTime) { this.lastChangeTime = lastChangeTime; } public void setPreserve1(String preserve1) { this.preserve1 = preserve1; } public void setPreserve2(String preserve2) { this.preserve2 = preserve2; } public void setPreserve3(String preserve3) { this.preserve3 = preserve3; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTMaterialMaster)) return false;  com.lenovo.iqs.entity.IbTMaterialMaster other = (com.lenovo.iqs.entity.IbTMaterialMaster)o; if (!other.canEqual(this)) return false;  Object this$id = getId(), other$id = other.getId(); if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id)) return false;  Object this$materialId = getMaterialId(), other$materialId = other.getMaterialId(); if ((this$materialId == null) ? (other$materialId != null) : !this$materialId.equals(other$materialId)) return false;  Object this$machineType = getMachineType(), other$machineType = other.getMachineType(); if ((this$machineType == null) ? (other$machineType != null) : !this$machineType.equals(other$machineType)) return false;  Object this$materialType = getMaterialType(), other$materialType = other.getMaterialType(); if ((this$materialType == null) ? (other$materialType != null) : !this$materialType.equals(other$materialType)) return false;  Object this$hierarchy = getHierarchy(), other$hierarchy = other.getHierarchy(); if ((this$hierarchy == null) ? (other$hierarchy != null) : !this$hierarchy.equals(other$hierarchy)) return false;  Object this$lastChangeTime = getLastChangeTime(), other$lastChangeTime = other.getLastChangeTime(); if ((this$lastChangeTime == null) ? (other$lastChangeTime != null) : !this$lastChangeTime.equals(other$lastChangeTime)) return false;  Object this$preserve1 = getPreserve1(), other$preserve1 = other.getPreserve1(); if ((this$preserve1 == null) ? (other$preserve1 != null) : !this$preserve1.equals(other$preserve1)) return false;  Object this$preserve2 = getPreserve2(), other$preserve2 = other.getPreserve2(); if ((this$preserve2 == null) ? (other$preserve2 != null) : !this$preserve2.equals(other$preserve2)) return false;  Object this$preserve3 = getPreserve3(), other$preserve3 = other.getPreserve3(); return !((this$preserve3 == null) ? (other$preserve3 != null) : !this$preserve3.equals(other$preserve3)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTMaterialMaster; } public int hashCode() { int PRIME = 59; result = 1; Object $id = getId(); result = result * 59 + (($id == null) ? 43 : $id.hashCode()); Object $materialId = getMaterialId(); result = result * 59 + (($materialId == null) ? 43 : $materialId.hashCode()); Object $machineType = getMachineType(); result = result * 59 + (($machineType == null) ? 43 : $machineType.hashCode()); Object $materialType = getMaterialType(); result = result * 59 + (($materialType == null) ? 43 : $materialType.hashCode()); Object $hierarchy = getHierarchy(); result = result * 59 + (($hierarchy == null) ? 43 : $hierarchy.hashCode()); Object $lastChangeTime = getLastChangeTime(); result = result * 59 + (($lastChangeTime == null) ? 43 : $lastChangeTime.hashCode()); Object $preserve1 = getPreserve1(); result = result * 59 + (($preserve1 == null) ? 43 : $preserve1.hashCode()); Object $preserve2 = getPreserve2(); result = result * 59 + (($preserve2 == null) ? 43 : $preserve2.hashCode()); Object $preserve3 = getPreserve3(); return result * 59 + (($preserve3 == null) ? 43 : $preserve3.hashCode()); } public String toString() { return "IbTMaterialMaster(id=" + getId() + ", materialId=" + getMaterialId() + ", machineType=" + getMachineType() + ", materialType=" + getMaterialType() + ", hierarchy=" + getHierarchy() + ", lastChangeTime=" + getLastChangeTime() + ", preserve1=" + getPreserve1() + ", preserve2=" + getPreserve2() + ", preserve3=" + getPreserve3() + ")"; }
/*    */    public Integer getId() {
/*  9 */     return this.id;
/*    */   } public String getMaterialId() {
/* 11 */     return this.materialId;
/*    */   } public String getMachineType() {
/* 13 */     return this.machineType;
/*    */   } public String getMaterialType() {
/* 15 */     return this.materialType;
/*    */   } public String getHierarchy() {
/* 17 */     return this.hierarchy;
/*    */   } public Date getLastChangeTime() {
/* 19 */     return this.lastChangeTime;
/*    */   } public String getPreserve1() {
/* 21 */     return this.preserve1;
/*    */   } public String getPreserve2() {
/* 23 */     return this.preserve2;
/*    */   } public String getPreserve3() {
/* 25 */     return this.preserve3;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTMaterialMaster.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */