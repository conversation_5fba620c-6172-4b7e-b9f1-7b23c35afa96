# IQS - Configuración de Spring Framework

## 🌱 Arquitectura Spring

El proyecto IQS utiliza **Spring Framework 4.2.1** con las siguientes configuraciones:
- **Spring MVC**: Para controladores web
- **Spring Core**: Inyección de dependencias
- **Spring DAO**: Acceso a datos
- **Spring Web Services**: Servicios web
- **Spring Batch**: Procesamiento por lotes

## 📁 Archivos de Configuración

### **1. applicationContext.xml** (Configuración Principal)

#### **Escaneo de Componentes**
```xml
<!-- Escaneo automático de componentes -->
<context:component-scan base-package="com.lenovo.iqs">
    <context:exclude-filter type="annotation"
        expression="org.springframework.stereotype.Controller" />
</context:component-scan>
```

#### **Configuración de Properties**
```xml
<!-- Lectura de archivos de propiedades -->
<bean id="propertyConfigurer"
    class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
    <property name="locations">
        <list>
            <value>classpath:config.properties</value>
        </list>
    </property>
    <property name="fileEncoding" value="UTF-8" />
</bean>
```

#### **Importación de Configuraciones**
```xml
<!-- Importar todas las configuraciones Spring -->
<import resource="classpath:**/spring-*" />
```

#### **Monitoreo JavaMelody**
```xml
<!-- Configuración de monitoreo -->
<bean id="monitoringAdvisor" class="net.bull.javamelody.MonitoringSpringAdvisor">
    <property name="pointcut">
        <bean class="net.bull.javamelody.MonitoredWithAnnotationPointcut"/>
    </property>
</bean>
```

### **2. spring-mvc.xml** (Configuración MVC)

#### **Configuración de Anotaciones**
```xml
<!-- Habilitar anotaciones MVC con validación -->
<mvc:annotation-driven validator="validator"
    ignore-default-model-on-redirect="true" />
```

#### **Escaneo de Controladores**
```xml
<!-- Escaneo específico de controladores -->
<context:component-scan base-package="com.lenovo.iqs.**.controller" />
```

#### **Configuración de Locale**
```xml
<!-- Locale fijo en inglés -->
<bean id="localeResolver"
    class="org.springframework.web.servlet.i18n.FixedLocaleResolver">
    <constructor-arg value="en" />
</bean>
```

#### **Validador Hibernate**
```xml
<!-- Configuración de validación -->
<bean id="validator"
    class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean">
    <property name="providerClass" value="org.hibernate.validator.HibernateValidator" />
    <property name="validationMessageSource" ref="messageSource" />
</bean>
```

### **3. spring-dao.xml** (Configuración de Datos)

#### **DataSource Principal (MySQL)**
```xml
<!-- DataSource MySQL principal -->
<bean name="dataSource" class="com.mysql.cj.jdbc.MysqlDataSource">
    <property name="url" value="${jdbc.url}" />
    <property name="user" value="${jdbc.username}" />
    <property name="password" value="${jdbc.password}" />
</bean>
```

#### **Pool Druid para MySQL**
```xml
<!-- Pool de conexiones Druid -->
<bean name="druidDataSource" class="com.alibaba.druid.pool.DruidDataSource">
    <property name="driverClassName" value="${jdbc.driverClassName}" />
    <property name="url" value="${jdbc.url}" />
    <property name="username" value="${jdbc.username}" />
    <property name="password" value="${jdbc.password}" />
    
    <!-- Configuración del pool -->
    <property name="maxActive" value="3000" />
    <property name="minIdle" value="10" />
    <property name="initialSize" value="10" />
    <property name="maxWait" value="60000" />
    
    <!-- Validación -->
    <property name="validationQuery" value="select 1" />
    <property name="testOnBorrow" value="true" />
    <property name="testOnReturn" value="false" />
    <property name="testWhileIdle" value="true" />
    
    <!-- Monitoreo -->
    <property name="filters" value="stat" />
</bean>
```

#### **DataSource PCG (SQL Server)**
```xml
<!-- DataSource SQL Server para PCG -->
<bean name="PCGDataSource" class="com.alibaba.druid.pool.DruidDataSource">
    <property name="driverClassName" value="${pcgjdbc.driverClassName}" />
    <property name="url" value="${pcgjdbc.url}" />
    <property name="username" value="${pcgjdbc.username}" />
    <property name="password" value="${pcgjdbc.password}" />
    <property name="maxActive" value="3000" />
    <property name="minIdle" value="10" />
    <property name="validationQuery" value="select 1" />
    <property name="filters" value="stat" />
    <property name="testOnBorrow" value="true" />
</bean>
```

#### **MyBatis SqlSessionFactory**
```xml
<!-- Configuración MyBatis -->
<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
    <property name="configLocation" value="classpath:sqlMapConfig.xml" />
    <property name="dataSource" ref="dataSource" />
    <property name="mapperLocations" value="classpath*:com/lenovo/iqs/**/mapper/*Mapper.xml" />
</bean>
```

#### **Gestión de Transacciones**
```xml
<!-- Transaction Manager -->
<bean id="transactionManager"
    class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
    <property name="dataSource" ref="dataSource" />
</bean>

<!-- Configuración de transacciones por defecto -->
<bean class="org.springframework.transaction.support.DefaultTransactionDefinition">
    <property name="propagationBehaviorName" value="PROPAGATION_REQUIRED" />
</bean>

<!-- Habilitar anotaciones de transacción -->
<tx:annotation-driven transaction-manager="transactionManager" />
```

### **4. spring-webservice.xml** (Servicios Web)

#### **Importación CXF**
```xml
<!-- Importar configuración CXF -->
<import resource="classpath*:META-INF/cxf/cxf.xml" />
```

#### **Servidor REST**
```xml
<!-- Configuración servidor REST -->
<jaxrs:server address="/rest" id="RESTWebService">
    <jaxrs:serviceBeans>
        <ref bean="rsuController"/>
        <ref bean="weChatController"/>
        <ref bean="simUnlockController"/>
        <ref bean="googleRPKController"/>
    </jaxrs:serviceBeans>

    <jaxrs:providers>
        <bean class="org.codehaus.jackson.jaxrs.JacksonJsonProvider" />
        <bean class="org.apache.cxf.jaxrs.provider.JAXBElementProvider" />
    </jaxrs:providers>
</jaxrs:server>
```

#### **Configuración HTTP**
```xml
<!-- Configuración de timeouts HTTP -->
<http-conf:conduit name="*.http-conduit">
    <http-conf:client ConnectionTimeout="30000" ReceiveTimeout="60000"/>
</http-conf:conduit>
```

## 🔧 Configuración de Propiedades

### **config.properties**
```properties
# Configuración de base de datos MySQL
jdbc.driverClassName=com.mysql.jdbc.Driver
jdbc.url=************************************************************************************
jdbc.username=root
jdbc.password=iqsprodnew

# Configuración SAP
ibase.sap.appServerHost=**********
ibase.sap.systemID=CSP
ibase.sap.client=301
ibase.sap.user=SYSB-MBGIQS
ibase.sap.password=1234qwer

# URLs de servicios externos
GPS_URL=https://wsgw.motorola.com:443/GPSTrustonicRSUService/RSUService
ibase_url=http://csp.lenovo.com/ibapp/POIRequest.jsp
```

## 🏗️ Configuración de Beans

### **Configuración Programática**
```java
@Configuration
@EnableWebMvc
@ComponentScan(basePackages = "com.lenovo.iqs")
public class WebConfig implements WebMvcConfigurer {
    
    @Bean
    public LocalValidatorFactoryBean validator() {
        LocalValidatorFactoryBean bean = new LocalValidatorFactoryBean();
        bean.setProviderClass(HibernateValidator.class);
        return bean;
    }
    
    @Bean
    public MessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        messageSource.setBasename("messages");
        messageSource.setDefaultEncoding("UTF-8");
        return messageSource;
    }
}
```

### **Configuración de Seguridad**
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {
    
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
            .csrf().disable()
            .authorizeRequests()
                .antMatchers("/webservice/**").permitAll()
                .antMatchers("/druid/**").hasRole("ADMIN")
                .anyRequest().authenticated()
            .and()
            .httpBasic();
    }
}
```

## 📊 Configuración de Monitoreo

### **JavaMelody**
```xml
<!-- Configuración de monitoreo de rendimiento -->
<bean id="monitoringAdvisor" class="net.bull.javamelody.MonitoringSpringAdvisor">
    <property name="pointcut">
        <bean class="net.bull.javamelody.MonitoredWithAnnotationPointcut"/>
    </property>
</bean>

<!-- Interceptor para monitoreo -->
<bean class="org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator">
    <property name="advisorBeanNamePrefix" value="monitoring"/>
</bean>
```

### **Druid Monitoring**
```xml
<!-- Configuración de filtros Druid -->
<bean class="com.alibaba.druid.support.spring.stat.DruidStatInterceptor"/>

<bean class="com.alibaba.druid.support.spring.stat.BeanTypeAutoProxyCreator">
    <property name="targetBeanType" value="org.springframework.jdbc.core.JdbcTemplate"/>
    <property name="interceptorNames">
        <list>
            <value>druidStatInterceptor</value>
        </list>
    </property>
</bean>
```

## 🔄 Configuración de Batch Processing

### **Spring Batch**
```xml
<!-- Configuración de trabajos por lotes -->
<batch:job-repository id="jobRepository" data-source="configDataSource"/>

<batch:job id="deviceProcessingJob">
    <batch:step id="step1">
        <batch:tasklet>
            <batch:chunk reader="deviceReader" processor="deviceProcessor" writer="deviceWriter" commit-interval="100"/>
        </batch:tasklet>
    </batch:step>
</batch:job>

<!-- Job Launcher -->
<bean id="jobLauncher" class="org.springframework.batch.core.launch.support.SimpleJobLauncher">
    <property name="jobRepository" ref="jobRepository"/>
</bean>
```

### **Quartz Scheduler**
```xml
<!-- Configuración de scheduler -->
<bean id="quartzScheduler" class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
    <property name="dataSource" ref="configDataSource"/>
    <property name="configLocation" value="classpath:quartz.properties"/>
    <property name="triggers">
        <list>
            <ref bean="deviceProcessingTrigger"/>
        </list>
    </property>
</bean>
```

## 🚨 Manejo de Errores

### **Exception Resolver**
```xml
<!-- Resolver de excepciones -->
<bean class="org.springframework.web.servlet.handler.SimpleMappingExceptionResolver">
    <property name="exceptionMappings">
        <props>
            <prop key="com.lenovo.iqs.exceptions.ValidationException">error/validation</prop>
            <prop key="com.lenovo.iqs.exceptions.ServiceException">error/service</prop>
        </props>
    </property>
    <property name="defaultErrorView" value="error/general"/>
</bean>
```

### **Global Exception Handler**
```java
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ErrorResponse> handleValidation(ValidationException e) {
        ErrorResponse error = new ErrorResponse(e.getErrorCode(), e.getMessage());
        return ResponseEntity.badRequest().body(error);
    }
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGeneral(Exception e) {
        ErrorResponse error = new ErrorResponse("INTERNAL_ERROR", "Internal server error");
        return ResponseEntity.status(500).body(error);
    }
}
```

## 🔧 Configuración de Perfiles

### **Perfiles de Ambiente**
```xml
<!-- Configuración por perfil -->
<beans profile="development">
    <bean id="dataSource" class="org.apache.commons.dbcp.BasicDataSource">
        <property name="url" value="${dev.jdbc.url}"/>
    </bean>
</beans>

<beans profile="production">
    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource">
        <property name="url" value="${prod.jdbc.url}"/>
    </bean>
</beans>
```

### **Activación de Perfiles**
```properties
# En application.properties
spring.profiles.active=production

# O como parámetro JVM
-Dspring.profiles.active=production
```
