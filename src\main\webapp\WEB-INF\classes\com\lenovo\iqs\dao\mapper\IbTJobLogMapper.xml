<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTJobLogMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTJobLog" >
    <id column="errorlog_id" property="errorlogId" jdbcType="INTEGER" />
    <result column="jobstatus_id" property="jobstatusId" jdbcType="INTEGER" />
    <result column="job_id" property="jobId" jdbcType="VARCHAR" />
    <result column="status_type" property="statusType" jdbcType="VARCHAR" />
    <result column="message_type" property="messageType" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="batch_number" property="batchNumber" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    errorlog_id, jobstatus_id, job_id, status_type, message_type, remark, create_time, 
    batch_number
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_job_log
    where errorlog_id = #{errorlogId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from ib_t_job_log
    where errorlog_id = #{errorlogId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTJobLog" >
    <selectKey resultType="java.lang.Integer" keyProperty="errorlogId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_job_log (jobstatus_id, job_id, status_type, 
      message_type, remark, create_time, 
      batch_number)
    values (#{jobstatusId,jdbcType=INTEGER}, #{jobId,jdbcType=VARCHAR}, #{statusType,jdbcType=VARCHAR}, 
      #{messageType,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{batchNumber,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTJobLog" >
    <selectKey resultType="java.lang.Integer" keyProperty="errorlogId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_job_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="jobstatusId != null" >
        jobstatus_id,
      </if>
      <if test="jobId != null" >
        job_id,
      </if>
      <if test="statusType != null" >
        status_type,
      </if>
      <if test="messageType != null" >
        message_type,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="batchNumber != null" >
        batch_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="jobstatusId != null" >
        #{jobstatusId,jdbcType=INTEGER},
      </if>
      <if test="jobId != null" >
        #{jobId,jdbcType=VARCHAR},
      </if>
      <if test="statusType != null" >
        #{statusType,jdbcType=VARCHAR},
      </if>
      <if test="messageType != null" >
        #{messageType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNumber != null" >
        #{batchNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTJobLog" >
    update ib_t_job_log
    <set >
      <if test="jobstatusId != null" >
        jobstatus_id = #{jobstatusId,jdbcType=INTEGER},
      </if>
      <if test="jobId != null" >
        job_id = #{jobId,jdbcType=VARCHAR},
      </if>
      <if test="statusType != null" >
        status_type = #{statusType,jdbcType=VARCHAR},
      </if>
      <if test="messageType != null" >
        message_type = #{messageType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNumber != null" >
        batch_number = #{batchNumber,jdbcType=VARCHAR},
      </if>
    </set>
    where errorlog_id = #{errorlogId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTJobLog" >
    update ib_t_job_log
    set jobstatus_id = #{jobstatusId,jdbcType=INTEGER},
      job_id = #{jobId,jdbcType=VARCHAR},
      status_type = #{statusType,jdbcType=VARCHAR},
      message_type = #{messageType,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      batch_number = #{batchNumber,jdbcType=VARCHAR}
    where errorlog_id = #{errorlogId,jdbcType=INTEGER}
  </update>
</mapper>