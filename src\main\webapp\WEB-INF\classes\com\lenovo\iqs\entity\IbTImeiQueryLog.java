/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ 
/*    */ @Repository
/*    */ public class IbTImeiQueryLog implements Serializable {
/*    */   private Long queryId;
/*    */   private String iobjectFound;
/*    */   private String userId;
/*    */   private String serialNumber;
/*    */   
/* 10 */   public void setQueryId(Long queryId) { this.queryId = queryId; } private String imeiNumber; private String exist; private String type; private Date searchDatetime; private static final long serialVersionUID = 1L; public void setIobjectFound(String iobjectFound) { this.iobjectFound = iobjectFound; } public void setUserId(String userId) { this.userId = userId; } public void setSerialNumber(String serialNumber) { this.serialNumber = serialNumber; } public void setImeiNumber(String imeiNumber) { this.imeiNumber = imeiNumber; } public void setExist(String exist) { this.exist = exist; } public void setType(String type) { this.type = type; } public void setSearchDatetime(Date searchDatetime) { this.searchDatetime = searchDatetime; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTImeiQueryLog)) return false;  com.lenovo.iqs.entity.IbTImeiQueryLog other = (com.lenovo.iqs.entity.IbTImeiQueryLog)o; if (!other.canEqual(this)) return false;  Object this$queryId = getQueryId(), other$queryId = other.getQueryId(); if ((this$queryId == null) ? (other$queryId != null) : !this$queryId.equals(other$queryId)) return false;  Object this$iobjectFound = getIobjectFound(), other$iobjectFound = other.getIobjectFound(); if ((this$iobjectFound == null) ? (other$iobjectFound != null) : !this$iobjectFound.equals(other$iobjectFound)) return false;  Object this$userId = getUserId(), other$userId = other.getUserId(); if ((this$userId == null) ? (other$userId != null) : !this$userId.equals(other$userId)) return false;  Object this$serialNumber = getSerialNumber(), other$serialNumber = other.getSerialNumber(); if ((this$serialNumber == null) ? (other$serialNumber != null) : !this$serialNumber.equals(other$serialNumber)) return false;  Object this$imeiNumber = getImeiNumber(), other$imeiNumber = other.getImeiNumber(); if ((this$imeiNumber == null) ? (other$imeiNumber != null) : !this$imeiNumber.equals(other$imeiNumber)) return false;  Object this$exist = getExist(), other$exist = other.getExist(); if ((this$exist == null) ? (other$exist != null) : !this$exist.equals(other$exist)) return false;  Object this$type = getType(), other$type = other.getType(); if ((this$type == null) ? (other$type != null) : !this$type.equals(other$type)) return false;  Object this$searchDatetime = getSearchDatetime(), other$searchDatetime = other.getSearchDatetime(); return !((this$searchDatetime == null) ? (other$searchDatetime != null) : !this$searchDatetime.equals(other$searchDatetime)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTImeiQueryLog; } public int hashCode() { int PRIME = 59; result = 1; Object $queryId = getQueryId(); result = result * 59 + (($queryId == null) ? 43 : $queryId.hashCode()); Object $iobjectFound = getIobjectFound(); result = result * 59 + (($iobjectFound == null) ? 43 : $iobjectFound.hashCode()); Object $userId = getUserId(); result = result * 59 + (($userId == null) ? 43 : $userId.hashCode()); Object $serialNumber = getSerialNumber(); result = result * 59 + (($serialNumber == null) ? 43 : $serialNumber.hashCode()); Object $imeiNumber = getImeiNumber(); result = result * 59 + (($imeiNumber == null) ? 43 : $imeiNumber.hashCode()); Object $exist = getExist(); result = result * 59 + (($exist == null) ? 43 : $exist.hashCode()); Object $type = getType(); result = result * 59 + (($type == null) ? 43 : $type.hashCode()); Object $searchDatetime = getSearchDatetime(); return result * 59 + (($searchDatetime == null) ? 43 : $searchDatetime.hashCode()); } public String toString() { return "IbTImeiQueryLog(queryId=" + getQueryId() + ", iobjectFound=" + getIobjectFound() + ", userId=" + getUserId() + ", serialNumber=" + getSerialNumber() + ", imeiNumber=" + getImeiNumber() + ", exist=" + getExist() + ", type=" + getType() + ", searchDatetime=" + getSearchDatetime() + ")"; }
/*    */   
/*    */   public Long getQueryId() {
/* 13 */     return this.queryId;
/*    */   } public String getIobjectFound() {
/* 15 */     return this.iobjectFound;
/*    */   } public String getUserId() {
/* 17 */     return this.userId;
/*    */   } public String getSerialNumber() {
/* 19 */     return this.serialNumber;
/*    */   } public String getImeiNumber() {
/* 21 */     return this.imeiNumber;
/*    */   } public String getExist() {
/* 23 */     return this.exist;
/*    */   } public String getType() {
/* 25 */     return this.type;
/*    */   } public Date getSearchDatetime() {
/* 27 */     return this.searchDatetime;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTImeiQueryLog.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */