/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTJobInfo implements Serializable { private String jobId;
/*    */   private String jobDes;
/*    */   private String emailLoading;
/*    */   private String emailProcessing;
/*    */   
/*  7 */   public void setJobId(String jobId) { this.jobId = jobId; } private String jobType; private String remark; private Date lastChangeTime; private static final long serialVersionUID = 1L; public void setJobDes(String jobDes) { this.jobDes = jobDes; } public void setEmailLoading(String emailLoading) { this.emailLoading = emailLoading; } public void setEmailProcessing(String emailProcessing) { this.emailProcessing = emailProcessing; } public void setJobType(String jobType) { this.jobType = jobType; } public void setRemark(String remark) { this.remark = remark; } public void setLastChangeTime(Date lastChangeTime) { this.lastChangeTime = lastChangeTime; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTJobInfo)) return false;  com.lenovo.iqs.entity.IbTJobInfo other = (com.lenovo.iqs.entity.IbTJobInfo)o; if (!other.canEqual(this)) return false;  Object this$jobId = getJobId(), other$jobId = other.getJobId(); if ((this$jobId == null) ? (other$jobId != null) : !this$jobId.equals(other$jobId)) return false;  Object this$jobDes = getJobDes(), other$jobDes = other.getJobDes(); if ((this$jobDes == null) ? (other$jobDes != null) : !this$jobDes.equals(other$jobDes)) return false;  Object this$emailLoading = getEmailLoading(), other$emailLoading = other.getEmailLoading(); if ((this$emailLoading == null) ? (other$emailLoading != null) : !this$emailLoading.equals(other$emailLoading)) return false;  Object this$emailProcessing = getEmailProcessing(), other$emailProcessing = other.getEmailProcessing(); if ((this$emailProcessing == null) ? (other$emailProcessing != null) : !this$emailProcessing.equals(other$emailProcessing)) return false;  Object this$jobType = getJobType(), other$jobType = other.getJobType(); if ((this$jobType == null) ? (other$jobType != null) : !this$jobType.equals(other$jobType)) return false;  Object this$remark = getRemark(), other$remark = other.getRemark(); if ((this$remark == null) ? (other$remark != null) : !this$remark.equals(other$remark)) return false;  Object this$lastChangeTime = getLastChangeTime(), other$lastChangeTime = other.getLastChangeTime(); return !((this$lastChangeTime == null) ? (other$lastChangeTime != null) : !this$lastChangeTime.equals(other$lastChangeTime)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTJobInfo; } public int hashCode() { int PRIME = 59; result = 1; Object $jobId = getJobId(); result = result * 59 + (($jobId == null) ? 43 : $jobId.hashCode()); Object $jobDes = getJobDes(); result = result * 59 + (($jobDes == null) ? 43 : $jobDes.hashCode()); Object $emailLoading = getEmailLoading(); result = result * 59 + (($emailLoading == null) ? 43 : $emailLoading.hashCode()); Object $emailProcessing = getEmailProcessing(); result = result * 59 + (($emailProcessing == null) ? 43 : $emailProcessing.hashCode()); Object $jobType = getJobType(); result = result * 59 + (($jobType == null) ? 43 : $jobType.hashCode()); Object $remark = getRemark(); result = result * 59 + (($remark == null) ? 43 : $remark.hashCode()); Object $lastChangeTime = getLastChangeTime(); return result * 59 + (($lastChangeTime == null) ? 43 : $lastChangeTime.hashCode()); } public String toString() { return "IbTJobInfo(jobId=" + getJobId() + ", jobDes=" + getJobDes() + ", emailLoading=" + getEmailLoading() + ", emailProcessing=" + getEmailProcessing() + ", jobType=" + getJobType() + ", remark=" + getRemark() + ", lastChangeTime=" + getLastChangeTime() + ")"; }
/*    */    public String getJobId() {
/*  9 */     return this.jobId;
/*    */   } public String getJobDes() {
/* 11 */     return this.jobDes;
/*    */   } public String getEmailLoading() {
/* 13 */     return this.emailLoading;
/*    */   } public String getEmailProcessing() {
/* 15 */     return this.emailProcessing;
/*    */   } public String getJobType() {
/* 17 */     return this.jobType;
/*    */   } public String getRemark() {
/* 19 */     return this.remark;
/*    */   } public Date getLastChangeTime() {
/* 21 */     return this.lastChangeTime;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTJobInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */