package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTObjectBp;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTObjectBpMapper {
  int deleteByPrimaryKey(Integer paramInteger);
  
  int insert(IbTObjectBp paramIbTObjectBp);
  
  IbTObjectBp selectByPrimaryKey(Integer paramInteger);
  
  int updateByPrimaryKey(IbTObjectBp paramIbTObjectBp);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTObjectBpMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */