/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.bean;
/*    */ 
/*    */ public class RSDRules {
/*    */   private boolean isEtokenAllowed;
/*    */   private boolean isPublicIPAllowed;
/*    */   private boolean isPrepaidAllowed;
/*  7 */   private static final Logger log = LogManager.getLogger(com.lenovo.iqs.datablocksign.bean.RSDRules.class); private boolean isReqTypeAllowed; private boolean isUserIdAllowed; private RSDRules() {} public void setEtokenAllowed(boolean isEtokenAllowed) {
/*  8 */     this.isEtokenAllowed = isEtokenAllowed; } public void setPublicIPAllowed(boolean isPublicIPAllowed) { this.isPublicIPAllowed = isPublicIPAllowed; } public void setPrepaidAllowed(boolean isPrepaidAllowed) { this.isPrepaidAllowed = isPrepaidAllowed; } public void setReqTypeAllowed(boolean isReqTypeAllowed) { this.isReqTypeAllowed = isReqTypeAllowed; } public void setUserIdAllowed(boolean isUserIdAllowed) { this.isUserIdAllowed = isUserIdAllowed; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.datablocksign.bean.RSDRules)) return false;  com.lenovo.iqs.datablocksign.bean.RSDRules other = (com.lenovo.iqs.datablocksign.bean.RSDRules)o; return !other.canEqual(this) ? false : ((isEtokenAllowed() != other.isEtokenAllowed()) ? false : ((isPublicIPAllowed() != other.isPublicIPAllowed()) ? false : ((isPrepaidAllowed() != other.isPrepaidAllowed()) ? false : ((isReqTypeAllowed() != other.isReqTypeAllowed()) ? false : (!(isUserIdAllowed() != other.isUserIdAllowed())))))); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.datablocksign.bean.RSDRules; } public int hashCode() { int PRIME = 59; result = 1; result = result * 59 + (isEtokenAllowed() ? 79 : 97); result = result * 59 + (isPublicIPAllowed() ? 79 : 97); result = result * 59 + (isPrepaidAllowed() ? 79 : 97); result = result * 59 + (isReqTypeAllowed() ? 79 : 97); return result * 59 + (isUserIdAllowed() ? 79 : 97); } public String toString() { return "RSDRules(isEtokenAllowed=" + isEtokenAllowed() + ", isPublicIPAllowed=" + isPublicIPAllowed() + ", isPrepaidAllowed=" + isPrepaidAllowed() + ", isReqTypeAllowed=" + isReqTypeAllowed() + ", isUserIdAllowed=" + isUserIdAllowed() + ")"; }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean isEtokenAllowed() {
/* 17 */     return this.isEtokenAllowed;
/* 18 */   } public boolean isPublicIPAllowed() { return this.isPublicIPAllowed; }
/* 19 */   public boolean isPrepaidAllowed() { return this.isPrepaidAllowed; }
/* 20 */   public boolean isReqTypeAllowed() { return this.isReqTypeAllowed; } public boolean isUserIdAllowed() {
/* 21 */     return this.isUserIdAllowed;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public RSDRules(List<Config> configs, RSDValidationResponse response) {
/* 51 */     log.info("entered RSDRules");
/* 52 */     for (Config c : configs) {
/* 53 */       if (c.getKey().equalsIgnoreCase("etoken")) {
/* 54 */         boolean DBFlag = c.getValue().equalsIgnoreCase("accept");
/* 55 */         boolean rsdFlag = response.getEtokenstatus().equalsIgnoreCase("1262");
/* 56 */         this.isEtokenAllowed = rsdFlag;
/*    */       } 
/* 58 */       if (c.getKey().equalsIgnoreCase("publicip")) {
/* 59 */         boolean DBFlag = c.getValue().equalsIgnoreCase("accept");
/* 60 */         boolean rsdFlag = response.getPublicipstatus().equalsIgnoreCase("1282");
/* 61 */         this.isPublicIPAllowed = (rsdFlag || DBFlag == rsdFlag);
/*    */       } 
/* 63 */       if (c.getKey().equalsIgnoreCase("reqtype")) {
/* 64 */         boolean DBFlag = c.getValue().equalsIgnoreCase("accept");
/* 65 */         boolean rsdFlag = response.getRequesttypeauthorized().equalsIgnoreCase("1292");
/* 66 */         this.isReqTypeAllowed = rsdFlag;
/*    */       } 
/*    */       
/* 69 */       if (c.getKey().equalsIgnoreCase("username")) {
/* 70 */         boolean DBFlag = c.getValue().equalsIgnoreCase("accept");
/* 71 */         boolean rsdFlag = response.getUserauthorized().equalsIgnoreCase("1272");
/* 72 */         this.isUserIdAllowed = rsdFlag;
/*    */       } 
/*    */       
/* 75 */       if (c.getKey().equalsIgnoreCase("prepaid")) {
/* 76 */         boolean rsdFlag = response.getPrepaidusage().equalsIgnoreCase("1251");
/* 77 */         this.isPrepaidAllowed = rsdFlag;
/*    */       } 
/* 79 */       log.info("completed RSDRules");
/*    */     } 
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\bean\RSDRules.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */