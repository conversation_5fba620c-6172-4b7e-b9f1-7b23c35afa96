/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.bean;
/*    */ 
/*    */ public class LogBean {
/*    */   public void setIstrSerialNo(String istrSerialNo) {
/*  5 */     this.istrSerialNo = istrSerialNo; } public void setIstrClientIpAdd(String istrClientIpAdd) { this.istrClientIpAdd = istrClientIpAdd; } public void setIstrProcessName(String istrProcessName) { this.istrProcessName = istrProcessName; } public void setIstrMessName(String istrMessName) { this.istrMessName = istrMessName; } public void setIstrServiceName(String istrServiceName) { this.istrServiceName = istrServiceName; } public void setIstrResCode(String istrResCode) { this.istrResCode = istrResCode; } public void setIstrResMess(String istrResMess) { this.istrResMess = istrResMess; } public void setIstrMascId(String istrMascId) { this.istrMascId = istrMascId; } public void setIstrTxnID(String istrTxnID) { this.istrTxnID = istrTxnID; } public void setIstrDirectShipCountryCode(String istrDirectShipCountryCode) { this.istrDirectShipCountryCode = istrDirectShipCountryCode; } public void setIstrStatusCode(String istrStatusCode) { this.istrStatusCode = istrStatusCode; } public void setIstrExternalMarketingName(String istrExternalMarketingName) { this.istrExternalMarketingName = istrExternalMarketingName; } public void setIstrCarrierModelInformation(String istrCarrierModelInformation) { this.istrCarrierModelInformation = istrCarrierModelInformation; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.datablocksign.bean.LogBean)) return false;  com.lenovo.iqs.datablocksign.bean.LogBean other = (com.lenovo.iqs.datablocksign.bean.LogBean)o; if (!other.canEqual(this)) return false;  Object this$istrSerialNo = getIstrSerialNo(), other$istrSerialNo = other.getIstrSerialNo(); if ((this$istrSerialNo == null) ? (other$istrSerialNo != null) : !this$istrSerialNo.equals(other$istrSerialNo)) return false;  Object this$istrClientIpAdd = getIstrClientIpAdd(), other$istrClientIpAdd = other.getIstrClientIpAdd(); if ((this$istrClientIpAdd == null) ? (other$istrClientIpAdd != null) : !this$istrClientIpAdd.equals(other$istrClientIpAdd)) return false;  Object this$istrProcessName = getIstrProcessName(), other$istrProcessName = other.getIstrProcessName(); if ((this$istrProcessName == null) ? (other$istrProcessName != null) : !this$istrProcessName.equals(other$istrProcessName)) return false;  Object this$istrMessName = getIstrMessName(), other$istrMessName = other.getIstrMessName(); if ((this$istrMessName == null) ? (other$istrMessName != null) : !this$istrMessName.equals(other$istrMessName)) return false;  Object this$istrServiceName = getIstrServiceName(), other$istrServiceName = other.getIstrServiceName(); if ((this$istrServiceName == null) ? (other$istrServiceName != null) : !this$istrServiceName.equals(other$istrServiceName)) return false;  Object this$istrResCode = getIstrResCode(), other$istrResCode = other.getIstrResCode(); if ((this$istrResCode == null) ? (other$istrResCode != null) : !this$istrResCode.equals(other$istrResCode)) return false;  Object this$istrResMess = getIstrResMess(), other$istrResMess = other.getIstrResMess(); if ((this$istrResMess == null) ? (other$istrResMess != null) : !this$istrResMess.equals(other$istrResMess)) return false;  Object this$istrMascId = getIstrMascId(), other$istrMascId = other.getIstrMascId(); if ((this$istrMascId == null) ? (other$istrMascId != null) : !this$istrMascId.equals(other$istrMascId)) return false;  Object this$istrTxnID = getIstrTxnID(), other$istrTxnID = other.getIstrTxnID(); if ((this$istrTxnID == null) ? (other$istrTxnID != null) : !this$istrTxnID.equals(other$istrTxnID)) return false;  Object this$istrDirectShipCountryCode = getIstrDirectShipCountryCode(), other$istrDirectShipCountryCode = other.getIstrDirectShipCountryCode(); if ((this$istrDirectShipCountryCode == null) ? (other$istrDirectShipCountryCode != null) : !this$istrDirectShipCountryCode.equals(other$istrDirectShipCountryCode)) return false;  Object this$istrStatusCode = getIstrStatusCode(), other$istrStatusCode = other.getIstrStatusCode(); if ((this$istrStatusCode == null) ? (other$istrStatusCode != null) : !this$istrStatusCode.equals(other$istrStatusCode)) return false;  Object this$istrExternalMarketingName = getIstrExternalMarketingName(), other$istrExternalMarketingName = other.getIstrExternalMarketingName(); if ((this$istrExternalMarketingName == null) ? (other$istrExternalMarketingName != null) : !this$istrExternalMarketingName.equals(other$istrExternalMarketingName)) return false;  Object this$istrCarrierModelInformation = getIstrCarrierModelInformation(), other$istrCarrierModelInformation = other.getIstrCarrierModelInformation(); return !((this$istrCarrierModelInformation == null) ? (other$istrCarrierModelInformation != null) : !this$istrCarrierModelInformation.equals(other$istrCarrierModelInformation)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.datablocksign.bean.LogBean; } public int hashCode() { int PRIME = 59; result = 1; Object $istrSerialNo = getIstrSerialNo(); result = result * 59 + (($istrSerialNo == null) ? 43 : $istrSerialNo.hashCode()); Object $istrClientIpAdd = getIstrClientIpAdd(); result = result * 59 + (($istrClientIpAdd == null) ? 43 : $istrClientIpAdd.hashCode()); Object $istrProcessName = getIstrProcessName(); result = result * 59 + (($istrProcessName == null) ? 43 : $istrProcessName.hashCode()); Object $istrMessName = getIstrMessName(); result = result * 59 + (($istrMessName == null) ? 43 : $istrMessName.hashCode()); Object $istrServiceName = getIstrServiceName(); result = result * 59 + (($istrServiceName == null) ? 43 : $istrServiceName.hashCode()); Object $istrResCode = getIstrResCode(); result = result * 59 + (($istrResCode == null) ? 43 : $istrResCode.hashCode()); Object $istrResMess = getIstrResMess(); result = result * 59 + (($istrResMess == null) ? 43 : $istrResMess.hashCode()); Object $istrMascId = getIstrMascId(); result = result * 59 + (($istrMascId == null) ? 43 : $istrMascId.hashCode()); Object $istrTxnID = getIstrTxnID(); result = result * 59 + (($istrTxnID == null) ? 43 : $istrTxnID.hashCode()); Object $istrDirectShipCountryCode = getIstrDirectShipCountryCode(); result = result * 59 + (($istrDirectShipCountryCode == null) ? 43 : $istrDirectShipCountryCode.hashCode()); Object $istrStatusCode = getIstrStatusCode(); result = result * 59 + (($istrStatusCode == null) ? 43 : $istrStatusCode.hashCode()); Object $istrExternalMarketingName = getIstrExternalMarketingName(); result = result * 59 + (($istrExternalMarketingName == null) ? 43 : $istrExternalMarketingName.hashCode()); Object $istrCarrierModelInformation = getIstrCarrierModelInformation(); return result * 59 + (($istrCarrierModelInformation == null) ? 43 : $istrCarrierModelInformation.hashCode()); } public String toString() { return "LogBean(istrSerialNo=" + getIstrSerialNo() + ", istrClientIpAdd=" + getIstrClientIpAdd() + ", istrProcessName=" + getIstrProcessName() + ", istrMessName=" + getIstrMessName() + ", istrServiceName=" + getIstrServiceName() + ", istrResCode=" + getIstrResCode() + ", istrResMess=" + getIstrResMess() + ", istrMascId=" + getIstrMascId() + ", istrTxnID=" + getIstrTxnID() + ", istrDirectShipCountryCode=" + getIstrDirectShipCountryCode() + ", istrStatusCode=" + getIstrStatusCode() + ", istrExternalMarketingName=" + getIstrExternalMarketingName() + ", istrCarrierModelInformation=" + getIstrCarrierModelInformation() + ")"; }
/*    */ 
/*    */   
/*  8 */   private String istrSerialNo = null; public String getIstrSerialNo() { return this.istrSerialNo; }
/*    */   
/* 10 */   private String istrClientIpAdd = null; public String getIstrClientIpAdd() { return this.istrClientIpAdd; }
/*    */   
/* 12 */   private String istrProcessName = null; public String getIstrProcessName() { return this.istrProcessName; }
/*    */   
/* 14 */   private String istrMessName = null; public String getIstrMessName() { return this.istrMessName; }
/*    */   
/* 16 */   private String istrServiceName = null; public String getIstrServiceName() { return this.istrServiceName; }
/*    */   
/* 18 */   private String istrResCode = null; public String getIstrResCode() { return this.istrResCode; }
/*    */   
/* 20 */   private String istrResMess = null; public String getIstrResMess() { return this.istrResMess; }
/*    */   
/* 22 */   private String istrMascId = null; public String getIstrMascId() { return this.istrMascId; }
/*    */   
/* 24 */   private String istrTxnID = null; public String getIstrTxnID() { return this.istrTxnID; }
/*    */   
/* 26 */   private String istrDirectShipCountryCode = null; public String getIstrDirectShipCountryCode() { return this.istrDirectShipCountryCode; }
/*    */   
/* 28 */   private String istrStatusCode = null; public String getIstrStatusCode() { return this.istrStatusCode; }
/*    */   
/* 30 */   private String istrExternalMarketingName = null; public String getIstrExternalMarketingName() { return this.istrExternalMarketingName; }
/*    */   
/* 32 */   private String istrCarrierModelInformation = null; public String getIstrCarrierModelInformation() { return this.istrCarrierModelInformation; }
/*    */ 
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\bean\LogBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */