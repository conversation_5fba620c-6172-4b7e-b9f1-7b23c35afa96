package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTZuserInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTZuserInfoMapper {
  int deleteByPrimaryKey(String paramString);
  
  int insert(IbTZuserInfo paramIbTZuserInfo);
  
  IbTZuserInfo selectByPrimaryKey(String paramString);
  
  int updateByPrimaryKey(IbTZuserInfo paramIbTZuserInfo);
  
  IbTZuserInfo selectByUP(@Param("username") String paramString1, @Param("password") String paramString2);
  
  IbTZuserInfo selectMatchCaseByUP(@Param("username") String paramString1, @Param("password") String paramString2);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTZuserInfoMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */