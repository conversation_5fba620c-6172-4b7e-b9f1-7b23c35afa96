/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTZuserFailLog implements Serializable {
/*    */   private Integer autoId;
/*    */   private String ip;
/*    */   private String userid;
/*    */   
/*  7 */   public void setAutoId(Integer autoId) { this.autoId = autoId; } private String pw; private Date accessTime; private String logtype; private static final long serialVersionUID = 1L; public void setIp(String ip) { this.ip = ip; } public void setUserid(String userid) { this.userid = userid; } public void setPw(String pw) { this.pw = pw; } public void setAccessTime(Date accessTime) { this.accessTime = accessTime; } public void setLogtype(String logtype) { this.logtype = logtype; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTZuserFailLog)) return false;  com.lenovo.iqs.entity.IbTZuserFailLog other = (com.lenovo.iqs.entity.IbTZuserFailLog)o; if (!other.canEqual(this)) return false;  Object this$autoId = getAutoId(), other$autoId = other.getAutoId(); if ((this$autoId == null) ? (other$autoId != null) : !this$autoId.equals(other$autoId)) return false;  Object this$ip = getIp(), other$ip = other.getIp(); if ((this$ip == null) ? (other$ip != null) : !this$ip.equals(other$ip)) return false;  Object this$userid = getUserid(), other$userid = other.getUserid(); if ((this$userid == null) ? (other$userid != null) : !this$userid.equals(other$userid)) return false;  Object this$pw = getPw(), other$pw = other.getPw(); if ((this$pw == null) ? (other$pw != null) : !this$pw.equals(other$pw)) return false;  Object this$accessTime = getAccessTime(), other$accessTime = other.getAccessTime(); if ((this$accessTime == null) ? (other$accessTime != null) : !this$accessTime.equals(other$accessTime)) return false;  Object this$logtype = getLogtype(), other$logtype = other.getLogtype(); return !((this$logtype == null) ? (other$logtype != null) : !this$logtype.equals(other$logtype)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTZuserFailLog; } public int hashCode() { int PRIME = 59; result = 1; Object $autoId = getAutoId(); result = result * 59 + (($autoId == null) ? 43 : $autoId.hashCode()); Object $ip = getIp(); result = result * 59 + (($ip == null) ? 43 : $ip.hashCode()); Object $userid = getUserid(); result = result * 59 + (($userid == null) ? 43 : $userid.hashCode()); Object $pw = getPw(); result = result * 59 + (($pw == null) ? 43 : $pw.hashCode()); Object $accessTime = getAccessTime(); result = result * 59 + (($accessTime == null) ? 43 : $accessTime.hashCode()); Object $logtype = getLogtype(); return result * 59 + (($logtype == null) ? 43 : $logtype.hashCode()); } public String toString() { return "IbTZuserFailLog(autoId=" + getAutoId() + ", ip=" + getIp() + ", userid=" + getUserid() + ", pw=" + getPw() + ", accessTime=" + getAccessTime() + ", logtype=" + getLogtype() + ")"; }
/*    */    public Integer getAutoId() {
/*  9 */     return this.autoId;
/*    */   } public String getIp() {
/* 11 */     return this.ip;
/*    */   } public String getUserid() {
/* 13 */     return this.userid;
/*    */   } public String getPw() {
/* 15 */     return this.pw;
/*    */   } public Date getAccessTime() {
/* 17 */     return this.accessTime;
/*    */   } public String getLogtype() {
/* 19 */     return this.logtype;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTZuserFailLog.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */