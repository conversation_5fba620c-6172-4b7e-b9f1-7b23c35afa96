/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.bean;
/*    */ 
/*    */ 
/*    */ public class RSDValidationResponse {
/*    */   @JsonProperty("etokenstatus")
/*    */   private String etokenstatus;
/*    */   @JsonProperty("userauthorized")
/*    */   private String userauthorized;
/*    */   
/* 10 */   public void setEtokenstatus(String etokenstatus) { this.etokenstatus = etokenstatus; } @JsonProperty("publicipstatus") private String publicipstatus; @JsonProperty("prepaidusage") private String prepaidusage; @JsonProperty("requesttypeauthorized") private String requesttypeauthorized; public void setUserauthorized(String userauthorized) { this.userauthorized = userauthorized; } public void setPublicipstatus(String publicipstatus) { this.publicipstatus = publicipstatus; } public void setPrepaidusage(String prepaidusage) { this.prepaidusage = prepaidusage; } public void setRequesttypeauthorized(String requesttypeauthorized) { this.requesttypeauthorized = requesttypeauthorized; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.datablocksign.bean.RSDValidationResponse)) return false;  com.lenovo.iqs.datablocksign.bean.RSDValidationResponse other = (com.lenovo.iqs.datablocksign.bean.RSDValidationResponse)o; if (!other.canEqual(this)) return false;  Object this$etokenstatus = getEtokenstatus(), other$etokenstatus = other.getEtokenstatus(); if ((this$etokenstatus == null) ? (other$etokenstatus != null) : !this$etokenstatus.equals(other$etokenstatus)) return false;  Object this$userauthorized = getUserauthorized(), other$userauthorized = other.getUserauthorized(); if ((this$userauthorized == null) ? (other$userauthorized != null) : !this$userauthorized.equals(other$userauthorized)) return false;  Object this$publicipstatus = getPublicipstatus(), other$publicipstatus = other.getPublicipstatus(); if ((this$publicipstatus == null) ? (other$publicipstatus != null) : !this$publicipstatus.equals(other$publicipstatus)) return false;  Object this$prepaidusage = getPrepaidusage(), other$prepaidusage = other.getPrepaidusage(); if ((this$prepaidusage == null) ? (other$prepaidusage != null) : !this$prepaidusage.equals(other$prepaidusage)) return false;  Object this$requesttypeauthorized = getRequesttypeauthorized(), other$requesttypeauthorized = other.getRequesttypeauthorized(); return !((this$requesttypeauthorized == null) ? (other$requesttypeauthorized != null) : !this$requesttypeauthorized.equals(other$requesttypeauthorized)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.datablocksign.bean.RSDValidationResponse; } public int hashCode() { int PRIME = 59; result = 1; Object $etokenstatus = getEtokenstatus(); result = result * 59 + (($etokenstatus == null) ? 43 : $etokenstatus.hashCode()); Object $userauthorized = getUserauthorized(); result = result * 59 + (($userauthorized == null) ? 43 : $userauthorized.hashCode()); Object $publicipstatus = getPublicipstatus(); result = result * 59 + (($publicipstatus == null) ? 43 : $publicipstatus.hashCode()); Object $prepaidusage = getPrepaidusage(); result = result * 59 + (($prepaidusage == null) ? 43 : $prepaidusage.hashCode()); Object $requesttypeauthorized = getRequesttypeauthorized(); return result * 59 + (($requesttypeauthorized == null) ? 43 : $requesttypeauthorized.hashCode()); } public String toString() { return "RSDValidationResponse(etokenstatus=" + getEtokenstatus() + ", userauthorized=" + getUserauthorized() + ", publicipstatus=" + getPublicipstatus() + ", prepaidusage=" + getPrepaidusage() + ", requesttypeauthorized=" + getRequesttypeauthorized() + ")"; }
/*    */   
/*    */   public String getEtokenstatus() {
/* 13 */     return this.etokenstatus;
/*    */   } public String getUserauthorized() {
/* 15 */     return this.userauthorized;
/*    */   } public String getPublicipstatus() {
/* 17 */     return this.publicipstatus;
/*    */   } public String getPrepaidusage() {
/* 19 */     return this.prepaidusage;
/*    */   } public String getRequesttypeauthorized() {
/* 21 */     return this.requesttypeauthorized;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\bean\RSDValidationResponse.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */