-- Script SQL para crear todas las tablas necesarias para IQS
-- Basado en análisis de mappers XML
-- Fecha: 2025-01-07

USE iqs_local;

-- =====================================================
-- TABLAS PRINCIPALES IDENTIFICADAS EN LOS MAPPERS
-- =====================================================

-- 1. Tabla para DatablockSignMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_white_mascs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    MASC_ID VARCHAR(50),
    ACTIVE_LOCK_CODE VARCHAR(255),
    NWSCP_LOCK_CODE VARCHAR(255),
    SSCP_LOCK_CODE VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_masc_id (MASC_ID)
);

CREATE TABLE IF NOT EXISTS ib_t_white_imei (
    id INT AUTO_INCREMENT PRIMARY KEY,
    SERIAL_NO VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_serial_no (SERIAL_NO)
);

-- 2. Tabla para IbTAccessLogMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_access_log (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    access_time TIMESTAMP,
    keyword VARCHAR(255),
    access_parameter TEXT,
    class_name VARCHAR(255),
    result TEXT
);

-- 3. Tabla para IbTZuserInfoMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_zuser_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    USER_ID VARCHAR(50),
    PASSWORD VARCHAR(255),
    LAST_CHANGE_TIME TIMESTAMP,
    LAST_CHANGE_BY VARCHAR(50),
    ISVALID VARCHAR(1) DEFAULT 'Y',
    USER_TYPE VARCHAR(20),
    SYSTEM VARCHAR(50),
    INDEX idx_user_id (USER_ID),
    UNIQUE KEY unique_user_id (USER_ID)
);

-- 4. Tabla para IbTZuserFailLogMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_zuser_fail_log (
    auto_id INT AUTO_INCREMENT PRIMARY KEY,
    ip VARCHAR(45),
    userid VARCHAR(50),
    pw VARCHAR(255),
    access_time TIMESTAMP,
    logtype VARCHAR(20)
);

-- 5. Tabla para IbTZuserLockMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_zuser_lock (
    auto_id INT AUTO_INCREMENT PRIMARY KEY,
    ip VARCHAR(45),
    userid VARCHAR(50),
    lock_time TIMESTAMP,
    unlock_time TIMESTAMP
);

-- 6. Tabla para IbTUpdConfigMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    SECTION VARCHAR(100),
    `key` VARCHAR(100),
    `value` TEXT,
    value1 TEXT,
    date_ended TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_section_key (SECTION, `key`),
    UNIQUE KEY unique_section_key (SECTION, `key`)
);

-- 7. Tabla para IbTImeiSnMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_imei_sn (
    id INT AUTO_INCREMENT PRIMARY KEY,
    imei_code VARCHAR(20),
    serial_number VARCHAR(50),
    product_id VARCHAR(50),
    imei_send_datetime TIMESTAMP,
    partition_char VARCHAR(10),
    last_change_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 8. Tabla para IbTImeiSnMbMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_imei_sn_mb (
    id INT AUTO_INCREMENT PRIMARY KEY,
    imei_code VARCHAR(20),
    serial_number VARCHAR(50),
    parts_type VARCHAR(50),
    imei_send_datetime TIMESTAMP,
    used_flag VARCHAR(1) DEFAULT 'N',
    last_change_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 9. Tabla para IbTImeiSnRwMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_imei_sn_rw (
    id INT AUTO_INCREMENT PRIMARY KEY,
    imei_code VARCHAR(20),
    serial_number VARCHAR(50),
    model VARCHAR(50),
    used_flag VARCHAR(1) DEFAULT 'N',
    last_change TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 10. Tabla para IbTJobInfoMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_job_info (
    job_id VARCHAR(50) PRIMARY KEY,
    job_des TEXT,
    email_loading VARCHAR(1) DEFAULT 'N',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 11. Tabla para IbTMaterialMasterMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_material_master (
    material_id VARCHAR(50) PRIMARY KEY,
    machine_type VARCHAR(50),
    material_type VARCHAR(50),
    last_change_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 12. Tabla para IbTMaterialDescriptionMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_material_description (
    id INT AUTO_INCREMENT PRIMARY KEY,
    material_id VARCHAR(50),
    language VARCHAR(10),
    description TEXT,
    last_change_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 13. Tabla para IbTBpMasterMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_bp_master (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bp_id VARCHAR(50),
    company_code VARCHAR(10),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    street VARCHAR(255),
    city VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 14. Tabla para IbTUpdModelMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_model (
    NAME VARCHAR(100),
    COUNTRY_CODE VARCHAR(10),
    DESCRIPTION TEXT,
    MARKDEL INT DEFAULT 0,
    PRIMARY KEY (NAME, COUNTRY_CODE)
);

-- 15. Tabla para IbTUpdCountryMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_country (
    COUNTRY_CODE VARCHAR(10) PRIMARY KEY,
    DESCRIPTION VARCHAR(255)
);

-- =====================================================
-- DATOS INICIALES PARA TESTING
-- =====================================================

-- Insertar usuario de prueba
INSERT IGNORE INTO ib_t_zuser_info (USER_ID, PASSWORD, ISVALID, USER_TYPE, SYSTEM) VALUES
('admin', 'admin123', 'Y', 'ADMIN', 'IQS'),
('test_user', 'test123', 'Y', 'USER', 'IQS');

-- Insertar configuraciones básicas
INSERT IGNORE INTO ib_t_upd_config (SECTION, `key`, `value`) VALUES
('DATABLOCK_SIGN', 'PKI_SERVER_IP', '127.0.0.1'),
('DATABLOCK_SIGN', 'PKI_SERVER_PORT', '8443'),
('RSUSERVICE', 'MNO', 'TMO'),
('RSUMODEL_MAP', 'MOTO_G', 'MOTO_G_MAPPED');

-- Insertar datos de prueba para white lists
INSERT IGNORE INTO ib_t_white_mascs (MASC_ID, ACTIVE_LOCK_CODE, NWSCP_LOCK_CODE, SSCP_LOCK_CODE) VALUES
('TEST_MASC_001', 'ACTIVE123', 'NWSCP123', 'SSCP123'),
('TEST_MASC_002', 'ACTIVE456', 'NWSCP456', 'SSCP456');

INSERT IGNORE INTO ib_t_white_imei (SERIAL_NO) VALUES
('123456789012345'),
('123456789012346'),
('123456789012347');

-- Insertar países de prueba
INSERT IGNORE INTO ib_t_upd_country (COUNTRY_CODE, DESCRIPTION) VALUES
('US', 'United States'),
('MX', 'Mexico'),
('BR', 'Brazil'),
('CA', 'Canada');

-- Insertar modelos de prueba
INSERT IGNORE INTO ib_t_upd_model (NAME, COUNTRY_CODE, DESCRIPTION) VALUES
('MOTO_G', 'US', 'Motorola G Series'),
('MOTO_E', 'US', 'Motorola E Series'),
('MOTO_X', 'US', 'Motorola X Series');

-- 16. Más tablas identificadas en otros mappers

-- Tabla para IbTImeiSnSwapMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_imei_sn_swap (
    id INT AUTO_INCREMENT PRIMARY KEY,
    old_imei VARCHAR(20),
    new_imei VARCHAR(20),
    serial_number VARCHAR(50),
    swap_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'ACTIVE'
);

-- Tabla para IbTJobLogMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_job_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    job_id VARCHAR(50),
    log_message TEXT,
    log_level VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTJobStatusMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_job_status (
    job_id VARCHAR(50) PRIMARY KEY,
    status VARCHAR(20),
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTImeiQueryLogMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_imei_query_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    imei VARCHAR(20),
    query_type VARCHAR(50),
    query_result TEXT,
    query_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    client_ip VARCHAR(45)
);

-- Tabla para IbTObjectBpMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_object_bp (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(50),
    bp_id VARCHAR(50),
    relationship_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTUpdCartonMrMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_carton_mr (
    id INT AUTO_INCREMENT PRIMARY KEY,
    carton_id VARCHAR(50),
    mr_number VARCHAR(50),
    status VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTUpdCountryCodeMappingMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_country_code_mapping (
    auto_id INT AUTO_INCREMENT PRIMARY KEY,
    country_code CHAR(10),
    iso_code CHAR(10),
    description CHAR(255),
    last_mod_date TIMESTAMP,
    last_mod_by CHAR(50),
    creation_datetime TIMESTAMP,
    created_by CHAR(50)
);

-- Tabla para IbTUpdCountryWarrantyExceptionMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_country_warranty_exception (
    id INT AUTO_INCREMENT PRIMARY KEY,
    country_code VARCHAR(10),
    exception_type VARCHAR(50),
    exception_value VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTUpdCountryWarrantyMappingMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_country_warranty_mapping (
    id INT AUTO_INCREMENT PRIMARY KEY,
    country_code VARCHAR(10),
    warranty_type VARCHAR(50),
    warranty_period INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTUpdIdenAttributesMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_iden_attributes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    attribute_name VARCHAR(100),
    attribute_value VARCHAR(255),
    attribute_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTUpdMomdelTacMatchMapper.xml (nota: parece ser un typo de "Model")
CREATE TABLE IF NOT EXISTS ib_t_upd_model_tac_match (
    id INT AUTO_INCREMENT PRIMARY KEY,
    model_name VARCHAR(100),
    tac_code VARCHAR(20),
    match_status VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTUpdPartiesMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_parties (
    id INT AUTO_INCREMENT PRIMARY KEY,
    party_id VARCHAR(50),
    party_name VARCHAR(255),
    party_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTUpdR12InboundMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_r12_inbound (
    id INT AUTO_INCREMENT PRIMARY KEY,
    serial_no VARCHAR(50),
    batch_number VARCHAR(50),
    status VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTUpdR12InboundHistoryMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_r12_inbound_history (
    auto_id INT AUTO_INCREMENT PRIMARY KEY,
    serial_no VARCHAR(50),
    create_time TIMESTAMP,
    batch_number VARCHAR(50)
);

-- Tabla para IbTUpdR12OutboundMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_r12_outbound (
    id INT AUTO_INCREMENT PRIMARY KEY,
    serial_no VARCHAR(50),
    batch_number VARCHAR(50),
    status VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTUpdR12OutboundHistoryMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_r12_outbound_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    serial_no VARCHAR(50),
    batch_number VARCHAR(50),
    create_time TIMESTAMP
);

-- Tabla para IbTUpdRoamAddressMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_roam_address (
    id INT AUTO_INCREMENT PRIMARY KEY,
    address_type VARCHAR(50),
    address_value VARCHAR(255),
    country_code VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTUpdServicelinkInboundMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_servicelink_inbound (
    id INT AUTO_INCREMENT PRIMARY KEY,
    serial_no VARCHAR(50),
    service_type VARCHAR(50),
    status VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTUpdServicelinkInboundHistoryMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_servicelink_inbound_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    serial_no VARCHAR(50),
    service_type VARCHAR(50),
    create_time TIMESTAMP
);

-- Tabla para IbTUpdServicelinkOutboundMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_servicelink_outbound (
    id INT AUTO_INCREMENT PRIMARY KEY,
    serial_no VARCHAR(50),
    service_type VARCHAR(50),
    status VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTUpdServicelinkOutboundHistoryMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_servicelink_outbound_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    serial_no VARCHAR(50),
    service_type VARCHAR(50),
    create_time TIMESTAMP
);

-- Tabla para IbTUpdStatusCodeRefMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_status_code_ref (
    status_code VARCHAR(20) PRIMARY KEY,
    description VARCHAR(255),
    category VARCHAR(50)
);

-- Tabla para IbTUpdStatusMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_status (
    id INT AUTO_INCREMENT PRIMARY KEY,
    status_code VARCHAR(20),
    status_description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTUpdSwwarrPeriodRefMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_swwarr_period_ref (
    APC_CODE VARCHAR(50),
    COUNTRY_CODE VARCHAR(10),
    SW_UPG_EXP_PERIOD INT,
    CREATED_BY VARCHAR(50),
    CREATION_DATETIME DATE,
    LAST_MOD_BY VARCHAR(50),
    PRIMARY KEY (APC_CODE, COUNTRY_CODE)
);

-- Tabla para IbTUpdWarrantyCancelCodeRefMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_warranty_cancel_code_ref (
    cancel_code VARCHAR(20) PRIMARY KEY,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTUpdWarrantyCodeRefMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_upd_warranty_code_ref (
    warranty_code VARCHAR(20) PRIMARY KEY,
    description VARCHAR(255),
    warranty_period INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para IbTWtyReportOutboundMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_wty_report_outbound (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id VARCHAR(50),
    ship_to_country VARCHAR(10),
    hierarchy VARCHAR(100),
    quantity INT,
    wty_start_date DATE,
    wty_dura VARCHAR(50),
    create_time TIMESTAMP,
    batch_number VARCHAR(50)
);

-- Tabla para IbTWtyReportOutboundErrorMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_wty_report_outbound_error (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id VARCHAR(50),
    ship_to_country VARCHAR(10),
    hierarchy VARCHAR(100),
    quantity INT,
    wty_start_date DATE,
    wty_dura VARCHAR(50),
    create_time TIMESTAMP,
    batch_number VARCHAR(50),
    error_message TEXT
);

-- Tabla para IbTZibpActivationInfoMapper.xml
CREATE TABLE IF NOT EXISTS ib_t_zibp_activation_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client INT,
    serial_no1 VARCHAR(50),
    serial_no2 VARCHAR(50),
    track_id VARCHAR(50),
    msn VARCHAR(50),
    country_iso_code VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMIT;

-- =====================================================
-- VERIFICACIÓN Y RESUMEN
-- =====================================================
SELECT 'Tablas IQS creadas exitosamente' as status;

-- Mostrar todas las tablas creadas
SHOW TABLES LIKE 'ib_t_%';

-- Contar total de tablas
SELECT COUNT(*) as total_tables FROM information_schema.tables
WHERE table_schema = 'iqs_local' AND table_name LIKE 'ib_t_%';

-- Verificar algunas tablas clave
SELECT 'Verificando tablas principales...' as info;
SELECT COUNT(*) as white_mascs_count FROM ib_t_white_mascs;
SELECT COUNT(*) as white_imei_count FROM ib_t_white_imei;
SELECT COUNT(*) as zuser_info_count FROM ib_t_zuser_info;
SELECT COUNT(*) as upd_config_count FROM ib_t_upd_config;
