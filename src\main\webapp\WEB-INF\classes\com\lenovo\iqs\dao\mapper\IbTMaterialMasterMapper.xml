<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenovo.iqs.dao.IbTMaterialMasterMapper">
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTMaterialMaster">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="material_id" jdbcType="VARCHAR" property="materialId" />
    <result column="machine_type" jdbcType="VARCHAR" property="machineType" />
    <result column="material_type" jdbcType="VARCHAR" property="materialType" />
    <result column="hierarchy" jdbcType="VARCHAR" property="hierarchy" />
    <result column="last_change_time" jdbcType="TIMESTAMP" property="lastChangeTime" />
    <result column="preserve1" jdbcType="VARCHAR" property="preserve1" />
    <result column="preserve2" jdbcType="VARCHAR" property="preserve2" />
    <result column="preserve3" jdbcType="VARCHAR" property="preserve3" />
  </resultMap>
  <sql id="Base_Column_List">
    id, material_id, machine_type, material_type, hierarchy, last_change_time, preserve1, 
    preserve2, preserve3
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ib_t_material_master
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByMaterialId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ib_t_material_master
    where material_id = #{materialId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from ib_t_material_master
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTMaterialMaster">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_material_master (material_id, machine_type, material_type, 
      hierarchy, last_change_time, preserve1, 
      preserve2, preserve3)
    values (#{materialId,jdbcType=VARCHAR}, #{machineType,jdbcType=VARCHAR}, #{materialType,jdbcType=VARCHAR}, 
      #{hierarchy,jdbcType=VARCHAR}, #{lastChangeTime,jdbcType=TIMESTAMP}, #{preserve1,jdbcType=VARCHAR}, 
      #{preserve2,jdbcType=VARCHAR}, #{preserve3,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTMaterialMaster">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_material_master
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="materialId != null">
        material_id,
      </if>
      <if test="machineType != null">
        machine_type,
      </if>
      <if test="materialType != null">
        material_type,
      </if>
      <if test="hierarchy != null">
        hierarchy,
      </if>
      <if test="lastChangeTime != null">
        last_change_time,
      </if>
      <if test="preserve1 != null">
        preserve1,
      </if>
      <if test="preserve2 != null">
        preserve2,
      </if>
      <if test="preserve3 != null">
        preserve3,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="materialId != null">
        #{materialId,jdbcType=VARCHAR},
      </if>
      <if test="machineType != null">
        #{machineType,jdbcType=VARCHAR},
      </if>
      <if test="materialType != null">
        #{materialType,jdbcType=VARCHAR},
      </if>
      <if test="hierarchy != null">
        #{hierarchy,jdbcType=VARCHAR},
      </if>
      <if test="lastChangeTime != null">
        #{lastChangeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preserve1 != null">
        #{preserve1,jdbcType=VARCHAR},
      </if>
      <if test="preserve2 != null">
        #{preserve2,jdbcType=VARCHAR},
      </if>
      <if test="preserve3 != null">
        #{preserve3,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTMaterialMaster">
    update ib_t_material_master
    <set>
      <if test="materialId != null">
        material_id = #{materialId,jdbcType=VARCHAR},
      </if>
      <if test="machineType != null">
        machine_type = #{machineType,jdbcType=VARCHAR},
      </if>
      <if test="materialType != null">
        material_type = #{materialType,jdbcType=VARCHAR},
      </if>
      <if test="hierarchy != null">
        hierarchy = #{hierarchy,jdbcType=VARCHAR},
      </if>
      <if test="lastChangeTime != null">
        last_change_time = #{lastChangeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preserve1 != null">
        preserve1 = #{preserve1,jdbcType=VARCHAR},
      </if>
      <if test="preserve2 != null">
        preserve2 = #{preserve2,jdbcType=VARCHAR},
      </if>
      <if test="preserve3 != null">
        preserve3 = #{preserve3,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTMaterialMaster">
    update ib_t_material_master
    set material_id = #{materialId,jdbcType=VARCHAR},
      machine_type = #{machineType,jdbcType=VARCHAR},
      material_type = #{materialType,jdbcType=VARCHAR},
      hierarchy = #{hierarchy,jdbcType=VARCHAR},
      last_change_time = #{lastChangeTime,jdbcType=TIMESTAMP},
      preserve1 = #{preserve1,jdbcType=VARCHAR},
      preserve2 = #{preserve2,jdbcType=VARCHAR},
      preserve3 = #{preserve3,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  
  
  
  
  <!-- chenye -->
   <select id="getFilterMOTO" resultType="String">
     /*balance*/
     select
      material_id
      from ib_t_material_master
      where 
      <bind name="hierarchys" value="@com.lenovo.iqs.utils.ProdHieHelper@motoMbgHierarchySet"/>
      <foreach collection="hierarchys" item="hierarchy"  separator="or">
      	  hierarchy like '${hierarchy}%'
      </foreach>
  </select>
  
  
</mapper>