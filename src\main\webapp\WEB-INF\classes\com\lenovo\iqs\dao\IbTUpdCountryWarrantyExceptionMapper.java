package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTUpdCountryWarrantyException;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTUpdCountryWarrantyExceptionMapper {
  int deleteByPrimaryKey(Long paramLong);
  
  int insert(IbTUpdCountryWarrantyException paramIbTUpdCountryWarrantyException);
  
  IbTUpdCountryWarrantyException selectByPrimaryKey(Long paramLong);
  
  int updateByPrimaryKey(IbTUpdCountryWarrantyException paramIbTUpdCountryWarrantyException);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTUpdCountryWarrantyExceptionMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */