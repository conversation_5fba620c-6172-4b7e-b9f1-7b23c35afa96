package WEB-INF.classes.com.lenovo.iqs.dao;

import com.lenovo.iqs.entity.IbTUpdWarrantyCodeRef;
import org.springframework.stereotype.Repository;

@Repository
public interface IbTUpdWarrantyCodeRefMapper {
  int deleteByPrimaryKey(String paramString);
  
  int insert(IbTUpdWarrantyCodeRef paramIbTUpdWarrantyCodeRef);
  
  IbTUpdWarrantyCodeRef selectByPrimaryKey(String paramString);
  
  int updateByPrimaryKey(IbTUpdWarrantyCodeRef paramIbTUpdWarrantyCodeRef);
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\dao\IbTUpdWarrantyCodeRefMapper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */