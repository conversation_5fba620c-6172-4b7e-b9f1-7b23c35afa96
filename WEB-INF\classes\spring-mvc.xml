<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:mvc="http://www.springframework.org/schema/mvc"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-4.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd">

	<!-- 配置Validator实现校验的提示的修改,redirect时,忽略ModelMap中的参数信息 -->
	<mvc:annotation-driven validator="validator"
		ignore-default-model-on-redirect="true" />

	<context:component-scan base-package="com.lenovo.iqs.**.controller" />

	<!-- 固定Locale为en -->
	<bean id="localeResolver"
		class="org.springframework.web.servlet.i18n.FixedLocaleResolver">
		<constructor-arg value="en" />
	</bean>

	<bean id="validator"
		class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean">
		<property name="providerClass" value="org.hibernate.validator.HibernateValidator" />
		<property name="validationMessageSource" ref="messageSource" />
	</bean>

</beans>