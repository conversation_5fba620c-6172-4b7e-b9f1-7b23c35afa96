/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.Constants;
/*    */ 
/*    */ import org.springframework.util.StringUtils;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public enum ClientTypeEnum
/*    */ {
/* 11 */   IMEI("0x00", "IMEI"),
/* 12 */   SIMLock("0x01", "SIMLock"),
/* 13 */   WhiteListSerial("0xFF", "White List Serial Number"),
/* 14 */   CID("0x02", "CID"),
/* 15 */   MTKSLA("0x03", "MTKSLA"),
/* 16 */   NVDRCM("0x04", "NVDRCM"),
/* 17 */   JanusOrIPRMKey("0x05", "Janus/IPRM Key request"),
/* 18 */   STE("0x06", "STE"),
/* 19 */   CID_BOOTLOADER("0x07", "CID_BOOTLOADER");
/*    */   ClientTypeEnum(String clientType, String clientTypeDesc) {
/*    */     this.clientType = clientType;
/*    */     this.clientTypeDesc = clientTypeDesc;
/*    */   }
/*    */   public static boolean contains(String clientType) {
/* 25 */     if (StringUtils.isEmpty(clientType)) {
/* 26 */       return false;
/*    */     }
/* 28 */     for (com.lenovo.iqs.datablocksign.Constants.ClientTypeEnum item : values()) {
/* 29 */       if (clientType.equals(item.clientType)) {
/* 30 */         return true;
/*    */       }
/*    */     } 
/* 33 */     return false;
/*    */   }
/*    */   public final String clientType; public final String clientTypeDesc;
/*    */   public static boolean isCheckForbidden(String clientType) {
/* 37 */     if (IMEI.clientType.equalsIgnoreCase(clientType) || CID.clientType.equalsIgnoreCase(clientType) || SIMLock.clientType.equalsIgnoreCase(clientType)) {
/* 38 */       return true;
/*    */     }
/* 40 */     return false;
/*    */   }
/*    */ }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\Constants\ClientTypeEnum.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */