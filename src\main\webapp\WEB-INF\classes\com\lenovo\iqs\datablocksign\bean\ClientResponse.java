/*    */ package WEB-INF.classes.com.lenovo.iqs.datablocksign.bean;
/*    */ @XmlAccessorType(XmlAccessType.FIELD)
/*    */ @XmlType(name = "ClientResponse", propOrder = {"istrPkiResponse", "istrStatusCode", "istrStatusData", "istrTransactionID", "istrNodeLockingResponse"}, namespace = "java:com.mot.dbs.services.bean")
/*    */ public class ClientResponse { @XmlElement(name = "statusCode", nillable = true, required = true, namespace = "java:com.mot.dbs.services.bean")
/*    */   private String istrStatusCode;
/*    */   @XmlElement(name = "statusData", nillable = true, required = true, namespace = "java:com.mot.dbs.services.bean")
/*    */   private String istrStatusData;
/*    */   
/*  9 */   public void setIstrStatusCode(String istrStatusCode) { this.istrStatusCode = istrStatusCode; } @XmlElement(name = "transactionID", nillable = true, required = true, namespace = "java:com.mot.dbs.services.bean") private String istrTransactionID; @XmlElement(name = "pkiResponse", nillable = true, required = true, namespace = "java:com.mot.dbs.services.bean") private byte[] istrPkiResponse; @XmlElement(name = "nodeLockingResponse", nillable = true, required = true, namespace = "java:com.mot.dbs.services.bean") private byte[] istrNodeLockingResponse; public void setIstrStatusData(String istrStatusData) { this.istrStatusData = istrStatusData; } public void setIstrTransactionID(String istrTransactionID) { this.istrTransactionID = istrTransactionID; } public void setIstrPkiResponse(byte[] istrPkiResponse) { this.istrPkiResponse = istrPkiResponse; } public void setIstrNodeLockingResponse(byte[] istrNodeLockingResponse) { this.istrNodeLockingResponse = istrNodeLockingResponse; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.datablocksign.bean.ClientResponse)) return false;  com.lenovo.iqs.datablocksign.bean.ClientResponse other = (com.lenovo.iqs.datablocksign.bean.ClientResponse)o; if (!other.canEqual(this)) return false;  Object this$istrStatusCode = getIstrStatusCode(), other$istrStatusCode = other.getIstrStatusCode(); if ((this$istrStatusCode == null) ? (other$istrStatusCode != null) : !this$istrStatusCode.equals(other$istrStatusCode)) return false;  Object this$istrStatusData = getIstrStatusData(), other$istrStatusData = other.getIstrStatusData(); if ((this$istrStatusData == null) ? (other$istrStatusData != null) : !this$istrStatusData.equals(other$istrStatusData)) return false;  Object this$istrTransactionID = getIstrTransactionID(), other$istrTransactionID = other.getIstrTransactionID(); return ((this$istrTransactionID == null) ? (other$istrTransactionID != null) : !this$istrTransactionID.equals(other$istrTransactionID)) ? false : (!Arrays.equals(getIstrPkiResponse(), other.getIstrPkiResponse()) ? false : (!!Arrays.equals(getIstrNodeLockingResponse(), other.getIstrNodeLockingResponse()))); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.datablocksign.bean.ClientResponse; } public int hashCode() { int PRIME = 59; result = 1; Object $istrStatusCode = getIstrStatusCode(); result = result * 59 + (($istrStatusCode == null) ? 43 : $istrStatusCode.hashCode()); Object $istrStatusData = getIstrStatusData(); result = result * 59 + (($istrStatusData == null) ? 43 : $istrStatusData.hashCode()); Object $istrTransactionID = getIstrTransactionID(); result = result * 59 + (($istrTransactionID == null) ? 43 : $istrTransactionID.hashCode()); result = result * 59 + Arrays.hashCode(getIstrPkiResponse()); return result * 59 + Arrays.hashCode(getIstrNodeLockingResponse()); } public String toString() { return "ClientResponse(istrStatusCode=" + getIstrStatusCode() + ", istrStatusData=" + getIstrStatusData() + ", istrTransactionID=" + getIstrTransactionID() + ", istrPkiResponse=" + Arrays.toString(getIstrPkiResponse()) + ", istrNodeLockingResponse=" + Arrays.toString(getIstrNodeLockingResponse()) + ")"; }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getIstrStatusCode() {
/* 15 */     return this.istrStatusCode;
/*    */   }
/*    */   public String getIstrStatusData() {
/* 18 */     return this.istrStatusData;
/*    */   }
/*    */   public String getIstrTransactionID() {
/* 21 */     return this.istrTransactionID;
/*    */   }
/*    */   public byte[] getIstrPkiResponse() {
/* 24 */     return this.istrPkiResponse;
/*    */   }
/*    */   public byte[] getIstrNodeLockingResponse() {
/* 27 */     return this.istrNodeLockingResponse;
/*    */   }
/*    */   public byte[] getPkiResponse() {
/* 30 */     return this.istrPkiResponse;
/*    */   }
/*    */   
/*    */   public void setPkiResponse(byte[] pstrPkiResponse) {
/* 34 */     this.istrPkiResponse = pstrPkiResponse;
/*    */   }
/*    */   
/*    */   public byte[] getNodeLockingResponse() {
/* 38 */     return this.istrNodeLockingResponse;
/*    */   }
/*    */   
/*    */   public void setNodeLockingResponse(byte[] istrNodeLockingResponse) {
/* 42 */     this.istrNodeLockingResponse = istrNodeLockingResponse;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\datablocksign\bean\ClientResponse.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */