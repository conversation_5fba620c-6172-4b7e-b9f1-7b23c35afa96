<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenovo.iqs.dao.IbTUpdServicelinkInboundMapper" >
  <resultMap id="BaseResultMap" type="com.lenovo.iqs.entity.IbTUpdServicelinkInbound" >
    <id column="auto_id" property="autoId" jdbcType="INTEGER" />
    <result column="serial_no" property="serialNo" jdbcType="VARCHAR" />
    <result column="srno_type" property="srnoType" jdbcType="VARCHAR" />
    <result column="warranty_claim_no" property="warrantyClaimNo" jdbcType="VARCHAR" />
    <result column="clms_date_recvd" property="clmsDateRecvd" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="batch_number" property="batchNumber" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    auto_id, serial_no, srno_type, warranty_claim_no, clms_date_recvd, create_time, batch_number
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ib_t_upd_servicelink_inbound
    where auto_id = #{autoId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from ib_t_upd_servicelink_inbound
    where auto_id = #{autoId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenovo.iqs.entity.IbTUpdServicelinkInbound" >
    <selectKey resultType="java.lang.Integer" keyProperty="autoId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_upd_servicelink_inbound (serial_no, srno_type, warranty_claim_no, 
      clms_date_recvd, create_time, batch_number
      )
    values (#{serialNo,jdbcType=VARCHAR}, #{srnoType,jdbcType=VARCHAR}, #{warrantyClaimNo,jdbcType=VARCHAR}, 
      #{clmsDateRecvd,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{batchNumber,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.lenovo.iqs.entity.IbTUpdServicelinkInbound" >
    <selectKey resultType="java.lang.Integer" keyProperty="autoId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ib_t_upd_servicelink_inbound
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="serialNo != null" >
        serial_no,
      </if>
      <if test="srnoType != null" >
        srno_type,
      </if>
      <if test="warrantyClaimNo != null" >
        warranty_claim_no,
      </if>
      <if test="clmsDateRecvd != null" >
        clms_date_recvd,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="batchNumber != null" >
        batch_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="serialNo != null" >
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="srnoType != null" >
        #{srnoType,jdbcType=VARCHAR},
      </if>
      <if test="warrantyClaimNo != null" >
        #{warrantyClaimNo,jdbcType=VARCHAR},
      </if>
      <if test="clmsDateRecvd != null" >
        #{clmsDateRecvd,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNumber != null" >
        #{batchNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenovo.iqs.entity.IbTUpdServicelinkInbound" >
    update ib_t_upd_servicelink_inbound
    <set >
      <if test="serialNo != null" >
        serial_no = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="srnoType != null" >
        srno_type = #{srnoType,jdbcType=VARCHAR},
      </if>
      <if test="warrantyClaimNo != null" >
        warranty_claim_no = #{warrantyClaimNo,jdbcType=VARCHAR},
      </if>
      <if test="clmsDateRecvd != null" >
        clms_date_recvd = #{clmsDateRecvd,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNumber != null" >
        batch_number = #{batchNumber,jdbcType=VARCHAR},
      </if>
    </set>
    where auto_id = #{autoId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenovo.iqs.entity.IbTUpdServicelinkInbound" >
    update ib_t_upd_servicelink_inbound
    set serial_no = #{serialNo,jdbcType=VARCHAR},
      srno_type = #{srnoType,jdbcType=VARCHAR},
      warranty_claim_no = #{warrantyClaimNo,jdbcType=VARCHAR},
      clms_date_recvd = #{clmsDateRecvd,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      batch_number = #{batchNumber,jdbcType=VARCHAR}
    where auto_id = #{autoId,jdbcType=INTEGER}
  </update>
</mapper>