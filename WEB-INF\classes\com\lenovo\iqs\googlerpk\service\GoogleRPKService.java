package WEB-INF.classes.com.lenovo.iqs.googlerpk.service;

import com.lenovo.iqs.googlerpk.bean.GoogleRPKRequest;
import com.lenovo.iqs.googlerpk.bean.GoogleRPKResponse;

public interface GoogleRPKService {
  GoogleRPKResponse processGoogleRPKRequest(GoogleRPKRequest paramGoogleRPKRequest) throws Exception;
}


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\googlerpk\service\GoogleRPKService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */