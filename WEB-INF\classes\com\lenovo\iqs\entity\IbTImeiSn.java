/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTImeiSn implements Serializable { private Integer id;
/*    */   private String imeiCode;
/*    */   private String serialNumber;
/*    */   private String productId;
/*    */   
/*  7 */   public void setId(Integer id) { this.id = id; } private Date imeiSendDatetime; private String partitionChar; private Date lastChangeTime; private static final long serialVersionUID = 1L; public void setImeiCode(String imeiCode) { this.imeiCode = imeiCode; } public void setSerialNumber(String serialNumber) { this.serialNumber = serialNumber; } public void setProductId(String productId) { this.productId = productId; } public void setImeiSendDatetime(Date imeiSendDatetime) { this.imeiSendDatetime = imeiSendDatetime; } public void setPartitionChar(String partitionChar) { this.partitionChar = partitionChar; } public void setLastChangeTime(Date lastChangeTime) { this.lastChangeTime = lastChangeTime; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTImeiSn)) return false;  com.lenovo.iqs.entity.IbTImeiSn other = (com.lenovo.iqs.entity.IbTImeiSn)o; if (!other.canEqual(this)) return false;  Object this$id = getId(), other$id = other.getId(); if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id)) return false;  Object this$imeiCode = getImeiCode(), other$imeiCode = other.getImeiCode(); if ((this$imeiCode == null) ? (other$imeiCode != null) : !this$imeiCode.equals(other$imeiCode)) return false;  Object this$serialNumber = getSerialNumber(), other$serialNumber = other.getSerialNumber(); if ((this$serialNumber == null) ? (other$serialNumber != null) : !this$serialNumber.equals(other$serialNumber)) return false;  Object this$productId = getProductId(), other$productId = other.getProductId(); if ((this$productId == null) ? (other$productId != null) : !this$productId.equals(other$productId)) return false;  Object this$imeiSendDatetime = getImeiSendDatetime(), other$imeiSendDatetime = other.getImeiSendDatetime(); if ((this$imeiSendDatetime == null) ? (other$imeiSendDatetime != null) : !this$imeiSendDatetime.equals(other$imeiSendDatetime)) return false;  Object this$partitionChar = getPartitionChar(), other$partitionChar = other.getPartitionChar(); if ((this$partitionChar == null) ? (other$partitionChar != null) : !this$partitionChar.equals(other$partitionChar)) return false;  Object this$lastChangeTime = getLastChangeTime(), other$lastChangeTime = other.getLastChangeTime(); return !((this$lastChangeTime == null) ? (other$lastChangeTime != null) : !this$lastChangeTime.equals(other$lastChangeTime)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTImeiSn; } public int hashCode() { int PRIME = 59; result = 1; Object $id = getId(); result = result * 59 + (($id == null) ? 43 : $id.hashCode()); Object $imeiCode = getImeiCode(); result = result * 59 + (($imeiCode == null) ? 43 : $imeiCode.hashCode()); Object $serialNumber = getSerialNumber(); result = result * 59 + (($serialNumber == null) ? 43 : $serialNumber.hashCode()); Object $productId = getProductId(); result = result * 59 + (($productId == null) ? 43 : $productId.hashCode()); Object $imeiSendDatetime = getImeiSendDatetime(); result = result * 59 + (($imeiSendDatetime == null) ? 43 : $imeiSendDatetime.hashCode()); Object $partitionChar = getPartitionChar(); result = result * 59 + (($partitionChar == null) ? 43 : $partitionChar.hashCode()); Object $lastChangeTime = getLastChangeTime(); return result * 59 + (($lastChangeTime == null) ? 43 : $lastChangeTime.hashCode()); } public String toString() { return "IbTImeiSn(id=" + getId() + ", imeiCode=" + getImeiCode() + ", serialNumber=" + getSerialNumber() + ", productId=" + getProductId() + ", imeiSendDatetime=" + getImeiSendDatetime() + ", partitionChar=" + getPartitionChar() + ", lastChangeTime=" + getLastChangeTime() + ")"; }
/*    */    public Integer getId() {
/*  9 */     return this.id;
/*    */   } public String getImeiCode() {
/* 11 */     return this.imeiCode;
/*    */   } public String getSerialNumber() {
/* 13 */     return this.serialNumber;
/*    */   } public String getProductId() {
/* 15 */     return this.productId;
/*    */   } public Date getImeiSendDatetime() {
/* 17 */     return this.imeiSendDatetime;
/*    */   } public String getPartitionChar() {
/* 19 */     return this.partitionChar;
/*    */   } public Date getLastChangeTime() {
/* 21 */     return this.lastChangeTime;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTImeiSn.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */