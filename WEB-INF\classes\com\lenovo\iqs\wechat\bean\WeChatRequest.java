/*    */ package WEB-INF.classes.com.lenovo.iqs.wechat.bean;
/*    */ public class WeChatRequest { private String serialNo; private String serialType;
/*    */   private String dualSerialNo;
/*    */   private String triSerialNo;
/*    */   private String rsdUser;
/*    */   private String mascId;
/*    */   
/*  8 */   public void setSerialNo(String serialNo) { this.serialNo = serialNo; } private String deviceModel; private String deviceIds; private String securityLevel; private String attkPubKey; private String brandName; private String trackId; public void setSerialType(String serialType) { this.serialType = serialType; } public void setDualSerialNo(String dualSerialNo) { this.dualSerialNo = dualSerialNo; } public void setTriSerialNo(String triSerialNo) { this.triSerialNo = triSerialNo; } public void setRsdUser(String rsdUser) { this.rsdUser = rsdUser; } public void setMascId(String mascId) { this.mascId = mascId; } public void setDeviceModel(String deviceModel) { this.deviceModel = deviceModel; } public void setDeviceIds(String deviceIds) { this.deviceIds = deviceIds; } public void setSecurityLevel(String securityLevel) { this.securityLevel = securityLevel; } public void setAttkPubKey(String attkPubKey) { this.attkPubKey = attkPubKey; } public void setBrandName(String brandName) { this.brandName = brandName; } public void setTrackId(String trackId) { this.trackId = trackId; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.wechat.bean.WeChatRequest)) return false;  com.lenovo.iqs.wechat.bean.WeChatRequest other = (com.lenovo.iqs.wechat.bean.WeChatRequest)o; if (!other.canEqual(this)) return false;  Object this$serialNo = getSerialNo(), other$serialNo = other.getSerialNo(); if ((this$serialNo == null) ? (other$serialNo != null) : !this$serialNo.equals(other$serialNo)) return false;  Object this$serialType = getSerialType(), other$serialType = other.getSerialType(); if ((this$serialType == null) ? (other$serialType != null) : !this$serialType.equals(other$serialType)) return false;  Object this$dualSerialNo = getDualSerialNo(), other$dualSerialNo = other.getDualSerialNo(); if ((this$dualSerialNo == null) ? (other$dualSerialNo != null) : !this$dualSerialNo.equals(other$dualSerialNo)) return false;  Object this$triSerialNo = getTriSerialNo(), other$triSerialNo = other.getTriSerialNo(); if ((this$triSerialNo == null) ? (other$triSerialNo != null) : !this$triSerialNo.equals(other$triSerialNo)) return false;  Object this$rsdUser = getRsdUser(), other$rsdUser = other.getRsdUser(); if ((this$rsdUser == null) ? (other$rsdUser != null) : !this$rsdUser.equals(other$rsdUser)) return false;  Object this$mascId = getMascId(), other$mascId = other.getMascId(); if ((this$mascId == null) ? (other$mascId != null) : !this$mascId.equals(other$mascId)) return false;  Object this$deviceModel = getDeviceModel(), other$deviceModel = other.getDeviceModel(); if ((this$deviceModel == null) ? (other$deviceModel != null) : !this$deviceModel.equals(other$deviceModel)) return false;  Object this$deviceIds = getDeviceIds(), other$deviceIds = other.getDeviceIds(); if ((this$deviceIds == null) ? (other$deviceIds != null) : !this$deviceIds.equals(other$deviceIds)) return false;  Object this$securityLevel = getSecurityLevel(), other$securityLevel = other.getSecurityLevel(); if ((this$securityLevel == null) ? (other$securityLevel != null) : !this$securityLevel.equals(other$securityLevel)) return false;  Object this$attkPubKey = getAttkPubKey(), other$attkPubKey = other.getAttkPubKey(); if ((this$attkPubKey == null) ? (other$attkPubKey != null) : !this$attkPubKey.equals(other$attkPubKey)) return false;  Object this$brandName = getBrandName(), other$brandName = other.getBrandName(); if ((this$brandName == null) ? (other$brandName != null) : !this$brandName.equals(other$brandName)) return false;  Object this$trackId = getTrackId(), other$trackId = other.getTrackId(); return !((this$trackId == null) ? (other$trackId != null) : !this$trackId.equals(other$trackId)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.wechat.bean.WeChatRequest; } public int hashCode() { int PRIME = 59; result = 1; Object $serialNo = getSerialNo(); result = result * 59 + (($serialNo == null) ? 43 : $serialNo.hashCode()); Object $serialType = getSerialType(); result = result * 59 + (($serialType == null) ? 43 : $serialType.hashCode()); Object $dualSerialNo = getDualSerialNo(); result = result * 59 + (($dualSerialNo == null) ? 43 : $dualSerialNo.hashCode()); Object $triSerialNo = getTriSerialNo(); result = result * 59 + (($triSerialNo == null) ? 43 : $triSerialNo.hashCode()); Object $rsdUser = getRsdUser(); result = result * 59 + (($rsdUser == null) ? 43 : $rsdUser.hashCode()); Object $mascId = getMascId(); result = result * 59 + (($mascId == null) ? 43 : $mascId.hashCode()); Object $deviceModel = getDeviceModel(); result = result * 59 + (($deviceModel == null) ? 43 : $deviceModel.hashCode()); Object $deviceIds = getDeviceIds(); result = result * 59 + (($deviceIds == null) ? 43 : $deviceIds.hashCode()); Object $securityLevel = getSecurityLevel(); result = result * 59 + (($securityLevel == null) ? 43 : $securityLevel.hashCode()); Object $attkPubKey = getAttkPubKey(); result = result * 59 + (($attkPubKey == null) ? 43 : $attkPubKey.hashCode()); Object $brandName = getBrandName(); result = result * 59 + (($brandName == null) ? 43 : $brandName.hashCode()); Object $trackId = getTrackId(); return result * 59 + (($trackId == null) ? 43 : $trackId.hashCode()); } public String toString() { return "WeChatRequest(serialNo=" + getSerialNo() + ", serialType=" + getSerialType() + ", dualSerialNo=" + getDualSerialNo() + ", triSerialNo=" + getTriSerialNo() + ", rsdUser=" + getRsdUser() + ", mascId=" + getMascId() + ", deviceModel=" + getDeviceModel() + ", deviceIds=" + getDeviceIds() + ", securityLevel=" + getSecurityLevel() + ", attkPubKey=" + getAttkPubKey() + ", brandName=" + getBrandName() + ", trackId=" + getTrackId() + ")"; }
/*    */   
/* 10 */   public String getSerialNo() { return this.serialNo; }
/* 11 */   public String getSerialType() { return this.serialType; }
/* 12 */   public String getDualSerialNo() { return this.dualSerialNo; }
/* 13 */   public String getTriSerialNo() { return this.triSerialNo; }
/* 14 */   public String getRsdUser() { return this.rsdUser; }
/* 15 */   public String getMascId() { return this.mascId; }
/* 16 */   public String getDeviceModel() { return this.deviceModel; }
/* 17 */   public String getDeviceIds() { return this.deviceIds; }
/* 18 */   public String getSecurityLevel() { return this.securityLevel; }
/* 19 */   public String getAttkPubKey() { return this.attkPubKey; }
/* 20 */   public String getBrandName() { return this.brandName; } public String getTrackId() {
/* 21 */     return this.trackId;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\wechat\bean\WeChatRequest.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */