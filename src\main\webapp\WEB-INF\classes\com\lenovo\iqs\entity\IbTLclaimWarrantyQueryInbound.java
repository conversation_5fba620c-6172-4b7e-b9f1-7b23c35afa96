/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTLclaimWarrantyQueryInbound implements Serializable { private Integer autoId; private int claimId; private String closeMonth;
/*    */   private String machineType;
/*    */   private String serialNumber;
/*    */   private String imei1;
/*    */   
/*  7 */   public void setAutoId(Integer autoId) { this.autoId = autoId; } private String imei2; private Date lastChangeTime; private String batchNumber; private String imei1Serial; private String imei2Serial; private static final long serialVersionUID = 1L; public void setClaimId(int claimId) { this.claimId = claimId; } public void setCloseMonth(String closeMonth) { this.closeMonth = closeMonth; } public void setMachineType(String machineType) { this.machineType = machineType; } public void setSerialNumber(String serialNumber) { this.serialNumber = serialNumber; } public void setImei1(String imei1) { this.imei1 = imei1; } public void setImei2(String imei2) { this.imei2 = imei2; } public void setLastChangeTime(Date lastChangeTime) { this.lastChangeTime = lastChangeTime; } public void setBatchNumber(String batchNumber) { this.batchNumber = batchNumber; } public void setImei1Serial(String imei1Serial) { this.imei1Serial = imei1Serial; } public void setImei2Serial(String imei2Serial) { this.imei2Serial = imei2Serial; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTLclaimWarrantyQueryInbound)) return false;  com.lenovo.iqs.entity.IbTLclaimWarrantyQueryInbound other = (com.lenovo.iqs.entity.IbTLclaimWarrantyQueryInbound)o; if (!other.canEqual(this)) return false;  Object this$autoId = getAutoId(), other$autoId = other.getAutoId(); if ((this$autoId == null) ? (other$autoId != null) : !this$autoId.equals(other$autoId)) return false;  if (getClaimId() != other.getClaimId()) return false;  Object this$closeMonth = getCloseMonth(), other$closeMonth = other.getCloseMonth(); if ((this$closeMonth == null) ? (other$closeMonth != null) : !this$closeMonth.equals(other$closeMonth)) return false;  Object this$machineType = getMachineType(), other$machineType = other.getMachineType(); if ((this$machineType == null) ? (other$machineType != null) : !this$machineType.equals(other$machineType)) return false;  Object this$serialNumber = getSerialNumber(), other$serialNumber = other.getSerialNumber(); if ((this$serialNumber == null) ? (other$serialNumber != null) : !this$serialNumber.equals(other$serialNumber)) return false;  Object this$imei1 = getImei1(), other$imei1 = other.getImei1(); if ((this$imei1 == null) ? (other$imei1 != null) : !this$imei1.equals(other$imei1)) return false;  Object this$imei2 = getImei2(), other$imei2 = other.getImei2(); if ((this$imei2 == null) ? (other$imei2 != null) : !this$imei2.equals(other$imei2)) return false;  Object this$lastChangeTime = getLastChangeTime(), other$lastChangeTime = other.getLastChangeTime(); if ((this$lastChangeTime == null) ? (other$lastChangeTime != null) : !this$lastChangeTime.equals(other$lastChangeTime)) return false;  Object this$batchNumber = getBatchNumber(), other$batchNumber = other.getBatchNumber(); if ((this$batchNumber == null) ? (other$batchNumber != null) : !this$batchNumber.equals(other$batchNumber)) return false;  Object this$imei1Serial = getImei1Serial(), other$imei1Serial = other.getImei1Serial(); if ((this$imei1Serial == null) ? (other$imei1Serial != null) : !this$imei1Serial.equals(other$imei1Serial)) return false;  Object this$imei2Serial = getImei2Serial(), other$imei2Serial = other.getImei2Serial(); return !((this$imei2Serial == null) ? (other$imei2Serial != null) : !this$imei2Serial.equals(other$imei2Serial)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTLclaimWarrantyQueryInbound; } public int hashCode() { int PRIME = 59; result = 1; Object $autoId = getAutoId(); result = result * 59 + (($autoId == null) ? 43 : $autoId.hashCode()); result = result * 59 + getClaimId(); Object $closeMonth = getCloseMonth(); result = result * 59 + (($closeMonth == null) ? 43 : $closeMonth.hashCode()); Object $machineType = getMachineType(); result = result * 59 + (($machineType == null) ? 43 : $machineType.hashCode()); Object $serialNumber = getSerialNumber(); result = result * 59 + (($serialNumber == null) ? 43 : $serialNumber.hashCode()); Object $imei1 = getImei1(); result = result * 59 + (($imei1 == null) ? 43 : $imei1.hashCode()); Object $imei2 = getImei2(); result = result * 59 + (($imei2 == null) ? 43 : $imei2.hashCode()); Object $lastChangeTime = getLastChangeTime(); result = result * 59 + (($lastChangeTime == null) ? 43 : $lastChangeTime.hashCode()); Object $batchNumber = getBatchNumber(); result = result * 59 + (($batchNumber == null) ? 43 : $batchNumber.hashCode()); Object $imei1Serial = getImei1Serial(); result = result * 59 + (($imei1Serial == null) ? 43 : $imei1Serial.hashCode()); Object $imei2Serial = getImei2Serial(); return result * 59 + (($imei2Serial == null) ? 43 : $imei2Serial.hashCode()); } public String toString() { return "IbTLclaimWarrantyQueryInbound(autoId=" + getAutoId() + ", claimId=" + getClaimId() + ", closeMonth=" + getCloseMonth() + ", machineType=" + getMachineType() + ", serialNumber=" + getSerialNumber() + ", imei1=" + getImei1() + ", imei2=" + getImei2() + ", lastChangeTime=" + getLastChangeTime() + ", batchNumber=" + getBatchNumber() + ", imei1Serial=" + getImei1Serial() + ", imei2Serial=" + getImei2Serial() + ")"; }
/*    */   
/*    */   public Integer getAutoId() {
/* 10 */     return this.autoId;
/*    */   } public int getClaimId() {
/* 12 */     return this.claimId;
/*    */   } public String getCloseMonth() {
/* 14 */     return this.closeMonth;
/*    */   } public String getMachineType() {
/* 16 */     return this.machineType;
/*    */   } public String getSerialNumber() {
/* 18 */     return this.serialNumber;
/*    */   } public String getImei1() {
/* 20 */     return this.imei1;
/*    */   } public String getImei2() {
/* 22 */     return this.imei2;
/*    */   } public Date getLastChangeTime() {
/* 24 */     return this.lastChangeTime;
/*    */   } public String getBatchNumber() {
/* 26 */     return this.batchNumber;
/*    */   }
/*    */   public String getImei1Serial() {
/* 29 */     return this.imei1Serial;
/*    */   }
/*    */   public String getImei2Serial() {
/* 32 */     return this.imei2Serial;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTLclaimWarrantyQueryInbound.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */