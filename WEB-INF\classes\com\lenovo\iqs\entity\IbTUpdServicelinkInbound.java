/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTUpdServicelinkInbound implements Serializable { private Integer autoId;
/*    */   private String serialNo;
/*    */   private String srnoType;
/*    */   private String warrantyClaimNo;
/*    */   
/*  7 */   public void setAutoId(Integer autoId) { this.autoId = autoId; } private String clmsDateRecvd; private Date createTime; private String batchNumber; private static final long serialVersionUID = 1L; public void setSerialNo(String serialNo) { this.serialNo = serialNo; } public void setSrnoType(String srnoType) { this.srnoType = srnoType; } public void setWarrantyClaimNo(String warrantyClaimNo) { this.warrantyClaimNo = warrantyClaimNo; } public void setClmsDateRecvd(String clmsDateRecvd) { this.clmsDateRecvd = clmsDateRecvd; } public void setCreateTime(Date createTime) { this.createTime = createTime; } public void setBatchNumber(String batchNumber) { this.batchNumber = batchNumber; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdServicelinkInbound)) return false;  com.lenovo.iqs.entity.IbTUpdServicelinkInbound other = (com.lenovo.iqs.entity.IbTUpdServicelinkInbound)o; if (!other.canEqual(this)) return false;  Object this$autoId = getAutoId(), other$autoId = other.getAutoId(); if ((this$autoId == null) ? (other$autoId != null) : !this$autoId.equals(other$autoId)) return false;  Object this$serialNo = getSerialNo(), other$serialNo = other.getSerialNo(); if ((this$serialNo == null) ? (other$serialNo != null) : !this$serialNo.equals(other$serialNo)) return false;  Object this$srnoType = getSrnoType(), other$srnoType = other.getSrnoType(); if ((this$srnoType == null) ? (other$srnoType != null) : !this$srnoType.equals(other$srnoType)) return false;  Object this$warrantyClaimNo = getWarrantyClaimNo(), other$warrantyClaimNo = other.getWarrantyClaimNo(); if ((this$warrantyClaimNo == null) ? (other$warrantyClaimNo != null) : !this$warrantyClaimNo.equals(other$warrantyClaimNo)) return false;  Object this$clmsDateRecvd = getClmsDateRecvd(), other$clmsDateRecvd = other.getClmsDateRecvd(); if ((this$clmsDateRecvd == null) ? (other$clmsDateRecvd != null) : !this$clmsDateRecvd.equals(other$clmsDateRecvd)) return false;  Object this$createTime = getCreateTime(), other$createTime = other.getCreateTime(); if ((this$createTime == null) ? (other$createTime != null) : !this$createTime.equals(other$createTime)) return false;  Object this$batchNumber = getBatchNumber(), other$batchNumber = other.getBatchNumber(); return !((this$batchNumber == null) ? (other$batchNumber != null) : !this$batchNumber.equals(other$batchNumber)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdServicelinkInbound; } public int hashCode() { int PRIME = 59; result = 1; Object $autoId = getAutoId(); result = result * 59 + (($autoId == null) ? 43 : $autoId.hashCode()); Object $serialNo = getSerialNo(); result = result * 59 + (($serialNo == null) ? 43 : $serialNo.hashCode()); Object $srnoType = getSrnoType(); result = result * 59 + (($srnoType == null) ? 43 : $srnoType.hashCode()); Object $warrantyClaimNo = getWarrantyClaimNo(); result = result * 59 + (($warrantyClaimNo == null) ? 43 : $warrantyClaimNo.hashCode()); Object $clmsDateRecvd = getClmsDateRecvd(); result = result * 59 + (($clmsDateRecvd == null) ? 43 : $clmsDateRecvd.hashCode()); Object $createTime = getCreateTime(); result = result * 59 + (($createTime == null) ? 43 : $createTime.hashCode()); Object $batchNumber = getBatchNumber(); return result * 59 + (($batchNumber == null) ? 43 : $batchNumber.hashCode()); } public String toString() { return "IbTUpdServicelinkInbound(autoId=" + getAutoId() + ", serialNo=" + getSerialNo() + ", srnoType=" + getSrnoType() + ", warrantyClaimNo=" + getWarrantyClaimNo() + ", clmsDateRecvd=" + getClmsDateRecvd() + ", createTime=" + getCreateTime() + ", batchNumber=" + getBatchNumber() + ")"; }
/*    */    public Integer getAutoId() {
/*  9 */     return this.autoId;
/*    */   } public String getSerialNo() {
/* 11 */     return this.serialNo;
/*    */   } public String getSrnoType() {
/* 13 */     return this.srnoType;
/*    */   } public String getWarrantyClaimNo() {
/* 15 */     return this.warrantyClaimNo;
/*    */   } public String getClmsDateRecvd() {
/* 17 */     return this.clmsDateRecvd;
/*    */   } public Date getCreateTime() {
/* 19 */     return this.createTime;
/*    */   } public String getBatchNumber() {
/* 21 */     return this.batchNumber;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdServicelinkInbound.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */