/*    */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*    */ public class IbTUpdWarrantyCancelCodeRef implements Serializable { private String warrantyCancelCode; private Date startDate;
/*    */   private Date endDate;
/*    */   private String warrantyCancelCodeDesc;
/*    */   private String effectStdWarranty;
/*    */   
/*  7 */   public void setWarrantyCancelCode(String warrantyCancelCode) { this.warrantyCancelCode = warrantyCancelCode; } private String effectExtWarranty; private String createdBy; private Date creationDatetime; private String lastModUser; private Date lastModDate; private static final long serialVersionUID = 1L; public void setStartDate(Date startDate) { this.startDate = startDate; } public void setEndDate(Date endDate) { this.endDate = endDate; } public void setWarrantyCancelCodeDesc(String warrantyCancelCodeDesc) { this.warrantyCancelCodeDesc = warrantyCancelCodeDesc; } public void setEffectStdWarranty(String effectStdWarranty) { this.effectStdWarranty = effectStdWarranty; } public void setEffectExtWarranty(String effectExtWarranty) { this.effectExtWarranty = effectExtWarranty; } public void setCreatedBy(String createdBy) { this.createdBy = createdBy; } public void setCreationDatetime(Date creationDatetime) { this.creationDatetime = creationDatetime; } public void setLastModUser(String lastModUser) { this.lastModUser = lastModUser; } public void setLastModDate(Date lastModDate) { this.lastModDate = lastModDate; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdWarrantyCancelCodeRef)) return false;  com.lenovo.iqs.entity.IbTUpdWarrantyCancelCodeRef other = (com.lenovo.iqs.entity.IbTUpdWarrantyCancelCodeRef)o; if (!other.canEqual(this)) return false;  Object this$warrantyCancelCode = getWarrantyCancelCode(), other$warrantyCancelCode = other.getWarrantyCancelCode(); if ((this$warrantyCancelCode == null) ? (other$warrantyCancelCode != null) : !this$warrantyCancelCode.equals(other$warrantyCancelCode)) return false;  Object this$startDate = getStartDate(), other$startDate = other.getStartDate(); if ((this$startDate == null) ? (other$startDate != null) : !this$startDate.equals(other$startDate)) return false;  Object this$endDate = getEndDate(), other$endDate = other.getEndDate(); if ((this$endDate == null) ? (other$endDate != null) : !this$endDate.equals(other$endDate)) return false;  Object this$warrantyCancelCodeDesc = getWarrantyCancelCodeDesc(), other$warrantyCancelCodeDesc = other.getWarrantyCancelCodeDesc(); if ((this$warrantyCancelCodeDesc == null) ? (other$warrantyCancelCodeDesc != null) : !this$warrantyCancelCodeDesc.equals(other$warrantyCancelCodeDesc)) return false;  Object this$effectStdWarranty = getEffectStdWarranty(), other$effectStdWarranty = other.getEffectStdWarranty(); if ((this$effectStdWarranty == null) ? (other$effectStdWarranty != null) : !this$effectStdWarranty.equals(other$effectStdWarranty)) return false;  Object this$effectExtWarranty = getEffectExtWarranty(), other$effectExtWarranty = other.getEffectExtWarranty(); if ((this$effectExtWarranty == null) ? (other$effectExtWarranty != null) : !this$effectExtWarranty.equals(other$effectExtWarranty)) return false;  Object this$createdBy = getCreatedBy(), other$createdBy = other.getCreatedBy(); if ((this$createdBy == null) ? (other$createdBy != null) : !this$createdBy.equals(other$createdBy)) return false;  Object this$creationDatetime = getCreationDatetime(), other$creationDatetime = other.getCreationDatetime(); if ((this$creationDatetime == null) ? (other$creationDatetime != null) : !this$creationDatetime.equals(other$creationDatetime)) return false;  Object this$lastModUser = getLastModUser(), other$lastModUser = other.getLastModUser(); if ((this$lastModUser == null) ? (other$lastModUser != null) : !this$lastModUser.equals(other$lastModUser)) return false;  Object this$lastModDate = getLastModDate(), other$lastModDate = other.getLastModDate(); return !((this$lastModDate == null) ? (other$lastModDate != null) : !this$lastModDate.equals(other$lastModDate)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdWarrantyCancelCodeRef; } public int hashCode() { int PRIME = 59; result = 1; Object $warrantyCancelCode = getWarrantyCancelCode(); result = result * 59 + (($warrantyCancelCode == null) ? 43 : $warrantyCancelCode.hashCode()); Object $startDate = getStartDate(); result = result * 59 + (($startDate == null) ? 43 : $startDate.hashCode()); Object $endDate = getEndDate(); result = result * 59 + (($endDate == null) ? 43 : $endDate.hashCode()); Object $warrantyCancelCodeDesc = getWarrantyCancelCodeDesc(); result = result * 59 + (($warrantyCancelCodeDesc == null) ? 43 : $warrantyCancelCodeDesc.hashCode()); Object $effectStdWarranty = getEffectStdWarranty(); result = result * 59 + (($effectStdWarranty == null) ? 43 : $effectStdWarranty.hashCode()); Object $effectExtWarranty = getEffectExtWarranty(); result = result * 59 + (($effectExtWarranty == null) ? 43 : $effectExtWarranty.hashCode()); Object $createdBy = getCreatedBy(); result = result * 59 + (($createdBy == null) ? 43 : $createdBy.hashCode()); Object $creationDatetime = getCreationDatetime(); result = result * 59 + (($creationDatetime == null) ? 43 : $creationDatetime.hashCode()); Object $lastModUser = getLastModUser(); result = result * 59 + (($lastModUser == null) ? 43 : $lastModUser.hashCode()); Object $lastModDate = getLastModDate(); return result * 59 + (($lastModDate == null) ? 43 : $lastModDate.hashCode()); } public String toString() { return "IbTUpdWarrantyCancelCodeRef(warrantyCancelCode=" + getWarrantyCancelCode() + ", startDate=" + getStartDate() + ", endDate=" + getEndDate() + ", warrantyCancelCodeDesc=" + getWarrantyCancelCodeDesc() + ", effectStdWarranty=" + getEffectStdWarranty() + ", effectExtWarranty=" + getEffectExtWarranty() + ", createdBy=" + getCreatedBy() + ", creationDatetime=" + getCreationDatetime() + ", lastModUser=" + getLastModUser() + ", lastModDate=" + getLastModDate() + ")"; }
/*    */    public String getWarrantyCancelCode() {
/*  9 */     return this.warrantyCancelCode;
/*    */   } public Date getStartDate() {
/* 11 */     return this.startDate;
/*    */   } public Date getEndDate() {
/* 13 */     return this.endDate;
/*    */   } public String getWarrantyCancelCodeDesc() {
/* 15 */     return this.warrantyCancelCodeDesc;
/*    */   } public String getEffectStdWarranty() {
/* 17 */     return this.effectStdWarranty;
/*    */   } public String getEffectExtWarranty() {
/* 19 */     return this.effectExtWarranty;
/*    */   } public String getCreatedBy() {
/* 21 */     return this.createdBy;
/*    */   } public Date getCreationDatetime() {
/* 23 */     return this.creationDatetime;
/*    */   } public String getLastModUser() {
/* 25 */     return this.lastModUser;
/*    */   } public Date getLastModDate() {
/* 27 */     return this.lastModDate;
/*    */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdWarrantyCancelCodeRef.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */