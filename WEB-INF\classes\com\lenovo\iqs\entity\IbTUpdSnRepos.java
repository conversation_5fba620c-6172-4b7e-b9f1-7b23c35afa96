/*     */ package WEB-INF.classes.com.lenovo.iqs.entity;
/*     */ public class IbTUpdSnRepos implements Serializable { private String serialNo; private String requestId; private String regionId; private String systemId; private String createdBy; private Date creationDatetime; private String lastModBy; private Date lastModDatetime; private String attribute01; private Date attribute02; private String attribute03; private String attribute04; private String attribute05; private String attribute06; private String attribute07; private String attribute08; private String attribute09; private Date attribute10; private String attribute11; private String attribute12; private String attribute13; private String attribute14; private String attribute15; private String attribute16; private String attribute17; private Date attribute18; private String attribute19; private String attribute20; private String attribute21; private String attribute22; private String attribute23; private String attribute24; private String attribute25; private String attribute26; private String attribute27; private String attribute28; private String attribute29; private String attribute30; private String attribute31; private String attribute32; private String attribute33; private String attribute34; private String attribute35; private String attribute36; private String attribute37; private String attribute38; private String attribute39; private String attribute40; private String attribute41; private String attribute42; private String attribute43; private String attribute44; private Date attribute45; private String attribute46; private String attribute47; private String attribute48; private Date attribute49; private Date attribute50; private Date attribute51; private Date attribute52; private Date attribute53; private String attribute54; private String attribute55; private String attribute56; private String attribute57; private String attribute58; private String attribute59; private String attribute60; private String attribute61; private String attribute62; private String attribute63; private String attribute64; private String attribute65; private String attribute66; private String attribute67; private String attribute68; private String attribute69; private String attribute70;
/*     */   private String attribute71;
/*     */   private String attribute72;
/*     */   private String attribute73;
/*     */   private String attribute74;
/*     */   
/*   8 */   public void setSerialNo(String serialNo) { this.serialNo = serialNo; } private String attribute75; private String attribute76; private String attribute77; private String attribute78; private Date attribute79; private String attribute80; private Date attribute81; private String attribute82; private String attribute83; private String attribute84; private String attribute85; private String attribute86; private String attribute87; private String attribute88; private String attribute89; private String attribute90; private String attribute91; private String attribute92; private String attribute93; private String attribute94; private String attribute95; private String attribute96; private String attribute97; private String attribute98; private String attribute99; private String attribute100; private String attribute101; private String attribute102; private String attribute103; private String attribute104; private String attribute105; private String attribute106; private String attribute107; private String attribute108; private String attribute109; private String attribute110; private String attribute111; private String attribute112; private String attribute113; private Date attribute117; private String attribute118; private Date attribute114; private Date attribute115; private String attribute116; private String attribute119; private String attribute120; private String attribute121; private String attribute122; private String attribute123; private String attribute124; private String attribute125; private String attribute126; private String attribute127; private String attribute128; private String attribute129; private String attribute130; private String attribute131; private String attribute132; private String attribute133; private String attribute134; private String attribute135; private String attribute136; private String attribute137; private String attribute138; private String attribute139; private String attribute140; private String attribute141; private String attribute142; private String attribute143; private String attribute144; private String attribute145; private String attribute146; private String attribute147; private String attribute148; private String attribute149; private String attribute150; private String lastUpBy; private Date lastUpDatetime; private String updfilename; private String updlineitem; private String lnvproIdentifier; private String updateStatus; private static final long serialVersionUID = 1L; public void setRequestId(String requestId) { this.requestId = requestId; } public void setRegionId(String regionId) { this.regionId = regionId; } public void setSystemId(String systemId) { this.systemId = systemId; } public void setCreatedBy(String createdBy) { this.createdBy = createdBy; } public void setCreationDatetime(Date creationDatetime) { this.creationDatetime = creationDatetime; } public void setLastModBy(String lastModBy) { this.lastModBy = lastModBy; } public void setLastModDatetime(Date lastModDatetime) { this.lastModDatetime = lastModDatetime; } public void setAttribute01(String attribute01) { this.attribute01 = attribute01; } public void setAttribute02(Date attribute02) { this.attribute02 = attribute02; } public void setAttribute03(String attribute03) { this.attribute03 = attribute03; } public void setAttribute04(String attribute04) { this.attribute04 = attribute04; } public void setAttribute05(String attribute05) { this.attribute05 = attribute05; } public void setAttribute06(String attribute06) { this.attribute06 = attribute06; } public void setAttribute07(String attribute07) { this.attribute07 = attribute07; } public void setAttribute08(String attribute08) { this.attribute08 = attribute08; } public void setAttribute09(String attribute09) { this.attribute09 = attribute09; } public void setAttribute10(Date attribute10) { this.attribute10 = attribute10; } public void setAttribute11(String attribute11) { this.attribute11 = attribute11; } public void setAttribute12(String attribute12) { this.attribute12 = attribute12; } public void setAttribute13(String attribute13) { this.attribute13 = attribute13; } public void setAttribute14(String attribute14) { this.attribute14 = attribute14; } public void setAttribute15(String attribute15) { this.attribute15 = attribute15; } public void setAttribute16(String attribute16) { this.attribute16 = attribute16; } public void setAttribute17(String attribute17) { this.attribute17 = attribute17; } public void setAttribute18(Date attribute18) { this.attribute18 = attribute18; } public void setAttribute19(String attribute19) { this.attribute19 = attribute19; } public void setAttribute20(String attribute20) { this.attribute20 = attribute20; } public void setAttribute21(String attribute21) { this.attribute21 = attribute21; } public void setAttribute22(String attribute22) { this.attribute22 = attribute22; } public void setAttribute23(String attribute23) { this.attribute23 = attribute23; } public void setAttribute24(String attribute24) { this.attribute24 = attribute24; } public void setAttribute25(String attribute25) { this.attribute25 = attribute25; } public void setAttribute26(String attribute26) { this.attribute26 = attribute26; } public void setAttribute27(String attribute27) { this.attribute27 = attribute27; } public void setAttribute28(String attribute28) { this.attribute28 = attribute28; } public void setAttribute29(String attribute29) { this.attribute29 = attribute29; } public void setAttribute30(String attribute30) { this.attribute30 = attribute30; } public void setAttribute31(String attribute31) { this.attribute31 = attribute31; } public void setAttribute32(String attribute32) { this.attribute32 = attribute32; } public void setAttribute33(String attribute33) { this.attribute33 = attribute33; } public void setAttribute34(String attribute34) { this.attribute34 = attribute34; } public void setAttribute35(String attribute35) { this.attribute35 = attribute35; } public void setAttribute36(String attribute36) { this.attribute36 = attribute36; } public void setAttribute37(String attribute37) { this.attribute37 = attribute37; } public void setAttribute38(String attribute38) { this.attribute38 = attribute38; } public void setAttribute39(String attribute39) { this.attribute39 = attribute39; } public void setAttribute40(String attribute40) { this.attribute40 = attribute40; } public void setAttribute41(String attribute41) { this.attribute41 = attribute41; } public void setAttribute42(String attribute42) { this.attribute42 = attribute42; } public void setAttribute43(String attribute43) { this.attribute43 = attribute43; } public void setAttribute44(String attribute44) { this.attribute44 = attribute44; } public void setAttribute45(Date attribute45) { this.attribute45 = attribute45; } public void setAttribute46(String attribute46) { this.attribute46 = attribute46; } public void setAttribute47(String attribute47) { this.attribute47 = attribute47; } public void setAttribute48(String attribute48) { this.attribute48 = attribute48; } public void setAttribute49(Date attribute49) { this.attribute49 = attribute49; } public void setAttribute50(Date attribute50) { this.attribute50 = attribute50; } public void setAttribute51(Date attribute51) { this.attribute51 = attribute51; } public void setAttribute52(Date attribute52) { this.attribute52 = attribute52; } public void setAttribute53(Date attribute53) { this.attribute53 = attribute53; } public void setAttribute54(String attribute54) { this.attribute54 = attribute54; } public void setAttribute55(String attribute55) { this.attribute55 = attribute55; } public void setAttribute56(String attribute56) { this.attribute56 = attribute56; } public void setAttribute57(String attribute57) { this.attribute57 = attribute57; } public void setAttribute58(String attribute58) { this.attribute58 = attribute58; } public void setAttribute59(String attribute59) { this.attribute59 = attribute59; } public void setAttribute60(String attribute60) { this.attribute60 = attribute60; } public void setAttribute61(String attribute61) { this.attribute61 = attribute61; } public void setAttribute62(String attribute62) { this.attribute62 = attribute62; } public void setAttribute63(String attribute63) { this.attribute63 = attribute63; } public void setAttribute64(String attribute64) { this.attribute64 = attribute64; } public void setAttribute65(String attribute65) { this.attribute65 = attribute65; } public void setAttribute66(String attribute66) { this.attribute66 = attribute66; } public void setAttribute67(String attribute67) { this.attribute67 = attribute67; } public void setAttribute68(String attribute68) { this.attribute68 = attribute68; } public void setAttribute69(String attribute69) { this.attribute69 = attribute69; } public void setAttribute70(String attribute70) { this.attribute70 = attribute70; } public void setAttribute71(String attribute71) { this.attribute71 = attribute71; } public void setAttribute72(String attribute72) { this.attribute72 = attribute72; } public void setAttribute73(String attribute73) { this.attribute73 = attribute73; } public void setAttribute74(String attribute74) { this.attribute74 = attribute74; } public void setAttribute75(String attribute75) { this.attribute75 = attribute75; } public void setAttribute76(String attribute76) { this.attribute76 = attribute76; } public void setAttribute77(String attribute77) { this.attribute77 = attribute77; } public void setAttribute78(String attribute78) { this.attribute78 = attribute78; } public void setAttribute79(Date attribute79) { this.attribute79 = attribute79; } public void setAttribute80(String attribute80) { this.attribute80 = attribute80; } public void setAttribute81(Date attribute81) { this.attribute81 = attribute81; } public void setAttribute82(String attribute82) { this.attribute82 = attribute82; } public void setAttribute83(String attribute83) { this.attribute83 = attribute83; } public void setAttribute84(String attribute84) { this.attribute84 = attribute84; } public void setAttribute85(String attribute85) { this.attribute85 = attribute85; } public void setAttribute86(String attribute86) { this.attribute86 = attribute86; } public void setAttribute87(String attribute87) { this.attribute87 = attribute87; } public void setAttribute88(String attribute88) { this.attribute88 = attribute88; } public void setAttribute89(String attribute89) { this.attribute89 = attribute89; } public void setAttribute90(String attribute90) { this.attribute90 = attribute90; } public void setAttribute91(String attribute91) { this.attribute91 = attribute91; } public void setAttribute92(String attribute92) { this.attribute92 = attribute92; } public void setAttribute93(String attribute93) { this.attribute93 = attribute93; } public void setAttribute94(String attribute94) { this.attribute94 = attribute94; } public void setAttribute95(String attribute95) { this.attribute95 = attribute95; } public void setAttribute96(String attribute96) { this.attribute96 = attribute96; } public void setAttribute97(String attribute97) { this.attribute97 = attribute97; } public void setAttribute98(String attribute98) { this.attribute98 = attribute98; } public void setAttribute99(String attribute99) { this.attribute99 = attribute99; } public void setAttribute100(String attribute100) { this.attribute100 = attribute100; } public void setAttribute101(String attribute101) { this.attribute101 = attribute101; } public void setAttribute102(String attribute102) { this.attribute102 = attribute102; } public void setAttribute103(String attribute103) { this.attribute103 = attribute103; } public void setAttribute104(String attribute104) { this.attribute104 = attribute104; } public void setAttribute105(String attribute105) { this.attribute105 = attribute105; } public void setAttribute106(String attribute106) { this.attribute106 = attribute106; } public void setAttribute107(String attribute107) { this.attribute107 = attribute107; } public void setAttribute108(String attribute108) { this.attribute108 = attribute108; } public void setAttribute109(String attribute109) { this.attribute109 = attribute109; } public void setAttribute110(String attribute110) { this.attribute110 = attribute110; } public void setAttribute111(String attribute111) { this.attribute111 = attribute111; } public void setAttribute112(String attribute112) { this.attribute112 = attribute112; } public void setAttribute113(String attribute113) { this.attribute113 = attribute113; } public void setAttribute117(Date attribute117) { this.attribute117 = attribute117; } public void setAttribute118(String attribute118) { this.attribute118 = attribute118; } public void setAttribute114(Date attribute114) { this.attribute114 = attribute114; } public void setAttribute115(Date attribute115) { this.attribute115 = attribute115; } public void setAttribute116(String attribute116) { this.attribute116 = attribute116; } public void setAttribute119(String attribute119) { this.attribute119 = attribute119; } public void setAttribute120(String attribute120) { this.attribute120 = attribute120; } public void setAttribute121(String attribute121) { this.attribute121 = attribute121; } public void setAttribute122(String attribute122) { this.attribute122 = attribute122; } public void setAttribute123(String attribute123) { this.attribute123 = attribute123; } public void setAttribute124(String attribute124) { this.attribute124 = attribute124; } public void setAttribute125(String attribute125) { this.attribute125 = attribute125; } public void setAttribute126(String attribute126) { this.attribute126 = attribute126; } public void setAttribute127(String attribute127) { this.attribute127 = attribute127; } public void setAttribute128(String attribute128) { this.attribute128 = attribute128; } public void setAttribute129(String attribute129) { this.attribute129 = attribute129; } public void setAttribute130(String attribute130) { this.attribute130 = attribute130; } public void setAttribute131(String attribute131) { this.attribute131 = attribute131; } public void setAttribute132(String attribute132) { this.attribute132 = attribute132; } public void setAttribute133(String attribute133) { this.attribute133 = attribute133; } public void setAttribute134(String attribute134) { this.attribute134 = attribute134; } public void setAttribute135(String attribute135) { this.attribute135 = attribute135; } public void setAttribute136(String attribute136) { this.attribute136 = attribute136; } public void setAttribute137(String attribute137) { this.attribute137 = attribute137; } public void setAttribute138(String attribute138) { this.attribute138 = attribute138; } public void setAttribute139(String attribute139) { this.attribute139 = attribute139; } public void setAttribute140(String attribute140) { this.attribute140 = attribute140; } public void setAttribute141(String attribute141) { this.attribute141 = attribute141; } public void setAttribute142(String attribute142) { this.attribute142 = attribute142; } public void setAttribute143(String attribute143) { this.attribute143 = attribute143; } public void setAttribute144(String attribute144) { this.attribute144 = attribute144; } public void setAttribute145(String attribute145) { this.attribute145 = attribute145; } public void setAttribute146(String attribute146) { this.attribute146 = attribute146; } public void setAttribute147(String attribute147) { this.attribute147 = attribute147; } public void setAttribute148(String attribute148) { this.attribute148 = attribute148; } public void setAttribute149(String attribute149) { this.attribute149 = attribute149; } public void setAttribute150(String attribute150) { this.attribute150 = attribute150; } public void setLastUpBy(String lastUpBy) { this.lastUpBy = lastUpBy; } public void setLastUpDatetime(Date lastUpDatetime) { this.lastUpDatetime = lastUpDatetime; } public void setUpdfilename(String updfilename) { this.updfilename = updfilename; } public void setUpdlineitem(String updlineitem) { this.updlineitem = updlineitem; } public void setLnvproIdentifier(String lnvproIdentifier) { this.lnvproIdentifier = lnvproIdentifier; } public void setUpdateStatus(String updateStatus) { this.updateStatus = updateStatus; } public boolean equals(Object o) { if (o == this) return true;  if (!(o instanceof com.lenovo.iqs.entity.IbTUpdSnRepos)) return false;  com.lenovo.iqs.entity.IbTUpdSnRepos other = (com.lenovo.iqs.entity.IbTUpdSnRepos)o; if (!other.canEqual(this)) return false;  Object this$serialNo = getSerialNo(), other$serialNo = other.getSerialNo(); if ((this$serialNo == null) ? (other$serialNo != null) : !this$serialNo.equals(other$serialNo)) return false;  Object this$requestId = getRequestId(), other$requestId = other.getRequestId(); if ((this$requestId == null) ? (other$requestId != null) : !this$requestId.equals(other$requestId)) return false;  Object this$regionId = getRegionId(), other$regionId = other.getRegionId(); if ((this$regionId == null) ? (other$regionId != null) : !this$regionId.equals(other$regionId)) return false;  Object this$systemId = getSystemId(), other$systemId = other.getSystemId(); if ((this$systemId == null) ? (other$systemId != null) : !this$systemId.equals(other$systemId)) return false;  Object this$createdBy = getCreatedBy(), other$createdBy = other.getCreatedBy(); if ((this$createdBy == null) ? (other$createdBy != null) : !this$createdBy.equals(other$createdBy)) return false;  Object this$creationDatetime = getCreationDatetime(), other$creationDatetime = other.getCreationDatetime(); if ((this$creationDatetime == null) ? (other$creationDatetime != null) : !this$creationDatetime.equals(other$creationDatetime)) return false;  Object this$lastModBy = getLastModBy(), other$lastModBy = other.getLastModBy(); if ((this$lastModBy == null) ? (other$lastModBy != null) : !this$lastModBy.equals(other$lastModBy)) return false;  Object this$lastModDatetime = getLastModDatetime(), other$lastModDatetime = other.getLastModDatetime(); if ((this$lastModDatetime == null) ? (other$lastModDatetime != null) : !this$lastModDatetime.equals(other$lastModDatetime)) return false;  Object this$attribute01 = getAttribute01(), other$attribute01 = other.getAttribute01(); if ((this$attribute01 == null) ? (other$attribute01 != null) : !this$attribute01.equals(other$attribute01)) return false;  Object this$attribute02 = getAttribute02(), other$attribute02 = other.getAttribute02(); if ((this$attribute02 == null) ? (other$attribute02 != null) : !this$attribute02.equals(other$attribute02)) return false;  Object this$attribute03 = getAttribute03(), other$attribute03 = other.getAttribute03(); if ((this$attribute03 == null) ? (other$attribute03 != null) : !this$attribute03.equals(other$attribute03)) return false;  Object this$attribute04 = getAttribute04(), other$attribute04 = other.getAttribute04(); if ((this$attribute04 == null) ? (other$attribute04 != null) : !this$attribute04.equals(other$attribute04)) return false;  Object this$attribute05 = getAttribute05(), other$attribute05 = other.getAttribute05(); if ((this$attribute05 == null) ? (other$attribute05 != null) : !this$attribute05.equals(other$attribute05)) return false;  Object this$attribute06 = getAttribute06(), other$attribute06 = other.getAttribute06(); if ((this$attribute06 == null) ? (other$attribute06 != null) : !this$attribute06.equals(other$attribute06)) return false;  Object this$attribute07 = getAttribute07(), other$attribute07 = other.getAttribute07(); if ((this$attribute07 == null) ? (other$attribute07 != null) : !this$attribute07.equals(other$attribute07)) return false;  Object this$attribute08 = getAttribute08(), other$attribute08 = other.getAttribute08(); if ((this$attribute08 == null) ? (other$attribute08 != null) : !this$attribute08.equals(other$attribute08)) return false;  Object this$attribute09 = getAttribute09(), other$attribute09 = other.getAttribute09(); if ((this$attribute09 == null) ? (other$attribute09 != null) : !this$attribute09.equals(other$attribute09)) return false;  Object this$attribute10 = getAttribute10(), other$attribute10 = other.getAttribute10(); if ((this$attribute10 == null) ? (other$attribute10 != null) : !this$attribute10.equals(other$attribute10)) return false;  Object this$attribute11 = getAttribute11(), other$attribute11 = other.getAttribute11(); if ((this$attribute11 == null) ? (other$attribute11 != null) : !this$attribute11.equals(other$attribute11)) return false;  Object this$attribute12 = getAttribute12(), other$attribute12 = other.getAttribute12(); if ((this$attribute12 == null) ? (other$attribute12 != null) : !this$attribute12.equals(other$attribute12)) return false;  Object this$attribute13 = getAttribute13(), other$attribute13 = other.getAttribute13(); if ((this$attribute13 == null) ? (other$attribute13 != null) : !this$attribute13.equals(other$attribute13)) return false;  Object this$attribute14 = getAttribute14(), other$attribute14 = other.getAttribute14(); if ((this$attribute14 == null) ? (other$attribute14 != null) : !this$attribute14.equals(other$attribute14)) return false;  Object this$attribute15 = getAttribute15(), other$attribute15 = other.getAttribute15(); if ((this$attribute15 == null) ? (other$attribute15 != null) : !this$attribute15.equals(other$attribute15)) return false;  Object this$attribute16 = getAttribute16(), other$attribute16 = other.getAttribute16(); if ((this$attribute16 == null) ? (other$attribute16 != null) : !this$attribute16.equals(other$attribute16)) return false;  Object this$attribute17 = getAttribute17(), other$attribute17 = other.getAttribute17(); if ((this$attribute17 == null) ? (other$attribute17 != null) : !this$attribute17.equals(other$attribute17)) return false;  Object this$attribute18 = getAttribute18(), other$attribute18 = other.getAttribute18(); if ((this$attribute18 == null) ? (other$attribute18 != null) : !this$attribute18.equals(other$attribute18)) return false;  Object this$attribute19 = getAttribute19(), other$attribute19 = other.getAttribute19(); if ((this$attribute19 == null) ? (other$attribute19 != null) : !this$attribute19.equals(other$attribute19)) return false;  Object this$attribute20 = getAttribute20(), other$attribute20 = other.getAttribute20(); if ((this$attribute20 == null) ? (other$attribute20 != null) : !this$attribute20.equals(other$attribute20)) return false;  Object this$attribute21 = getAttribute21(), other$attribute21 = other.getAttribute21(); if ((this$attribute21 == null) ? (other$attribute21 != null) : !this$attribute21.equals(other$attribute21)) return false;  Object this$attribute22 = getAttribute22(), other$attribute22 = other.getAttribute22(); if ((this$attribute22 == null) ? (other$attribute22 != null) : !this$attribute22.equals(other$attribute22)) return false;  Object this$attribute23 = getAttribute23(), other$attribute23 = other.getAttribute23(); if ((this$attribute23 == null) ? (other$attribute23 != null) : !this$attribute23.equals(other$attribute23)) return false;  Object this$attribute24 = getAttribute24(), other$attribute24 = other.getAttribute24(); if ((this$attribute24 == null) ? (other$attribute24 != null) : !this$attribute24.equals(other$attribute24)) return false;  Object this$attribute25 = getAttribute25(), other$attribute25 = other.getAttribute25(); if ((this$attribute25 == null) ? (other$attribute25 != null) : !this$attribute25.equals(other$attribute25)) return false;  Object this$attribute26 = getAttribute26(), other$attribute26 = other.getAttribute26(); if ((this$attribute26 == null) ? (other$attribute26 != null) : !this$attribute26.equals(other$attribute26)) return false;  Object this$attribute27 = getAttribute27(), other$attribute27 = other.getAttribute27(); if ((this$attribute27 == null) ? (other$attribute27 != null) : !this$attribute27.equals(other$attribute27)) return false;  Object this$attribute28 = getAttribute28(), other$attribute28 = other.getAttribute28(); if ((this$attribute28 == null) ? (other$attribute28 != null) : !this$attribute28.equals(other$attribute28)) return false;  Object this$attribute29 = getAttribute29(), other$attribute29 = other.getAttribute29(); if ((this$attribute29 == null) ? (other$attribute29 != null) : !this$attribute29.equals(other$attribute29)) return false;  Object this$attribute30 = getAttribute30(), other$attribute30 = other.getAttribute30(); if ((this$attribute30 == null) ? (other$attribute30 != null) : !this$attribute30.equals(other$attribute30)) return false;  Object this$attribute31 = getAttribute31(), other$attribute31 = other.getAttribute31(); if ((this$attribute31 == null) ? (other$attribute31 != null) : !this$attribute31.equals(other$attribute31)) return false;  Object this$attribute32 = getAttribute32(), other$attribute32 = other.getAttribute32(); if ((this$attribute32 == null) ? (other$attribute32 != null) : !this$attribute32.equals(other$attribute32)) return false;  Object this$attribute33 = getAttribute33(), other$attribute33 = other.getAttribute33(); if ((this$attribute33 == null) ? (other$attribute33 != null) : !this$attribute33.equals(other$attribute33)) return false;  Object this$attribute34 = getAttribute34(), other$attribute34 = other.getAttribute34(); if ((this$attribute34 == null) ? (other$attribute34 != null) : !this$attribute34.equals(other$attribute34)) return false;  Object this$attribute35 = getAttribute35(), other$attribute35 = other.getAttribute35(); if ((this$attribute35 == null) ? (other$attribute35 != null) : !this$attribute35.equals(other$attribute35)) return false;  Object this$attribute36 = getAttribute36(), other$attribute36 = other.getAttribute36(); if ((this$attribute36 == null) ? (other$attribute36 != null) : !this$attribute36.equals(other$attribute36)) return false;  Object this$attribute37 = getAttribute37(), other$attribute37 = other.getAttribute37(); if ((this$attribute37 == null) ? (other$attribute37 != null) : !this$attribute37.equals(other$attribute37)) return false;  Object this$attribute38 = getAttribute38(), other$attribute38 = other.getAttribute38(); if ((this$attribute38 == null) ? (other$attribute38 != null) : !this$attribute38.equals(other$attribute38)) return false;  Object this$attribute39 = getAttribute39(), other$attribute39 = other.getAttribute39(); if ((this$attribute39 == null) ? (other$attribute39 != null) : !this$attribute39.equals(other$attribute39)) return false;  Object this$attribute40 = getAttribute40(), other$attribute40 = other.getAttribute40(); if ((this$attribute40 == null) ? (other$attribute40 != null) : !this$attribute40.equals(other$attribute40)) return false;  Object this$attribute41 = getAttribute41(), other$attribute41 = other.getAttribute41(); if ((this$attribute41 == null) ? (other$attribute41 != null) : !this$attribute41.equals(other$attribute41)) return false;  Object this$attribute42 = getAttribute42(), other$attribute42 = other.getAttribute42(); if ((this$attribute42 == null) ? (other$attribute42 != null) : !this$attribute42.equals(other$attribute42)) return false;  Object this$attribute43 = getAttribute43(), other$attribute43 = other.getAttribute43(); if ((this$attribute43 == null) ? (other$attribute43 != null) : !this$attribute43.equals(other$attribute43)) return false;  Object this$attribute44 = getAttribute44(), other$attribute44 = other.getAttribute44(); if ((this$attribute44 == null) ? (other$attribute44 != null) : !this$attribute44.equals(other$attribute44)) return false;  Object this$attribute45 = getAttribute45(), other$attribute45 = other.getAttribute45(); if ((this$attribute45 == null) ? (other$attribute45 != null) : !this$attribute45.equals(other$attribute45)) return false;  Object this$attribute46 = getAttribute46(), other$attribute46 = other.getAttribute46(); if ((this$attribute46 == null) ? (other$attribute46 != null) : !this$attribute46.equals(other$attribute46)) return false;  Object this$attribute47 = getAttribute47(), other$attribute47 = other.getAttribute47(); if ((this$attribute47 == null) ? (other$attribute47 != null) : !this$attribute47.equals(other$attribute47)) return false;  Object this$attribute48 = getAttribute48(), other$attribute48 = other.getAttribute48(); if ((this$attribute48 == null) ? (other$attribute48 != null) : !this$attribute48.equals(other$attribute48)) return false;  Object this$attribute49 = getAttribute49(), other$attribute49 = other.getAttribute49(); if ((this$attribute49 == null) ? (other$attribute49 != null) : !this$attribute49.equals(other$attribute49)) return false;  Object this$attribute50 = getAttribute50(), other$attribute50 = other.getAttribute50(); if ((this$attribute50 == null) ? (other$attribute50 != null) : !this$attribute50.equals(other$attribute50)) return false;  Object this$attribute51 = getAttribute51(), other$attribute51 = other.getAttribute51(); if ((this$attribute51 == null) ? (other$attribute51 != null) : !this$attribute51.equals(other$attribute51)) return false;  Object this$attribute52 = getAttribute52(), other$attribute52 = other.getAttribute52(); if ((this$attribute52 == null) ? (other$attribute52 != null) : !this$attribute52.equals(other$attribute52)) return false;  Object this$attribute53 = getAttribute53(), other$attribute53 = other.getAttribute53(); if ((this$attribute53 == null) ? (other$attribute53 != null) : !this$attribute53.equals(other$attribute53)) return false;  Object this$attribute54 = getAttribute54(), other$attribute54 = other.getAttribute54(); if ((this$attribute54 == null) ? (other$attribute54 != null) : !this$attribute54.equals(other$attribute54)) return false;  Object this$attribute55 = getAttribute55(), other$attribute55 = other.getAttribute55(); if ((this$attribute55 == null) ? (other$attribute55 != null) : !this$attribute55.equals(other$attribute55)) return false;  Object this$attribute56 = getAttribute56(), other$attribute56 = other.getAttribute56(); if ((this$attribute56 == null) ? (other$attribute56 != null) : !this$attribute56.equals(other$attribute56)) return false;  Object this$attribute57 = getAttribute57(), other$attribute57 = other.getAttribute57(); if ((this$attribute57 == null) ? (other$attribute57 != null) : !this$attribute57.equals(other$attribute57)) return false;  Object this$attribute58 = getAttribute58(), other$attribute58 = other.getAttribute58(); if ((this$attribute58 == null) ? (other$attribute58 != null) : !this$attribute58.equals(other$attribute58)) return false;  Object this$attribute59 = getAttribute59(), other$attribute59 = other.getAttribute59(); if ((this$attribute59 == null) ? (other$attribute59 != null) : !this$attribute59.equals(other$attribute59)) return false;  Object this$attribute60 = getAttribute60(), other$attribute60 = other.getAttribute60(); if ((this$attribute60 == null) ? (other$attribute60 != null) : !this$attribute60.equals(other$attribute60)) return false;  Object this$attribute61 = getAttribute61(), other$attribute61 = other.getAttribute61(); if ((this$attribute61 == null) ? (other$attribute61 != null) : !this$attribute61.equals(other$attribute61)) return false;  Object this$attribute62 = getAttribute62(), other$attribute62 = other.getAttribute62(); if ((this$attribute62 == null) ? (other$attribute62 != null) : !this$attribute62.equals(other$attribute62)) return false;  Object this$attribute63 = getAttribute63(), other$attribute63 = other.getAttribute63(); if ((this$attribute63 == null) ? (other$attribute63 != null) : !this$attribute63.equals(other$attribute63)) return false;  Object this$attribute64 = getAttribute64(), other$attribute64 = other.getAttribute64(); if ((this$attribute64 == null) ? (other$attribute64 != null) : !this$attribute64.equals(other$attribute64)) return false;  Object this$attribute65 = getAttribute65(), other$attribute65 = other.getAttribute65(); if ((this$attribute65 == null) ? (other$attribute65 != null) : !this$attribute65.equals(other$attribute65)) return false;  Object this$attribute66 = getAttribute66(), other$attribute66 = other.getAttribute66(); if ((this$attribute66 == null) ? (other$attribute66 != null) : !this$attribute66.equals(other$attribute66)) return false;  Object this$attribute67 = getAttribute67(), other$attribute67 = other.getAttribute67(); if ((this$attribute67 == null) ? (other$attribute67 != null) : !this$attribute67.equals(other$attribute67)) return false;  Object this$attribute68 = getAttribute68(), other$attribute68 = other.getAttribute68(); if ((this$attribute68 == null) ? (other$attribute68 != null) : !this$attribute68.equals(other$attribute68)) return false;  Object this$attribute69 = getAttribute69(), other$attribute69 = other.getAttribute69(); if ((this$attribute69 == null) ? (other$attribute69 != null) : !this$attribute69.equals(other$attribute69)) return false;  Object this$attribute70 = getAttribute70(), other$attribute70 = other.getAttribute70(); if ((this$attribute70 == null) ? (other$attribute70 != null) : !this$attribute70.equals(other$attribute70)) return false;  Object this$attribute71 = getAttribute71(), other$attribute71 = other.getAttribute71(); if ((this$attribute71 == null) ? (other$attribute71 != null) : !this$attribute71.equals(other$attribute71)) return false;  Object this$attribute72 = getAttribute72(), other$attribute72 = other.getAttribute72(); if ((this$attribute72 == null) ? (other$attribute72 != null) : !this$attribute72.equals(other$attribute72)) return false;  Object this$attribute73 = getAttribute73(), other$attribute73 = other.getAttribute73(); if ((this$attribute73 == null) ? (other$attribute73 != null) : !this$attribute73.equals(other$attribute73)) return false;  Object this$attribute74 = getAttribute74(), other$attribute74 = other.getAttribute74(); if ((this$attribute74 == null) ? (other$attribute74 != null) : !this$attribute74.equals(other$attribute74)) return false;  Object this$attribute75 = getAttribute75(), other$attribute75 = other.getAttribute75(); if ((this$attribute75 == null) ? (other$attribute75 != null) : !this$attribute75.equals(other$attribute75)) return false;  Object this$attribute76 = getAttribute76(), other$attribute76 = other.getAttribute76(); if ((this$attribute76 == null) ? (other$attribute76 != null) : !this$attribute76.equals(other$attribute76)) return false;  Object this$attribute77 = getAttribute77(), other$attribute77 = other.getAttribute77(); if ((this$attribute77 == null) ? (other$attribute77 != null) : !this$attribute77.equals(other$attribute77)) return false;  Object this$attribute78 = getAttribute78(), other$attribute78 = other.getAttribute78(); if ((this$attribute78 == null) ? (other$attribute78 != null) : !this$attribute78.equals(other$attribute78)) return false;  Object this$attribute79 = getAttribute79(), other$attribute79 = other.getAttribute79(); if ((this$attribute79 == null) ? (other$attribute79 != null) : !this$attribute79.equals(other$attribute79)) return false;  Object this$attribute80 = getAttribute80(), other$attribute80 = other.getAttribute80(); if ((this$attribute80 == null) ? (other$attribute80 != null) : !this$attribute80.equals(other$attribute80)) return false;  Object this$attribute81 = getAttribute81(), other$attribute81 = other.getAttribute81(); if ((this$attribute81 == null) ? (other$attribute81 != null) : !this$attribute81.equals(other$attribute81)) return false;  Object this$attribute82 = getAttribute82(), other$attribute82 = other.getAttribute82(); if ((this$attribute82 == null) ? (other$attribute82 != null) : !this$attribute82.equals(other$attribute82)) return false;  Object this$attribute83 = getAttribute83(), other$attribute83 = other.getAttribute83(); if ((this$attribute83 == null) ? (other$attribute83 != null) : !this$attribute83.equals(other$attribute83)) return false;  Object this$attribute84 = getAttribute84(), other$attribute84 = other.getAttribute84(); if ((this$attribute84 == null) ? (other$attribute84 != null) : !this$attribute84.equals(other$attribute84)) return false;  Object this$attribute85 = getAttribute85(), other$attribute85 = other.getAttribute85(); if ((this$attribute85 == null) ? (other$attribute85 != null) : !this$attribute85.equals(other$attribute85)) return false;  Object this$attribute86 = getAttribute86(), other$attribute86 = other.getAttribute86(); if ((this$attribute86 == null) ? (other$attribute86 != null) : !this$attribute86.equals(other$attribute86)) return false;  Object this$attribute87 = getAttribute87(), other$attribute87 = other.getAttribute87(); if ((this$attribute87 == null) ? (other$attribute87 != null) : !this$attribute87.equals(other$attribute87)) return false;  Object this$attribute88 = getAttribute88(), other$attribute88 = other.getAttribute88(); if ((this$attribute88 == null) ? (other$attribute88 != null) : !this$attribute88.equals(other$attribute88)) return false;  Object this$attribute89 = getAttribute89(), other$attribute89 = other.getAttribute89(); if ((this$attribute89 == null) ? (other$attribute89 != null) : !this$attribute89.equals(other$attribute89)) return false;  Object this$attribute90 = getAttribute90(), other$attribute90 = other.getAttribute90(); if ((this$attribute90 == null) ? (other$attribute90 != null) : !this$attribute90.equals(other$attribute90)) return false;  Object this$attribute91 = getAttribute91(), other$attribute91 = other.getAttribute91(); if ((this$attribute91 == null) ? (other$attribute91 != null) : !this$attribute91.equals(other$attribute91)) return false;  Object this$attribute92 = getAttribute92(), other$attribute92 = other.getAttribute92(); if ((this$attribute92 == null) ? (other$attribute92 != null) : !this$attribute92.equals(other$attribute92)) return false;  Object this$attribute93 = getAttribute93(), other$attribute93 = other.getAttribute93(); if ((this$attribute93 == null) ? (other$attribute93 != null) : !this$attribute93.equals(other$attribute93)) return false;  Object this$attribute94 = getAttribute94(), other$attribute94 = other.getAttribute94(); if ((this$attribute94 == null) ? (other$attribute94 != null) : !this$attribute94.equals(other$attribute94)) return false;  Object this$attribute95 = getAttribute95(), other$attribute95 = other.getAttribute95(); if ((this$attribute95 == null) ? (other$attribute95 != null) : !this$attribute95.equals(other$attribute95)) return false;  Object this$attribute96 = getAttribute96(), other$attribute96 = other.getAttribute96(); if ((this$attribute96 == null) ? (other$attribute96 != null) : !this$attribute96.equals(other$attribute96)) return false;  Object this$attribute97 = getAttribute97(), other$attribute97 = other.getAttribute97(); if ((this$attribute97 == null) ? (other$attribute97 != null) : !this$attribute97.equals(other$attribute97)) return false;  Object this$attribute98 = getAttribute98(), other$attribute98 = other.getAttribute98(); if ((this$attribute98 == null) ? (other$attribute98 != null) : !this$attribute98.equals(other$attribute98)) return false;  Object this$attribute99 = getAttribute99(), other$attribute99 = other.getAttribute99(); if ((this$attribute99 == null) ? (other$attribute99 != null) : !this$attribute99.equals(other$attribute99)) return false;  Object this$attribute100 = getAttribute100(), other$attribute100 = other.getAttribute100(); if ((this$attribute100 == null) ? (other$attribute100 != null) : !this$attribute100.equals(other$attribute100)) return false;  Object this$attribute101 = getAttribute101(), other$attribute101 = other.getAttribute101(); if ((this$attribute101 == null) ? (other$attribute101 != null) : !this$attribute101.equals(other$attribute101)) return false;  Object this$attribute102 = getAttribute102(), other$attribute102 = other.getAttribute102(); if ((this$attribute102 == null) ? (other$attribute102 != null) : !this$attribute102.equals(other$attribute102)) return false;  Object this$attribute103 = getAttribute103(), other$attribute103 = other.getAttribute103(); if ((this$attribute103 == null) ? (other$attribute103 != null) : !this$attribute103.equals(other$attribute103)) return false;  Object this$attribute104 = getAttribute104(), other$attribute104 = other.getAttribute104(); if ((this$attribute104 == null) ? (other$attribute104 != null) : !this$attribute104.equals(other$attribute104)) return false;  Object this$attribute105 = getAttribute105(), other$attribute105 = other.getAttribute105(); if ((this$attribute105 == null) ? (other$attribute105 != null) : !this$attribute105.equals(other$attribute105)) return false;  Object this$attribute106 = getAttribute106(), other$attribute106 = other.getAttribute106(); if ((this$attribute106 == null) ? (other$attribute106 != null) : !this$attribute106.equals(other$attribute106)) return false;  Object this$attribute107 = getAttribute107(), other$attribute107 = other.getAttribute107(); if ((this$attribute107 == null) ? (other$attribute107 != null) : !this$attribute107.equals(other$attribute107)) return false;  Object this$attribute108 = getAttribute108(), other$attribute108 = other.getAttribute108(); if ((this$attribute108 == null) ? (other$attribute108 != null) : !this$attribute108.equals(other$attribute108)) return false;  Object this$attribute109 = getAttribute109(), other$attribute109 = other.getAttribute109(); if ((this$attribute109 == null) ? (other$attribute109 != null) : !this$attribute109.equals(other$attribute109)) return false;  Object this$attribute110 = getAttribute110(), other$attribute110 = other.getAttribute110(); if ((this$attribute110 == null) ? (other$attribute110 != null) : !this$attribute110.equals(other$attribute110)) return false;  Object this$attribute111 = getAttribute111(), other$attribute111 = other.getAttribute111(); if ((this$attribute111 == null) ? (other$attribute111 != null) : !this$attribute111.equals(other$attribute111)) return false;  Object this$attribute112 = getAttribute112(), other$attribute112 = other.getAttribute112(); if ((this$attribute112 == null) ? (other$attribute112 != null) : !this$attribute112.equals(other$attribute112)) return false;  Object this$attribute113 = getAttribute113(), other$attribute113 = other.getAttribute113(); if ((this$attribute113 == null) ? (other$attribute113 != null) : !this$attribute113.equals(other$attribute113)) return false;  Object this$attribute117 = getAttribute117(), other$attribute117 = other.getAttribute117(); if ((this$attribute117 == null) ? (other$attribute117 != null) : !this$attribute117.equals(other$attribute117)) return false;  Object this$attribute118 = getAttribute118(), other$attribute118 = other.getAttribute118(); if ((this$attribute118 == null) ? (other$attribute118 != null) : !this$attribute118.equals(other$attribute118)) return false;  Object this$attribute114 = getAttribute114(), other$attribute114 = other.getAttribute114(); if ((this$attribute114 == null) ? (other$attribute114 != null) : !this$attribute114.equals(other$attribute114)) return false;  Object this$attribute115 = getAttribute115(), other$attribute115 = other.getAttribute115(); if ((this$attribute115 == null) ? (other$attribute115 != null) : !this$attribute115.equals(other$attribute115)) return false;  Object this$attribute116 = getAttribute116(), other$attribute116 = other.getAttribute116(); if ((this$attribute116 == null) ? (other$attribute116 != null) : !this$attribute116.equals(other$attribute116)) return false;  Object this$attribute119 = getAttribute119(), other$attribute119 = other.getAttribute119(); if ((this$attribute119 == null) ? (other$attribute119 != null) : !this$attribute119.equals(other$attribute119)) return false;  Object this$attribute120 = getAttribute120(), other$attribute120 = other.getAttribute120(); if ((this$attribute120 == null) ? (other$attribute120 != null) : !this$attribute120.equals(other$attribute120)) return false;  Object this$attribute121 = getAttribute121(), other$attribute121 = other.getAttribute121(); if ((this$attribute121 == null) ? (other$attribute121 != null) : !this$attribute121.equals(other$attribute121)) return false;  Object this$attribute122 = getAttribute122(), other$attribute122 = other.getAttribute122(); if ((this$attribute122 == null) ? (other$attribute122 != null) : !this$attribute122.equals(other$attribute122)) return false;  Object this$attribute123 = getAttribute123(), other$attribute123 = other.getAttribute123(); if ((this$attribute123 == null) ? (other$attribute123 != null) : !this$attribute123.equals(other$attribute123)) return false;  Object this$attribute124 = getAttribute124(), other$attribute124 = other.getAttribute124(); if ((this$attribute124 == null) ? (other$attribute124 != null) : !this$attribute124.equals(other$attribute124)) return false;  Object this$attribute125 = getAttribute125(), other$attribute125 = other.getAttribute125(); if ((this$attribute125 == null) ? (other$attribute125 != null) : !this$attribute125.equals(other$attribute125)) return false;  Object this$attribute126 = getAttribute126(), other$attribute126 = other.getAttribute126(); if ((this$attribute126 == null) ? (other$attribute126 != null) : !this$attribute126.equals(other$attribute126)) return false;  Object this$attribute127 = getAttribute127(), other$attribute127 = other.getAttribute127(); if ((this$attribute127 == null) ? (other$attribute127 != null) : !this$attribute127.equals(other$attribute127)) return false;  Object this$attribute128 = getAttribute128(), other$attribute128 = other.getAttribute128(); if ((this$attribute128 == null) ? (other$attribute128 != null) : !this$attribute128.equals(other$attribute128)) return false;  Object this$attribute129 = getAttribute129(), other$attribute129 = other.getAttribute129(); if ((this$attribute129 == null) ? (other$attribute129 != null) : !this$attribute129.equals(other$attribute129)) return false;  Object this$attribute130 = getAttribute130(), other$attribute130 = other.getAttribute130(); if ((this$attribute130 == null) ? (other$attribute130 != null) : !this$attribute130.equals(other$attribute130)) return false;  Object this$attribute131 = getAttribute131(), other$attribute131 = other.getAttribute131(); if ((this$attribute131 == null) ? (other$attribute131 != null) : !this$attribute131.equals(other$attribute131)) return false;  Object this$attribute132 = getAttribute132(), other$attribute132 = other.getAttribute132(); if ((this$attribute132 == null) ? (other$attribute132 != null) : !this$attribute132.equals(other$attribute132)) return false;  Object this$attribute133 = getAttribute133(), other$attribute133 = other.getAttribute133(); if ((this$attribute133 == null) ? (other$attribute133 != null) : !this$attribute133.equals(other$attribute133)) return false;  Object this$attribute134 = getAttribute134(), other$attribute134 = other.getAttribute134(); if ((this$attribute134 == null) ? (other$attribute134 != null) : !this$attribute134.equals(other$attribute134)) return false;  Object this$attribute135 = getAttribute135(), other$attribute135 = other.getAttribute135(); if ((this$attribute135 == null) ? (other$attribute135 != null) : !this$attribute135.equals(other$attribute135)) return false;  Object this$attribute136 = getAttribute136(), other$attribute136 = other.getAttribute136(); if ((this$attribute136 == null) ? (other$attribute136 != null) : !this$attribute136.equals(other$attribute136)) return false;  Object this$attribute137 = getAttribute137(), other$attribute137 = other.getAttribute137(); if ((this$attribute137 == null) ? (other$attribute137 != null) : !this$attribute137.equals(other$attribute137)) return false;  Object this$attribute138 = getAttribute138(), other$attribute138 = other.getAttribute138(); if ((this$attribute138 == null) ? (other$attribute138 != null) : !this$attribute138.equals(other$attribute138)) return false;  Object this$attribute139 = getAttribute139(), other$attribute139 = other.getAttribute139(); if ((this$attribute139 == null) ? (other$attribute139 != null) : !this$attribute139.equals(other$attribute139)) return false;  Object this$attribute140 = getAttribute140(), other$attribute140 = other.getAttribute140(); if ((this$attribute140 == null) ? (other$attribute140 != null) : !this$attribute140.equals(other$attribute140)) return false;  Object this$attribute141 = getAttribute141(), other$attribute141 = other.getAttribute141(); if ((this$attribute141 == null) ? (other$attribute141 != null) : !this$attribute141.equals(other$attribute141)) return false;  Object this$attribute142 = getAttribute142(), other$attribute142 = other.getAttribute142(); if ((this$attribute142 == null) ? (other$attribute142 != null) : !this$attribute142.equals(other$attribute142)) return false;  Object this$attribute143 = getAttribute143(), other$attribute143 = other.getAttribute143(); if ((this$attribute143 == null) ? (other$attribute143 != null) : !this$attribute143.equals(other$attribute143)) return false;  Object this$attribute144 = getAttribute144(), other$attribute144 = other.getAttribute144(); if ((this$attribute144 == null) ? (other$attribute144 != null) : !this$attribute144.equals(other$attribute144)) return false;  Object this$attribute145 = getAttribute145(), other$attribute145 = other.getAttribute145(); if ((this$attribute145 == null) ? (other$attribute145 != null) : !this$attribute145.equals(other$attribute145)) return false;  Object this$attribute146 = getAttribute146(), other$attribute146 = other.getAttribute146(); if ((this$attribute146 == null) ? (other$attribute146 != null) : !this$attribute146.equals(other$attribute146)) return false;  Object this$attribute147 = getAttribute147(), other$attribute147 = other.getAttribute147(); if ((this$attribute147 == null) ? (other$attribute147 != null) : !this$attribute147.equals(other$attribute147)) return false;  Object this$attribute148 = getAttribute148(), other$attribute148 = other.getAttribute148(); if ((this$attribute148 == null) ? (other$attribute148 != null) : !this$attribute148.equals(other$attribute148)) return false;  Object this$attribute149 = getAttribute149(), other$attribute149 = other.getAttribute149(); if ((this$attribute149 == null) ? (other$attribute149 != null) : !this$attribute149.equals(other$attribute149)) return false;  Object this$attribute150 = getAttribute150(), other$attribute150 = other.getAttribute150(); if ((this$attribute150 == null) ? (other$attribute150 != null) : !this$attribute150.equals(other$attribute150)) return false;  Object this$lastUpBy = getLastUpBy(), other$lastUpBy = other.getLastUpBy(); if ((this$lastUpBy == null) ? (other$lastUpBy != null) : !this$lastUpBy.equals(other$lastUpBy)) return false;  Object this$lastUpDatetime = getLastUpDatetime(), other$lastUpDatetime = other.getLastUpDatetime(); if ((this$lastUpDatetime == null) ? (other$lastUpDatetime != null) : !this$lastUpDatetime.equals(other$lastUpDatetime)) return false;  Object this$updfilename = getUpdfilename(), other$updfilename = other.getUpdfilename(); if ((this$updfilename == null) ? (other$updfilename != null) : !this$updfilename.equals(other$updfilename)) return false;  Object this$updlineitem = getUpdlineitem(), other$updlineitem = other.getUpdlineitem(); if ((this$updlineitem == null) ? (other$updlineitem != null) : !this$updlineitem.equals(other$updlineitem)) return false;  Object this$lnvproIdentifier = getLnvproIdentifier(), other$lnvproIdentifier = other.getLnvproIdentifier(); if ((this$lnvproIdentifier == null) ? (other$lnvproIdentifier != null) : !this$lnvproIdentifier.equals(other$lnvproIdentifier)) return false;  Object this$updateStatus = getUpdateStatus(), other$updateStatus = other.getUpdateStatus(); return !((this$updateStatus == null) ? (other$updateStatus != null) : !this$updateStatus.equals(other$updateStatus)); } protected boolean canEqual(Object other) { return other instanceof com.lenovo.iqs.entity.IbTUpdSnRepos; } public int hashCode() { int PRIME = 59; result = 1; Object $serialNo = getSerialNo(); result = result * 59 + (($serialNo == null) ? 43 : $serialNo.hashCode()); Object $requestId = getRequestId(); result = result * 59 + (($requestId == null) ? 43 : $requestId.hashCode()); Object $regionId = getRegionId(); result = result * 59 + (($regionId == null) ? 43 : $regionId.hashCode()); Object $systemId = getSystemId(); result = result * 59 + (($systemId == null) ? 43 : $systemId.hashCode()); Object $createdBy = getCreatedBy(); result = result * 59 + (($createdBy == null) ? 43 : $createdBy.hashCode()); Object $creationDatetime = getCreationDatetime(); result = result * 59 + (($creationDatetime == null) ? 43 : $creationDatetime.hashCode()); Object $lastModBy = getLastModBy(); result = result * 59 + (($lastModBy == null) ? 43 : $lastModBy.hashCode()); Object $lastModDatetime = getLastModDatetime(); result = result * 59 + (($lastModDatetime == null) ? 43 : $lastModDatetime.hashCode()); Object $attribute01 = getAttribute01(); result = result * 59 + (($attribute01 == null) ? 43 : $attribute01.hashCode()); Object $attribute02 = getAttribute02(); result = result * 59 + (($attribute02 == null) ? 43 : $attribute02.hashCode()); Object $attribute03 = getAttribute03(); result = result * 59 + (($attribute03 == null) ? 43 : $attribute03.hashCode()); Object $attribute04 = getAttribute04(); result = result * 59 + (($attribute04 == null) ? 43 : $attribute04.hashCode()); Object $attribute05 = getAttribute05(); result = result * 59 + (($attribute05 == null) ? 43 : $attribute05.hashCode()); Object $attribute06 = getAttribute06(); result = result * 59 + (($attribute06 == null) ? 43 : $attribute06.hashCode()); Object $attribute07 = getAttribute07(); result = result * 59 + (($attribute07 == null) ? 43 : $attribute07.hashCode()); Object $attribute08 = getAttribute08(); result = result * 59 + (($attribute08 == null) ? 43 : $attribute08.hashCode()); Object $attribute09 = getAttribute09(); result = result * 59 + (($attribute09 == null) ? 43 : $attribute09.hashCode()); Object $attribute10 = getAttribute10(); result = result * 59 + (($attribute10 == null) ? 43 : $attribute10.hashCode()); Object $attribute11 = getAttribute11(); result = result * 59 + (($attribute11 == null) ? 43 : $attribute11.hashCode()); Object $attribute12 = getAttribute12(); result = result * 59 + (($attribute12 == null) ? 43 : $attribute12.hashCode()); Object $attribute13 = getAttribute13(); result = result * 59 + (($attribute13 == null) ? 43 : $attribute13.hashCode()); Object $attribute14 = getAttribute14(); result = result * 59 + (($attribute14 == null) ? 43 : $attribute14.hashCode()); Object $attribute15 = getAttribute15(); result = result * 59 + (($attribute15 == null) ? 43 : $attribute15.hashCode()); Object $attribute16 = getAttribute16(); result = result * 59 + (($attribute16 == null) ? 43 : $attribute16.hashCode()); Object $attribute17 = getAttribute17(); result = result * 59 + (($attribute17 == null) ? 43 : $attribute17.hashCode()); Object $attribute18 = getAttribute18(); result = result * 59 + (($attribute18 == null) ? 43 : $attribute18.hashCode()); Object $attribute19 = getAttribute19(); result = result * 59 + (($attribute19 == null) ? 43 : $attribute19.hashCode()); Object $attribute20 = getAttribute20(); result = result * 59 + (($attribute20 == null) ? 43 : $attribute20.hashCode()); Object $attribute21 = getAttribute21(); result = result * 59 + (($attribute21 == null) ? 43 : $attribute21.hashCode()); Object $attribute22 = getAttribute22(); result = result * 59 + (($attribute22 == null) ? 43 : $attribute22.hashCode()); Object $attribute23 = getAttribute23(); result = result * 59 + (($attribute23 == null) ? 43 : $attribute23.hashCode()); Object $attribute24 = getAttribute24(); result = result * 59 + (($attribute24 == null) ? 43 : $attribute24.hashCode()); Object $attribute25 = getAttribute25(); result = result * 59 + (($attribute25 == null) ? 43 : $attribute25.hashCode()); Object $attribute26 = getAttribute26(); result = result * 59 + (($attribute26 == null) ? 43 : $attribute26.hashCode()); Object $attribute27 = getAttribute27(); result = result * 59 + (($attribute27 == null) ? 43 : $attribute27.hashCode()); Object $attribute28 = getAttribute28(); result = result * 59 + (($attribute28 == null) ? 43 : $attribute28.hashCode()); Object $attribute29 = getAttribute29(); result = result * 59 + (($attribute29 == null) ? 43 : $attribute29.hashCode()); Object $attribute30 = getAttribute30(); result = result * 59 + (($attribute30 == null) ? 43 : $attribute30.hashCode()); Object $attribute31 = getAttribute31(); result = result * 59 + (($attribute31 == null) ? 43 : $attribute31.hashCode()); Object $attribute32 = getAttribute32(); result = result * 59 + (($attribute32 == null) ? 43 : $attribute32.hashCode()); Object $attribute33 = getAttribute33(); result = result * 59 + (($attribute33 == null) ? 43 : $attribute33.hashCode()); Object $attribute34 = getAttribute34(); result = result * 59 + (($attribute34 == null) ? 43 : $attribute34.hashCode()); Object $attribute35 = getAttribute35(); result = result * 59 + (($attribute35 == null) ? 43 : $attribute35.hashCode()); Object $attribute36 = getAttribute36(); result = result * 59 + (($attribute36 == null) ? 43 : $attribute36.hashCode()); Object $attribute37 = getAttribute37(); result = result * 59 + (($attribute37 == null) ? 43 : $attribute37.hashCode()); Object $attribute38 = getAttribute38(); result = result * 59 + (($attribute38 == null) ? 43 : $attribute38.hashCode()); Object $attribute39 = getAttribute39(); result = result * 59 + (($attribute39 == null) ? 43 : $attribute39.hashCode()); Object $attribute40 = getAttribute40(); result = result * 59 + (($attribute40 == null) ? 43 : $attribute40.hashCode()); Object $attribute41 = getAttribute41(); result = result * 59 + (($attribute41 == null) ? 43 : $attribute41.hashCode()); Object $attribute42 = getAttribute42(); result = result * 59 + (($attribute42 == null) ? 43 : $attribute42.hashCode()); Object $attribute43 = getAttribute43(); result = result * 59 + (($attribute43 == null) ? 43 : $attribute43.hashCode()); Object $attribute44 = getAttribute44(); result = result * 59 + (($attribute44 == null) ? 43 : $attribute44.hashCode()); Object $attribute45 = getAttribute45(); result = result * 59 + (($attribute45 == null) ? 43 : $attribute45.hashCode()); Object $attribute46 = getAttribute46(); result = result * 59 + (($attribute46 == null) ? 43 : $attribute46.hashCode()); Object $attribute47 = getAttribute47(); result = result * 59 + (($attribute47 == null) ? 43 : $attribute47.hashCode()); Object $attribute48 = getAttribute48(); result = result * 59 + (($attribute48 == null) ? 43 : $attribute48.hashCode()); Object $attribute49 = getAttribute49(); result = result * 59 + (($attribute49 == null) ? 43 : $attribute49.hashCode()); Object $attribute50 = getAttribute50(); result = result * 59 + (($attribute50 == null) ? 43 : $attribute50.hashCode()); Object $attribute51 = getAttribute51(); result = result * 59 + (($attribute51 == null) ? 43 : $attribute51.hashCode()); Object $attribute52 = getAttribute52(); result = result * 59 + (($attribute52 == null) ? 43 : $attribute52.hashCode()); Object $attribute53 = getAttribute53(); result = result * 59 + (($attribute53 == null) ? 43 : $attribute53.hashCode()); Object $attribute54 = getAttribute54(); result = result * 59 + (($attribute54 == null) ? 43 : $attribute54.hashCode()); Object $attribute55 = getAttribute55(); result = result * 59 + (($attribute55 == null) ? 43 : $attribute55.hashCode()); Object $attribute56 = getAttribute56(); result = result * 59 + (($attribute56 == null) ? 43 : $attribute56.hashCode()); Object $attribute57 = getAttribute57(); result = result * 59 + (($attribute57 == null) ? 43 : $attribute57.hashCode()); Object $attribute58 = getAttribute58(); result = result * 59 + (($attribute58 == null) ? 43 : $attribute58.hashCode()); Object $attribute59 = getAttribute59(); result = result * 59 + (($attribute59 == null) ? 43 : $attribute59.hashCode()); Object $attribute60 = getAttribute60(); result = result * 59 + (($attribute60 == null) ? 43 : $attribute60.hashCode()); Object $attribute61 = getAttribute61(); result = result * 59 + (($attribute61 == null) ? 43 : $attribute61.hashCode()); Object $attribute62 = getAttribute62(); result = result * 59 + (($attribute62 == null) ? 43 : $attribute62.hashCode()); Object $attribute63 = getAttribute63(); result = result * 59 + (($attribute63 == null) ? 43 : $attribute63.hashCode()); Object $attribute64 = getAttribute64(); result = result * 59 + (($attribute64 == null) ? 43 : $attribute64.hashCode()); Object $attribute65 = getAttribute65(); result = result * 59 + (($attribute65 == null) ? 43 : $attribute65.hashCode()); Object $attribute66 = getAttribute66(); result = result * 59 + (($attribute66 == null) ? 43 : $attribute66.hashCode()); Object $attribute67 = getAttribute67(); result = result * 59 + (($attribute67 == null) ? 43 : $attribute67.hashCode()); Object $attribute68 = getAttribute68(); result = result * 59 + (($attribute68 == null) ? 43 : $attribute68.hashCode()); Object $attribute69 = getAttribute69(); result = result * 59 + (($attribute69 == null) ? 43 : $attribute69.hashCode()); Object $attribute70 = getAttribute70(); result = result * 59 + (($attribute70 == null) ? 43 : $attribute70.hashCode()); Object $attribute71 = getAttribute71(); result = result * 59 + (($attribute71 == null) ? 43 : $attribute71.hashCode()); Object $attribute72 = getAttribute72(); result = result * 59 + (($attribute72 == null) ? 43 : $attribute72.hashCode()); Object $attribute73 = getAttribute73(); result = result * 59 + (($attribute73 == null) ? 43 : $attribute73.hashCode()); Object $attribute74 = getAttribute74(); result = result * 59 + (($attribute74 == null) ? 43 : $attribute74.hashCode()); Object $attribute75 = getAttribute75(); result = result * 59 + (($attribute75 == null) ? 43 : $attribute75.hashCode()); Object $attribute76 = getAttribute76(); result = result * 59 + (($attribute76 == null) ? 43 : $attribute76.hashCode()); Object $attribute77 = getAttribute77(); result = result * 59 + (($attribute77 == null) ? 43 : $attribute77.hashCode()); Object $attribute78 = getAttribute78(); result = result * 59 + (($attribute78 == null) ? 43 : $attribute78.hashCode()); Object $attribute79 = getAttribute79(); result = result * 59 + (($attribute79 == null) ? 43 : $attribute79.hashCode()); Object $attribute80 = getAttribute80(); result = result * 59 + (($attribute80 == null) ? 43 : $attribute80.hashCode()); Object $attribute81 = getAttribute81(); result = result * 59 + (($attribute81 == null) ? 43 : $attribute81.hashCode()); Object $attribute82 = getAttribute82(); result = result * 59 + (($attribute82 == null) ? 43 : $attribute82.hashCode()); Object $attribute83 = getAttribute83(); result = result * 59 + (($attribute83 == null) ? 43 : $attribute83.hashCode()); Object $attribute84 = getAttribute84(); result = result * 59 + (($attribute84 == null) ? 43 : $attribute84.hashCode()); Object $attribute85 = getAttribute85(); result = result * 59 + (($attribute85 == null) ? 43 : $attribute85.hashCode()); Object $attribute86 = getAttribute86(); result = result * 59 + (($attribute86 == null) ? 43 : $attribute86.hashCode()); Object $attribute87 = getAttribute87(); result = result * 59 + (($attribute87 == null) ? 43 : $attribute87.hashCode()); Object $attribute88 = getAttribute88(); result = result * 59 + (($attribute88 == null) ? 43 : $attribute88.hashCode()); Object $attribute89 = getAttribute89(); result = result * 59 + (($attribute89 == null) ? 43 : $attribute89.hashCode()); Object $attribute90 = getAttribute90(); result = result * 59 + (($attribute90 == null) ? 43 : $attribute90.hashCode()); Object $attribute91 = getAttribute91(); result = result * 59 + (($attribute91 == null) ? 43 : $attribute91.hashCode()); Object $attribute92 = getAttribute92(); result = result * 59 + (($attribute92 == null) ? 43 : $attribute92.hashCode()); Object $attribute93 = getAttribute93(); result = result * 59 + (($attribute93 == null) ? 43 : $attribute93.hashCode()); Object $attribute94 = getAttribute94(); result = result * 59 + (($attribute94 == null) ? 43 : $attribute94.hashCode()); Object $attribute95 = getAttribute95(); result = result * 59 + (($attribute95 == null) ? 43 : $attribute95.hashCode()); Object $attribute96 = getAttribute96(); result = result * 59 + (($attribute96 == null) ? 43 : $attribute96.hashCode()); Object $attribute97 = getAttribute97(); result = result * 59 + (($attribute97 == null) ? 43 : $attribute97.hashCode()); Object $attribute98 = getAttribute98(); result = result * 59 + (($attribute98 == null) ? 43 : $attribute98.hashCode()); Object $attribute99 = getAttribute99(); result = result * 59 + (($attribute99 == null) ? 43 : $attribute99.hashCode()); Object $attribute100 = getAttribute100(); result = result * 59 + (($attribute100 == null) ? 43 : $attribute100.hashCode()); Object $attribute101 = getAttribute101(); result = result * 59 + (($attribute101 == null) ? 43 : $attribute101.hashCode()); Object $attribute102 = getAttribute102(); result = result * 59 + (($attribute102 == null) ? 43 : $attribute102.hashCode()); Object $attribute103 = getAttribute103(); result = result * 59 + (($attribute103 == null) ? 43 : $attribute103.hashCode()); Object $attribute104 = getAttribute104(); result = result * 59 + (($attribute104 == null) ? 43 : $attribute104.hashCode()); Object $attribute105 = getAttribute105(); result = result * 59 + (($attribute105 == null) ? 43 : $attribute105.hashCode()); Object $attribute106 = getAttribute106(); result = result * 59 + (($attribute106 == null) ? 43 : $attribute106.hashCode()); Object $attribute107 = getAttribute107(); result = result * 59 + (($attribute107 == null) ? 43 : $attribute107.hashCode()); Object $attribute108 = getAttribute108(); result = result * 59 + (($attribute108 == null) ? 43 : $attribute108.hashCode()); Object $attribute109 = getAttribute109(); result = result * 59 + (($attribute109 == null) ? 43 : $attribute109.hashCode()); Object $attribute110 = getAttribute110(); result = result * 59 + (($attribute110 == null) ? 43 : $attribute110.hashCode()); Object $attribute111 = getAttribute111(); result = result * 59 + (($attribute111 == null) ? 43 : $attribute111.hashCode()); Object $attribute112 = getAttribute112(); result = result * 59 + (($attribute112 == null) ? 43 : $attribute112.hashCode()); Object $attribute113 = getAttribute113(); result = result * 59 + (($attribute113 == null) ? 43 : $attribute113.hashCode()); Object $attribute117 = getAttribute117(); result = result * 59 + (($attribute117 == null) ? 43 : $attribute117.hashCode()); Object $attribute118 = getAttribute118(); result = result * 59 + (($attribute118 == null) ? 43 : $attribute118.hashCode()); Object $attribute114 = getAttribute114(); result = result * 59 + (($attribute114 == null) ? 43 : $attribute114.hashCode()); Object $attribute115 = getAttribute115(); result = result * 59 + (($attribute115 == null) ? 43 : $attribute115.hashCode()); Object $attribute116 = getAttribute116(); result = result * 59 + (($attribute116 == null) ? 43 : $attribute116.hashCode()); Object $attribute119 = getAttribute119(); result = result * 59 + (($attribute119 == null) ? 43 : $attribute119.hashCode()); Object $attribute120 = getAttribute120(); result = result * 59 + (($attribute120 == null) ? 43 : $attribute120.hashCode()); Object $attribute121 = getAttribute121(); result = result * 59 + (($attribute121 == null) ? 43 : $attribute121.hashCode()); Object $attribute122 = getAttribute122(); result = result * 59 + (($attribute122 == null) ? 43 : $attribute122.hashCode()); Object $attribute123 = getAttribute123(); result = result * 59 + (($attribute123 == null) ? 43 : $attribute123.hashCode()); Object $attribute124 = getAttribute124(); result = result * 59 + (($attribute124 == null) ? 43 : $attribute124.hashCode()); Object $attribute125 = getAttribute125(); result = result * 59 + (($attribute125 == null) ? 43 : $attribute125.hashCode()); Object $attribute126 = getAttribute126(); result = result * 59 + (($attribute126 == null) ? 43 : $attribute126.hashCode()); Object $attribute127 = getAttribute127(); result = result * 59 + (($attribute127 == null) ? 43 : $attribute127.hashCode()); Object $attribute128 = getAttribute128(); result = result * 59 + (($attribute128 == null) ? 43 : $attribute128.hashCode()); Object $attribute129 = getAttribute129(); result = result * 59 + (($attribute129 == null) ? 43 : $attribute129.hashCode()); Object $attribute130 = getAttribute130(); result = result * 59 + (($attribute130 == null) ? 43 : $attribute130.hashCode()); Object $attribute131 = getAttribute131(); result = result * 59 + (($attribute131 == null) ? 43 : $attribute131.hashCode()); Object $attribute132 = getAttribute132(); result = result * 59 + (($attribute132 == null) ? 43 : $attribute132.hashCode()); Object $attribute133 = getAttribute133(); result = result * 59 + (($attribute133 == null) ? 43 : $attribute133.hashCode()); Object $attribute134 = getAttribute134(); result = result * 59 + (($attribute134 == null) ? 43 : $attribute134.hashCode()); Object $attribute135 = getAttribute135(); result = result * 59 + (($attribute135 == null) ? 43 : $attribute135.hashCode()); Object $attribute136 = getAttribute136(); result = result * 59 + (($attribute136 == null) ? 43 : $attribute136.hashCode()); Object $attribute137 = getAttribute137(); result = result * 59 + (($attribute137 == null) ? 43 : $attribute137.hashCode()); Object $attribute138 = getAttribute138(); result = result * 59 + (($attribute138 == null) ? 43 : $attribute138.hashCode()); Object $attribute139 = getAttribute139(); result = result * 59 + (($attribute139 == null) ? 43 : $attribute139.hashCode()); Object $attribute140 = getAttribute140(); result = result * 59 + (($attribute140 == null) ? 43 : $attribute140.hashCode()); Object $attribute141 = getAttribute141(); result = result * 59 + (($attribute141 == null) ? 43 : $attribute141.hashCode()); Object $attribute142 = getAttribute142(); result = result * 59 + (($attribute142 == null) ? 43 : $attribute142.hashCode()); Object $attribute143 = getAttribute143(); result = result * 59 + (($attribute143 == null) ? 43 : $attribute143.hashCode()); Object $attribute144 = getAttribute144(); result = result * 59 + (($attribute144 == null) ? 43 : $attribute144.hashCode()); Object $attribute145 = getAttribute145(); result = result * 59 + (($attribute145 == null) ? 43 : $attribute145.hashCode()); Object $attribute146 = getAttribute146(); result = result * 59 + (($attribute146 == null) ? 43 : $attribute146.hashCode()); Object $attribute147 = getAttribute147(); result = result * 59 + (($attribute147 == null) ? 43 : $attribute147.hashCode()); Object $attribute148 = getAttribute148(); result = result * 59 + (($attribute148 == null) ? 43 : $attribute148.hashCode()); Object $attribute149 = getAttribute149(); result = result * 59 + (($attribute149 == null) ? 43 : $attribute149.hashCode()); Object $attribute150 = getAttribute150(); result = result * 59 + (($attribute150 == null) ? 43 : $attribute150.hashCode()); Object $lastUpBy = getLastUpBy(); result = result * 59 + (($lastUpBy == null) ? 43 : $lastUpBy.hashCode()); Object $lastUpDatetime = getLastUpDatetime(); result = result * 59 + (($lastUpDatetime == null) ? 43 : $lastUpDatetime.hashCode()); Object $updfilename = getUpdfilename(); result = result * 59 + (($updfilename == null) ? 43 : $updfilename.hashCode()); Object $updlineitem = getUpdlineitem(); result = result * 59 + (($updlineitem == null) ? 43 : $updlineitem.hashCode()); Object $lnvproIdentifier = getLnvproIdentifier(); result = result * 59 + (($lnvproIdentifier == null) ? 43 : $lnvproIdentifier.hashCode()); Object $updateStatus = getUpdateStatus(); return result * 59 + (($updateStatus == null) ? 43 : $updateStatus.hashCode()); } public String toString() { return "IbTUpdSnRepos(serialNo=" + getSerialNo() + ", requestId=" + getRequestId() + ", regionId=" + getRegionId() + ", systemId=" + getSystemId() + ", createdBy=" + getCreatedBy() + ", creationDatetime=" + getCreationDatetime() + ", lastModBy=" + getLastModBy() + ", lastModDatetime=" + getLastModDatetime() + ", attribute01=" + getAttribute01() + ", attribute02=" + getAttribute02() + ", attribute03=" + getAttribute03() + ", attribute04=" + getAttribute04() + ", attribute05=" + getAttribute05() + ", attribute06=" + getAttribute06() + ", attribute07=" + getAttribute07() + ", attribute08=" + getAttribute08() + ", attribute09=" + getAttribute09() + ", attribute10=" + getAttribute10() + ", attribute11=" + getAttribute11() + ", attribute12=" + getAttribute12() + ", attribute13=" + getAttribute13() + ", attribute14=" + getAttribute14() + ", attribute15=" + getAttribute15() + ", attribute16=" + getAttribute16() + ", attribute17=" + getAttribute17() + ", attribute18=" + getAttribute18() + ", attribute19=" + getAttribute19() + ", attribute20=" + getAttribute20() + ", attribute21=" + getAttribute21() + ", attribute22=" + getAttribute22() + ", attribute23=" + getAttribute23() + ", attribute24=" + getAttribute24() + ", attribute25=" + getAttribute25() + ", attribute26=" + getAttribute26() + ", attribute27=" + getAttribute27() + ", attribute28=" + getAttribute28() + ", attribute29=" + getAttribute29() + ", attribute30=" + getAttribute30() + ", attribute31=" + getAttribute31() + ", attribute32=" + getAttribute32() + ", attribute33=" + getAttribute33() + ", attribute34=" + getAttribute34() + ", attribute35=" + getAttribute35() + ", attribute36=" + getAttribute36() + ", attribute37=" + getAttribute37() + ", attribute38=" + getAttribute38() + ", attribute39=" + getAttribute39() + ", attribute40=" + getAttribute40() + ", attribute41=" + getAttribute41() + ", attribute42=" + getAttribute42() + ", attribute43=" + getAttribute43() + ", attribute44=" + getAttribute44() + ", attribute45=" + getAttribute45() + ", attribute46=" + getAttribute46() + ", attribute47=" + getAttribute47() + ", attribute48=" + getAttribute48() + ", attribute49=" + getAttribute49() + ", attribute50=" + getAttribute50() + ", attribute51=" + getAttribute51() + ", attribute52=" + getAttribute52() + ", attribute53=" + getAttribute53() + ", attribute54=" + getAttribute54() + ", attribute55=" + getAttribute55() + ", attribute56=" + getAttribute56() + ", attribute57=" + getAttribute57() + ", attribute58=" + getAttribute58() + ", attribute59=" + getAttribute59() + ", attribute60=" + getAttribute60() + ", attribute61=" + getAttribute61() + ", attribute62=" + getAttribute62() + ", attribute63=" + getAttribute63() + ", attribute64=" + getAttribute64() + ", attribute65=" + getAttribute65() + ", attribute66=" + getAttribute66() + ", attribute67=" + getAttribute67() + ", attribute68=" + getAttribute68() + ", attribute69=" + getAttribute69() + ", attribute70=" + getAttribute70() + ", attribute71=" + getAttribute71() + ", attribute72=" + getAttribute72() + ", attribute73=" + getAttribute73() + ", attribute74=" + getAttribute74() + ", attribute75=" + getAttribute75() + ", attribute76=" + getAttribute76() + ", attribute77=" + getAttribute77() + ", attribute78=" + getAttribute78() + ", attribute79=" + getAttribute79() + ", attribute80=" + getAttribute80() + ", attribute81=" + getAttribute81() + ", attribute82=" + getAttribute82() + ", attribute83=" + getAttribute83() + ", attribute84=" + getAttribute84() + ", attribute85=" + getAttribute85() + ", attribute86=" + getAttribute86() + ", attribute87=" + getAttribute87() + ", attribute88=" + getAttribute88() + ", attribute89=" + getAttribute89() + ", attribute90=" + getAttribute90() + ", attribute91=" + getAttribute91() + ", attribute92=" + getAttribute92() + ", attribute93=" + getAttribute93() + ", attribute94=" + getAttribute94() + ", attribute95=" + getAttribute95() + ", attribute96=" + getAttribute96() + ", attribute97=" + getAttribute97() + ", attribute98=" + getAttribute98() + ", attribute99=" + getAttribute99() + ", attribute100=" + getAttribute100() + ", attribute101=" + getAttribute101() + ", attribute102=" + getAttribute102() + ", attribute103=" + getAttribute103() + ", attribute104=" + getAttribute104() + ", attribute105=" + getAttribute105() + ", attribute106=" + getAttribute106() + ", attribute107=" + getAttribute107() + ", attribute108=" + getAttribute108() + ", attribute109=" + getAttribute109() + ", attribute110=" + getAttribute110() + ", attribute111=" + getAttribute111() + ", attribute112=" + getAttribute112() + ", attribute113=" + getAttribute113() + ", attribute117=" + getAttribute117() + ", attribute118=" + getAttribute118() + ", attribute114=" + getAttribute114() + ", attribute115=" + getAttribute115() + ", attribute116=" + getAttribute116() + ", attribute119=" + getAttribute119() + ", attribute120=" + getAttribute120() + ", attribute121=" + getAttribute121() + ", attribute122=" + getAttribute122() + ", attribute123=" + getAttribute123() + ", attribute124=" + getAttribute124() + ", attribute125=" + getAttribute125() + ", attribute126=" + getAttribute126() + ", attribute127=" + getAttribute127() + ", attribute128=" + getAttribute128() + ", attribute129=" + getAttribute129() + ", attribute130=" + getAttribute130() + ", attribute131=" + getAttribute131() + ", attribute132=" + getAttribute132() + ", attribute133=" + getAttribute133() + ", attribute134=" + getAttribute134() + ", attribute135=" + getAttribute135() + ", attribute136=" + getAttribute136() + ", attribute137=" + getAttribute137() + ", attribute138=" + getAttribute138() + ", attribute139=" + getAttribute139() + ", attribute140=" + getAttribute140() + ", attribute141=" + getAttribute141() + ", attribute142=" + getAttribute142() + ", attribute143=" + getAttribute143() + ", attribute144=" + getAttribute144() + ", attribute145=" + getAttribute145() + ", attribute146=" + getAttribute146() + ", attribute147=" + getAttribute147() + ", attribute148=" + getAttribute148() + ", attribute149=" + getAttribute149() + ", attribute150=" + getAttribute150() + ", lastUpBy=" + getLastUpBy() + ", lastUpDatetime=" + getLastUpDatetime() + ", updfilename=" + getUpdfilename() + ", updlineitem=" + getUpdlineitem() + ", lnvproIdentifier=" + getLnvproIdentifier() + ", updateStatus=" + getUpdateStatus() + ")"; }
/*     */    public String getSerialNo() {
/*  10 */     return this.serialNo;
/*     */   } public String getRequestId() {
/*  12 */     return this.requestId;
/*     */   } public String getRegionId() {
/*  14 */     return this.regionId;
/*     */   } public String getSystemId() {
/*  16 */     return this.systemId;
/*     */   } public String getCreatedBy() {
/*  18 */     return this.createdBy;
/*     */   } public Date getCreationDatetime() {
/*  20 */     return this.creationDatetime;
/*     */   } public String getLastModBy() {
/*  22 */     return this.lastModBy;
/*     */   } public Date getLastModDatetime() {
/*  24 */     return this.lastModDatetime;
/*     */   } public String getAttribute01() {
/*  26 */     return this.attribute01;
/*     */   } public Date getAttribute02() {
/*  28 */     return this.attribute02;
/*     */   } public String getAttribute03() {
/*  30 */     return this.attribute03;
/*     */   } public String getAttribute04() {
/*  32 */     return this.attribute04;
/*     */   } public String getAttribute05() {
/*  34 */     return this.attribute05;
/*     */   } public String getAttribute06() {
/*  36 */     return this.attribute06;
/*     */   } public String getAttribute07() {
/*  38 */     return this.attribute07;
/*     */   } public String getAttribute08() {
/*  40 */     return this.attribute08;
/*     */   } public String getAttribute09() {
/*  42 */     return this.attribute09;
/*     */   } public Date getAttribute10() {
/*  44 */     return this.attribute10;
/*     */   } public String getAttribute11() {
/*  46 */     return this.attribute11;
/*     */   } public String getAttribute12() {
/*  48 */     return this.attribute12;
/*     */   } public String getAttribute13() {
/*  50 */     return this.attribute13;
/*     */   } public String getAttribute14() {
/*  52 */     return this.attribute14;
/*     */   } public String getAttribute15() {
/*  54 */     return this.attribute15;
/*     */   } public String getAttribute16() {
/*  56 */     return this.attribute16;
/*     */   } public String getAttribute17() {
/*  58 */     return this.attribute17;
/*     */   } public Date getAttribute18() {
/*  60 */     return this.attribute18;
/*     */   } public String getAttribute19() {
/*  62 */     return this.attribute19;
/*     */   } public String getAttribute20() {
/*  64 */     return this.attribute20;
/*     */   } public String getAttribute21() {
/*  66 */     return this.attribute21;
/*     */   } public String getAttribute22() {
/*  68 */     return this.attribute22;
/*     */   } public String getAttribute23() {
/*  70 */     return this.attribute23;
/*     */   } public String getAttribute24() {
/*  72 */     return this.attribute24;
/*     */   } public String getAttribute25() {
/*  74 */     return this.attribute25;
/*     */   } public String getAttribute26() {
/*  76 */     return this.attribute26;
/*     */   } public String getAttribute27() {
/*  78 */     return this.attribute27;
/*     */   } public String getAttribute28() {
/*  80 */     return this.attribute28;
/*     */   } public String getAttribute29() {
/*  82 */     return this.attribute29;
/*     */   } public String getAttribute30() {
/*  84 */     return this.attribute30;
/*     */   } public String getAttribute31() {
/*  86 */     return this.attribute31;
/*     */   } public String getAttribute32() {
/*  88 */     return this.attribute32;
/*     */   } public String getAttribute33() {
/*  90 */     return this.attribute33;
/*     */   } public String getAttribute34() {
/*  92 */     return this.attribute34;
/*     */   } public String getAttribute35() {
/*  94 */     return this.attribute35;
/*     */   } public String getAttribute36() {
/*  96 */     return this.attribute36;
/*     */   } public String getAttribute37() {
/*  98 */     return this.attribute37;
/*     */   } public String getAttribute38() {
/* 100 */     return this.attribute38;
/*     */   } public String getAttribute39() {
/* 102 */     return this.attribute39;
/*     */   } public String getAttribute40() {
/* 104 */     return this.attribute40;
/*     */   } public String getAttribute41() {
/* 106 */     return this.attribute41;
/*     */   } public String getAttribute42() {
/* 108 */     return this.attribute42;
/*     */   } public String getAttribute43() {
/* 110 */     return this.attribute43;
/*     */   } public String getAttribute44() {
/* 112 */     return this.attribute44;
/*     */   } public Date getAttribute45() {
/* 114 */     return this.attribute45;
/*     */   } public String getAttribute46() {
/* 116 */     return this.attribute46;
/*     */   } public String getAttribute47() {
/* 118 */     return this.attribute47;
/*     */   } public String getAttribute48() {
/* 120 */     return this.attribute48;
/*     */   } public Date getAttribute49() {
/* 122 */     return this.attribute49;
/*     */   } public Date getAttribute50() {
/* 124 */     return this.attribute50;
/*     */   } public Date getAttribute51() {
/* 126 */     return this.attribute51;
/*     */   } public Date getAttribute52() {
/* 128 */     return this.attribute52;
/*     */   } public Date getAttribute53() {
/* 130 */     return this.attribute53;
/*     */   } public String getAttribute54() {
/* 132 */     return this.attribute54;
/*     */   } public String getAttribute55() {
/* 134 */     return this.attribute55;
/*     */   } public String getAttribute56() {
/* 136 */     return this.attribute56;
/*     */   } public String getAttribute57() {
/* 138 */     return this.attribute57;
/*     */   } public String getAttribute58() {
/* 140 */     return this.attribute58;
/*     */   } public String getAttribute59() {
/* 142 */     return this.attribute59;
/*     */   } public String getAttribute60() {
/* 144 */     return this.attribute60;
/*     */   } public String getAttribute61() {
/* 146 */     return this.attribute61;
/*     */   } public String getAttribute62() {
/* 148 */     return this.attribute62;
/*     */   } public String getAttribute63() {
/* 150 */     return this.attribute63;
/*     */   } public String getAttribute64() {
/* 152 */     return this.attribute64;
/*     */   } public String getAttribute65() {
/* 154 */     return this.attribute65;
/*     */   } public String getAttribute66() {
/* 156 */     return this.attribute66;
/*     */   } public String getAttribute67() {
/* 158 */     return this.attribute67;
/*     */   } public String getAttribute68() {
/* 160 */     return this.attribute68;
/*     */   } public String getAttribute69() {
/* 162 */     return this.attribute69;
/*     */   } public String getAttribute70() {
/* 164 */     return this.attribute70;
/*     */   } public String getAttribute71() {
/* 166 */     return this.attribute71;
/*     */   } public String getAttribute72() {
/* 168 */     return this.attribute72;
/*     */   } public String getAttribute73() {
/* 170 */     return this.attribute73;
/*     */   } public String getAttribute74() {
/* 172 */     return this.attribute74;
/*     */   } public String getAttribute75() {
/* 174 */     return this.attribute75;
/*     */   } public String getAttribute76() {
/* 176 */     return this.attribute76;
/*     */   } public String getAttribute77() {
/* 178 */     return this.attribute77;
/*     */   } public String getAttribute78() {
/* 180 */     return this.attribute78;
/*     */   } public Date getAttribute79() {
/* 182 */     return this.attribute79;
/*     */   } public String getAttribute80() {
/* 184 */     return this.attribute80;
/*     */   } public Date getAttribute81() {
/* 186 */     return this.attribute81;
/*     */   } public String getAttribute82() {
/* 188 */     return this.attribute82;
/*     */   } public String getAttribute83() {
/* 190 */     return this.attribute83;
/*     */   } public String getAttribute84() {
/* 192 */     return this.attribute84;
/*     */   } public String getAttribute85() {
/* 194 */     return this.attribute85;
/*     */   } public String getAttribute86() {
/* 196 */     return this.attribute86;
/*     */   } public String getAttribute87() {
/* 198 */     return this.attribute87;
/*     */   } public String getAttribute88() {
/* 200 */     return this.attribute88;
/*     */   } public String getAttribute89() {
/* 202 */     return this.attribute89;
/*     */   } public String getAttribute90() {
/* 204 */     return this.attribute90;
/*     */   } public String getAttribute91() {
/* 206 */     return this.attribute91;
/*     */   } public String getAttribute92() {
/* 208 */     return this.attribute92;
/*     */   } public String getAttribute93() {
/* 210 */     return this.attribute93;
/*     */   } public String getAttribute94() {
/* 212 */     return this.attribute94;
/*     */   } public String getAttribute95() {
/* 214 */     return this.attribute95;
/*     */   } public String getAttribute96() {
/* 216 */     return this.attribute96;
/*     */   } public String getAttribute97() {
/* 218 */     return this.attribute97;
/*     */   } public String getAttribute98() {
/* 220 */     return this.attribute98;
/*     */   } public String getAttribute99() {
/* 222 */     return this.attribute99;
/*     */   } public String getAttribute100() {
/* 224 */     return this.attribute100;
/*     */   } public String getAttribute101() {
/* 226 */     return this.attribute101;
/*     */   } public String getAttribute102() {
/* 228 */     return this.attribute102;
/*     */   } public String getAttribute103() {
/* 230 */     return this.attribute103;
/*     */   } public String getAttribute104() {
/* 232 */     return this.attribute104;
/*     */   } public String getAttribute105() {
/* 234 */     return this.attribute105;
/*     */   } public String getAttribute106() {
/* 236 */     return this.attribute106;
/*     */   } public String getAttribute107() {
/* 238 */     return this.attribute107;
/*     */   } public String getAttribute108() {
/* 240 */     return this.attribute108;
/*     */   } public String getAttribute109() {
/* 242 */     return this.attribute109;
/*     */   } public String getAttribute110() {
/* 244 */     return this.attribute110;
/*     */   } public String getAttribute111() {
/* 246 */     return this.attribute111;
/*     */   } public String getAttribute112() {
/* 248 */     return this.attribute112;
/*     */   } public String getAttribute113() {
/* 250 */     return this.attribute113;
/*     */   } public Date getAttribute117() {
/* 252 */     return this.attribute117;
/*     */   } public String getAttribute118() {
/* 254 */     return this.attribute118;
/*     */   } public Date getAttribute114() {
/* 256 */     return this.attribute114;
/*     */   } public Date getAttribute115() {
/* 258 */     return this.attribute115;
/*     */   } public String getAttribute116() {
/* 260 */     return this.attribute116;
/*     */   } public String getAttribute119() {
/* 262 */     return this.attribute119;
/*     */   } public String getAttribute120() {
/* 264 */     return this.attribute120;
/*     */   } public String getAttribute121() {
/* 266 */     return this.attribute121;
/*     */   } public String getAttribute122() {
/* 268 */     return this.attribute122;
/*     */   } public String getAttribute123() {
/* 270 */     return this.attribute123;
/*     */   }
/* 272 */   public String getAttribute124() { return this.attribute124; }
/* 273 */   public String getAttribute125() { return this.attribute125; }
/* 274 */   public String getAttribute126() { return this.attribute126; }
/* 275 */   public String getAttribute127() { return this.attribute127; }
/* 276 */   public String getAttribute128() { return this.attribute128; }
/* 277 */   public String getAttribute129() { return this.attribute129; }
/* 278 */   public String getAttribute130() { return this.attribute130; }
/* 279 */   public String getAttribute131() { return this.attribute131; }
/* 280 */   public String getAttribute132() { return this.attribute132; }
/* 281 */   public String getAttribute133() { return this.attribute133; }
/* 282 */   public String getAttribute134() { return this.attribute134; }
/* 283 */   public String getAttribute135() { return this.attribute135; }
/* 284 */   public String getAttribute136() { return this.attribute136; }
/* 285 */   public String getAttribute137() { return this.attribute137; }
/* 286 */   public String getAttribute138() { return this.attribute138; }
/* 287 */   public String getAttribute139() { return this.attribute139; }
/* 288 */   public String getAttribute140() { return this.attribute140; }
/* 289 */   public String getAttribute141() { return this.attribute141; }
/* 290 */   public String getAttribute142() { return this.attribute142; }
/* 291 */   public String getAttribute143() { return this.attribute143; }
/* 292 */   public String getAttribute144() { return this.attribute144; }
/* 293 */   public String getAttribute145() { return this.attribute145; }
/* 294 */   public String getAttribute146() { return this.attribute146; }
/* 295 */   public String getAttribute147() { return this.attribute147; }
/* 296 */   public String getAttribute148() { return this.attribute148; }
/* 297 */   public String getAttribute149() { return this.attribute149; } public String getAttribute150() {
/* 298 */     return this.attribute150;
/*     */   } public String getLastUpBy() {
/* 300 */     return this.lastUpBy;
/*     */   } public Date getLastUpDatetime() {
/* 302 */     return this.lastUpDatetime;
/*     */   } public String getUpdfilename() {
/* 304 */     return this.updfilename;
/*     */   } public String getUpdlineitem() {
/* 306 */     return this.updlineitem;
/*     */   } public String getLnvproIdentifier() {
/* 308 */     return this.lnvproIdentifier;
/*     */   } public String getUpdateStatus() {
/* 310 */     return this.updateStatus;
/*     */   } }


/* Location:              E:\Moto_S3_Downloads\2025\moto-rsd-prod-secure\Wars\iqs-0.0.1-SNAPSHOT.war!\WEB-INF\classes\com\lenovo\iqs\entity\IbTUpdSnRepos.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */